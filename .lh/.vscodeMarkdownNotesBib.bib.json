{"sourceFile": ".vscodeMarkdownNotesBib.bib", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1747258249865, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747258255297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,41 @@\n-.vscodeMarkdownNotesBib.bib\n\\ No newline at end of file\n+@article{saas,\n+  author = {SAAS},\n+  title = {SAAS Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n+\n+@article{crono,\n+  author = {CRONO},\n+  title = {CRONO Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n+\n+@article{mañana,\n+  author = {MAÑANA},\n+  title = {MAÑANA Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n+\n+@article{paisaje,\n+  author = {PAISAJE},\n+  title = {PAISAJE Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n+\n+@article{quick,\n+  author = {QUICK},\n+  title = {QUICK Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n+\n+@article{play,\n+  author = {PLAY},\n+  title = {PLAY Tag},\n+  journal = {Tags},\n+  year = {2025}\n+}\n\\ No newline at end of file\n"}], "date": 1747258249865, "name": "Commit-0", "content": ".vscodeMarkdownNotesBib.bib"}]}