{"sourceFile": "BRAIN/FRAMEWORKS/Estudiar.md", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1724679406781, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1724679595660, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,9 +13,10 @@\n \n ## Libros y recursos para investigar\n \n - Lectura rápida (Está el podcast de Superhábitos)\n-- HOW TO TAKE SMART NOTES by Sönke Ahrens\n+- Make it Stick\n+- Good Habits, Bad Habits\n \n ## Framework simplificado\n \n - Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n"}, {"date": 1724680759131, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,9 +19,9 @@\n \n ## Framework simplificado\n \n - Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n-- Escribir lo que recuerdo con mis palabras\n-- Hacer preguntas sobre el texto (consultar AI)\n+- Escribir lo que recuerdo con mis palabras o desde un copy paste\n+- Procesar con AI [[generar_wiki.md]]\n - Verificar veracidad de la información\n - Estructurar la info\n - Generar memories con enlaces\n"}, {"date": 1724681038579, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,7 +21,5 @@\n \n - Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n - Escribir lo que recuerdo con mis palabras o desde un copy paste\n - Procesar con AI [[generar_wiki.md]]\n-- Verificar veracidad de la información\n-- Estructurar la info\n-- Generar memories con enlaces\n+- Copiar los datos en el MD correspondiente en WIKI y MEMORY\n"}, {"date": 1724681077223, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n # Estudiar\n \n ## Objetivo\n \n-La idea es poder empezar a armar un framework para estudiar distintos temas. Empezando por una lista de cosas que quiero estudiar, y 2 frameworks, uno simplificado para cosas chicas o que sólo quiero saber por arriba, y otro más completo para cosas que quiero estudiar en profundidad.\n+La idea es poder empezar a armar un framework para estudiar distintos temas. Empezando por una lista de cosas que quiero estudiar, y un framework de como procesar cada idea.\n \n Toda la info va a ir a WIKI y si es un tema importante, se agrupa en un MD en FRAMEWORK. Además se separan disparadores en un MD en MEMORY.\n \n Voy a capturar información desde distintos lugares, como libros, podcasts, cursos, etc. guardando en alguno de los lugares temporales: mail, Todoist o Hoja\n@@ -16,9 +16,9 @@\n - Lectura rápida (Está el podcast de Superhábitos)\n - Make it Stick\n - Good Habits, Bad Habits\n \n-## Framework simplificado\n+## Framework\n \n - Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n - Escribir lo que recuerdo con mis palabras o desde un copy paste\n - Procesar con AI [[generar_wiki.md]]\n"}, {"date": 1738350103391, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,37 @@\n+# Estudiar\n+\n+## Objetivo\n+\n+La idea es poder empezar a armar un framework para estudiar distintos temas. Empezando por una lista de cosas que quiero estudiar, y un framework de como procesar cada idea.\n+\n+Toda la info va a ir a WIKI y si es un tema importante, se agrupa en un MD en FRAMEWORK. Además se separan disparadores en un MD en MEMORY.\n+\n+Voy a capturar información desde distintos lugares, como libros, podcasts, cursos, etc. guardando en alguno de los lugares temporales: mail, Todoist o Hoja\n+\n+## Cosas que quiero estudiar\n+\n+\n+## Libros y recursos para investigar\n+\n+- Lectura rápida (Está el podcast de Superhábitos)\n+- Make it Stick\n+- Good Habits, Bad Habits\n+\n+## Framework\n+\n+- Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n+- Escribir lo que recuerdo con mis palabras o desde un copy paste\n+- Procesar con AI [[generar_wiki.md]]\n+- Copiar los datos en el MD correspondiente en WIKI y MEMORY\n+\n+## FRAMEWORK AMPLIADO\n+\n+La técnica más importante que se destaca es la práctica de recuperación (retrieval practice), que consiste en intentar recordar información en lugar de simplemente repasarla. Esto ayuda a consolidar el aprendizaje y a entender mejor los conceptos. Además, se mencionan otras estrategias efectivas:\n+\n+- Espaciado (spacing): Distribuir el estudio en el tiempo en lugar de concentrarlo en una sola sesión.\n+- Intercalado (interleaving): Alternar entre diferentes temas o tipos de problemas durante el estudio.\n+- Elaboración: Profundizar en los conceptos, relacionándolos con lo que ya sabes.\n+- Generación: Crear tus propias respuestas o explicaciones en lugar de solo leerlas.\n+- Reflexión: Pensar críticamente sobre lo que has aprendido y cómo lo has aplicado.\n+\n+El video también recomienda un artículo llamado \"Teaching the Science of Learning\", que resume estas técnicas de manera más detallada. El libro en cuestión es breve (menos de 300 páginas) y está respaldado por investigaciones científicas, lo que lo convierte en una herramienta valiosa para cualquiera que quiera mejorar su capacidad de aprendizaje.\n\\ No newline at end of file\n"}, {"date": 1738350238792, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,29 +34,21 @@\n - Generación: Crear tus propias respuestas o explicaciones en lugar de solo leerlas.\n - Reflexión: Pensar críticamente sobre lo que has aprendido y cómo lo has aplicado.\n \n El video también recomienda un artículo llamado \"Teaching the Science of Learning\", que resume estas técnicas de manera más detallada. El libro en cuestión es breve (menos de 300 páginas) y está respaldado por investigaciones científicas, lo que lo convierte en una herramienta valiosa para cualquiera que quiera mejorar su capacidad de aprendizaje.\n-# Estudiar\n \n-## Objetivo\n+Es muy útil generar preguntas relacionadas\n \n-La idea es poder empezar a armar un framework para estudiar distintos temas. Empezando por una lista de cosas que quiero estudiar, y un framework de como procesar cada idea.\n+1. ¿Cuál es la definición principal del concepto presentado?\n+2. ¿Cuáles son los aspectos más importantes a considerar sobre este tema?\n+3. ¿Qué aplicaciones prácticas tiene en el mundo real?\n+4. ¿Existen teorías o modelos que respalden esta idea?\n+5. ¿Cuáles son las ventajas y desventajas asociadas con este concepto?\n \n-Toda la info va a ir a WIKI y si es un tema importante, se agrupa en un MD en FRAMEWORK. Además se separan disparadores en un MD en MEMORY.\n+Como también es útil pensar, generar y escribir estos conceptos:\n \n-Voy a capturar información desde distintos lugares, como libros, podcasts, cursos, etc. guardando en alguno de los lugares temporales: mail, Todoist o Hoja\n-\n-## Cosas que quiero estudiar\n-\n-\n-## Libros y recursos para investigar\n-\n-- Lectura rápida (Está el podcast de Superhábitos)\n-- Make it Stick\n-- Good Habits, Bad Habits\n\\ No newline at end of file\n-\n-## Framework\n-\n-- Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n-- Escribir lo que recuerdo con mis palabras o desde un copy paste\n-- Procesar con AI [[generar_wiki.md]]\n-- Copiar los datos en el MD correspondiente en WIKI y MEMORY\n+- **Definición y Contexto:**\n+- **Aspectos Clave:**\n+- **Aplicaciones Prácticas:**\n+- **Teorías y Modelos:**\n+- **Ventajas y Desventajas:**\n+- **Resúmen en un párrafo para que cualquiera lo pueda entender**\n\\ No newline at end of file\n"}], "date": 1724679406781, "name": "Commit-0", "content": "# Estudiar\n\n## Objetivo\n\nLa idea es poder empezar a armar un framework para estudiar distintos temas. Empezando por una lista de cosas que quiero estudiar, y 2 frameworks, uno simplificado para cosas chicas o que sólo quiero saber por arriba, y otro más completo para cosas que quiero estudiar en profundidad.\n\nToda la info va a ir a WIKI y si es un tema importante, se agrupa en un MD en FRAMEWORK. Además se separan disparadores en un MD en MEMORY.\n\nVoy a capturar información desde distintos lugares, como libros, podcasts, cursos, etc. guardando en alguno de los lugares temporales: mail, Todoist o Hoja\n\n## Cosas que quiero estudiar\n\n\n## Libros y recursos para investigar\n\n- Lectura rápida (Está el podcast de Superhábitos)\n- HOW TO TAKE SMART NOTES by Sönk<PERSON> Ahrens\n\n## Framework simplificado\n\n- Evaluar si ya existe un MD sobre esto y si vale la pena crear un MD nuevo o que vaya directo a MEMORY\n- Escribir lo que recuerdo con mis palabras\n- Hacer preguntas sobre el texto (consultar AI)\n- Verificar veracidad de la información\n- Estructurar la info\n- Generar memories con enlaces\n"}]}