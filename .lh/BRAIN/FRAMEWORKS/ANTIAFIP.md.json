{"sourceFile": "BRAIN/FRAMEWORKS/ANTIAFIP.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1734966776693, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1734966776693, "name": "Commit-0", "content": "# Framework ANTIAFIP\r\n\r\n## Pasos para controlar\r\n\r\n- Correr un corregir desde el mail4\r\n- Entrar a prod y ver si tiene bien el crt e ini\r\n- Correr un antiafip salida y ver si da error\r\n- Si dan todos error, puede ser que el último número aprobado esté mal, revisar desde el sistema que te lleva a AFIP\r\n\r\n---\r\n\r\n- Tener en cuenta que todas las modificaciones de código hay que hacerlas en prod\r\n- Si el error es NoneType' object has no attribute 'copy', es porque el último número aprobado está mal en la tabla categorias_ventas\r\n\r\n## Comandos para verificar info\r\n\r\nSELECT idventa, estado, estadocae, numero, total, cae, cuit, dni, idcliente FROM saas_7242.ventas WHERE idtipoventa = 1 AND numero > 26820 ORDER BY numero LIMIT 200;\r\n\r\n## Enlaces de numeración"}]}