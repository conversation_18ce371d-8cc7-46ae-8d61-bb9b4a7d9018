{"sourceFile": "BRAIN/FRAMEWORKS/TODO.md", "activeCommit": 0, "commits": [{"activePatchIndex": 14, "patches": [{"date": 1747221233442, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747221937442, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,11 @@\n+# OBJETIVOS\n+\n+Estoy jugando a cambiar mi sistema de TODO con los siguiente en mente:\n+\n+- Sólo texto markdown en múltiples archivos\n+- Sincronizado con celular (a través de MEGA y con markdown en celu también)\n+- Integrado a VSC\n+\n+## TODO\n+\n+- [ ] Buscar forma de utilizar fechas recurrentes\n"}, {"date": 1747222766625, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,7 +5,10 @@\n - Sólo texto markdown en múltiples archivos\n - Sincronizado con celular (a través de MEGA y con markdown en celu también)\n - Integrado a VSC\n \n+- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n+\n ## TODO\n \n - [ ] Buscar forma de utilizar fechas recurrentes\n+- [ ]\n"}, {"date": 1747222790349, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,14 @@\n - Sólo texto markdown en múltiples archivos\n - Sincronizado con celular (a través de MEGA y con markdown en celu también)\n - Integrado a VSC\n \n-- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n+*REVISAR:*\n+- Tratar de terminar en orden cada flow\n+- Planear al iniciar con AI 10 80 10\n+- Terminar con doc y MKT\n+- No hay week sino flujos interminables\n+- Toggl con @\n \n ## TODO\n \n - [ ] Buscar forma de utilizar fechas recurrentes\n"}, {"date": 1747222882981, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,36 @@\n - Terminar con doc y MKT\n - No hay week sino flujos interminables\n - Toggl con @\n \n+Estoy jugando a reformular mí BRAIN\n+\n+Con el nuevo libro, todo texto en VSC\n+\n+- Buscar filtro de tags con colores\n+- Pasar Week matándola\n+- Audio a texto y archivo\n+- Nuevo teclado y atajos con machete\n+- Mas memoria en mail\n+- Toggl en vsc\n+- Calendario compartido\n+- Toggle text copiloto\n+- color en p\n+- Deploy BETA a PROD\n+- Ver si me queda más cómodo Obsidian\n+    - [ ] Buscar un par de plugins\n+    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n+    - [ ] Ver algo @p1\n+    - [ ] Acostumbrar a los teclados #flow #focus #high\n+    - Ordenar esta semana\n+    - Que funcione en celu +SAAS\n+    - Algo de +CRONO\n+    -\n+    - Refill por ahora framework y después automatizado\n+- Quiero planear como empezar con el entrenamiento\n+- Ordenar la oficina\n+\n+\n ## TODO\n \n - [ ] Buscar forma de utilizar fechas recurrentes\n - [ ]\n"}, {"date": 1747222937258, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,8 +38,10 @@\n     -\n     - Refill por ahora framework y después automatizado\n - Quiero planear como empezar con el entrenamiento\n - Ordenar la oficina\n+- Tener un buen día\n+- Tener un registro de done\n \n \n ## TODO\n \n"}, {"date": 1747229946714, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,9 +22,9 @@\n - Audio a texto y archivo\n - Nuevo teclado y atajos con machete\n - Mas memoria en mail\n - Toggl en vsc\n-- Calendario compartido\n+- Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n - Toggle text copiloto\n - color en p\n - Deploy BETA a PROD\n - Ver si me queda más cómodo Obsidian\n"}, {"date": 1747229957389, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,8 +23,9 @@\n - Nuevo teclado y atajos con machete\n - Mas memoria en mail\n - Toggl en vsc\n - Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n+- Ver como puedo ver viajes fácil\n - Toggle text copiloto\n - color en p\n - Deploy BETA a PROD\n - Ver si me queda más cómodo Obsidian\n"}, {"date": 1747230106732, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,8 +18,9 @@\n Con el nuevo libro, todo texto en VSC\n \n - Buscar filtro de tags con colores\n - Pasar Week matándola\n+- El board pasarlo completo a todo.md\n - Audio a texto y archivo\n - Nuevo teclado y atajos con machete\n - Mas memoria en mail\n - Toggl en vsc\n"}, {"date": 1747231563505, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,8 +19,9 @@\n \n - Buscar filtro de tags con colores\n - Pasar Week matándola\n - El board pasarlo completo a todo.md\n+- Ampliar el mail memory\n - Audio a texto y archivo\n - Nuevo teclado y atajos con machete\n - Mas memoria en mail\n - Toggl en vsc\n"}, {"date": 1747233286637, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,8 +20,10 @@\n - Buscar filtro de tags con colores\n - Pasar Week matándola\n - El board pasarlo completo a todo.md\n - Ampliar el mail memory\n+- Los pagos me tienen que quedar cómodos como cada mes\n+- Ver un buen día arriba de todo\n - Audio a texto y archivo\n - Nuevo teclado y atajos con machete\n - Mas memoria en mail\n - Toggl en vsc\n"}, {"date": 1747252798082, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,54 +1,12 @@\n # OBJETIVOS\n \n-Estoy jugando a cambiar mi sistema de TODO con los siguiente en mente:\n+Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:\n \n-- Sólo texto markdown en múltiples archivos\n-- Sincronizado con celular (a través de MEGA y con markdown en celu también)\n-- Integrado a VSC\n+- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)\n+- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)\n+- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)\n+- Planear cada @flow al iniciar con AI 10-80-10\n \n-*REVISAR:*\n-- Tratar de terminar en orden cada flow\n-- Planear al iniciar con AI 10 80 10\n-- Terminar con doc y MKT\n-- No hay week sino flujos interminables\n-- Toggl con @\n+## PROCESOS Y FRAMEWORKS\n \n-Estoy jugando a reformular mí BRAIN\n-\n-Con el nuevo libro, todo texto en VSC\n-\n-- Buscar filtro de tags con colores\n-- Pasar Week matándola\n-- El board pasarlo completo a todo.md\n-- Ampliar el mail memory\n-- Los pagos me tienen que quedar cómodos como cada mes\n-- Ver un buen día arriba de todo\n-- Audio a texto y archivo\n-- Nuevo teclado y atajos con machete\n-- Mas memoria en mail\n-- Toggl en vsc\n-- Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n-- Ver como puedo ver viajes fácil\n-- Toggle text copiloto\n-- color en p\n-- Deploy BETA a PROD\n-- Ver si me queda más cómodo Obsidian\n-    - [ ] Buscar un par de plugins\n-    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n-    - [ ] Ver algo @p1\n-    - [ ] Acostumbrar a los teclados #flow #focus #high\n-    - Ordenar esta semana\n-    - Que funcione en celu +SAAS\n-    - Algo de +CRONO\n-    -\n-    - Refill por ahora framework y después automatizado\n-- Quiero planear como empezar con el entrenamiento\n-- Ordenar la oficina\n-- Tener un buen día\n-- Tener un registro de done\n-\n-\n-## TODO\n-\n-- [ ] Buscar forma de utilizar fechas recurrentes\n-- [ ]\n+- Puedo pasar audio a texto con MEGA y `transcribir`\n"}, {"date": 1747252834480, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,4 +9,15 @@\n \n ## PROCESOS Y FRAMEWORKS\n \n - Puedo pasar audio a texto con MEGA y `transcribir`\n+\n+\n+## TODO\n+\n+- [ ] Ampliar el mail memory\n+- [ ] Terminar el script para pasar audio a texto\n+- [ ] Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n+- [ ] Ver como puedo ver viajes fácil\n+- [ ] Crear el framework para [[next.md]]\n+- [ ] Los pagos me tienen que quedar cómodos como cada mes\n+- [ ] Buscar forma de utilizar fechas recurrentes\n"}, {"date": 1747252848709, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,4 +20,5 @@\n - [ ] Ver como puedo ver viajes fácil\n - [ ] Crear el framework para [[next.md]]\n - [ ] Los pagos me tienen que quedar cómodos como cada mes\n - [ ] Buscar forma de utilizar fechas recurrentes\n+- [ ] Terminar de pasar el resto de Todoist\n"}, {"date": 1747252951424, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,24 +0,0 @@\n-# OBJETIVOS\n-\n-Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:\n-\n-- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)\n-- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)\n-- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)\n-- Planear cada @flow al iniciar con AI 10-80-10\n-\n-## PROCESOS Y FRAMEWORKS\n-\n-- Puedo pasar audio a texto con MEGA y `transcribir`\n-\n-\n-## TODO\n-\n-- [ ] Ampliar el mail memory\n-- [ ] Terminar el script para pasar audio a texto\n-- [ ] Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n-- [ ] Ver como puedo ver viajes fácil\n-- [ ] Crear el framework para [[next.md]]\n-- [ ] Los pagos me tienen que quedar cómodos como cada mes\n-- [ ] Buscar forma de utilizar fechas recurrentes\n-- [ ] Terminar de pasar el resto de Todoist\n\\ No newline at end of file\n"}], "date": 1747221233442, "name": "Commit-0", "content": ""}]}