{"sourceFile": "BRAIN/FRAMEWORKS/Prompt AI.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1738196826914, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1738196826914, "name": "Commit-0", "content": "# HOW TO WRITE PROMPTS\r\n\r\n- Personalization: 222The prompt should be clear and concise. It should indicate exactly what the AI agent is expected to do and how you want it to write to you. For example, \"You are a specialist in the Stripe payment methods API and you are here to help with the use of the API. Always speak in a friendly and close manner, include code examples with the use of the API...\"\r\n\r\n- Context: The initial prompt should be related to the context in which the AI agent will be used. For example, if the agent will be used to help developers write code linked to a Python repository, the prompt could be something like \"Help with writing Python code.\"\r\n\r\n- Limitations: It's important to set limitations for the AI agent. For example, if you only want the agent to answer questions related to the information added in the context, you could set a rule that says \"Only answer questions related to the provided context.\"\r\n\r\n## Generic, Impersonal Content\r\n\r\nAI’s work can lack the depth and nuance that human writers provide. Make the text more interesting and relatable by:\r\n\r\n- Asking it to use interesting metaphors, vibrant descriptions, emotional appeal, or storytelling elements in its output\r\n\r\n- Requesting that it leave placeholders or suggest areas where you can include personal anecdotes or opinions\r\n\r\n## Unnatural Sentence Flow\r\n\r\nAI tends to produce content with uniform sentence lengths — making the text sound robotic. For a more engaging flow, request in your prompt that it vary rhythm and pacing by including a mix of short, medium, and long sentences.\r\nRepetitive Phrases\r\n\r\nAI can be a creative of habit — sticking to the same ol’ phrases again and again. To help it get out of its rut:\r\n\r\n- Tell it to avoid using one or more of these common phrases\r\n\r\n- If a common phrase (or any phrase you don’t love) sneaks into the output, ask it to rewrite that specific sentence in a way that replaces or removes that phrase\r\n\r\n## Lack of Substance\r\n\r\nAI likes to include data without always providing context or interpretation. Offer your readers greater insight by:\r\n\r\n- Requesting a detailed analysis of or interpretive commentary on the information it provides\r\n\r\n- Asking follow-up questions to specific content & asking it to rewrite its output to include the new information\r\n\r\n- Infusing its output with your own insights or specific details relevant to your audience\r\n\r\n## Verbosity\r\n\r\nAI has A LOT to say… and often adds in unnecessary details. To ensure every word and sentence add value:\r\n\r\n- In your prompt, say something like “keep your output brief”, “in 1-2 sentences” or “just the key points”\r\n\r\n- If it still generates a lengthy output, ask it to try again, giving it specific direction about what you do or don’t want to see\r\n\r\n- As a last stitch effort, manually remove redundant words, phrases, & sentences from the generated content\r\n\r\n## Formal Language\r\n\r\nAI often sounds pretty, well… stuffy. To reduce the formality:\r\n\r\n- In your prompt, be specific about what tone you want (e.g. ask it to write in an ‘informal’, ‘conversational’, or ‘professional yet slightly casual’ tone)\r\n\r\n- Consider asking it to use contradictions or include colloquial expressions (if that resonates with your audience)\r\n\r\nBy leveraging these tips, you’ll not only make more compelling content but ensure that your AI-assisted writing stands out in an increasingly automated world.\r\n\r\n## Mejorar escritura\r\n\r\nNecesito que mejores este texto que escribí y durante el proceso quiero que seas claro y conciso, llendo al punto y usando palabras simples. Dame 3 ejemplos para elegir."}]}