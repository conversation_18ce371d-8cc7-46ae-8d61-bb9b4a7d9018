{"sourceFile": "BRAIN/inbox.md", "activeCommit": 0, "commits": [{"activePatchIndex": 11, "patches": [{"date": 1747179215430, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747180117872, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,15 @@\n Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n \n Probando un tag @algo #algo\n \n-Ok entonces los #transponder\n+Ok entonces los #transponder ((tag|label))\n+((tag|label|background-color))\n+((tag|label|background-color|foreground-color))\n+((<tag|label))\n \n \n \n+\n Subir archivo Travesías\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n"}, {"date": 1747182904094, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,10 +11,22 @@\n ((tag|label|background-color))\n ((tag|label|background-color|foreground-color))\n ((<tag|label))\n \n+*lla*\n+- [x] <PERSON><PERSON> solo me faltan @label #high\n+- [ ]\n+- [ ] c :-):\n+- [ ] :kissing:\n+- [ ] :emoji:\n+- [ ] :box:💯\n+- [ ] :kiss:👍\n+- 🕙\n+- :+1:\n+-\n+_alto_\n \n+## dos\n \n-\n Subir archivo Travesías\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n"}, {"date": 1747182921635, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,33 @@\n+---\n+tags:\n+  - prueba\n+  - aguasabiertas\n+---\n+Ag<PERSON>gar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n+\n+Probando un tag @algo #algo\n+\n+Ok entonces los #transponder ((tag|label))\n+((tag|label|background-color))\n+((tag|label|background-color|foreground-color))\n+((<tag|label))\n+\n+*lla*\n+- [x] Tarea solo me faltan @label #high\n+- [ ]\n+- [ ] c :-):\n+- [ ] :kissing:\n+- [ ] :emoji:\n+- [ ] :box:💯\n+- [ ] :kiss:👍\n+- 🕙\n+- :+1:\n+- 👍\n+- 😗\n+_alto_\n+\n+## dos\n+\n+Subir archivo Travesías\n+https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n+\n"}, {"date": 1747183030017, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -30,36 +30,5 @@\n \n Subir archivo Travesías\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n----\n-tags:\n-  - prueba\n-  - aguasabiertas\n----\n-Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n-\n-Probando un tag @algo #algo\n-\n-Ok entonces los #transponder ((tag|label))\n-((tag|label|background-color))\n-((tag|label|background-color|foreground-color))\n-((<tag|label))\n-\n-*lla*\n-- [x] Tarea solo me faltan @label #high\n-- [ ]\n-- [ ] c :-):\n-- [ ] :kissing:\n-- [ ] :emoji:\n-- [ ] :box:💯\n-- [ ] :kiss:👍\n-- 🕙\n-- :+1:\n--\n-_alto_\n-\n-## dos\n-\n-Subir archivo Travesías\n-https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n-\n+- https://github.com/davraamides/todotxt-mode\n"}, {"date": 1747183041185, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,4 +31,138 @@\n Subir archivo Travesías\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n - https://github.com/davraamides/todotxt-mode\n+\n+\n+## NEXT MILESTONES\n+\n+- [ ] Primera\n+- [ ] Segunda\n+\n+\n+* Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n+\n+SAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\n+\n+Escribir ventajas de SaaS + Qloud para informática @focus\t- [https://mail.google.com/mail/u/0/#inbox/********************************](https://mail.google.com/mail/u/0/#inbox/********************************) - Analizar lo del mail de Diego sobre impuestos de productos\n+\n+CRONO Definir Framework Grabaciones @flow\tGrabar material de inscripciones y sports para ahora  Grabar todo para páginas de deportes  Dejar ordenado próximas grabaciones\n+\n+Grabar los vídeos de deportes\n+\n+- [  ] Armar curso fácil\n+\n+[ ]  A ver\n+\n+```html\n+lalalala\n+lalalala\n+```\n+\n+\n+\n+\n+\n+Ver y resumir consejos y los procesos que va a llevar (herramientas de AI)\n+\n+Probar técnicas de grabación (mkt y cursos)\n+\n+Encontrar buen ángulo para la cámara\n+\n+Probar fondo verde\n+\n+Errores Felipe y minors @flow\t- Calcula mal el descuento - Ver usuario - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático - Ya podemos hacer la carga en SaaS automática - Revisar el error de Rally Bs.As. y sólo errores urgentes - Mostrar totales de todas las cajas [https://scripts.saasargentina.com/?script=4](https://scripts.saasargentina.com/?script=4) - Igualar sueldos Crono y dividendos negativos - Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir. - De alguna forma se está pudiendo repetir los nombres de los eventos.\n+\n+[EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46) @flow\n+\n+[Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303) @flow\n+\n+AGENTE MCP Consultas @flow\tHasta tener un sincronizador y re ordenar proyecto\n+\n+SAAS Ayuda @flow\n+\n+CRONO Chips sólo n° @flow\n+\n+[Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n+\n+## FRAMEWORK\n+\n+Estoy jugando a reformular mí BRAIN\n+\n+Con el nuevo libro, todo texto en VSC\n+\n+- Buscar filtro de tags con colores\n+- Pasar Week matándola\n+- Audio a texto y archivo\n+- Nuevo teclado y atajos con machete\n+- Mas memoria en mail\n+- Toggl en vsc\n+- Calendario compartido\n+- Toggle text copiloto\n+- color en p\n+- Deploy BETA a PROD\n+- Ver si me queda más cómodo Obsidian\n+    - [ ] Buscar un par de plugins\n+    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n+    - [ ] Ver algo @p1\n+    - [ ] Acostumbrar a los teclados #flow #focus #high\n+    - Ordenar esta semana\n+    - Que funcione en celu +SAAS\n+    - Algo de +CRONO\n+    -\n+    - Refill por ahora framework y después automatizado\n+- Quiero planear como empezar con el entrenamiento\n+- Ordenar la oficina\n+\n+Ok anduvo algo\n+- [ ] Algo @tag @p1\n+- [ ] lala\n+\n+## PRIORIDA\n+\n+Desde el celular\n+\n+Bueno cambio algo más\n+\n+[Algo]()\n+\n+## TODOIST\n+\n+(La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n+\n+```todoist\n+filter: \"today | overdue\"\n+```\n+\n+Averiguar por ALGE Timing\n+\n+Today\n+\n+quick\n+\n+SOPORTE 🥇 / VENTAS\n+\n+Armar mi teclado Corne\n+\n+Av la plata 61 10B entre Rivadavia y Chaco CP 1184 CABA...\n+\n+Today\n+\n+play\n+\n+NIRVANA 👌 / UBUNTU\n+\n+Deploy BETA a PROD\n+\n+- Hay algo de la API para revisar...\n+\n+Today\n+\n+focus\n+\n+DEV 📦\n+\n+Vuelvo a la pileta 👏💪\n+\n+Today\n+nueva\n\\ No newline at end of file\n"}, {"date": 1747221624587, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,12 @@\n tags:\n   - prueba\n   - aguasabiertas\n ---\n+\n+👍\n+🗳️\n+}\n Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n \n Probando un tag @algo #algo\n \n@@ -13,9 +17,9 @@\n ((<tag|label))\n \n *lla*\n - [x] <PERSON><PERSON> solo me faltan @label #high\n-- [ ]\n+- [ ] ⏱️ 🟥 🔴 🛍️ 🪦 💯  🏃 🤸 🤸\n - [ ] c :-):\n - [ ] :kissing:\n - [ ] :emoji:\n - [ ] :box:💯\n"}, {"date": 1747223074719, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,73 +1,22 @@\n----\n-tags:\n-  - prueba\n-  - aguasabiertas\n----\n \n-👍\n-🗳️\n-}\n-Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n \n-Probando un tag @algo #algo\n \n-Ok entonces los #transponder ((tag|label))\n-((tag|label|background-color))\n-((tag|label|background-color|foreground-color))\n-((<tag|label))\n+## REVISAR QUE ES\n \n-*lla*\n-- [x] Tarea solo me faltan @label #high\n-- [ ] ⏱️ 🟥 🔴 🛍️ 🪦 💯  🏃 🤸 🤸\n-- [ ] c :-):\n-- [ ] :kissing:\n-- [ ] :emoji:\n-- [ ] :box:💯\n-- [ ] :kiss:👍\n-- 🕙\n-- :+1:\n-- 👍\n-- 😗\n-_alto_\n+-  Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n \n-## dos\n \n-Subir archivo Travesías\n+\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n - https://github.com/davraamides/todotxt-mode\n \n \n-## NEXT MILESTONES\n \n-- [ ] Primera\n-- [ ] Segunda\n \n \n-* Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n \n-SAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\n-\n-Escribir ventajas de SaaS + Qloud para informática @focus\t- [https://mail.google.com/mail/u/0/#inbox/********************************](https://mail.google.com/mail/u/0/#inbox/********************************) - Analizar lo del mail de Diego sobre impuestos de productos\n-\n-CRONO Definir Framework Grabaciones @flow\tGrabar material de inscripciones y sports para ahora  Grabar todo para páginas de deportes  Dejar ordenado próximas grabaciones\n-\n-Grabar los vídeos de deportes\n-\n-- [  ] Armar curso fácil\n-\n-[ ]  A ver\n-\n-```html\n-lalalala\n-lalalala\n-```\n-\n-\n-\n-\n-\n Ver y resumir consejos y los procesos que va a llevar (herramientas de AI)\n \n Probar técnicas de grabación (mkt y cursos)\n \n@@ -90,47 +39,10 @@\n [Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n \n ## FRAMEWORK\n \n-Estoy jugando a reformular mí BRAIN\n \n-Con el nuevo libro, todo texto en VSC\n \n-- Buscar filtro de tags con colores\n-- Pasar Week matándola\n-- Audio a texto y archivo\n-- Nuevo teclado y atajos con machete\n-- Mas memoria en mail\n-- Toggl en vsc\n-- Calendario compartido\n-- Toggle text copiloto\n-- color en p\n-- Deploy BETA a PROD\n-- Ver si me queda más cómodo Obsidian\n-    - [ ] Buscar un par de plugins\n-    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n-    - [ ] Ver algo @p1\n-    - [ ] Acostumbrar a los teclados #flow #focus #high\n-    - Ordenar esta semana\n-    - Que funcione en celu +SAAS\n-    - Algo de +CRONO\n-    -\n-    - Refill por ahora framework y después automatizado\n-- Quiero planear como empezar con el entrenamiento\n-- Ordenar la oficina\n-\n-Ok anduvo algo\n-- [ ] Algo @tag @p1\n-- [ ] lala\n-\n-## PRIORIDA\n-\n-Desde el celular\n-\n-Bueno cambio algo más\n-\n-[Algo]()\n-\n ## TODOIST\n \n (La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n \n"}, {"date": 1747229507552, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,75 +10,4 @@\n https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n \n - https://github.com/davraamides/todotxt-mode\n \n-\n-\n-\n-\n-\n-Ver y resumir consejos y los procesos que va a llevar (herramientas de AI)\n-\n-Probar técnicas de grabación (mkt y cursos)\n-\n-Encontrar buen ángulo para la cámara\n-\n-Probar fondo verde\n-\n-<PERSON><PERSON><PERSON> y minors @flow\t- Calcula mal el descuento - Ver usuario - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático - Ya podemos hacer la carga en SaaS automática - Revisar el error de Rally Bs.As. y sólo errores urgentes - Mostrar totales de todas las cajas [https://scripts.saasargentina.com/?script=4](https://scripts.saasargentina.com/?script=4) - Igualar sueldos Crono y dividendos negativos - Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir. - De alguna forma se está pudiendo repetir los nombres de los eventos.\n-\n-[EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46) @flow\n-\n-[Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303) @flow\n-\n-AGENTE MCP Consultas @flow\tHasta tener un sincronizador y re ordenar proyecto\n-\n-SAAS Ayuda @flow\n-\n-CRONO Chips sólo n° @flow\n-\n-[Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n-\n-## FRAMEWORK\n-\n-\n-\n-## TODOIST\n-\n-(La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n-\n-```todoist\n-filter: \"today | overdue\"\n-```\n-\n-Averiguar por ALGE Timing\n-\n-Today\n-\n-quick\n-\n-SOPORTE 🥇 / VENTAS\n-\n-Armar mi teclado Corne\n-\n-Av la plata 61 10B entre Rivadavia y Chaco CP 1184 CABA...\n-\n-Today\n-\n-play\n-\n-NIRVANA 👌 / UBUNTU\n-\n-Deploy BETA a PROD\n-\n-- Hay algo de la API para revisar...\n-\n-Today\n-\n-focus\n-\n-DEV 📦\n-\n-Vuelvo a la pileta 👏💪\n-\n-Today\n-nueva\n\\ No newline at end of file\n"}, {"date": 1747229672557, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,7 @@\n+## ORDENAR\n \n \n-\n ## REVISAR QUE ES\n \n -  Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n \n-\n-\n-https://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n-\n-- https://github.com/davraamides/todotxt-mode\n-\n"}, {"date": 1747341885775, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,13 @@\n ## ORDENAR\n \n-Arreglar puerta del auto \n+- [ ] Arreglar puerta del auto\n+- [ ] Pasar tenencias de Binance a Nexo\n+- [ ] Arreglar silla Maty\n+\n 100 gym\n 89 raksha\n \n-creo que puedo esitar\n \n ## REVISAR QUE ES\n \n -  Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n"}, {"date": 1747428005082, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,7 @@\n+## ORDENAR\n+\n+\n+\n+## REVISAR QUE ES\n+\n+-  Agregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n\\ No newline at end of file\n"}], "date": 1747179215430, "name": "Commit-0", "content": "---\ntags:\n  - prueba\n  - aguasabiertas\n---\nAgregar al archivo de deploy o crearlo, la explicación de Gemini para configurar Gitlab CI: https://gemini.google.com/app/d8cf47f8f39c825d?hl=es_419\n\nProbando un tag @algo #algo\n\nOk entonces los #transponder\n\n\n\nSubir archivo Travesías\nhttps://www.poppularshop.com/morbopoly?mcp_token=eyJwaWQiOjcwOTYxOSwic2lkIjoxNTgwMjAwNzI5LCJheCI6IjhjMzZhMTY3Y2RmMDlmYjJlYjYxMWQwYTZhZWFmNDY4IiwidHMiOjE3NDcxNzM3NDksImV4cCI6MTc0OTU5Mjk0OX0.Bd4ip_USgKxyNgllwRVntLovXyaVXlFGcGOaZvKqCWQ&fbclid=PAQ0xDSwKQoM9leHRuA2FlbQIxMAABp8Os3xxO19QNtYu5xoM66n_PbuL1D34vOMoZ0ywosLt7XRsRTOJYif9owwdO_aem_XK3Qu8k_9sP7Qh3dK-kvSQ\n\n"}]}