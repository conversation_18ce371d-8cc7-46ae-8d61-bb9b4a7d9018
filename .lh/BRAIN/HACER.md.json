{"sourceFile": "BRAIN/HACER.md", "activeCommit": 0, "commits": [{"activePatchIndex": 39, "patches": [{"date": 1725110713342, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1725113420078, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,24 +1,27 @@\n ## AHORA\n \n-- Chips Chile\n-- Reader Aldofo\n-- Puntos Emex\n+- [x] Chips Chile\n+- [x] Reader Aldofo\n+- [ ] Puntos Emex\n+- [ ] <PERSON><PERSON> Damian\n \n-- Llamada Nestor Caballo<PERSON>\n-- <PERSON><PERSON><PERSON>\n+- [ ] Llamada Nestor Caballos\n+- [ ] L<PERSON>ada Leandro Gonzales\n \n-- <PERSON><PERSON>\n-- Presupuesto Joaco Necochea\n-- Preparar pedido <PERSON>\n-- Preparar pedido <PERSON>\n+- [ ] Presupuesto Joaco Necochea\n+- [ ] Preparar pedido <PERSON>\n+- [ ] Preparar pedido <PERSON>\n+- [ ] Preparar evento Cuba\n+- [ ] Preparar pedido China\n \n-- Cargar pagos Ecuador\n+- [ ] Mandar a issues las ideas de Widgets y Acuatlón\n+- [ ] Cargar pagos Ecuador\n \n-- Tengo un pago de Claudio ya facturado sin informar\n-- <PERSON>ol<PERSON> usua<PERSON> tiene a favor un evento\n+- [ ] Tengo un pago de Claudio ya facturado sin informar\n+- [ ] Rodolfo usuahia tiene a favor un evento\n \n+\n ## A TODOIST\n \n - Buscar tu brújula y ver de ponerla en el auto\n - Probar la GoPro 3\n-- Lista de ideas para Acuatlón\n"}, {"date": 1725126176653, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,14 @@\n ## AHORA\n \n - [x] Chips Chile\n - [x] Reader Aldofo\n+\n - [ ] Puntos Emex\n - [ ] <PERSON><PERSON>\n \n - [ ] Llamada Nestor Caballos\n-- [ ] Llamada Leandro Go<PERSON>\n+- [ ] <PERSON><PERSON><PERSON> (falta mandarle documentación y hacer usuario)\n \n - [ ] Presupuesto Joaco Necochea\n - [ ] Preparar pedido <PERSON>\n - [ ] Preparar pedido <PERSON>\n"}, {"date": 1725205387566, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,24 +2,25 @@\n \n - [x] Chips Chile\n - [x] Reader Aldofo\n \n+- [ ] Preparar pedido <PERSON>\n+- [ ] Preparar pedido Claudio\n+- [ ] Cargar stock\n+- [ ] Presupuesto Joaco Necochea\n+- [ ] Preparar evento Cuba\n+- [ ] Preparar pedido China\n+\n - [ ] Puntos Emex\n - [ ] Saldo Damian\n \n - [ ] Llamada Nestor Caballos\n - [ ] <PERSON><PERSON><PERSON> (falta mandarle documentación y hacer usuario)\n \n-- [ ] Presupuesto Joaco Necochea\n-- [ ] Preparar pedido Juan Villalba\n-- [ ] Preparar pedido Claudio\n-- [ ] Preparar evento Cuba\n-- [ ] Preparar pedido China\n-\n - [ ] Mandar a issues las ideas de Widgets y Acuatlón\n - [ ] Cargar pagos Ecuador\n \n-- [ ] Tengo un pago de Claudio ya facturado sin informar\n+- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n - [ ] Rodolfo usuahia tiene a favor un evento\n \n \n ## A TODOIST\n"}, {"date": 1725243767634, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,29 +0,0 @@\n-## AHORA\n-\n-- [x] Chips Chile\n-- [x] Reader Aldofo\n-\n-- [ ] Preparar pedido <PERSON>\n-- [ ] Preparar pedido Claudio\n-- [ ] Cargar stock\n-- [ ] Presupuesto Joaco Necochea\n-- [ ] Preparar evento Cuba\n-- [ ] Preparar pedido China\n-\n-- [ ] Puntos Emex\n-- [ ] <PERSON><PERSON>\n-\n-- [ ] Llamada Nestor Caballos\n-- [ ] <PERSON><PERSON><PERSON> (falta mandarle documentación y hacer usuario)\n-\n-- [ ] Mandar a issues las ideas de Widgets y Acuatlón\n-- [ ] Cargar pagos Ecuador\n-\n-- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n-- [ ] <PERSON><PERSON><PERSON> usua<PERSON> tiene a favor un evento\n-\n-\n-## A TODOIST\n-\n-- Buscar tu brújula y ver de ponerla en el auto\n-- Probar la GoPro 3\n\\ No newline at end of file\n"}, {"date": 1726069469873, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,12 @@\n+# PRÓXIMOS\n \n+- [ ] Localidad para SARR\n+- [ ] Arrancar con cobros Colombia\n+\n+\n+\n+\n+# REFORMULAR TU ACEPTACIÓN DE PRIORIDADES\n+\n+- No puedo prometer funcionalidades, hay que resolverlo con lo que ya está\n+- La prioridad del otro, no es la mía\n"}, {"date": 1726069533694, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,7 @@\n # PRÓXIMOS\n \n+- [ ] Revisar error DH Andres Colombia\n - [ ] Localidad para SARR\n - [ ] Arrancar con cobros Colombia\n \n \n"}, {"date": 1726069809932, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +0,0 @@\n-# PRÓXIMOS\n-\n-- [ ] Revisar error DH Andres Colombia\n-- [ ] Localidad para SARR\n-- [ ] Arrancar con cobros Colombia\n-\n-\n-\n-\n-# REFORMULAR TU ACEPTACIÓN DE PRIORIDADES\n-\n-- No puedo prometer funcionalidades, hay que resolverlo con lo que ya está\n-- La prioridad del otro, no es la mía\n\\ No newline at end of file\n"}, {"date": 1726071366959, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,3 @@\n+### PENSAR PRIORIDADES\n \n+- Como reaccionar a: \"No te olvides ...\", \"Necesito urgente ...\"\n\\ No newline at end of file\n"}, {"date": 1726081198283, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,4 @@\n ### PENSAR PRIORIDADES\n \n-- Como reaccionar a: \"No te olvides ...\", \"Necesito urgente ...\"\n\\ No newline at end of file\n+- Cómo reaccionar a: \"No te olvides ...\", \"Necesito urgente ...\"\n+- Cómo gestionar las prioridades reales\n\\ No newline at end of file\n"}, {"date": 1726152413412, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +0,0 @@\n-### PENSAR PRIORIDADES\n-\n-- Cómo reaccionar a: \"No te olvides ...\", \"Necesito urgente ...\"\n-- Cómo gestionar las prioridades reales\n\\ No newline at end of file\n"}, {"date": 1726426781374, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,1 @@\n+Test unison\n\\ No newline at end of file\n"}, {"date": 1726426845049, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +0,0 @@\n-Test unison\n\\ No newline at end of file\n"}, {"date": 1730321040532, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,7 @@\n+AUDIOSTONE\n+\n+- [ ] Agregar una página más en el menú\n+- [ ] Modificar el botón en el home para que lleve a esa página\n+- [ ] Agregar los precios en cada página como flotante\n+- [ ] Agregar Whatsapp flotando\n+- [ ] Deploy\n\\ No newline at end of file\n"}, {"date": 1730378567134, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,10 @@\n AUDIOSTONE\n \n-- [ ] Agregar una página más en el menú\n-- [ ] Modificar el botón en el home para que lleve a esa página\n-- [ ] Agregar los precios en cada página como flotante\n-- [ ] Agregar Whatsapp flotando\n-- [ ] Deploy\n\\ No newline at end of file\n+- [x] Agregar una página más en el menú\n+- [x] Modificar el botón en el home para que lleve a esa página\n+- [x] Crear la nueva página\n+- [x] Agregar los precios en cada página como flotante\n+- [ ] No me aparecen los precios ni el WhatsApp flotante. O aparece y  está muy chiquito\n+- [ ] Line ARRIVED en lugar de arriving SOON\n+- [ ] Mail de Pablo al <NAME_EMAIL>\n+- [ ] Deploy\n"}, {"date": 1730382063307, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +0,0 @@\n-AUDIOSTONE\n-\n-- [x] Agregar una página más en el menú\n-- [x] Modificar el botón en el home para que lleve a esa página\n-- [x] Crear la nueva página\n-- [x] Agregar los precios en cada página como flotante\n-- [ ] No me aparecen los precios ni el WhatsApp flotante. O aparece y  está muy chiquito\n-- [ ] Line ARRIVED en lugar de arriving SOON\n-- [ ] Mail de Pablo al <NAME_EMAIL>\n-- [ ] Deploy\n\\ No newline at end of file\n"}, {"date": 1734361149849, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,7 @@\n-\n+- [ ] Ocultar aviso de URL\n+- [ ] Re-imprimir lo de Prefectura\n+- [ ] Calendario pastillas Mati\n+- [ ] Vamos con MailZero\n+- [ ] Mover travesía de los cerros\n+- [ ] Hacer lo de Brain disciplina\n+- [ ] Tratar de cerrar las cajas\n\\ No newline at end of file\n"}, {"date": 1734361222430, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n - [ ] Ocultar aviso de URL\n - [ ] Re-imprimir lo de Prefectura\n - [ ] Calendario pastillas Mati\n+- [ ] Ordenar oficina\n - [ ] Vamos con MailZero\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n\\ No newline at end of file\n"}, {"date": 1734365319502, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,5 +4,11 @@\n - [ ] Ordenar oficina\n - [ ] Vamos con MailZero\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n-- [ ] Tratar de cerrar las cajas\n\\ No newline at end of file\n+- [ ] Tratar de cerrar las cajas\n+\n+IMPRIMIR:\n+- Entradas recitales\n+- Fotos probar con delfines\n+- Calcomanía\n+- Hacer el cuadro de crono\n"}, {"date": 1734365467834, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,15 @@\n - [ ] Ocultar aviso de URL\n+- [ ] Ordenar oficina\n - [ ] Re-imprimir lo de Prefectura\n - [ ] Calendario pastillas Mati\n-- [ ] Ordenar oficina\n - [ ] Vamos con MailZero\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n \n IMPRIMIR:\n+- Calendario Mati\n - Entradas recitales\n - Fotos probar con delfines\n - Calcomanía\n - Hacer el cuadro de crono\n"}, {"date": 1734368432226, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,10 +6,23 @@\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n \n+NIRVANA\n+\n+- [ ] Cerrar el año con el documento y escribiendo tu archivo\n+- [ ] Editar Brain en celular y tablet\n+- [ ] 1 Sólo Now\n+\n+\n IMPRIMIR:\n - Calendario Mati\n - Entradas recitales\n - Fotos probar con delfines\n - Calcomanía\n - Hacer el cuadro de crono\n+\n+FLOW:\n+\n+- Quiero mejorar mi balance de 4 pilares: Dormir <PERSON>, Comer Bien, Entrenar y Meditar\n+- Me falta un plan y disciplina para Entrenar\n+- Me falta un poco de voluntad para Comer bien\n"}, {"date": 1734369023864, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,10 @@\n - [ ] Vamos con MailZero\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n+- [ ] Limpiar eventos de Crono\n+- [ ] Limpiar un poco el Board hasta fin de año\n \n NIRVANA\n \n - [ ] Cerrar el año con el documento y escribiendo tu archivo\n"}, {"date": 1734369239422, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,18 @@\n-- [ ] Ocultar aviso de URL\n+- [x] Ocultar aviso de URL\n - [ ] Ordenar oficina\n - [ ] Re-imprimir lo de Prefectura\n - [ ] Calendario pastillas Mati\n - [ ] Vamos con MailZero\n+- [ ] Consultas de ayuda SaaS\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n - [ ] Limpiar eventos de Crono\n-- [ ] Limpiar un poco el Board hasta fin de año\n \n NIRVANA\n \n+- [ ] Limpiar un poco el Board hasta fin de año\n - [ ] Cerrar el año con el documento y escribiendo tu archivo\n - [ ] Editar Brain en celular y tablet\n - [ ] 1 Sólo Now\n \n@@ -27,4 +28,5 @@\n \n - Quiero mejorar mi balance de 4 pilares: <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>trenar y Meditar\n - Me falta un plan y disciplina para Entrenar\n - Me falta un poco de voluntad para Comer bien\n+- Creo que podría funcionar en Hoy (ponerlo para evaluar más adelante en alguna de las revisiones)\n"}, {"date": 1734372440909, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,30 +1,28 @@\n - [x] Ocultar aviso de URL\n-- [ ] Ordenar oficina\n+- [x] Ordenar oficina\n - [ ] Re-imprimir lo de Prefectura\n - [ ] Calendario pastillas Mati\n+\n - [ ] Vamos con MailZero\n - [ ] Consultas de ayuda SaaS\n+\n - [ ] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n - [ ] Limpiar eventos de Crono\n \n+- [ ] Hacer el cuadro de crono\n+- [ ] Decorar la pieza\n+\n NIRVANA\n \n - [ ] Limpiar un poco el Board hasta fin de año\n - [ ] Cerrar el año con el documento y escribiendo tu archivo\n - [ ] Editar Brain en celular y tablet\n - [ ] 1 Sólo Now\n \n \n-IMPRIMIR:\n-- Calendario Mati\n-- Entradas recitales\n-- Fotos probar con delfines\n-- Calcomanía\n-- Hacer el cuadro de crono\n-\n FLOW:\n \n - Quiero mejorar mi balance de 4 pilares: Dormir Bien, Comer Bien, Entrenar y Meditar\n - Me falta un plan y disciplina para Entrenar\n"}, {"date": 1734378388066, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,17 @@\n - [x] Ocultar aviso de URL\n - [x] Ordenar oficina\n-- [ ] Re-imprimir lo de Prefectura\n-- [ ] Calendario pastillas Mati\n+- [x] Calendario pastillas Mati\n \n - [ ] Vamos con MailZero\n - [ ] Consultas de ayuda SaaS\n \n-- [ ] Mover travesía de los cerros\n+- [x] Mover travesía de los cerros\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n - [ ] Limpiar eventos de Crono\n \n+- [ ] Re-imprimir lo de Prefectura, calendario y otros\n - [ ] Hacer el cuadro de crono\n - [ ] Decorar la pieza\n \n NIRVANA\n"}, {"date": 1734441002419, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,20 +1,24 @@\n - [x] Ocultar aviso de URL\n - [x] Ordenar oficina\n - [x] Calendario pastillas Mati\n+- [x] Mover travesía de los cerros\n \n-- [ ] Vamos con MailZero\n+- [x] Vamos con MailZero\n - [ ] Consultas de ayuda SaaS\n+- [ ] Reunión Google\n \n-- [x] Mover travesía de los cerros\n+- [ ] Re-imprimir lo de Prefectura y entregar\n+\n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n - [ ] Limpiar eventos de Crono\n \n-- [ ] Re-imprimir lo de Prefectura, calendario y otros\n - [ ] Hacer el cuadro de crono\n-- [ ] Decorar la pieza\n+- [ ] Terminar mi pieza\n+- [ ] Imprimesiones\n \n+\n NIRVANA\n \n - [ ] Limpiar un poco el Board hasta fin de año\n - [ ] Cerrar el año con el documento y escribiendo tu archivo\n"}, {"date": 1734446132977, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,15 +3,20 @@\n - [x] Calendario pastillas Mati\n - [x] Mover travesía de los cerros\n \n - [x] Vamos con MailZero\n-- [ ] Consultas de ayuda SaaS\n-- [ ] Reunión Google\n+- [x] Reunión Google\n \n+- [ ] Consultas de ayuda SaaS @soporte\n+- [ ] Un poco DEV SaaS\n - [ ] Re-imprimir lo de Prefectura y entregar\n \n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n+\n+- [ ] Ver si puedo meter lo de datos extras bien para Gaby\n+- [ ] Facturas a Claudio\n+\n - [ ] Limpiar eventos de Crono\n \n - [ ] Hacer el cuadro de crono\n - [ ] Terminar mi pieza\n@@ -23,8 +28,9 @@\n - [ ] Limpiar un poco el Board hasta fin de año\n - [ ] Cerrar el año con el documento y escribiendo tu archivo\n - [ ] Editar Brain en celular y tablet\n - [ ] 1 Sólo Now\n+- [ ] Re-leer en tablet el plan de SaaS y de Crono\n \n \n FLOW:\n \n"}, {"date": 1734474502650, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,12 +4,12 @@\n - [x] Mover travesía de los cerros\n \n - [x] Vamos con MailZero\n - [x] Reunión Google\n+- [x] Re-imprimir lo de Prefectura y entregar\n \n-- [ ] Consultas de ayuda SaaS @soporte\n+- [x] Consultas de ayuda SaaS @soporte\n - [ ] Un poco DEV SaaS\n-- [ ] Re-imprimir lo de Prefectura y entregar\n \n - [ ] Hacer lo de Brain disciplina\n - [ ] Tratar de cerrar las cajas\n \n"}, {"date": 1734531249078, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,40 +0,0 @@\n-- [x] Ocultar aviso de URL\n-- [x] Ordenar oficina\n-- [x] Calendario pastillas Mati\n-- [x] Mover travesía de los cerros\n-\n-- [x] Vamos con MailZero\n-- [x] Reunión Google\n-- [x] Re-imprimir lo de Prefectura y entregar\n-\n-- [x] Consultas de ayuda SaaS @soporte\n-- [ ] Un poco DEV SaaS\n-\n-- [ ] Hacer lo de Brain disciplina\n-- [ ] Tratar de cerrar las cajas\n-\n-- [ ] Ver si puedo meter lo de datos extras bien para Gaby\n-- [ ] Facturas a Claudio\n-\n-- [ ] Limpiar eventos de Crono\n-\n-- [ ] Hacer el cuadro de crono\n-- [ ] Terminar mi pieza\n-- [ ] Imprimesiones\n-\n-\n-NIRVANA\n-\n-- [ ] Limpiar un poco el Board hasta fin de año\n-- [ ] Cerrar el año con el documento y escribiendo tu archivo\n-- [ ] Editar Brain en celular y tablet\n-- [ ] 1 Sólo Now\n-- [ ] Re-leer en tablet el plan de SaaS y de Crono\n-\n-\n-FLOW:\n-\n-- Quiero mejorar mi balance de 4 pilares: Dormir Bien, Comer Bien, Entrenar y Meditar\n-- Me falta un plan y disciplina para Entrenar\n-- Me falta un poco de voluntad para Comer bien\n-- Creo que podría funcionar en Hoy (ponerlo para evaluar más adelante en alguna de las revisiones)\n\\ No newline at end of file\n"}, {"date": 1734908987456, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,5 @@\n-\n+- Responder Uriel CAE\n+- Revisar chips Kart Chile\n+- Grupo Whatsapp prensa (mensaje de bienvenida)\n+- MailZero\n+- Brain desde tablet\n"}, {"date": 1734909003210, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n - Responder Uriel CAE\n - Revisar chips Kart Chile\n - Grupo Whatsapp prensa (mensaje de bienvenida)\n-- MailZero\n+- MailZero y semana\n - Brain desde tablet\n"}, {"date": 1734911917825, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +0,0 @@\n-- Responder Uriel CAE\n-- Revisar chips Kart Chile\n-- Grupo Whatsapp prensa (mensaje de bienvenida)\n-- MailZero y semana\n-- Brain desde tablet\n\\ No newline at end of file\n"}, {"date": 1735311156941, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,20 @@\n \n+\n+\n+\n+\n+*BRAIN VIAJE*\n+\n+*EJERCICIO*\n+\n+¿Cómo vas a entrenar? ¿En qué horario? ¿Qué técnicas de hábitos y disciplina podes usar? ¿Con Mati y/o Juli?¿Lo hacemos con app y/o con Simón?\n+\n+*AIC*\n+\n+- Pensar la grilla de posibilidades\n+- Pensar como trabajar con Mariano\n+\n+*TODOIST*\n+\n+- Se viene otro año con más tareas que tiempo\n+- ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\n\\ No newline at end of file\n"}, {"date": 1735311402710, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,4 @@\n-\n-\n-\n-\n-\n *BRAIN VIAJE*\n \n *EJERCICIO*\n \n"}, {"date": 1735311554973, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,5 +11,14 @@\n \n *TODOIST*\n \n - Se viene otro año con más tareas que tiempo\n-- ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\n\\ No newline at end of file\n+- ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\n+\n+*ENTRENAR*\n+\n+- Llevar archivo con plan y seguimiento de cada ejercicio\n+- Por 3 meses no cambiar el plan\n+- 3 veces por semana calistenia y 2 correr o nadar. Pero empiezo de a poco por un mes creando el hábito y midiendo sin avanzar.\n+- Elijo los ejercicios de calistenia para la plaza o casa, primero los básicos en 3 dias según grupo muscular\n+- Hago con YouTube a modo de recompensa\n+- El horario puede ser a las 11 AM, sino a las 15 y sino a las 19 si o si\n"}, {"date": 1735313305252, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,4 +21,9 @@\n - 3 veces por semana calistenia y 2 correr o nadar. Pero empiezo de a poco por un mes creando el hábito y midiendo sin avanzar.\n - Elijo los ejercicios de calistenia para la plaza o casa, primero los básicos en 3 dias según grupo muscular\n - <PERSON><PERSON> con YouTube a modo de recompensa\n - El horario puede ser a las 11 AM, sino a las 15 y sino a las 19 si o si\n+\n+*CHARLAS*\n+\n+- ¿Quieros?\n+- Historia Japón hija Mariano\n"}, {"date": 1735323247317, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,9 @@\n *TODOIST*\n \n - Se viene otro año con más tareas que tiempo\n - ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\n+- ¿Cómo va a ser tu web?\n \n *ENTRENAR*\n \n - Llevar archivo con plan y seguimiento de cada ejercicio\n"}, {"date": 1735323308626, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n \n - Se viene otro año con más tareas que tiempo\n - ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\n - ¿Cómo va a ser tu web?\n+- Hackatones con Prisci (trabajar en la web y el MKT de SaaS)\n \n *ENTRENAR*\n \n - Llevar archivo con plan y seguimiento de cada ejercicio\n"}, {"date": 1735563655322, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,26 +2,41 @@\n \r\n *AIC*\r\n \r\n - Pensar la grilla de posibilidades\r\n-- Proponer a Priscila trabajar en equipo. Si acepta empezar por reunión semanal y escribo el plan \r\n+- Proponer a Priscila trabajar en equipo. Si acepta empezar por reunión semanal y escribo el plan\r\n - Hackatones con Prisci (trabajar en la web y el MKT de SaaS)\r\n \r\n *TODOIST*\r\n \r\n - Se viene otro año con más tareas que tiempo\r\n - ¿Qué técnicas de time managment conocés? ¿Cuales te funcionaron o no? ¿Cuales no probaste?\r\n-Voy a probar bloqueo de calendario a la mañana \r\n+- Voy a probar bloqueo de calendario a la mañana\r\n \r\n *ENTRENAR*\r\n \r\n - Llevar archivo con plan y seguimiento de cada ejercicio. Va a ser un excel con columnas para los días y meses y filas los ejercicios. El plan va en el Excel.\r\n-Los ejercicios por ahora los elijo de Yt\r\n+- Los ejercicios por ahora los elijo de Yt\r\n - Por 3 meses no cambiar el plan\r\n - 3 veces por semana calistenia y 2 correr o nadar. Pero empiezo de a poco por un mes creando el hábito y midiendo sin avanzar.\r\n - Elijo los ejercicios de calistenia para la plaza o casa, primero los básicos en 3 dias según grupo muscular\r\n - Hago con YouTube a modo de recompensa\r\n - El horario puede ser a las 11 AM, sino a las 15 y sino a las 19 si o si\r\n \r\n+*SAAS*\r\n+\r\n+Las 2 prioridades van a ser\r\n+\r\n+- Scripts con AI (Incluyendo: integraciones, API, Logs, ML, etc)\r\n+- MKT de micro nicho\r\n+\r\n+*CRONO*\r\n+\r\n+Las prioridades van a ser\r\n+\r\n+- Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\r\n+- MKT de micro nicho por deporte\r\n+- Integración con Hardware en App v3.x\r\n+\r\n *FALTA*\r\n \r\n - Cómo va a ser tu web?\n\\ No newline at end of file\n"}, {"date": 1742306309582, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,26 @@\n+# AHORA\n+\n - [ ] Mails admin\n - [ ] Pagos Ride\n - [ ] ORDENAR cuentas Ecuador, Toti y deudores\n - [ ] ORDENAR Board SaaS, pasando issues a Diego\n\\ No newline at end of file\n - [ ] TERMINAR Selenium en n8n\n-- [ ] ORDENAR Todoist (reunión mañana)\n+- [ ] MKT SaaS\n+- [ ] MKT Crono\n+- [ ] ORDENAR Todoist (reunión mañana)\n+- [ ] Pedir otro reader + antenas\n+\n+\n+## SAAS\n+\n+- Pasar issues a Diego\n+- FE ARBA\n+- Ayuda\n+- Enlaces\n+- RAG\n+\n+## CRONO\n+\n+- Empezar eventos\n+- Avancar con tema pagos a Juli\n+- Encontrar Pico\n"}], "date": 1725110713342, "name": "Commit-0", "content": "## AHORA\n\n- Chips Chile\n- Reader Aldofo\n- Puntos Emex\n\n- Llamada Nestor <PERSON>\n- <PERSON><PERSON><PERSON>\n\n- <PERSON><PERSON>\n- Presupuesto <PERSON>och<PERSON>\n- Preparar pedido <PERSON>\n- Preparar pedido <PERSON>\n\n- Cargar pagos Ecuador\n\n- Tengo un pago de Claudio ya facturado sin informar\n- <PERSON><PERSON>fo usua<PERSON> tiene a favor un evento\n\n## A TODOIST\n\n- Buscar tu brújula y ver de ponerla en el auto\n- Probar la GoPro 3\n- Lista de ideas para Acuatlón\n"}]}