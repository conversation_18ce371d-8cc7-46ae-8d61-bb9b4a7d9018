{"sourceFile": "BRAIN/OUTPUTS/SOPORTE.html", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1747318729296, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1747318729296, "name": "Commit-0", "content": "<!DOCTYPE html>\r\n<html>\r\n<head>\r\n<title>SOPORTE.md</title>\r\n<meta http-equiv=\"Content-type\" content=\"text/html;charset=UTF-8\">\r\n\r\n<style>\r\n/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */\r\n/*---------------------------------------------------------------------------------------------\r\n *  Copyright (c) Microsoft Corporation. All rights reserved.\r\n *  Licensed under the MIT License. See License.txt in the project root for license information.\r\n *--------------------------------------------------------------------------------------------*/\r\n\r\nbody {\r\n\tfont-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, \"Segoe WPC\", \"Segoe UI\", \"Ubuntu\", \"Droid Sans\", sans-serif);\r\n\tfont-size: var(--vscode-markdown-font-size, 14px);\r\n\tpadding: 0 26px;\r\n\tline-height: var(--vscode-markdown-line-height, 22px);\r\n\tword-wrap: break-word;\r\n}\r\n\r\n#code-csp-warning {\r\n\tposition: fixed;\r\n\ttop: 0;\r\n\tright: 0;\r\n\tcolor: white;\r\n\tmargin: 16px;\r\n\ttext-align: center;\r\n\tfont-size: 12px;\r\n\tfont-family: sans-serif;\r\n\tbackground-color:#444444;\r\n\tcursor: pointer;\r\n\tpadding: 6px;\r\n\tbox-shadow: 1px 1px 1px rgba(0,0,0,.25);\r\n}\r\n\r\n#code-csp-warning:hover {\r\n\ttext-decoration: none;\r\n\tbackground-color:#007acc;\r\n\tbox-shadow: 2px 2px 2px rgba(0,0,0,.25);\r\n}\r\n\r\nbody.scrollBeyondLastLine {\r\n\tmargin-bottom: calc(100vh - 22px);\r\n}\r\n\r\nbody.showEditorSelection .code-line {\r\n\tposition: relative;\r\n}\r\n\r\nbody.showEditorSelection .code-active-line:before,\r\nbody.showEditorSelection .code-line:hover:before {\r\n\tcontent: \"\";\r\n\tdisplay: block;\r\n\tposition: absolute;\r\n\ttop: 0;\r\n\tleft: -12px;\r\n\theight: 100%;\r\n}\r\n\r\nbody.showEditorSelection li.code-active-line:before,\r\nbody.showEditorSelection li.code-line:hover:before {\r\n\tleft: -30px;\r\n}\r\n\r\n.vscode-light.showEditorSelection .code-active-line:before {\r\n\tborder-left: 3px solid rgba(0, 0, 0, 0.15);\r\n}\r\n\r\n.vscode-light.showEditorSelection .code-line:hover:before {\r\n\tborder-left: 3px solid rgba(0, 0, 0, 0.40);\r\n}\r\n\r\n.vscode-light.showEditorSelection .code-line .code-line:hover:before {\r\n\tborder-left: none;\r\n}\r\n\r\n.vscode-dark.showEditorSelection .code-active-line:before {\r\n\tborder-left: 3px solid rgba(255, 255, 255, 0.4);\r\n}\r\n\r\n.vscode-dark.showEditorSelection .code-line:hover:before {\r\n\tborder-left: 3px solid rgba(255, 255, 255, 0.60);\r\n}\r\n\r\n.vscode-dark.showEditorSelection .code-line .code-line:hover:before {\r\n\tborder-left: none;\r\n}\r\n\r\n.vscode-high-contrast.showEditorSelection .code-active-line:before {\r\n\tborder-left: 3px solid rgba(255, 160, 0, 0.7);\r\n}\r\n\r\n.vscode-high-contrast.showEditorSelection .code-line:hover:before {\r\n\tborder-left: 3px solid rgba(255, 160, 0, 1);\r\n}\r\n\r\n.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {\r\n\tborder-left: none;\r\n}\r\n\r\nimg {\r\n\tmax-width: 100%;\r\n\tmax-height: 100%;\r\n}\r\n\r\na {\r\n\ttext-decoration: none;\r\n}\r\n\r\na:hover {\r\n\ttext-decoration: underline;\r\n}\r\n\r\na:focus,\r\ninput:focus,\r\nselect:focus,\r\ntextarea:focus {\r\n\toutline: 1px solid -webkit-focus-ring-color;\r\n\toutline-offset: -1px;\r\n}\r\n\r\nhr {\r\n\tborder: 0;\r\n\theight: 2px;\r\n\tborder-bottom: 2px solid;\r\n}\r\n\r\nh1 {\r\n\tpadding-bottom: 0.3em;\r\n\tline-height: 1.2;\r\n\tborder-bottom-width: 1px;\r\n\tborder-bottom-style: solid;\r\n}\r\n\r\nh1, h2, h3 {\r\n\tfont-weight: normal;\r\n}\r\n\r\ntable {\r\n\tborder-collapse: collapse;\r\n}\r\n\r\ntable > thead > tr > th {\r\n\ttext-align: left;\r\n\tborder-bottom: 1px solid;\r\n}\r\n\r\ntable > thead > tr > th,\r\ntable > thead > tr > td,\r\ntable > tbody > tr > th,\r\ntable > tbody > tr > td {\r\n\tpadding: 5px 10px;\r\n}\r\n\r\ntable > tbody > tr + tr > td {\r\n\tborder-top: 1px solid;\r\n}\r\n\r\nblockquote {\r\n\tmargin: 0 7px 0 5px;\r\n\tpadding: 0 16px 0 10px;\r\n\tborder-left-width: 5px;\r\n\tborder-left-style: solid;\r\n}\r\n\r\ncode {\r\n\tfont-family: Menlo, Monaco, Consolas, \"Droid Sans Mono\", \"Courier New\", monospace, \"Droid Sans Fallback\";\r\n\tfont-size: 1em;\r\n\tline-height: 1.357em;\r\n}\r\n\r\nbody.wordWrap pre {\r\n\twhite-space: pre-wrap;\r\n}\r\n\r\npre:not(.hljs),\r\npre.hljs code > div {\r\n\tpadding: 16px;\r\n\tborder-radius: 3px;\r\n\toverflow: auto;\r\n}\r\n\r\npre code {\r\n\tcolor: var(--vscode-editor-foreground);\r\n\ttab-size: 4;\r\n}\r\n\r\n/** Theming */\r\n\r\n.vscode-light pre {\r\n\tbackground-color: rgba(220, 220, 220, 0.4);\r\n}\r\n\r\n.vscode-dark pre {\r\n\tbackground-color: rgba(10, 10, 10, 0.4);\r\n}\r\n\r\n.vscode-high-contrast pre {\r\n\tbackground-color: rgb(0, 0, 0);\r\n}\r\n\r\n.vscode-high-contrast h1 {\r\n\tborder-color: rgb(0, 0, 0);\r\n}\r\n\r\n.vscode-light table > thead > tr > th {\r\n\tborder-color: rgba(0, 0, 0, 0.69);\r\n}\r\n\r\n.vscode-dark table > thead > tr > th {\r\n\tborder-color: rgba(255, 255, 255, 0.69);\r\n}\r\n\r\n.vscode-light h1,\r\n.vscode-light hr,\r\n.vscode-light table > tbody > tr + tr > td {\r\n\tborder-color: rgba(0, 0, 0, 0.18);\r\n}\r\n\r\n.vscode-dark h1,\r\n.vscode-dark hr,\r\n.vscode-dark table > tbody > tr + tr > td {\r\n\tborder-color: rgba(255, 255, 255, 0.18);\r\n}\r\n\r\n</style>\r\n\r\n<style>\r\n/* Tomorrow Theme */\r\n/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */\r\n/* Original theme - https://github.com/chriskempson/tomorrow-theme */\r\n\r\n/* Tomorrow Comment */\r\n.hljs-comment,\r\n.hljs-quote {\r\n\tcolor: #8e908c;\r\n}\r\n\r\n/* Tomorrow Red */\r\n.hljs-variable,\r\n.hljs-template-variable,\r\n.hljs-tag,\r\n.hljs-name,\r\n.hljs-selector-id,\r\n.hljs-selector-class,\r\n.hljs-regexp,\r\n.hljs-deletion {\r\n\tcolor: #c82829;\r\n}\r\n\r\n/* Tomorrow Orange */\r\n.hljs-number,\r\n.hljs-built_in,\r\n.hljs-builtin-name,\r\n.hljs-literal,\r\n.hljs-type,\r\n.hljs-params,\r\n.hljs-meta,\r\n.hljs-link {\r\n\tcolor: #f5871f;\r\n}\r\n\r\n/* Tomorrow Yellow */\r\n.hljs-attribute {\r\n\tcolor: #eab700;\r\n}\r\n\r\n/* Tomorrow Green */\r\n.hljs-string,\r\n.hljs-symbol,\r\n.hljs-bullet,\r\n.hljs-addition {\r\n\tcolor: #718c00;\r\n}\r\n\r\n/* Tomorrow Blue */\r\n.hljs-title,\r\n.hljs-section {\r\n\tcolor: #4271ae;\r\n}\r\n\r\n/* Tomorrow Purple */\r\n.hljs-keyword,\r\n.hljs-selector-tag {\r\n\tcolor: #8959a8;\r\n}\r\n\r\n.hljs {\r\n\tdisplay: block;\r\n\toverflow-x: auto;\r\n\tcolor: #4d4d4c;\r\n\tpadding: 0.5em;\r\n}\r\n\r\n.hljs-emphasis {\r\n\tfont-style: italic;\r\n}\r\n\r\n.hljs-strong {\r\n\tfont-weight: bold;\r\n}\r\n</style>\r\n\r\n<style>\r\n/*\r\n * Markdown PDF CSS\r\n */\r\n\r\n body {\r\n\tfont-family: -apple-system, BlinkMacSystemFont, \"Segoe WPC\", \"Segoe UI\", \"Ubuntu\", \"Droid Sans\", sans-serif, \"Meiryo\";\r\n\tpadding: 0 12px;\r\n}\r\n\r\npre {\r\n\tbackground-color: #f8f8f8;\r\n\tborder: 1px solid #cccccc;\r\n\tborder-radius: 3px;\r\n\toverflow-x: auto;\r\n\twhite-space: pre-wrap;\r\n\toverflow-wrap: break-word;\r\n}\r\n\r\npre:not(.hljs) {\r\n\tpadding: 23px;\r\n\tline-height: 19px;\r\n}\r\n\r\nblockquote {\r\n\tbackground: rgba(127, 127, 127, 0.1);\r\n\tborder-color: rgba(0, 122, 204, 0.5);\r\n}\r\n\r\n.emoji {\r\n\theight: 1.4em;\r\n}\r\n\r\ncode {\r\n\tfont-size: 14px;\r\n\tline-height: 19px;\r\n}\r\n\r\n/* for inline code */\r\n:not(pre):not(.hljs) > code {\r\n\tcolor: #C9AE75; /* Change the old color so it seems less like an error */\r\n\tfont-size: inherit;\r\n}\r\n\r\n/* Page Break : use <div class=\"page\"/> to insert page break\r\n-------------------------------------------------------- */\r\n.page {\r\n\tpage-break-after: always;\r\n}\r\n\r\n</style>\r\n\r\n<script src=\"https://unpkg.com/mermaid/dist/mermaid.min.js\"></script>\r\n</head>\r\n<body>\r\n  <script>\r\n    mermaid.initialize({\r\n      startOnLoad: true,\r\n      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')\r\n          ? 'dark'\r\n          : 'default'\r\n    });\r\n  </script>\r\n<h1 id=\"%F0%9F%A5%87-roads--crono--soporte\">🏅 ROADS &gt; CRONO &gt; SOPORTE</h1>\r\n<hr>\r\n<h2 id=\"varios\">VARIOS</h2>\r\n<ul>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox0\" checked=\"true\"><label for=\"checkbox0\">Habilitar Beta Multimoneda a Javi</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox1\" checked=\"true\"><label for=\"checkbox1\">Recuperar tu tools</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox2\" checked=\"true\"><label for=\"checkbox2\">Arreglar chips Kart Chile (anotar que debe septiembre)</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox3\" checked=\"true\"><label for=\"checkbox3\">Aprobar pago Venezuela y comprar BTC</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox4\" checked=\"true\"><label for=\"checkbox4\">Desactivar enlace Panamá</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox5\" checked=\"true\"><label for=\"checkbox5\">Sacar texto Mendoza</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox6\" checked=\"true\"><label for=\"checkbox6\">Feedback Gaby Género</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox7\" checked=\"true\"><label for=\"checkbox7\">Responder Lona a Claudio</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox8\"><label for=\"checkbox8\">Procesar tus audios</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox9\"><label for=\"checkbox9\">Ver que terminas haciendo con Nequi</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox10\"><label for=\"checkbox10\">Configurar botones de pago Ride</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox11\"><label for=\"checkbox11\">SARR: Logos + Cat</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox12\"><label for=\"checkbox12\">Pedido Colombia</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox13\"><label for=\"checkbox13\">Vídeo explicativo Orozco</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox14\"><label for=\"checkbox14\">Reunion España rosfit</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox15\"><label for=\"checkbox15\">Factura 60 a Gabriela</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox16\"><label for=\"checkbox16\">Ordenar Todoist</label></p>\r\n</li>\r\n<li>\r\n<p><input type=\"checkbox\" id=\"checkbox17\"><label for=\"checkbox17\">Configurar botones Javi Ushuaia y ver presupuesto</label></p>\r\n</li>\r\n</ul>\r\n<h2 id=\"ordenar\">ORDENAR</h2>\r\n<ul>\r\n<li><input type=\"checkbox\" id=\"checkbox18\"><label for=\"checkbox18\">Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)</label></li>\r\n<li><input type=\"checkbox\" id=\"checkbox19\"><label for=\"checkbox19\">Buscar pileta</label></li>\r\n<li><input type=\"checkbox\" id=\"checkbox20\"><label for=\"checkbox20\">Cortar árbol mañana</label></li>\r\n<li><input type=\"checkbox\" id=\"checkbox21\"><label for=\"checkbox21\">Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)</label></li>\r\n</ul>\r\n<h2 id=\"ahora\">AHORA</h2>\r\n<p>Medio de pago Mercadopago\r\nPrecio 50.000 (pre inscripción)\r\nCarreras en las que se aplica (Open Shimano Enduro Las Vegas) O (campeonato Argentino Enduro\r\nCantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones) no hay limite cantidad.\r\nFecha y hora desde que se habilita este precio desde hoy\r\nFecha y hora hasta que se deshabilita este precio hasta el día 30/9 23:30hs</p>\r\n<p>Y si se puede configurar que ese pago tenga una especie de ID mejor para despues filtrar y hacer las cuentas</p>\r\n<hr>\r\n<p>.sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {\r\ndisplay: block;\r\n}</p>\r\n<p>.medalla {\r\ndisplay: none;\r\n}</p>\r\n<p>dale la factura al cuit de mi empresa 30-71745220-4 Bellas sur sas\r\nFactura por $60000</p>\r\n<ul>\r\n<li><input type=\"checkbox\" id=\"checkbox22\"><label for=\"checkbox22\">Tengo un pago de Claudio ya facturado sin informar, dejarla a favor</label></li>\r\n<li><input type=\"checkbox\" id=\"checkbox23\"><label for=\"checkbox23\">Rodolfo usuahia tiene a favor un evento</label></li>\r\n<li><input type=\"checkbox\" id=\"checkbox24\"><label for=\"checkbox24\">Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario</label></li>\r\n</ul>\r\n<h2 id=\"actualizo-apellidos\">ACTUALIZO APELLIDOS</h2>\r\n<p>UPDATE participantes AS p SET apellido =\r\n(SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\r\nWHERE idevento = 2176;</p>\r\n<p>UPDATE datosxparticipantes\r\nSET dato = DATE_FORMAT(STR_TO_DATE(dato, '%d/%m/%Y'), '%Y-%m-%d')\r\nWHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;</p>\r\n<p>UPDATE datosxparticipantes\r\nSET dato = TRUNCATE(dato, 0)\r\nWHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;</p>\r\n<h2 id=\"botones-por-pa%C3%ADs\">BOTONES POR PAÍS</h2>\r\n<p>PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\r\nCOLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\r\nCHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd</p>\r\n<p>-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\r\n-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG</p>\r\n<h2 id=\"precios-de-karting\">PRECIOS DE KARTING</h2>\r\n<p>Abono Sistema Karting x 1 mes: U$D 50\r\nhttps://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne</p>\r\n<p>Abono Sistema Karting x 6 meses: U$D 250\r\nhttps://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX</p>\r\n<p>Abono Sistema Karting x 12 meses: U$D 400\r\nhttps://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY</p>\r\n<p>Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)</p>\r\n<h2 id=\"mercadopago-para-ride\">MERCADOPAGO PARA RIDE</h2>\r\n<p>UPDATE precios SET idplataforma = 6, url = '<a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>' WHERE idprecio = 12;\r\nUPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;</p>\r\n<p>EX BOTÓN DE LA KAVA: <a href=\"https://mpago.la/1R2pnUB\" target=\"_blank\">Pagar</a>\r\nEX BOTÓN DE RIDE: <a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a></p>\r\n<p>RIDE:\r\nhttps://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D</p>\r\n<h2 id=\"chips-movidos\">CHIPS MOVIDOS</h2>\r\n<p>Son de ID 4 los míos (Hasta CI0200)\r\nUPDATE <code>tags</code> SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;</p>\r\n<p>Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\r\nUPDATE <code>tags</code> SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;</p>\r\n<p>Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\r\nUPDATE <code>tags</code> SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';</p>\r\n<p>Son de ID 41 de Ecuador-Colombia\r\nUPDATE <code>tags</code> SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt;= 7019 AND codigo &lt; 7400;\r\nUPDATE <code>tags</code> SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt; 7500 AND codigo &lt; 7821;\r\nUPDATE <code>tags</code> SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo &gt;= 7872 AND codigo &lt;= 8350;</p>\r\n<p>Son de ID 84 de Esquel\r\nUPDATE <code>tags</code> SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\r\nUPDATE <code>tags</code> SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';</p>\r\n<p>ES0000 a <EMAIL> (374)\r\nUPDATE <code>tags</code> SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\r\nDT000 a <EMAIL> (379)\r\nUPDATE <code>tags</code> SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';</p>\r\n<p>Son el ID 482 de <EMAIL>\r\n000000000000000000000001</p>\r\n<h2 id=\"chips-en-karting-kartodromo\">CHIPS EN KARTING KARTODROMO</h2>\r\n<p>SELECT * FROM <code>tags</code> WHERE idtag &gt;= 36206 AND idtag &lt;= 36235 ORDER BY idtag;\r\nhttps://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\r\nhttps://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950</p>\r\n<p><EMAIL>\r\nSan Juan F1</p>\r\n<p>CHILE:</p>\r\n<p>idorganizacion = 472;</p>\r\n<h2 id=\"respuesta-youtube\">RESPUESTA YOUTUBE</h2>\r\n<p>Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.</p>\r\n<p>Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.</p>\r\n<p>https://web.whatsapp.com/send?phone=5492944551009</p>\r\n\r\n</body>\r\n</html>\r\n"}]}