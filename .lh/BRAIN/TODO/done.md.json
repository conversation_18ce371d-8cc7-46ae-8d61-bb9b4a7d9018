{"sourceFile": "BRAIN/TODO/done.md", "activeCommit": 0, "commits": [{"activePatchIndex": 8, "patches": [{"date": 1747529982931, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748272041735, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,4 +62,46 @@\n ---\n \n ## 2025-05-18\n \n+\n+@varios\n+- [x] Firmar lunes antes de pileta\n+- [x] Reunión Añelo Run\n+- [x] Evaluar si usamos Crossing Capital\n+  - Reunión programada con Jon 19 de may. de 2025 14:45 -03 (-03:00)\n+- [x] Ver mail Josefina y limpiar mail inbox\n+- [x] Renovar Payoneer\n+\n+---\n+\n+@saas\n+- [x] Revisar y documentar API Pedido A https://gitlab.com/saasargentina/app/-/issues/2132\n+- [x] Revisar Ayuda y campos FAQ https://gitlab.com/saasargentina/app/-/issues/2103\n+\n+@crono\n+- [x] Reunión con Matias Ola por crono en Calafate\n+- [x] Transferir a Lucas\n+- [x] Pago 145 ya está en la personal\n+- [x] Transferir $605k a Claudio, menos eventos que debe y 2 cables 8m y cargar pagos\n+- [x] Encontrar pago Rally BSAS (https://alfa.saasargentina.com/clientes.php?a=ver&id=223)\n+\n+**Deployar primer versión de EVENTOS**\n+  - [x] [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n+\n+---\n+\n+@saas\n+- [x] Errores nuevos en SaaS\n+\n+**Preparar Añelo**\n+- [x] Preparar chips\n+- [x] Armar valija con reader y netbook\n+- [x] Cambiar la palabra deslinde por DESLIGUE O EXENCIÓN en la casilla de acepto el deslinde de responsabilidad\n+- [x] Preparar podio para vecinos de Añelo\n+\n+---\n+\n+@saas\n+- [x] Errores de Multimoneda\n+\n+## 2025-05-25\n\\ No newline at end of file\n"}, {"date": 1748272067069, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -97,8 +97,9 @@\n - [x] Preparar chips\n - [x] Armar valija con reader y netbook\n - [x] Cambiar la palabra deslinde por DESLIGUE O EXENCIÓN en la casilla de acepto el deslinde de responsabilidad\n - [x] Preparar podio para vecinos de Añelo\n+- [x] Issue de Chips (https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/304)\n \n ---\n \n @saas\n"}, {"date": 1748273482778, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -98,8 +98,10 @@\n - [x] Armar valija con reader y netbook\n - [x] Cambiar la palabra deslinde por DESLIGUE O EXENCIÓN en la casilla de acepto el deslinde de responsabilidad\n - [x] Preparar podio para vecinos de Añelo\n - [x] Issue de Chips (https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/304)\n+- [x] Probar truss y armar caja para llevar materiales\n+- [x] Nuevo mic para grabar\n \n ---\n \n @saas\n"}, {"date": 1748990752926, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -106,5 +106,53 @@\n \n @saas\n - [x] Errores de Multimoneda\n \n-## 2025-05-25\n\\ No newline at end of file\n+## 2025-05-25\n+\n+- [x] Framework @next\n+- [x] Ordenar index\n+\n+- [x] Deployar Eventos de verdad\n+  - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n+\n+---\n+\n+@saas\n+- [x] ElastiCache service update\n+  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n+\n+@crono\n+- [x] Responder a Andres de Colombia y audios sobre ayer\n+- [x] Configurar nuevo router\n+- [x] Arreglar Chromecast\n+- [x] Reunión Comodoro\n+- [x] Acomodar cuentas y mails\n+- [x] Cotización para José de chile\n+- [x] Cambios en Travesía de los Cerros\n+\n+---\n+\n+@saas\n+- [x] ANTIAFIP\n+- [x] Ver consulta Fernando\n+- [x] Responder mails SaaS\n+\n+@crono\n+- [x] Contar los chips usados en Añelo y sacar deuda final\n+- [x] Imprimir y firmar papel para Silvio ok\n+- [x] Recibir chips de Damian\n+- [x] Cambios palabras Paraguay\n+- [x] Actualizar mensaje fotocélulas y de participantes\n+\n+@varios\n+- [x] Pedir turnos para mi espalda (pagar reserva)\n+- [x] Configurar router a Mati\n+- [x] Botones Pulsador (Recibir, probar, despachar y grabar vídeo)\n+\n+---\n+\n+- [x] Tapas de vinilos e impresiones\n+- [x] Ordenar todo el equipo (preparar para Hybrid)\n+- [x] Recibir y jugar con mi nuevo teclado (https://andreani.com/envio/360002611905310)\n+\n+## 2025-06-01\n\\ No newline at end of file\n"}, {"date": 1749403416436, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -154,5 +154,73 @@\n - [x] Tapas de vinilos e impresiones\n - [x] Ordenar todo el equipo (preparar para Hybrid)\n - [x] Recibir y jugar con mi nuevo teclado (https://andreani.com/envio/360002611905310)\n \n-## 2025-06-01\n\\ No newline at end of file\n+## 2025-06-01\n+\n+\n+@saas\n+- [x] Empezar proceso @next y @mes\n+\n+@varios\n+- [x] Pagar aguinaldo colegio Maty\n+- [x] Pagar otro envío Costa Rica\n+\n+@crono\n+- [x] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+- [x] Estoy con picos\n+- [x] Tengo algún Sentry\n+- [x] Gaby necesita texto en el ticket\n+- [x] Pagué 150 a Mati y 50 a DHL, 30min de ventas\n+\n+---\n+\n+@saas\n+- [x] Mandar archivos Uriel clientes y proveedores con provincial\n+- [x] Soporte restaurar clientes y proveedores\n+- [x] Soporte del error en permisos\n+- [x] Escribir issues a Diego de separación decimal\n+- [x] Probar si es fácil pasar a tablas los productos en la impresión de ventas\n+- [x] Lo de tiendas\n+  - [x] Leer lo que tenemos\n+  - [x] Escribir nota interna de estrategia ayuda explicando nuevos campos\n+  - [x] Escribir estrategia integraciones en una nueva nota interna, incluir que ofrecemos con lo de ventaxmayor\n+\n+\n+@varios\n+- [x] Pasar por cerrajero y buscar a Mati\n+- [x] Papel para el auto\n+- [x] Comprar diclofenac\n+- [x] Ordenar lecturas en Raindrop y acceder a MEGA en tablet\n+\n+**CRONO Definir Framework Grabaciones**\n+_Hasta terminar crudos para mkt y poder pasar los cursos a video_\n+\n+---\n+\n+@crono\n+- [x] Cargar pago chips Toti\n+- [x] Cerra caja con los ingresos que no sabemos de quienes son\n+\n+@saas\n+- [x] Contar todo lo que hice de integraciones y pedirle las landings a Andiaco\n+- [x] Ver soporte plugin\n+\n+@crono\n+- [x] Crear respuestas de chip time y css\n+- [x] Terminar Admin\n+\n+- [x] Pagar estudio Pri\n+- [x] Actualizar filtro de newsletter\n+- [x] Pasar datos a Gustavo\n+\n+---\n+\n+- [x] Mailzero\n+- [x] Error Atuntaqui Ecuador\n+- [x] Arreglar error de Nota de crédito\n+- [x] Arreglar error de multimoneda\n+\n+- [x] Imprimir calco para red wifi\n+- [x] Imprimir juego https://www.poppularshop.com/morbopoly\n+- [x] Imprimir calco para valija crono y antena 9d\n+- [x] Imprimir calendario\n"}, {"date": 1750687279962, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,8 @@\n - [x] Recibir y jugar con mi nuevo teclado (https://andreani.com/envio/360002611905310)\n \n ## 2025-06-01\n \n-\n @saas\n - [x] Empezar proceso @next y @mes\n \n @varios\n@@ -223,4 +222,50 @@\n - [x] Imprimir calco para red wifi\n - [x] Imprimir juego https://www.poppularshop.com/morbopoly\n - [x] Imprimir calco para valija crono y antena 9d\n - [x] Imprimir calendario\n+\n+## 2025-06-14\n+\n+\n+- [x] Ordenar inbox y next\n+- [x] MailZero\n+- [x] Ordenar equipos\n+\n+---\n+\n+- [x] Arreglar puerta del auto\n+- [x] Comprar dólares\n+\n+- [x] Apagué el crontab de los eventos\n+- [x] Cargar pago Hybrid\n+- [x] Revisar cámara 241\n+- [x] Cargar pago Ecuador (sueldo)\n+- [x] Enviar equipo chips a Juan Villalba\n+- [x] Factura Claudio y mandar listado de sus chips\n+- [x] Armar presupuesto Rafting Aluminé\n+\n+---\n+\n+@inversor\n+- [x] Ver pago viaje Maty\n+- [x] Averiguar por créditos (https://mail.google.com/mail/u/0/#inbox/********************************)\n+- [x] Ya tengo los recibos\n+- [x] Comprar ticket streaming Ozzy 5 Julio 11am\n+\n+@saas\n+- [x] Ver localidades Uriel (consulta de ayuda)\n+- [x] Ver que tuve que cambiar el antiafip\n+\n+---\n+\n+- [x] Consultas de ayuda\n+\n+@crono\n+- [x] Factura Sarr de lo que falta\n+- [x] Tengo Sentry\n+- [x] Agregar datos extras para Claudio y Gaby\n+\n+- [x] Invertir en Fondo\n+- [x] 15hs Reunión CrossingCapital\n+\n+## 2025-06-22\n\\ No newline at end of file\n"}, {"date": 1751629287750, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -267,5 +267,22 @@\n \n - [x] Invertir en Fondo\n - [x] 15hs Reunión CrossingCapital\n \n-## 2025-06-22\n\\ No newline at end of file\n+## 2025-06-22\n+\n+- [x] Soporte SaaS\n+- [x] Responder mail a contadora\n+- [x] Ver presupuesto Chile\n+\n+- [x] Hacer pedido a China Feibot\n+\n+- [x] Revisar SaaS AFIPSDK\n+- [x] Actualizar Deploy SaaS\n+- [x] MailZero SaaS\n+\n+- [x] Error que me pasó Gaby hoy y Ecuador\n+- [x] Ver algo de Catarun\n+- [x] Agregar códigos de descuentos a Smart Hyrox y revisar enlace\n+\n+## 2025-06-29\n+\n"}, {"date": 1752458471802, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,349 @@\n+\n+## 2025-05-12\n+\n+@saas\n+**Deploy BETA a PROD**\n+  - [x] Hay un MR\n+  - [x] Al deployar prod se rompe proxy\n+  - [x] Antiafip y se traba devuelta\n+  - [x] Revisar fechas en informes con estilo 1\n+  - [x] Preparar el deploy\n+\n+- [x] Seguir con ordenando este framework ( https://kortina.nyc/essays/suping-up-vs-code-as-a-markdown-notebook/ )\n+\n+---\n+\n+@saas\n+- [x] Avisar cuando esté por mail a https://mail.google.com/mail/u/0/#inbox/********************************\n+- [x] Avisar el que quería API Categoría de cliente predeterminada\n+\n+@play\n+- [x] Buscar moladora en lo de Andres\n+- [x] Buscar las tarjetas en el banco\n+\n+@paisaje\n+- [x] Volver con calistenia\n+\n+@crono\n+- [x] Llamar a Toti\n+- [x] Revisar acreditaciones Tempo\n+- [x] Ordenar TODO\n+- [x] Limpiar los mails y cerrar las cajas\n+\n+---\n+\n+@play\n+- [x] Preparar envío de teclado\n+- [x] Reunión Tría\n+- [x] Comprar router\n+- [x] El board pasarlo completo a todo.md\n+\n+**Errores y mejoras en admin**\n+  - [x] Warnings en sentry\n+  - [x] Calcula mal el descuento\n+\n+---\n+\n+@crono\n+- [x] Cargar pago Ecuador (acomodar cajas)\n+- [x] Vídeo de como testear la tensión en la fuente del reader\n+- [x] Subir archivo Travesía\n+- [x] Configurar puntos Anibal\n+- [x] Cargar chips Damian\n+- [x] Configurar pasarela de pagos MP\n+- [x] Ordenar eventos en calendario\n+\n+**Errores y mejoras en admin**\n+- [x] Errores Felipe\n+- [x] Revisar historial Colombia\n+- [x] Mostrar totales de todas las cajas https://scripts.saasargentina.com/?script=4\n+- [x] Igualar sueldos Crono y dividendos negativos\n+\n+---\n+\n+## 2025-05-18\n+\n+\n+@varios\n+- [x] Firmar lunes antes de pileta\n+- [x] Reunión Añelo Run\n+- [x] Evaluar si usamos Crossing Capital\n+  - Reunión programada con Jon 19 de may. de 2025 14:45 -03 (-03:00)\n+- [x] Ver mail Josefina y limpiar mail inbox\n+- [x] Renovar Payoneer\n+\n+---\n+\n+@saas\n+- [x] Revisar y documentar API Pedido A https://gitlab.com/saasargentina/app/-/issues/2132\n+- [x] Revisar Ayuda y campos FAQ https://gitlab.com/saasargentina/app/-/issues/2103\n+\n+@crono\n+- [x] Reunión con Matias Ola por crono en Calafate\n+- [x] Transferir a Lucas\n+- [x] Pago 145 ya está en la personal\n+- [x] Transferir $605k a Claudio, menos eventos que debe y 2 cables 8m y cargar pagos\n+- [x] Encontrar pago Rally BSAS (https://alfa.saasargentina.com/clientes.php?a=ver&id=223)\n+\n+**Deployar primer versión de EVENTOS**\n+  - [x] [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n+\n+---\n+\n+@saas\n+- [x] Errores nuevos en SaaS\n+\n+**Preparar Añelo**\n+- [x] Preparar chips\n+- [x] Armar valija con reader y netbook\n+- [x] Cambiar la palabra deslinde por DESLIGUE O EXENCIÓN en la casilla de acepto el deslinde de responsabilidad\n+- [x] Preparar podio para vecinos de Añelo\n+- [x] Issue de Chips (https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/304)\n+- [x] Probar truss y armar caja para llevar materiales\n+- [x] Nuevo mic para grabar\n+\n+---\n+\n+@saas\n+- [x] Errores de Multimoneda\n+\n+## 2025-05-25\n+\n+- [x] Framework @next\n+- [x] Ordenar index\n+\n+- [x] Deployar Eventos de verdad\n+  - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n+\n+---\n+\n+@saas\n+- [x] ElastiCache service update\n+  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n+\n+@crono\n+- [x] Responder a Andres de Colombia y audios sobre ayer\n+- [x] Configurar nuevo router\n+- [x] Arreglar Chromecast\n+- [x] Reunión Comodoro\n+- [x] Acomodar cuentas y mails\n+- [x] Cotización para José de chile\n+- [x] Cambios en Travesía de los Cerros\n+\n+---\n+\n+@saas\n+- [x] ANTIAFIP\n+- [x] Ver consulta Fernando\n+- [x] Responder mails SaaS\n+\n+@crono\n+- [x] Contar los chips usados en Añelo y sacar deuda final\n+- [x] Imprimir y firmar papel para Silvio ok\n+- [x] Recibir chips de Damian\n+- [x] Cambios palabras Paraguay\n+- [x] Actualizar mensaje fotocélulas y de participantes\n+\n+@varios\n+- [x] Pedir turnos para mi espalda (pagar reserva)\n+- [x] Configurar router a Mati\n+- [x] Botones Pulsador (Recibir, probar, despachar y grabar vídeo)\n+\n+---\n+\n+- [x] Tapas de vinilos e impresiones\n+- [x] Ordenar todo el equipo (preparar para Hybrid)\n+- [x] Recibir y jugar con mi nuevo teclado (https://andreani.com/envio/360002611905310)\n+\n+## 2025-06-01\n+\n+@saas\n+- [x] Empezar proceso @next y @mes\n+\n+@varios\n+- [x] Pagar aguinaldo colegio Maty\n+- [x] Pagar otro envío Costa Rica\n+\n+@crono\n+- [x] Mandar subdiario Crono a Josefina (Y pagar $30.000 de honorarios)\n+- [x] Estoy con picos\n+- [x] Tengo algún Sentry\n+- [x] Gaby necesita texto en el ticket\n+- [x] Pagué 150 a Mati y 50 a DHL, 30min de ventas\n+\n+---\n+\n+@saas\n+- [x] Mandar archivos Uriel clientes y proveedores con provincial\n+- [x] Soporte restaurar clientes y proveedores\n+- [x] Soporte del error en permisos\n+- [x] Escribir issues a Diego de separación decimal\n+- [x] Probar si es fácil pasar a tablas los productos en la impresión de ventas\n+- [x] Lo de tiendas\n+  - [x] Leer lo que tenemos\n+  - [x] Escribir nota interna de estrategia ayuda explicando nuevos campos\n+  - [x] Escribir estrategia integraciones en una nueva nota interna, incluir que ofrecemos con lo de ventaxmayor\n+\n+\n+@varios\n+- [x] Pasar por cerrajero y buscar a Mati\n+- [x] Papel para el auto\n+- [x] Comprar diclofenac\n+- [x] Ordenar lecturas en Raindrop y acceder a MEGA en tablet\n+\n+**CRONO Definir Framework Grabaciones**\n+_Hasta terminar crudos para mkt y poder pasar los cursos a video_\n+\n+---\n+\n+@crono\n+- [x] Cargar pago chips Toti\n+- [x] Cerra caja con los ingresos que no sabemos de quienes son\n+\n+@saas\n+- [x] Contar todo lo que hice de integraciones y pedirle las landings a Andiaco\n+- [x] Ver soporte plugin\n+\n+@crono\n+- [x] Crear respuestas de chip time y css\n+- [x] Terminar Admin\n+\n+- [x] Pagar estudio Pri\n+- [x] Actualizar filtro de newsletter\n+- [x] Pasar datos a Gustavo\n+\n+---\n+\n+- [x] Mailzero\n+- [x] Error Atuntaqui Ecuador\n+- [x] Arreglar error de Nota de crédito\n+- [x] Arreglar error de multimoneda\n+\n+- [x] Imprimir calco para red wifi\n+- [x] Imprimir juego https://www.poppularshop.com/morbopoly\n+- [x] Imprimir calco para valija crono y antena 9d\n+- [x] Imprimir calendario\n+\n+## 2025-06-14\n+\n+\n+- [x] Ordenar inbox y next\n+- [x] MailZero\n+- [x] Ordenar equipos\n+\n+---\n+\n+- [x] Arreglar puerta del auto\n+- [x] Comprar dólares\n+\n+- [x] Apagué el crontab de los eventos\n+- [x] Cargar pago Hybrid\n+- [x] Revisar cámara 241\n+- [x] Cargar pago Ecuador (sueldo)\n+- [x] Enviar equipo chips a Juan Villalba\n+- [x] Factura Claudio y mandar listado de sus chips\n+- [x] Armar presupuesto Rafting Aluminé\n+\n+---\n+\n+@inversor\n+- [x] Ver pago viaje Maty\n+- [x] Averiguar por créditos (https://mail.google.com/mail/u/0/#inbox/********************************)\n+- [x] Ya tengo los recibos\n+- [x] Comprar ticket streaming Ozzy 5 Julio 11am\n+\n+@saas\n+- [x] Ver localidades Uriel (consulta de ayuda)\n+- [x] Ver que tuve que cambiar el antiafip\n+\n+---\n+\n+- [x] Consultas de ayuda\n+\n+@crono\n+- [x] Factura Sarr de lo que falta\n+- [x] Tengo Sentry\n+- [x] Agregar datos extras para Claudio y Gaby\n+\n+- [x] Invertir en Fondo\n+- [x] 15hs Reunión CrossingCapital\n+\n+## 2025-06-22\n+\n+- [x] Soporte SaaS\n+- [x] Responder mail a contadora\n+- [x] Ver presupuesto Chile\n+\n+- [x] Hacer pedido a China Feibot\n+\n+- [x] Revisar SaaS AFIPSDK\n+- [x] Actualizar Deploy SaaS\n+- [x] MailZero SaaS\n+\n+- [x] Error que me pasó Gaby hoy y Ecuador\n+- [x] Ver algo de Catarun\n+- [x] Agregar códigos de descuentos a Smart Hyrox y revisar enlace\n+\n+## 2025-06-29\n+\n+- [x] ORDENAR TAREA\n+\n+- [x] Pagar tarjeta\n+- [x] Pagar sueldos y acomodar cuentas\n+\n+- [x] Reunión con Ramiro (Escribir acuerdo)\n+- [x] Reunión con Patricio FINA 15hs\n+\n+- [x] Actualizar chips Karting\n+- [x] Borrar evento Colombia\n+- [x] No funciona el ticket para ver el estado de los equipos\n+- [x] Ver antena Feibot\n+\n+- [x] Arreglar teclado\n+- [x] Ver deuda Colombia y coordinar reunión\n+- [x] Cargar pago Ecuador\n+- [x] Cerrar cajas Crono y SV\n+- [x] Pedido a China, ver si consigo antenas acá\n+\n+---\n+\n+- [x] Mandar archivos Uriel\n+- [x] Poner multi cuit Ramiro\n+- [x] Mandar Ozzy a Martín y Santi\n+- [x] Coordinar con Pri vídeo Gaby: opus.pro o short.ai\n+- [x] Reunión con Ale para Configurar Tría Cross\n+\n+---\n+\n+@saas\n+- [x] Ver lo que pasó con 901\n+- [x] Continuar con ARCA (actualizar plan con nuevos precios de AFIPSDK)\n+- [x] Ordenar el DEV y ver lo que puede entrar en Julio\n+\n+@crono\n+- [x] Agregar datos extras Ecuador\n+- [x] Ordenar desarrollo\n+- [x] Ordenar tareas para Calafate\n+- [x] Escribir plan e ideas sobre migrar site a md\n+- [x] Ordenar tareas para Hybrid y funcionalidad/servicio exclusivo\n+- [x] Pruebas de botones (duración y conexión normal cerrado)\n+- [x] MailZero\n+- [x] Los eventos de Perú están en Ecuador\n+- [x] Conseguir administrador de inscripciones para Tría\n+\n+---\n+\n+@crono\n+- [x] Pagar China\n+- [x] Cargar pagos y cerrar cajas\n+- [x] Documento Colombia\n+- [x] Etapas Ecuador\n+\n+@saas\n+- [x] Ver porqué se colgó\n+- [x] No terminé lo de 901\n+  - Monitorear, deployar cambio y seguir monitoreando\n+  - Cambiar lo del 901, mandar a antiafip y ver resultado\n+- [x] Consulta de ayuda sobre cotización y ver issue\n+\n+## 2025-07-13\n"}], "date": 1747529982931, "name": "Commit-0", "content": "\n## 2025-05-12\n\n@saas\n**Deploy BETA a PROD**\n  - [x] Hay un MR\n  - [x] Al deployar prod se rompe proxy\n  - [x] Antiafip y se traba devuelta\n  - [x] Revisar fechas en informes con estilo 1\n  - [x] Preparar el deploy\n\n- [x] Seguir con ordenando este framework ( https://kortina.nyc/essays/suping-up-vs-code-as-a-markdown-notebook/ )\n\n---\n\n@saas\n- [x] Avisar cuando esté por mail a https://mail.google.com/mail/u/0/#inbox/********************************\n- [x] Avisar el que quería API Categoría de cliente predeterminada\n\n@play\n- [x] Buscar moladora en lo de Andres\n- [x] Buscar las tarjetas en el banco\n\n@paisaje\n- [x] Volver con calistenia\n\n@crono\n- [x] Llamar a Toti\n- [x] Revisar acreditaciones Tempo\n- [x] Ordenar TODO\n- [x] Limpiar los mails y cerrar las cajas\n\n---\n\n@play\n- [x] Preparar envío de teclado\n- [x] Reunión Tría\n- [x] Comprar router\n- [x] El board pasarlo completo a todo.md\n\n**Errores y mejoras en admin**\n  - [x] Warnings en sentry\n  - [x] Calcula mal el descuento\n\n---\n\n@crono\n- [x] Cargar pago Ecuador (acomodar cajas)\n- [x] Vídeo de como testear la tensión en la fuente del reader\n- [x] Subir archivo Travesía\n- [x] Configurar puntos Anibal\n- [x] Cargar chips Damian\n- [x] Configurar pasarela de pagos MP\n- [x] Ordenar eventos en calendario\n\n**Errores y mejoras en admin**\n- [x] Errores Felipe\n- [x] Revisar historial Colombia\n- [x] Mostrar totales de todas las cajas https://scripts.saasargentina.com/?script=4\n- [x] Igualar sueldos Crono y dividendos negativos\n\n---\n\n## 2025-05-18\n\n"}]}