{"sourceFile": "BRAIN/TODO/next.md", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1747309718095, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747309779359, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,2 +1,19 @@\n+\n+## ORDENAR\n - Pasar lo completado a [[done.md]]\n - Ver que toca para sumar 1% diario [BRAIN 🧠](../ROADS/BRAIN/BRAIN.md)\n+\n+Work-life balance:\n+Pay me first\n+Have fun\n+Ordenar fotos\n+Focus time with flow\n+Build something\n+Cocinar a la parrilla\n+\n+1 - Check roads, milestones, tasks & time. ¿Fue una semana exitosa? ¿Cómo hubiese sido mejor?\n+2 - ¿Qué haría que esta semana fuera exitosa? ¿Cuántos focos (un poco menos) y en qué?\n+3 - Revisar TODOS los Boards\n+4 - Las horas y los focus te tienen que cerrar. Clasificar las tareas según la matríz de <PERSON>\n+5 - Controlar KPIs mensualmente\n+6 - Actualizar el Buen día\n"}, {"date": 1747310065435, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,5 @@\n+# NEXT 🏹\n \n ## ORDENAR\n - Pasar lo completado a [[done.md]]\n - Ver que toca para sumar 1% diario [BRAIN 🧠](../ROADS/BRAIN/BRAIN.md)\n"}, {"date": 1747311730341, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,9 @@\n # NEXT 🏹\n \n ## ORDENAR\n+\n+- Recorrer todas los md de NIRVANA que puse ahí\n - Pasar lo completado a [[done.md]]\n - Ver que toca para sumar 1% diario [BRAIN 🧠](../ROADS/BRAIN/BRAIN.md)\n \n Work-life balance:\n"}, {"date": 1747528816403, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,4 +19,6 @@\n 3 - Revisar TODOS los Boards\n 4 - Las horas y los focus te tienen que cerrar. Clasificar las tareas según la matríz <PERSON>\n 5 - Controlar KPIs mensualmente\n 6 - Actualizar el Buen día\n+\n+- [ ] Dar de baja Todoist Pro\n"}, {"date": 1747910386099, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,59 @@\n # NEXT 🏹\n \n+@offline\n+- [ ] Colgar macetas en el patio\n+\n+@mobile\n+- [ ] Arreglar puerta del auto\n+- [ ] Actualizar mensaje fotocélulas\n+- [ ] Coordinar mis 2 sponsors\n+\n+@play\n+- [ ] Imprimir juego https://www.poppularshop.com/morbopoly\n+- [ ] Imprimir tapa rocola\n+- [ ] Imprimir entradas\n+- [ ] Imprimir calco para valija crono y antena 9db\n+\n+*******************************************************************************\n+\n+## NEXT MILESTONES\n+\n+**AGENTE MCP Consultas**\n+  _Hasta tener un sincronizador y re ordenar proyecto_\n+\n+**SAAS Estadísticas, Landings y primeros pasos de Plan Micro**\n+    _Hasta terminar todos los planes y Landings_\n+  - [ ] Escribir ventajas de SaaS + Qloud para informática\n+  - [ ] https://mail.google.com/mail/u/0/#inbox/********************************\n+  - [ ] Analizar lo del mail de Diego sobre impuestos de productos\n+\n+**CRONO Definir Framework Grabaciones**\n+  - Grabar material de inscripciones y sports para ahora\n+  - Grabar todo para páginas de deportes\n+  - Dejar ordenado próximas grabaciones\n+  - Grabar los vídeos de deportes\n+  - https://pocket.co/share/c992bc5b-2275-4004-a2bd-5e1e495f9108\n+\n+---\n+\n+**Evaluar Transferencias Inteligentes**\n+  [Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303)\n+\n+**EVENTOS Alta**\n+  - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático\n+  - Agregar para ver usuarios\n+\n+**SAAS Ayuda**\n+\n+**Picos de consumo**\n+  - [Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300)\n+\n+**Terminar EVENTOS**\n+  - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n+\n+*******************************************************************************\n+\n ## ORDENAR\n \n - Recorrer todas los md de NIRVANA que puse ahí\n - Pasar lo completado a [[done.md]]\n"}, {"date": 1747910614117, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -74,4 +74,5 @@\n 5 - Controlar KPIs mensualmente\n 6 - Actualizar el Buen día\n \n - [ ] Dar de baja Todoist Pro\n+- [ ] Comprar una ledger y guardar 50% del BTC ahí\n"}, {"date": 1748276085327, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,51 @@\n # NEXT 🏹\n \n+## FRAMEWORK\n+\n+- No es más necesariamente semanal\n+- Son todos flujos contínuos para hacer cuando tengas ganas\n+- Cada tanto utilizo @next para rellenar los flujos:\n+  - [ ] Actualizar el [BUEN DÍA](buendia.md)\n+  - [ ] Limpiar el [INBOX](inbox.md)\n+  - [ ] Ordenar los DEV y SOPORTE de [SAAS 📦](../ROADS/SAAS/SAAS.md)\n+  - [ ] Ordenar los DEV y SOPORTE de [CRONO 🏅](../ROADS/CRONO/CRONO.md)\n+  - [ ] Revisar [AGENTE 🤖](../ROADS/AGENTE/AGENTE.md)\n+  - [ ] Revisar los ROADS:\n+    - [QUIEROS](../ROADS/NIRVANA/QUIEROS.md)\n+    - [PLAY](../ROADS/NIRVANA/PLAY.md)\n+    - [DIY](../ROADS/NIRVANA/DIY.md)\n+    - [PAISAJES](../ROADS/NIRVANA/PAISAJES.md)\n+    - [BRAIN](../ROADS/BRAIN/BRAIN.md)\n+\n+\n+*******************************************************************************\n+\n+\n+**SAAS ARCA CON AFIPSDK**\n+- [ ] Ver lo de conectar beta con Lambda\n+- [ ] ElastiCache service update\n+  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n+\n+@varios\n+- [ ] Configurar Streamings con Meli+\n+- [ ] Configurar nuevo router\n+- [ ] Configurar Crossing Capital\n+\n+**TERMINAR esta versión de EVENTOS**\n+- [ ] Agregar el mail como contacto\n+- [ ] Agregar el buscador\n+- [ ] Auto-posicionar en el mes\n+- [ ] Poner enlace en la web\n+- [ ] Ordenar los eventos públicos y que los usuarios puedan publicar\n+- [ ] Subir actualización a 2 horas\n+\n+\n+\n+\n+Ordenar los DEV y SOPORTE\n+\n+\n @offline\n - [ ] Colgar macetas en el patio\n \n @mobile\n@@ -50,29 +95,10 @@\n \n **Terminar EVENTOS**\n   - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n \n+\n *******************************************************************************\n \n-## ORDENAR\n+## WAITING\n \n-- Recorrer todas los md de NIRVANA que puse ahí\n-- Pasar lo completado a [[done.md]]\n-- Ver que toca para sumar 1% diario [BRAIN 🧠](../ROADS/BRAIN/BRAIN.md)\n-\n-Work-life balance:\n-Pay me first\n-Have fun\n-Ordenar fotos\n-Focus time with flow\n-Build something\n-Cocinar a la parrilla\n-\n-1 - Check roads, milestones, tasks & time. ¿Fue una semana exitosa? ¿Cómo hubiese sido mejor?\n-2 - ¿Qué haría que esta semana fuera exitosa? ¿Cuántos focos (un poco menos) y en qué?\n-3 - Revisar TODOS los Boards\n-4 - Las horas y los focus te tienen que cerrar. Clasificar las tareas según la matríz de Eisenhower\n-5 - Controlar KPIs mensualmente\n-6 - Actualizar el Buen día\n-\n-- [ ] Dar de baja Todoist Pro\n-- [ ] Comprar una ledger y guardar 50% del BTC ahí\n+- [ ] Averiguar por créditos (https://mail.google.com/mail/u/0/#inbox/********************************)\n"}, {"date": 1748286080881, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,10 +4,10 @@\n \n - No es más necesariamente semanal\n - Son todos flujos contínuos para hacer cuando tengas ganas\n - Cada tanto utilizo @next para rellenar los flujos:\n-  - [ ] Actualizar el [BUEN DÍA](buendia.md)\n-  - [ ] Limpiar el [INBOX](inbox.md)\n+  - [x] Actualizar el [BUEN DÍA](buendia.md)\n+  - [x] Limpiar el [INBOX](inbox.md)\n   - [ ] Ordenar los DEV y SOPORTE de [SAAS 📦](../ROADS/SAAS/SAAS.md)\n   - [ ] Ordenar los DEV y SOPORTE de [CRONO 🏅](../ROADS/CRONO/CRONO.md)\n   - [ ] Revisar [AGENTE 🤖](../ROADS/AGENTE/AGENTE.md)\n   - [ ] Revisar los ROADS:\n@@ -19,51 +19,22 @@\n \n \n *******************************************************************************\n \n+## ORDENAR\n \n-**SAAS ARCA CON AFIPSDK**\n-- [ ] Ver lo de conectar beta con Lambda\n-- [ ] ElastiCache service update\n-  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n \n-@varios\n-- [ ] Configurar Streamings con Meli+\n-- [ ] Configurar nuevo router\n-- [ ] Configurar Crossing Capital\n \n-**TERMINAR esta versión de EVENTOS**\n-- [ ] Agregar el mail como contacto\n-- [ ] Agregar el buscador\n-- [ ] Auto-posicionar en el mes\n-- [ ] Poner enlace en la web\n-- [ ] Ordenar los eventos públicos y que los usuarios puedan publicar\n-- [ ] Subir actualización a 2 horas\n \n-\n-\n-\n-Ordenar los DEV y SOPORTE\n-\n-\n-@offline\n-- [ ] Colgar macetas en el patio\n-\n-@mobile\n-- [ ] Arreglar puerta del auto\n-- [ ] Actualizar mensaje fotocélulas\n-- [ ] Coordinar mis 2 sponsors\n-\n-@play\n-- [ ] Imprimir juego https://www.poppularshop.com/morbopoly\n-- [ ] Imprimir tapa rocola\n-- [ ] Imprimir entradas\n-- [ ] Imprimir calco para valija crono y antena 9db\n-\n *******************************************************************************\n \n ## NEXT MILESTONES\n \n+**SAAS ARCA CON AFIPSDK**\n+- [ ] Ver lo de conectar beta con Lambda\n+- [ ] ElastiCache service update\n+  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n+\n **AGENTE MCP Consultas**\n   _Hasta tener un sincronizador y re ordenar proyecto_\n \n **SAAS Estadísticas, Landings y primeros pasos de Plan Micro**\n"}, {"date": 1748286493290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,5 +70,5 @@\n *******************************************************************************\n \n ## WAITING\n \n-- [ ] Averiguar por créditos (ht\n\\ No newline at end of file\n+- [ ] Averiguar por créditos (https://mail.google.com/mail/u/0/#inbox/********************************)\n"}, {"date": 1748286885630, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,9 @@\n   - [x] Limpiar el [INBOX](inbox.md)\n   - [ ] Ordenar los DEV y SOPORTE de [SAAS 📦](../ROADS/SAAS/SAAS.md)\n   - [ ] Ordenar los DEV y SOPORTE de [CRONO 🏅](../ROADS/CRONO/CRONO.md)\n   - [ ] Revisar [AGENTE 🤖](../ROADS/AGENTE/AGENTE.md)\n-  - [ ] Revisar los ROADS:\n+  - [x] Revisar los ROADS:\n     - [QUIEROS](../ROADS/NIRVANA/QUIEROS.md)\n     - [PLAY](../ROADS/NIRVANA/PLAY.md)\n     - [DIY](../ROADS/NIRVANA/DIY.md)\n     - [PAISAJES](../ROADS/NIRVANA/PAISAJES.md)\n@@ -27,31 +27,14 @@\n *******************************************************************************\n \n ## NEXT MILESTONES\n \n-**SAAS ARCA CON AFIPSDK**\n-- [ ] Ver lo de conectar beta con Lambda\n-- [ ] ElastiCache service update\n-  - https://sa-east-1.console.aws.amazon.com/elasticache/home?region=sa-east-1#/service-updates\n-\n-**AGENTE MCP Consultas**\n-  _Hasta tener un sincronizador y re ordenar proyecto_\n-\n **SAAS Estadísticas, Landings y primeros pasos de Plan Micro**\n     _Hasta terminar todos los planes y Landings_\n   - [ ] Escribir ventajas de SaaS + Qloud para informática\n   - [ ] https://mail.google.com/mail/u/0/#inbox/********************************\n   - [ ] Analizar lo del mail de Diego sobre impuestos de productos\n \n-**CRONO Definir Framework Grabaciones**\n-  - Grabar material de inscripciones y sports para ahora\n-  - Grabar todo para páginas de deportes\n-  - Dejar ordenado próximas grabaciones\n-  - Grabar los vídeos de deportes\n-  - https://pocket.co/share/c992bc5b-2275-4004-a2bd-5e1e495f9108\n-\n----\n-\n **Evaluar Transferencias Inteligentes**\n   [Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303)\n \n **EVENTOS Alta**\n@@ -62,12 +45,9 @@\n \n **Picos de consumo**\n   - [Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300)\n \n-**Terminar EVENTOS**\n-  - [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46)\n \n-\n *******************************************************************************\n \n ## WAITING\n \n"}], "date": 1747309718095, "name": "Commit-0", "content": "- Pasar lo completado a [[done.md]]\n- Ver que toca para sumar 1% diario [BRAIN 🧠](../ROADS/BRAIN/BRAIN.md)\n"}]}