{"sourceFile": "BRAIN/MEMORY/mysql.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1734446644244, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1740773348880, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,5 +4,6 @@\n - En mysql se usa sólo un index por tabla en los where\n - En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados\n - The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards\n - Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\"\n-- Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\"\n\\ No newline at end of file\n+- Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\"\n+- Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n\\ No newline at end of file\n"}, {"date": 1742226282761, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,5 +5,6 @@\n - En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados\n - The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards\n - Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\"\n - Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\"\n-- Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n\\ No newline at end of file\n+- Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n+- Al crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`\n\\ No newline at end of file\n"}, {"date": 1752425126188, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,5 +6,7 @@\n - The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards\n - Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\"\n - Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\"\n - Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n-- <PERSON> crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`\n\\ No newline at end of file\n+- Al crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`\n+-   20500\n+- Para restar segundos `UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 29 SECOND) WHERE idcontrol = 20500;`\n"}, {"date": 1752425167375, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,6 +7,6 @@\n - Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\"\n - Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\"\n - Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n - Al crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`\n--   20500\n+- Para sumar minutos `UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 20500;`\n - Para restar segundos `UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 29 SECOND) WHERE idcontrol = 20500;`\n"}, {"date": 1753486399501, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,4 +9,5 @@\n - Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`\n - Al crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`\n - Para sumar minutos `UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 20500;`\n - Para restar segundos `UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 29 SECOND) WHERE idcontrol = 20500;`\n+- Para duplicar la tabla productos `INSERT INTO saas_12905.productos SELECT * FROM saas_874.productos;`\n"}], "date": 1734446644244, "name": "Commit-0", "content": "# MEJORES QUERYS\n\n- En mysql usar UNION en lugar de OR\n- En mysql se usa sólo un index por tabla en los where\n- En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados\n- The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards\n- Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\"\n- Se puede generar un campo con numeración por ej \"SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo\""}]}