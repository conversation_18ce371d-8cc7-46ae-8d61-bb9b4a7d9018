{"sourceFile": "BRAIN/MEMORY/cripto.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,7 +7,7 @@\n - Bitcoin represents a return to common-sense economics as opposed to the \"money grows on tress\" policies implemented by central banks worldwide.\n \n # CRIPTOS\n \n-Cardano es una cadena de bloques de código abierto, así como una plataforma para ejecutar contratos inteligentes y emitir su propia moneda digital, el ada.\n-\n-Cardano fue fundada en 2015 por el cofundador de Ethereum, <PERSON>. El desarrollo del proyecto está supervisado por la Fundación Cardano, con sede en Zug (Suiza).1​2​ Es una de las criptomonedas que utiliza una blockchain de prueba de participación, que se considera una alternativa más ecológica a los protocolos de prueba de trabajo.3​\n\\ No newline at end of file\n+- Cardano (ADA) es una cadena de bloques de código abierto, así como una plataforma para ejecutar contratos inteligentes y emitir su propia moneda digital, el ADA. Cardano fue fundada en 2015 por el cofundador de Ethereum, Charles Hoskinson. El desarrollo del proyecto está supervisado por la Fundación Cardano, con sede en Zug (Suiza).2​ Es una de las criptomonedas que utiliza una blockchain de prueba de participación, que se considera una alternativa más ecológica a los protocolos de prueba de trabajo.\n+- Solana (SOL) es una criptomoneda que funciona en una blockchain diseñada para aplicaciones descentralizadas (DApps). Es una de las criptomonedas con mayor capitalización de mercado.\n+Solana se creó en 2017 y se lanzó en 2020. Su objetivo es convertirse en la infraestructura blockchain para las aplicaciones modernas de internet.\n\\ No newline at end of file\n"}], "date": *************, "name": "Commit-0", "content": ""}]}