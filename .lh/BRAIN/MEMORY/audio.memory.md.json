{"sourceFile": "BRAIN/MEMORY/audio.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1736979645085, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1736979683676, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,3 @@\n+# AUDIO\n+\n+- El decibelio (dB) es una unidad de medida logarítmica que expresa la relación entre una cantidad física (como la intensidad del sonido) y un valor de referencia, permitiendo comparar niveles de manera relativa; los valores negativos indican que la cantidad medida es menor que el valor de referencia, como ocurre al bajar el volumen.\n\\ No newline at end of file\n"}, {"date": 1736979711830, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,5 @@\n # AUDIO\n \n-- El decibelio (dB) es una unidad de medida logarítmica que expresa la relación entre una cantidad física (como la intensidad del sonido) y un valor de referencia, permitiendo comparar niveles de manera relativa; los valores negativos indican que la cantidad medida es menor que el valor de referencia, como ocurre al bajar el volumen.\n\\ No newline at end of file\n+- El decibelio (dB) es una unidad de medida logarítmica que expresa la relación entre una cantidad física (como la intensidad del sonido) y un valor de referencia, permitiendo comparar niveles de manera relativa; los valores negativos indican que la cantidad medida es menor que el valor de referencia, como ocurre al bajar el volumen.\n+- Escala logarítmica: La escala de decibelios es logarítmica, lo que significa que un aumento o disminución de una cierta cantidad de decibelios corresponde a una multiplicación o división por un factor fijo.\n+- Valor de referencia: Se establece un valor de referencia para comparar las diferentes medidas. Al bajar el volumen, la intensidad del sonido se reduce en comparación con este valor de referencia, por lo que el resultado en decibelios es negativo.\n\\ No newline at end of file\n"}], "date": 1736979645085, "name": "Commit-0", "content": ""}]}