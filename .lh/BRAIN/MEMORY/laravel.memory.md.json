{"sourceFile": "BRAIN/MEMORY/laravel.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1743720745522, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1743720745522, "name": "Commit-0", "content": "# DEVELOPER FRASES\n\n- A user interface is like a joke, if you have to explain it, it's not that good.\n- There's a well-known quote by author <PERSON>: \"Eat food. Not too much. Mostly plants.\". We can put it also like \"Write code. Not too much. Mostly functions\".\n- Aprende a Aprender. Descubre la manera en que aprendes mejor. Desarrolla modelos mentales al rededor de cómo te gusta resolver problemas. Úsalos a discreción.\n- Procura La Consistencia. En lo que haces y cómo lo haces. No importa el lenguaje, la tecnología o la metodología que uses. Te harás notar por la fiabilidad de tus contribuciones. Y nada es mejor para incrementar tu fiabilidad, que la constancia.\n- Experimenta Resolviendo Problemas Sin Código. Antes de correr a escribir código, prueba descomponiendo el problema en una hoja de Excel. Usa una herramienta no-code para validar tu lógica. Escribir el código es relativamente sencillo si tienes bien claro lo que tienes que hacer.\n- Favorece la Simplicidad Sobre Soluciones Brillantes. El código se lee muchas más veces de las que se escribe. Procura que no te gane la idea de que el código más complejo es inherentemente mejor. Menos es más.\n- Conviértete en un Desarrollador 10X. Un desarrollador 10X no es aquel que hace el trabajo de otras 10 personas, sino aquel que ayuda a que 10 de sus compañeros hagan el mejor trabajo posible. No acapares. Ayuda.\n- Busca Una Organización que Valga La Pena. Vas a hacer tu mejor trabajo en una organización que te deje: • Usar la tecnología que te gusta • Hacer el tipo de trabajo en el que te sientes más cómodo • Resolver problemas inspiran\n- Sé Profesional, No Mercenario. Un profesional actúa con base en sus principios y ética de trabajo. Un mercenario actúa para el que pague más. El profesional da confianza. El mercenario la quita.\n- Sal de Tu Zona de Confort. Una cosa es la consistencia. Otra es hacer exactamente lo mismo durante 10 años. Asegúrate de que vas modulando, poco a poco, la complejidad de los retos que tomas. Sube las apuestas. Sigue tus principios.\n- A user interface is like a joke, if you have to explain it, it's not that good.\n\n## ARTISAN MAINTENANCE MODE\n\nphp artisan down\nphp artisan down — refresh=15 -- message \"Estamos actualizando\"\nphp artisan down — secret=”1630542a-246b-4b66-afa1-dd72a4c43515\"\n\n## ARTISAN MIGRATE\n\nphp artisan schema:dump --prune\nphp artisan migrate:rollback --step=1\n\n## FILAMENT ASSETS\n\nphp artisan filament:assets\n\n## LIVEWIRE\n\nwire:model.defer\nwire:model.debounce:500ms\nwire:model.debounce:1s\n\n## QUEUES\n\nphp artisan queue:work --stop-when-empty --once --queue=tiempos\nphp artisan queue:work --queue=tiempos,default\nphp artisan queue:retry all\nphp artisan livewire:publish --assets\n\n## MODELOS DESDE DB\n\nPara crear modelos desde db (https://github.com/reliese/laravel):\n- composer require reliese/laravel\n- php artisan vendor:publish --tag=reliese-models\n- php artisan code:models --table=datosxparticipantes\n- php artisan vendor:publish --tag=reliese-models\n- clear\n- php artisan code:models --table=datosxparticipantes\n- php artisan config:clear\n- php artisan code:models --table=datosxparticipantes\n\n## REFACTOR\n\n- Restructuring a Laravel Controller using Services, Events, Jobs, Actions: https://laravel-news.com/controller-refactor"}]}