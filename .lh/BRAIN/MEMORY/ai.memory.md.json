{"sourceFile": "BRAIN/MEMORY/ai.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1751884931279, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1751884931279, "name": "Commit-0", "content": "# AI FRASES\n\n- Algunas voces proponen un cambio de nombre: inteligencia colectiva. No es solo un giro semántico. Es una invitación a pensarlo todo distinto. Porque si entendemos la IA como una forma de inteligencia común, acumulada, accesible, entonces usarla no nos hace menos humanos, sino quizá un poco más.\n\n\n# AI FRAMEWORKS\n\n- Lo más importante es el mindset: Cada tarea intentar primero con AI\n- Genius como primera opción, después ChatGPT u otros en el navegador si necesito más contexto o subir archivos (yo ya se cuál usar)\n- Pasar los Docs al LLM junto con el proyecto, antes de empezar a integrar un código con una API\n- No necesito resúmen de información en noticias, uso sólo Newsletter (Gmail ya tiene AI) y Youtube (lo uso como entretenimiento)\n- Audio a texto (en móvil y PC) con contacto Zapia en Whatsapp\n- Herramientas de generación de vídeo incluyendo audio en marcadores para probar cual es mejor\n- Types of Automation. **Traditional automation** is designed to execute repetitive tasks based on explicit, predefined rules determined by humans and does not learn or evolve. **AI automation** uses artificial intelligence capabilities like machine learning and natural language processing (NLP) to enable machines to learn from data and experiences, recognize patterns, and make human-like decisions. AI automation tools do adapt and evolve over time. **AI Agents** Similar to AI automation, AI agents are designed to make human-like decisions. However, unlike AI automation tools, AI agents take autonomous actions (i.e., without needing any human input).\n\n\n# AI DATA\n\n- We have a network of neurons (100 billions) connected to each other (10 000 connections per neuron), reacting to context, learning from experience, and generating an appropriate (but often hard to predict exactly) answer. In other words, apart from the fact that our algorithm is chemical rather than digital, the structure is similar.\n- 3 orders of magnitude in complexity: The human brain has 1000 times more connections than GPT-4 has parameters. As a result, it can handle more complex situations.\n- Ongoing learning: The brain keeps learning, including during a conversation, whereas GPT has finished its training long before the start of the conversation.\n- Limited input: The only thing GPT knows about the conversation is the text. Up to 60% of human communication is nonverbal: the tone of voice, the rhythm of the voice, the facial expression, even some subconscious factors like smell play a part. GPT misses all of that.\n- GPT doesn’t have emotions: Human emotions involve a lot of glands and hormones that have complex interactions with the brain.\n\n## PLAN OPENAI\n\nEl mapa hacia la superinteligencia:\n- Nivel 1 (Actual): IA conversacional, como ChatGPT. Interactúa en lenguaje natural pero con limitaciones.\n- Nivel 2 \"Razonadores\": IA capaz de resolver problemas complejos al nivel de alguien con un doctorado, y sin herramientas externas.\n- Nivel 3 \"Agentes\": Sistemas que pueden trabajar de forma autónoma durante días, gestionando varias tareas.\n- Nivel 4 \"Innovadores\": IA que genera ideas originales y realiza descubrimientos científicos por sí misma.\n- Nivel 5 \"Organizaciones\": IA con capacidad para dirigir operaciones complejas, como una empresa entera.\n"}]}