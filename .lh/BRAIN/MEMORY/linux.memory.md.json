{"sourceFile": "BRAIN/MEMORY/linux.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1740323474999, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748614236987, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,16 @@\n # LINUX COMMANDS\n \n+## COMPOSER\n+\n+composer install --prefer-dist --optimize-autoloader --no-dev\n+\n+--prefer-dist: preferir descargar paquetes como archivos comprimidos (dist) en lugar de clonar el repositorio desde su sistema de control de versiones (source)\n+--optimize-autoloader: optimiza el autoloader durante la instalación\n+--no-dev: no instale las dependencias que de \"require-dev\"\n+\n+## VARIOS SUELTOS\n+\n - Folder disk space: du -hs /home/\n - sudo du -h / | grep '[0-9\\.]\\+G' > espacio\n \n - ifconfig is now: ip a (Try: ip -s -c -h a)\n"}], "date": 1740323474999, "name": "Commit-0", "content": "# LINUX COMMANDS\n\n- Folder disk space: du -hs /home/\n- sudo du -h / | grep '[0-9\\.]\\+G' > espacio\n\n- ifconfig is now: ip a (Try: ip -s -c -h a)\n- traceroute cronometrajeinstantaneo.com\n- curl ifconfig.me\n\n- find * -type f | fzf > selected\n- sed -i 's/cadena_original/cadena_nueva/g' archivo.txt\n\n- GREP AND: grep -E 'pattern1.*pattern2' filename\n- GREP OR: grep -E 'pattern1|pattern2' filename\n- Wordcount with grep: grep -w foo README.txt\n- preg_match('/beta/', $_SESSION['servidor_url'])\n- Contar cantidad de líneas: wc -l archivo.txt\n\n- git remote add prod ssh://<EMAIL>:/home/<USER>/gits/andresmaiden.git\n- git remote set-url beta ec2-user@*************:/home/<USER>/gits/api-beta\n- git remote set-url alfa ec2-user@*************:/home/<USER>/gits/api-alfa\n- git remote set-url prod ec2-user@*************:/home/<USER>/gits/api\n- git fetch --all\n\n- ctrl+U: borrar todo\n- ctrl+ k/w: borra desde el cursor todo hasta el principio (con w) o hasta el fin (con k)\n- ctrl+ r: search history (ctrl+r next & ctrl+s back)\n- !!: last command (ej. sudo !!)\n- !: last specific command (ej. !cat)\n- history | grep subl\n- !linea (ej !455)\n\n- alias desk=cd\\ /home/<USER>/Escritorio\n- alias files=find\\ .\\ -type\\ f\\ \\|\\ wc\\ -l\n- alias network=sudo\\ service\\ network-manager\\ restart\n- alias conectados='find /saas/customer/services/acc/empresas/logs/ -maxdepth 1 -mmin -60 -type f -exec ls -la {} \\;'\n- alias pesados='find /saas/customer/services -type f -size +10M -exec ls -la {} \\;'\n- function mkcd\n- alias modificados=find . -type f -mmin -1 -exec ls -lt --time-style=+\"%Y-%m-%d %H:%M:%S\" {} +\n\n- sudo timedatectl set-time '2023-04-10 15:30:00'\n- setsid gedit\n\n- youtube-dl link\n- youtube-dl --extract-audio --audio-format mp3 <video URL>\n- ffmpeg -i link.mp4 -f mp3 music.mp3\n- ffmpeg -i video.mp4 -vn -ab 128k -ar 44100 -y video.mp3\n- ffmpeg -i video.mp4 -i video.wav -c copy -map 0:v:0 -map 1:a:0 video.mkv\n- Reducir resolución vídeo a 720p: ffmpeg -i input.mp4 -vf \"scale=-1:720\" -c:a copy output.mp4\n\n- convert input.jpg output.png\n- convert input.png -transparent white output.png\n\n- alias f=\"fabric\"\n- alias copy='xsel --clipboard --input'\n- alias paste='xsel --clipboard --output'\n"}]}