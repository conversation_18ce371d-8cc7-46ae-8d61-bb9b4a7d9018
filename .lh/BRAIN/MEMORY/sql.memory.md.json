{"sourceFile": "BRAIN/MEMORY/sql.memory.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1734446442410, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1734446442410, "name": "Commit-0", "content": "# MEJORES QUERYS\n\n- En mysql usar UNION en lugar de OR\n- En mysql se usa sólo un index por tabla en los where\n- En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados\n- The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards\n- Se puede enviar directamente a un archivo por ej \"SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '\"' LINES TERMINATED BY '\\n';\""}]}