{"sourceFile": "BRAIN/AI/CRONO_generar_textos_inscripciones.md", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1727815084593, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1728053479992, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,12 +7,13 @@\n ## EVENTO PUNTUAL\n \n Puntualmente estos son los datos del evento en cuestión.\n \n-- Deporte o disciplina: Pumptrack\n-- Nombre del evento: Open Bike Pumptrack La Kava\n-- Fecha: 21-07-2024\n-- Organizador: ASOCIACION CIVIL CLUB DE MX Y MTB LA KAVA PARANA\n+- Deporte o disciplina: Acuatlón, SwimRun, Aguas Abiertas y Running\n+- Nombre del evento: Acuatlón Fest 2025\n+- Fecha: 18 y 19 de enero del 2025\n+- Organizador: Acuatlón Series\n+- Descripción del evento: Aquatlón Fest es el primer evento que te ofrece la experiencia de aguas abiertas y del running en todas sus combinaciones en un mismo fin de semana. Vení a disfrutar de dos días, participando en hasta tres carreras y compartí con tu familia y amigos, la cultura y el deporte de Piedra del Águila.\n \n \n ---\n \n"}, {"date": 1738349303108, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,21 +3,8 @@\n Estoy escribiendo los textos base para un sistema de gestión de eventos deportivos. Los textos son para explicar a los futuros participantes los pasos a seguir. Estos textos los organizadores del evento los van a poder editar. Todos los textos deben ser informales y dando aliento sin ser demasiado emotivos y sin dejar de ser profesionales. Los textos deben ser en html pero sin encabezado, solamente lo que va a ir dentro del <body>\n \n ---\n \n-## EVENTO PUNTUAL\n-\n-Puntualmente estos son los datos del evento en cuestión.\n-\n-- Deporte o disciplina: Acuatlón, SwimRun, Aguas Abiertas y Running\n-- Nombre del evento: Acuatlón Fest 2025\n-- Fecha: 18 y 19 de enero del 2025\n-- Organizador: Acuatlón Series\n-- Descripción del evento: Aquatlón Fest es el primer evento que te ofrece la experiencia de aguas abiertas y del running en todas sus combinaciones en un mismo fin de semana. Vení a disfrutar de dos días, participando en hasta tres carreras y compartí con tu familia y amigos, la cultura y el deporte de Piedra del Águila.\n-\n-\n----\n-\n ## TEXTOS NECESARIOS\n \n 1) Texto en pantalla pre formulario: se va a mostrar antes del formulario de inscripción y tiene que dar la bienvenida y pedir que completen el siguiente formulario.\n \n@@ -32,4 +19,9 @@\n 6) Texto en pantalla y por mail de pago rechazado: es el texto que se muestra cuando el pago fue rechazado, pidiendo que lo intente nuevamente.\n \n 7) Texto en pantalla para inscripciones cerradas: es el texto que se muestra cuando las inscripciones se encuentran cerradas.\n \n+---\n+\n+## EVENTO PUNTUAL\n+\n+Puntualmente estos son los datos del evento en cuestión.\n"}, {"date": 1738349844164, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,4 +24,6 @@\n \n ## EVENTO PUNTUAL\n \n Puntualmente estos son los datos del evento en cuestión.\n+\n+{{input}}\n"}], "date": 1727815084593, "name": "Commit-0", "content": "## DESCRIPCIÓN DE LA TAREA\n\nEstoy escribiendo los textos base para un sistema de gestión de eventos deportivos. Los textos son para explicar a los futuros participantes los pasos a seguir. Estos textos los organizadores del evento los van a poder editar. Todos los textos deben ser informales y dando aliento sin ser demasiado emotivos y sin dejar de ser profesionales. Los textos deben ser en html pero sin encabezado, solamente lo que va a ir dentro del <body>\n\n---\n\n## EVENTO PUNTUAL\n\nPuntualmente estos son los datos del evento en cuestión.\n\n- Deporte o disciplina: Pumptrack\n- Nombre del evento: Open Bike Pumptrack La Kava\n- Fecha: 21-07-2024\n- Organizador: ASOCIACION CIVIL CLUB DE MX Y MTB LA KAVA PARANA\n\n\n---\n\n## TEXTOS NECESARIOS\n\n1) Texto en pantalla pre formulario: se va a mostrar antes del formulario de inscripción y tiene que dar la bienvenida y pedir que completen el siguiente formulario.\n\n2) Texto en pantalla post formulario: es el texto que se muestra una vez que el participante ya completó el formulario y ahora debe pagar para poder confirmar su participación.\n\n3) Texto en mail post formulario: es el texto que se envía una vez que el participante ya completó el formulario y ahora debe pagar para poder confirmar su participación. Debe ser parecido al texto en pantalla post formulario.\n\n4) Texto en pantalla post pago: es el texto que se muestra una vez que el participante ya pagó y su inscripción está confirmada, dando las gracias y diciendo que lo esperamos en el evento.\n\n5) Texto en mail post pago: es el texto que se muestra una vez que el participante ya pagó y su inscripción está confirmada, dando las gracias y diciendo que lo esperamos en el evento.\n\n6) Texto en pantalla y por mail de pago rechazado: es el texto que se muestra cuando el pago fue rechazado, pidiendo que lo intente nuevamente.\n\n7) Texto en pantalla para inscripciones cerradas: es el texto que se muestra cuando las inscripciones se encuentran cerradas.\n\n"}]}