{"sourceFile": "BRAIN/test.todo", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1747219118962, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747219396190, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,6 @@\n-test.todo\n\\ No newline at end of file\n+# NO TENGO TÍTULO\n+\n+👍\n+\n+- [ ] Esto es una @critical +SAAS @saas\n+No\n"}, {"date": 1747219461955, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,5 +2,7 @@\n \n 👍\n \n - [ ] Esto es una @critical +SAAS @saas\n-No\n+☐ No me auto-completa\n+    ✔ No me auto-completa @done (14/5/2025, 07:44:04)\n+    Puedo escribir cualquier cosa en el medio\n"}, {"date": 1747220934093, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,4 +5,12 @@\n - [ ] Esto es una @critical +SAAS @saas\n ☐ No me auto-completa\n     ✔ No me auto-completa @done (14/5/2025, 07:44:04)\n     Puedo escribir cualquier cosa en el medio\n+\n+\n+###\n+\n+```html\n+esto es código\n+```\n+\n"}], "date": 1747219118962, "name": "Commit-0", "content": "test.todo"}]}