{"sourceFile": "BRAIN/todo.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1747179195913, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747179624148, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,9 +67,11 @@\n - Deploy BETA a PROD\n - Ver si me queda más cómodo Obsidian\n     - [ ] Buscar un par de plugins\n     - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n-    - [ ] Acostumbrar a los teclados\n+    - [ ] Ver algo @p1\n+    Acostumbrar a los teclados #flow #focus #high {cm:2025-05-13}\n+    - [ ]\n     - Ordenar esta semana\n     - Que funcione en celu\n     - Refill por ahora framework y después automatizado\n - Quiero planear como empezar con el entrenamiento\n"}, {"date": 1747179635033, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,11 +67,10 @@\n - Deploy BETA a PROD\n - Ver si me queda más cómodo Obsidian\n     - [ ] Buscar un par de plugins\n     - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n-    - [ ] Ver algo @p1\n-    Acostumbrar a los teclados #flow #focus #high {cm:2025-05-13}\n-    - [ ]\n+    - [ ] Ver algo @p1 \n+    - [ ] Acostumbrar a los teclados #flow #focus #high\n     - Ordenar esta semana\n     - Que funcione en celu\n     - Refill por ahora framework y después automatizado\n - Quiero planear como empezar con el entrenamiento\n"}, {"date": 1747179808203, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,9 +70,11 @@\n     - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n     - [ ] Ver algo @p1\n     - [ ] Acostumbrar a los teclados #flow #focus #high\n     - Ordenar esta semana\n-    - Que funcione en celu\n+    - Que funcione en celu +SAAS\n+    - Algo de +CRONO\n+    -\n     - Refill por ahora framework y después automatizado\n - Quiero planear como empezar con el entrenamiento\n - Ordenar la oficina\n \n@@ -125,5 +127,6 @@\n DEV 📦\n \n Vuelvo a la pileta 👏💪\n \n-Today\n\\ No newline at end of file\n+Today\n+nueva\n\\ No newline at end of file\n"}, {"date": 1747220595415, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,29 @@\n+- [Si títulos](#si-títulos)\n+  - [doble](#doble)\n+    - [tercero](#tercero)\n+\n+\n+# Si títulos\n+\n+## doble\n+\n+### tercero\n+\n+---\n+\n+(A) claro estoy con esto @late @today\n+\n+- [ ] Ahora tengo de vuelta este\n+    - [x] Se mueve\n+    - [ ] Tengo label @saas @quick @quick @saas\n+\n+```html\n+esto es código\n+```\n+\n+\n+\n+\n+\n+\n+\n"}], "date": 1747179195913, "name": "Commit-0", "content": "## NEXT MILESTONES\n\n- [ ] Primera\n- [ ] Segunda\n\n\n* Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n\nSAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\n\nEscribir ventajas de SaaS + Qloud para informática @focus\t- [https://mail.google.com/mail/u/0/#inbox/********************************](https://mail.google.com/mail/u/0/#inbox/********************************) - <PERSON><PERSON><PERSON> lo del mail de Diego sobre impuestos de productos\n\nCRONO Definir Framework Grabaciones @flow\tGrabar material de inscripciones y sports para ahora  Grabar todo para páginas de deportes  Dejar ordenado próximas grabaciones\n\nGrabar los vídeos de deportes\n\n- [  ] Armar curso fácil\n\n[ ]  A ver\n\n```html\nlalalala\nlalalala\n```\n\n\n\n\n\nVer y resumir consejos y los procesos que va a llevar (herramientas de AI)\n\nProbar técnicas de grabación (mkt y cursos)\n\nEncontrar buen ángulo para la cámara\n\nProbar fondo verde\n\nErrores Felipe y minors @flow\t- Calcula mal el descuento - Ver usuario - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático - Ya podemos hacer la carga en SaaS automática - Revisar el error de Rally Bs.As. y sólo errores urgentes - Mostrar totales de todas las cajas [https://scripts.saasargentina.com/?script=4](https://scripts.saasargentina.com/?script=4) - Igualar sueldos Crono y dividendos negativos - Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir. - De alguna forma se está pudiendo repetir los nombres de los eventos.\n\n[EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46) @flow\n\n[Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303) @flow\n\nAGENTE MCP Consultas @flow\tHasta tener un sincronizador y re ordenar proyecto\n\nSAAS Ayuda @flow\n\nCRONO Chips sólo n° @flow\n\n[Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n\n## FRAMEWORK\n\nEstoy jugando a reformular mí BRAIN\n\nCon el nuevo libro, todo texto en VSC\n\n- Buscar filtro de tags con colores\n- Pasar Week matándola\n- Audio a texto y archivo\n- Nuevo teclado y atajos con machete\n- Mas memoria en mail\n- Toggl en vsc\n- Calendario compartido\n- Toggle text copiloto\n- color en p\n- Deploy BETA a PROD\n- Ver si me queda más cómodo Obsidian\n    - [ ] Buscar un par de plugins\n    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n    - [ ] Acostumbrar a los teclados\n    - Ordenar esta semana\n    - Que funcione en celu\n    - Refill por ahora framework y después automatizado\n- Quiero planear como empezar con el entrenamiento\n- Ordenar la oficina\n\nOk anduvo algo\n- [ ] Algo @tag @p1\n- [ ] lala\n\n## PRIORIDA\n\nDesde el celular\n\nBueno cambio algo más\n\n[Algo]()\n\n## TODOIST\n\n(La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n\n```todoist\nfilter: \"today | overdue\"\n```\n\nAveriguar por ALGE Timing\n\nToday\n\nquick\n\nSOPORTE 🥇 / VENTAS\n\nArmar mi teclado Corne\n\nAv la plata 61 10B entre Rivadavia y Chaco CP 1184 CABA...\n\nToday\n\nplay\n\nNIRVANA 👌 / UBUNTU\n\nDeploy BETA a PROD\n\n- Hay algo de la API para revisar...\n\nToday\n\nfocus\n\nDEV 📦\n\nVuelvo a la pileta 👏💪\n\nToday"}]}