{"sourceFile": "BRAIN/WIKI/Configuración manual de nube híbrida.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1735821689516, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1735824695440, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,9 +33,9 @@\n DELETE FROM etapas WHERE idevento NOT IN (1497);\n DELETE FROM controles WHERE idevento NOT IN (1497);\n \n DELETE FROM config_cronometraje WHERE idevento NOT IN (1497);\n-DELETE FROM config_inscripciones WHERE idevento NOT IN (1497);\n+DELETE FROM config_organizacion WHERE idevento NOT IN (1497);\n DELETE FROM config_vivos WHERE idevento NOT IN (1497);\n \n TRUNCATE lecturas;\n TRUNCATE penas;\n"}, {"date": 1************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,9 @@\n DELETE FROM controles WHERE idevento NOT IN (1497);\n \n DELETE FROM config_cronometraje WHERE idevento NOT IN (1497);\n DELETE FROM config_organizacion WHERE idevento NOT IN (1497);\n-DELETE FROM config_vivos WHERE idevento NOT IN (1497);\n+DELETE FROM config_vivo WHERE idevento NOT IN (1497);\n \n TRUNCATE lecturas;\n TRUNCATE penas;\n ALTER TABLE `lecturas` DROP INDEX `uuid`;\n"}], "date": 1735821689516, "name": "Commit-0", "content": "\n#******************************************************************************\n# PARA SYNC DOWN NUBES:\n#******************************************************************************\nprod_crono\ncd /var/www/cronometrajeinstantaneo/www/descargas\nrm cronometrajeinstantaneo.sql\nmysqldump -uroot -pFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz --databases cronometrajeinstantaneo > cronometrajeinstantaneo.sql\nsu pi\ncd home/pi\nwget --no-check-certificate https://cronometrajeinstantaneo.com/descargas/cronometrajeinstantaneo.sql\nrm cronometrajeinstantaneo.sql\n\necho 1497 >> idevento\ncat idevento\nsudo mysqldump -uroot -p8des4rollo --databases cronometrajeinstantaneo > cronometrajeinstantaneo-736.sql\nsudo mysql -uroot -p\n\n\nDROP DATABASE cronometrajeinstantaneo;\nCREATE DATABASE cronometrajeinstantaneo;\nUSE cronometrajeinstantaneo;\nSOURCE cronometrajeinstantaneo.sql;\n\nDELETE FROM participantes WHERE idevento NOT IN (1497);\nDELETE FROM datosxparticipantes WHERE idevento NOT IN (1497);\nDELETE FROM datosxeventos WHERE idevento NOT IN (1497);\n\nDELETE FROM organizaciones WHERE idorganizacion NOT IN (SELECT idorganizacion FROM eventos WHERE idevento IN (1497));\nDELETE FROM eventos WHERE idevento NOT IN (1497);\nDELETE FROM carreras WHERE idevento NOT IN (1497);\nDELETE FROM categorias WHERE idevento NOT IN (1497);\nDELETE FROM etapas WHERE idevento NOT IN (1497);\nDELETE FROM controles WHERE idevento NOT IN (1497);\n\nDELETE FROM config_cronometraje WHERE idevento NOT IN (1497);\nDELETE FROM config_inscripciones WHERE idevento NOT IN (1497);\nDELETE FROM config_vivos WHERE idevento NOT IN (1497);\n\nTRUNCATE lecturas;\nTRUNCATE penas;\nALTER TABLE `lecturas` DROP INDEX `uuid`;\n\nALTER TABLE `participantes` CHANGE `estado_carrera` `race_state` ENUM('inrace','hooked','lap','dnf','dsq','dns','otro') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT 'inrace';\n\nALTER TABLE `participantes` CHANGE `estado_carrera_otro` `race_state_custom` VARCHAR(20) CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n\nALTER TABLE eventos\nADD `sync_apps` tinyint(1) NOT NULL DEFAULT 1,\nADD `tiene_cronometraje` tinyint(1) NOT NULL DEFAULT 1,\nADD `tiene_milisegundos` tinyint(1) NOT NULL DEFAULT 1,\nADD `solo_segundos` tinyint(1) NOT NULL DEFAULT 1,\nADD `equipos` tinyint(1) NOT NULL DEFAULT 1,\nADD `cambios_de_tiempos` tinyint(1) NOT NULL DEFAULT 1,\n\nADD `tipolargada` enum('unica_largada','unica_largada_con_etapas','carreras_con_largadas','etapas_con_largadas','carreras_con_largadas_con_etapas','largadas_individuales','largadas_individuales_con_etapas','vueltas_fijas','etapas_con_largadas_vueltas','largadas_individuales_con_vueltas','largadas_individuales_con_etapas_continuas','') NOT NULL DEFAULT 'unica_largada',\nADD `tipo_largada` enum('evento','carreras','etapas','participantes','participantes_etapas') NOT NULL DEFAULT 'evento',\nADD `precision` enum('minutos','segundos','decisegundos','centisegundos','milisegundos','solo-minutos','solo-segundos') NOT NULL DEFAULT 'segundos',\nADD `ticket` tinyint(1) NOT NULL DEFAULT 1,\nADD `tiempo_vuelta` int(10) UNSIGNED NOT NULL DEFAULT 30,\nADD `penas` tinyint(1) NOT NULL DEFAULT 0,\nADD `penas_en_minutos` tinyint(4) NOT NULL DEFAULT 1,\nADD `limitar_penas` varchar(60) NOT NULL,\nADD `unificar_penas` tinyint(1) NOT NULL DEFAULT 1,\nADD `ordenar_ultima_etapa` tinyint(1) NOT NULL DEFAULT 0,\nADD `estado_carrera` tinyint(1) NOT NULL DEFAULT 1,\nADD `parciales` tinyint(1) NOT NULL DEFAULT 0,\nADD `minuto_cerrado` tinyint(1) NOT NULL DEFAULT 0,\nADD `no_oficial` tinyint(1) NOT NULL DEFAULT 0,\nADD `podios` int(10) UNSIGNED NOT NULL DEFAULT 3,\nADD `texto_resultados` varchar(191) NOT NULL,\nADD `abiertas` tinyint(1) NOT NULL DEFAULT 0,\nADD `multi_idparticipante` tinyint(1) NOT NULL DEFAULT 0,\nADD `multi_categoria` tinyint(1) NOT NULL DEFAULT 0,\nADD `equipo_participantes` varchar(191) NOT NULL DEFAULT '',\nADD `auto_numeracion` tinyint(1) NOT NULL DEFAULT 0,\nADD `auto_mail` tinyint(1) NOT NULL DEFAULT 0,\nADD `estado_predeterminado` enum('preinscripto','inscripto','acreditado','abandono','descalificado','anulado') NOT NULL DEFAULT 'preinscripto',\nADD `tipo_nombre` enum('libre','Nombre Apellido','Apellido, Nombre','APELLIDO Nombre') NOT NULL DEFAULT 'libre';\n\nUPDATE organizaciones SET pass = 'd61f29328cbd43d836ef31aefb7236fd' WHERE idorganizacion = 335;\n\n\n# ARMAR LISTA DE NUBES Y LAS FECHAS DE ACTUALIZADAS\n\n\n# PARA NUBES NUEVAS:\n# Agregar este código al apache\n<Directory /var/www/www_encoded/public/>\n    Options Indexes FollowSymLinks MultiViews\n    AllowOverride All\n    Order allow,deny\n    allow from all\n</Directory>\n\n# Agregar al crontab\n* * * * * ( sleep 00 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 01 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 02 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 03 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 04 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 05 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 06 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 07 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 08 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 09 ; /var/www/www_encoded/cache/cache.php )\n\n* * * * * ( sleep 10 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 11 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 12 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 13 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 14 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 15 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 16 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 17 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 18 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 19 ; /var/www/www_encoded/cache/cache.php )\n\n* * * * * ( sleep 20 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 21 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 22 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 23 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 24 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 25 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 26 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 27 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 28 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 29 ; /var/www/www_encoded/cache/cache.php )\n\n* * * * * ( sleep 30 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 31 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 32 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 33 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 34 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 35 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 36 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 37 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 38 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 39 ; /var/www/www_encoded/cache/cache.php )\n\n* * * * * ( sleep 40 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 41 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 42 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 43 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 44 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 45 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 46 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 47 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 48 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 49 ; /var/www/www_encoded/cache/cache.php )\n\n* * * * * ( sleep 50 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 51 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 52 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 53 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 54 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 55 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 56 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 57 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 58 ; /var/www/www_encoded/cache/cache.php )\n* * * * * ( sleep 59 ; /var/www/www_encoded/cache/cache.php )\n\n# Actualizar software\ncrono_encoded # y seguir los pasos\nwget https://cronometrajeinstantaneo.com/descargas/www_encoded_lxxq.tar.gz\ntar -zxvf www_encoded_lxxq.tar.gz\n\nrm -rf /var/www/old_www_encoded/\nsudo mv /var/www/{,old_}www_encoded\nsudo mv www_encoded /var/www\n\n# Configurar permisos de Laravel\nchmod -R 777 /var/www/www_encoded/storage\nchmod -R 777 /var/www/www_encoded/bootstrap/cache\nchmod -R 777 /var/www/www_encoded/cache\n\n# Actualizar la app con .lan revisando .env y cronometraje.env\nsed -i \"s/cronometrajeinstantaneo.des/cronometrajeinstantaneo.lan/g\" /var/www/www_encoded/.env\nsed -i \"s/yosoyroot/cronometrajeinstantaneo/g\" /var/www/www_encoded/.env\nsed -i \"s/\\/home\\/<USER>\\/www\\/cronometrajeinstantaneo/\\/var\\/www\\/www_encoded/g\" /var/www/www_encoded/.env\n\nsed -i \"s/cronometrajeinstantaneo.des/cronometrajeinstantaneo.lan/g\" /var/www/www_encoded/cronometrajeinstantaneo.env\nsed -i \"s/yosoyroot/cronometrajeinstantaneo/g\" /var/www/www_encoded/cronometrajeinstantaneo.env\nsed -i \"s/\\/home\\/<USER>\\/www\\/cronometrajeinstantaneo/\\/var\\/www\\/www_encoded/g\" /var/www/www_encoded/cronometrajeinstantaneo.env\n\n# Clear Laravel's Cache\nphp /var/www/www_encoded/artisan cache:clear\nphp /var/www/www_encoded/artisan config:clear\nphp /var/www/www_encoded/artisan view:clear\n\n# Actualizar la base de datos\n# Synd down\n\n\n# Si se rompen las tablas en la nube híbrida\n\nmysqlcheck --repair --all-databases / mysqlcheck -uroot -p --repair --all-databases\n\n\n#*******************************************************************************\n# PARA SYNC UP NUBES:\n#*******************************************************************************\n(Otra posible conexión: ftp -p lumapatagonia.com.ar luma itrmnvF254Cs)\n\nsudo mysqldump -uroot -p8des4rollo --databases cronometrajeinstantaneo > cronometrajeinstantaneo-388.sql\nftp -p ftp.patagoniainfinita.com\ninfinita\njB254vx134\nls\nput cronometrajeinstantaneo-388.sql\nget cronometrajeinstantaneo-388.sql\ndelete cronometrajeinstantaneo-388.sql\n\n# Exportos las tablas en la nube\nSELECT * FROM eventos WHERE idevento = 388;\n\n# Borro online el evento y lo importo manualmente\nDELETE FROM eventos WHERE idevento = 388;\n# ya no funciona INSERT INTO `eventos` (`idevento`, `idorganizacion`, `nombre`, `codigo`, `inscripciones`, `resultados`, `cronometraje`, `mail`, `fecha`, `localidad`, `tipolargada`, `equipos`, `cambios_de_tiempos`, `tiene_milisegundos`, `largada`, `terminada`, `inscripciones_estilo`, `inscripciones_texto`, `inscripciones_js`, `inscripciones_preinscripto`, `resultados_estilo`, `resultados_con_estilo`, `resultados_filtros`, `resultados_buscador`, `auto_numeracion`, `auto_mail`, `auto_refresh`, `mail_preinscripto`, `mail_inscripto`, `etapas`, `precio`, `pago`, `multicarrera`) VALUES\n(388, 41, 'Copa Nariño de Downhill 2019 Válida 1 Jojoa', 'copa-narino-de-downhill-2019-valida-1-jojoa', 0, 1, 1, '', '2019-05-19', 'Pasto', 'largadas_individuales_con_etapas', 0, 1, 1, '2019-05-19 09:50:16.268', '0000-00-00 00:00:00.000', '', '', '', '', '', 1, 0, 0, 0, 0, 0, '', '', 0, '100.00', 0, 0);\n\n# Verificar manualmente las carreras, etapas y controles\nSELECT * FROM carreras WHERE idevento = 388;\nSELECT * FROM categorias WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 388);\nSELECT * FROM etapas WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 388);\nSELECT * FROM datosxeventos WHERE idevento = 388;\n\n# Conocer cuál es el primer y último idinscripcion de la nube y de online\nSELECT idinscripcion FROM participantes WHERE idevento = 388 ORDER BY idinscripcion ASC LIMIT 1;\nSELECT idinscripcion FROM participantes WHERE idevento = 388 ORDER BY idinscripcion DESC LIMIT 1;\nSELECT idinscripcion FROM participantes ORDER BY idinscripcion DESC LIMIT 1;\n\n# Calcular la diferencia y sumarla en la nube\nUPDATE participantes SET idinscripcion = idinscripcion + 700 WHERE idevento = 388;\nUPDATE datosxparticipantes SET idinscripcion = idinscripcion + 700 WHERE idevento = 388;\n\n# Exporto las tablas en la nube\nSELECT * FROM participantes WHERE idevento = 388;\nSELECT * FROM datosxparticipantes WHERE idevento = 388;\n\n# Borro online los datos y los importo con los archivos\nDELETE FROM participantes WHERE idevento = 388;\nDELETE FROM datosxparticipantes WHERE idevento = 388;\n\n# Hacer lo mismo con lecturas\nSELECT * FROM lecturas WHERE idcontrol IN\n(SELECT idcontrol FROM controles WHERE idetapa IN\n(SELECT idetapa FROM etapas WHERE idcarrera IN\n(SELECT idcarrera FROM carreras WHERE idevento = 388)));\n"}]}