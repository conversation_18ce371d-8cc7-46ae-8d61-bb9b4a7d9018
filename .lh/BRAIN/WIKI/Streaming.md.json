{"sourceFile": "BRAIN/WIKI/Streaming.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1732141758193, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1732141758193, "name": "Commit-0", "content": "El sistema cuenta con un widgets que generan información para utilizar en softwares de streaming para TV y redes sociales (OBS Studio, vMix, Wirecast, Streamlabs OSB, etc.). Les cuento y paso los enlaces a cada uno de dichos widgets. Todos tienen un chroma key #00FF00\n\nGeneralmente mostramos un reloj con el tiempo del que está en carrera y además el podio al terminar cada categoría. Con respecto al reloj, tenemos distintas opciones que pueden agregar:\n- Pre-largada: si van a tener cámara en la largada y tiempo para filmar antes de que salgan. Lo usamos en los DH Urbanos de RedBull donde se sigue al corredor en todo el recorrido y no hay nunca 2 ciclistas en pista\n- Pre-llegada: este es útil para poner un control unos 200m o 300m antes de la llegada y así asegurar que el que aparece en pantalla es realmente el que viene y no un resagado\n- Parcial: este lo usamos para algún punto intermedio y detiene el reloj unos 30 segundos en la mitad de la pista.\nTambién recomiendo si no van a tener pre-llegada, que haya alguien con handy para anunciar quien viene. Esto es porque el momento de tomar el tiempo en la llegada, si el operador comete un error (este domingo hubo un caso), sale mal la información en pantalla. Si bien los tiempos se corrigen y no hay problemas en la clasificación, queda feo en pantalla eso\n\nTengo entendido que vamos sólo el reloj con tiempo en carrera y tiempo de llegada (les comento las opciones para que sepan que se puede hacer) y también el widget del Podio.\n\n*RELOJ*\n\nEntonces ese widget va a estar en el siguiente enlace:\n\nhttps://admin.cronometrajeinstantaneo.com/vivos/red-bull-carros-locos/reloj\n\n*PODIO*\n\nAdemás de eso, cuando termina cada categoría, está bueno mostrar como quedó y mejor si la muestra en pantalla de esta ventana está coordinada con el locutor. Para eso, tenemos el siguiente widget:\n\nhttps://admin.cronometrajeinstantaneo.com/vivos/red-bull-carros-locos/podio\n\n---\n\nAdemás les paso esta información para que tengan en cuenta.\n\n*ADMINISTRACIÓN*\n\nPara tener en cuenta hay 2 cosas. Una es que se puede cambiar las categorías que se muestran en pantallas, en el sistema hay que entrar a *Configuración > Streaming > Opciones*\n\nLa otra es que el reloj muestra el tiempo en carrera del que tendría que llegar, pero hay casos de sobrepasos o caídas y ahí llega otro participante. Para sacar a ese participante del reloj, se lo puede pasar a DNF en el Panel de Control.\n\n*SINCRONIZACIÓN*\n\nEn el reloj, se utiliza la hora de la PC que está transmitiendo, para calcular el tiempo en carrera y por eso es MUY IMPORTANTE que esté sincronziada con el reloj de los celulares que toman largada y llegada. No afecta el cronometraje pero si se ve mal la información en pantalla queda muy feo.\n\n*ERRORES DE CRONOMETRAJE*\n\nEstamos acostumbrados a que podemos corregir cualquier error en el cronometraje en la app y es muy fácil. Pero cuando estamos haciendo streaming, cada error que cometemos se ve en pantalla y queda feo. Es muy importante que los operadores de las fotocélulas NO SE EQUIVOQUEN, especialmente en la llegada. Lo ideal es poner alguien a 200m que informe por handy/radio que número de bici es el que realmente está llegando.\n\n*DISEÑO DE PANTALLAS*\n\nEl sistema tiene para que podamos hacer cualquier diseño con CSS, pero la verdad es que lo mejor es poner una imagen de fondo y acomodar el tiempo por sobre esa imagen. Les voy a pasar un par de ejemplos para que puedan basarse y generar el diseño."}]}