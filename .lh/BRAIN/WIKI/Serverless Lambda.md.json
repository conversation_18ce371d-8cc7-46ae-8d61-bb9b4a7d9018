{"sourceFile": "BRAIN/WIKI/Serverless Lambda.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1740432885996, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748525021776, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,13 +10,14 @@\n \n 1. Crear una carpeta en el repo `lambda` con el nombre de la lambda, por ej `myFunction`\n 2. Entrar a la carpeta y comenzar un bref project con `composer require bref/bref`\n 3. Iniciar el proyecto con `vendor/bin/bref init`\n-4. Crear el .gitignore con `echo \"vendor\" > .gitignore` y `echo \".serverless\" >> .gitignore`\n+4. Crear el .gitignore con `echo \"vendor\" > .gitignore; echo \".serverless\" >> .gitignore`\n 5. Edita el archivo serverless.yml para configurar tu función Lambda. Asegúrate de especificar el `service: nombre_app`, `stage: dev`, `runtime: provided.al2`, `region: sa-east-1` y el handler correcto.\n-6. Ir a *AWS > Lambda > Crear una función* con el nombre de la carpeta precedida por `app-dev-`\n+<!-- 6. Ir a *AWS > Lambda > Crear una función* con el nombre de la carpeta precedida por `app-dev-` -->\n 7. Deploy con `serverless deploy`\n 8. Probar la function en local con `serverless bref:local --function fe --data '{\"name\": \"Test\"}` o en AWS pero desde mi consola con `serverless invoke -f myFunction`\n+9. Para usar variables de entorno con .env `composer require vlucas/phpdotenv`\n \n \n ## ME FALTA VER Y DOCUMENTAR\n \n"}, {"date": 1748525045574, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,14 +13,13 @@\n 3. Iniciar el proyecto con `vendor/bin/bref init`\n 4. Crear el .gitignore con `echo \"vendor\" > .gitignore; echo \".serverless\" >> .gitignore`\n 5. Edita el archivo serverless.yml para configurar tu función Lambda. Asegúrate de especificar el `service: nombre_app`, `stage: dev`, `runtime: provided.al2`, `region: sa-east-1` y el handler correcto.\n <!-- 6. Ir a *AWS > Lambda > Crear una función* con el nombre de la carpeta precedida por `app-dev-` -->\n-7. Deploy con `serverless deploy`\n+7. Deploy con `serverless deploy --stage=alfa`\n 8. Probar la function en local con `serverless bref:local --function fe --data '{\"name\": \"Test\"}` o en AWS pero desde mi consola con `serverless invoke -f myFunction`\n 9. Para usar variables de entorno con .env `composer require vlucas/phpdotenv`\n \n \n ## ME FALTA VER Y DOCUMENTAR\n \n composer install --prefer-dist --optimize-autoloader --no-dev\n-serverless deploy --stage=prod\n Lambda creará un rol de ejecución denominado app-dev-empresa_9589_script_1-role-4mnxfi2q, con permiso para cargar registros a Amazon CloudWatch Logs.\n"}, {"date": 1748610854373, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,11 @@\n ## ACLARACIONES\n \n - Para lambdas de empresas, generamos igual un registro de su tabla scripts\n - En AWS Lambda usamos el framework https://bref.sh/ para deployar lambdas en PHP más fácilmente\n-- Define your application in a simple `serverless.yml` file and deploy with `bref deploy`\n+- Define your application in a simple `serverless.yml` file and deploy with `serverless deploy`\n - Tenemos 2 librerías para llamar a functions de Lambdas desde código ( `app/public/librerias/funciones_aws.php` y `api/funciones_api.php` ) que tienen instalados los SDK de AWS. Además se puede llamar desde cron\n-- Vamos a agregar todo lo que lleva queue en Lambdas\n+- Vamos a agregar todo lo que lleva queue en Lambdas y SQS\n \n ## PASOS PARA CREAR UNA LAMBDA\n \n 1. Crear una carpeta en el repo `lambda` con el nombre de la lambda, por ej `myFunction`\n"}, {"date": 1748614250870, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,6 +20,5 @@\n \n \n ## ME FALTA VER Y DOCUMENTAR\n \n-composer install --prefer-dist --optimize-autoloader --no-dev\n Lambda creará un rol de ejecución denominado app-dev-empresa_9589_script_1-role-4mnxfi2q, con permiso para cargar registros a Amazon CloudWatch Logs.\n"}], "date": 1740432885996, "name": "Commit-0", "content": "## ACLARACIONES\n\n- Para lambdas de empresas, generamos igual un registro de su tabla scripts\n- En AWS Lambda usamos el framework https://bref.sh/ para deployar lambdas en PHP más fácilmente\n- Define your application in a simple `serverless.yml` file and deploy with `bref deploy`\n- Tenemos 2 librerías para llamar a functions de Lambdas desde código ( `app/public/librerias/funciones_aws.php` y `api/funciones_api.php` ) que tienen instalados los SDK de AWS. Además se puede llamar desde cron\n- Vamos a agregar todo lo que lleva queue en Lambdas\n\n## PASOS PARA CREAR UNA LAMBDA\n\n1. Crear una carpeta en el repo `lambda` con el nombre de la lambda, por ej `myFunction`\n2. Entrar a la carpeta y comenzar un bref project con `composer require bref/bref`\n3. Iniciar el proyecto con `vendor/bin/bref init`\n4. Crear el .gitignore con `echo \"vendor\" > .gitignore` y `echo \".serverless\" >> .gitignore`\n5. Edita el archivo serverless.yml para configurar tu función Lambda. Asegúrate de especificar el `service: nombre_app`, `stage: dev`, `runtime: provided.al2`, `region: sa-east-1` y el handler correcto.\n6. Ir a *AWS > Lambda > Crear una función* con el nombre de la carpeta precedida por `app-dev-`\n7. Deploy con `serverless deploy`\n8. Probar la function en local con `serverless bref:local --function fe --data '{\"name\": \"Test\"}` o en AWS pero desde mi consola con `serverless invoke -f myFunction`\n\n\n## ME FALTA VER Y DOCUMENTAR\n\ncomposer install --prefer-dist --optimize-autoloader --no-dev\nserverless deploy --stage=prod\nLambda creará un rol de ejecución denominado app-dev-empresa_9589_script_1-role-4mnxfi2q, con permiso para cargar registros a Amazon CloudWatch Logs.\n"}]}