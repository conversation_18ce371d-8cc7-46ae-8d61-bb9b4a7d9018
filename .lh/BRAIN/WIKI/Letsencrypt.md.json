{"sourceFile": "BRAIN/WIKI/Letsencrypt.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1752691782488, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1752691782488, "name": "Commit-0", "content": "sudo certbot --apache\n# sudo letsencrypt certonly --apache -d cronometrajeinstantaneo.com -d www.cronometrajeinstantaneo.com -d admin.cronometrajeinstantaneo.com -d app.cronometrajeinstantaneo.com -d wp.cronometrajeinstantaneo.com -d beta.cronometrajeinstantaneo.com\n# sudo letsencrypt renew --force-renew --manual-public-ip-logging-ok\n\n# sudo letsencrypt certonly --apache -d agente.ar -d www.agente.ar -d n8n.agente.ar -d app.agente.ar\n\nUSAR NUEVO PLUGIN PARA WP\nWP Free SSL – Free SSL Certificate for WordPress and force HTTPS\n\nNO USAR MÁS - RENOVAR EN WP ENCRPYT\n- Visitar el plugin con &includewww=1 atrás\nhttps://nodomateriales.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1\n- <PERSON><PERSON> y descargar los 2 archivos de validación\n- Ir a la descarga y subir directo a la carpeta online (ojo que agrega txt y lo sacamos acá) Qu9jfvVrU7YW\nscp nombre-archivo-validacion.txt <EMAIL>:/home/<USER>/public_html/.well-known/acme-challenge/nombre-archivo-validacion\n- Verify Challenge y descargar cert y key\n- Ir a http://vps-1723455-x.dattaweb.com:2083/#/domain/ssl en el dominio correcto e Instalar Nuevo\n\nhttps://nodomateriales.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1\nhttps://quetrihueviajesyturismo.tur.ar/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1\nhttps://simplementeviviendo.com/wp-admin/admin.php?page=wp_encryption&subdir=1&includewww=1\n\n\nEN NODO DESACTIVAR EL PLUGIN DEL PDF PARA QUE FUNCIONE"}]}