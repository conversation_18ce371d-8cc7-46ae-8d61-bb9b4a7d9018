{"sourceFile": "BRAIN/WIKI/Configuración de eventos.sql.md", "activeCommit": 0, "commits": [{"activePatchIndex": 19, "patches": [{"date": 1726407825431, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1735824695441, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -185,9 +185,9 @@\n DELETE FROM etapas WHERE idcarrera IN\n     (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\n DELETE FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM config_vivos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM config_inscripciones WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n+DELETE FROM config_organizacion WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM config_cronometraje WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM eventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n \n ## En SaaS\n"}, {"date": 1735824730511, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -184,9 +184,9 @@\n         (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979)));\n DELETE FROM etapas WHERE idcarrera IN\n     (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\n DELETE FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM config_vivos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n+DELETE FROM config_vivo WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM config_organizacion WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM config_cronometraje WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n DELETE FROM eventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n \n"}, {"date": 1737474822434, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -244,8 +244,16 @@\n GROUP BY idevento\n ORDER BY idevento\n \n \n+#### SUMAR AÑOS A LA CATEGORIA\n+\n+UPDATE categorias SET\n+nacimiento_desde = DATE_ADD(nacimiento_desde, INTERVAL 1 YEAR),\n+nacimiento_hasta = DATE_ADD(nacimiento_hasta, INTERVAL 1 YEAR)\n+WHERE idevento = 2301;\n+\n+\n ##### CAMBIAR EVENTO DE CLIENTE\n \n UPDATE cronometrajeinstantaneo.eventos SET idorganizacion = 353 WHERE idevento IN (897, 1419);\n \n"}, {"date": 1737477931554, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -252,22 +252,22 @@\n nacimiento_hasta = DATE_ADD(nacimiento_hasta, INTERVAL 1 YEAR)\n WHERE idevento = 2301;\n \n \n-##### CAMBIAR EVENTO DE CLIENTE\n+##### CAMBIAR / MOVER EVENTO DE CLIENTE\n \n-UPDATE cronometrajeinstantaneo.eventos SET idorganizacion = 353 WHERE idevento IN (897, 1419);\n+UPDATE cronometrajeinstantaneo.eventos SET idorganizacion = 542 WHERE idevento IN (1880, 2246);\n \n-UPDATE saas_161.servicios SET idcliente = 353 WHERE idservicio IN (897, 1419);\n-UPDATE saas_161.ventas SET idcliente = 353 WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419);\n-UPDATE saas_161.ventaspagos SET idcliente = 353 WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419));\n-UPDATE saas_161.ventasxclientes SET idcliente = 353 WHERE idtipoventa = 4 AND id IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419));\n+UPDATE saas_161.servicios SET idcliente = 542 WHERE idservicio IN (1880, 2246);\n+UPDATE saas_161.ventas SET idcliente = 542 WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246);\n+UPDATE saas_161.ventaspagos SET idcliente = 542 WHERE idventa IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246));\n+UPDATE saas_161.ventasxclientes SET idcliente = 542 WHERE idtipoventa = 4 AND id IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246));\n \n-UPDATE saas_161.ventasxclientes SET idcliente = 353 WHERE idtipoventa = 0 AND id IN\n+UPDATE saas_161.ventasxclientes SET idcliente = 542 WHERE idtipoventa = 0 AND id IN\n     (SELECT idventapago FROM saas_161.ventaspagos WHERE idventa IN\n-        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419)));\n+        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246)));\n \n ##### RECUPERAR LECTURAS\n \n UPDATE lecturas_archivadas SET idevento = (SELECT idevento FROM carreras WHERE idcarrera = (SELECT idcarrera FROM etapas WHERE idetapa = (SELECT idetapa FROM controles WHERE controles.idcontrol = lecturas_archivadas.idcontrol)))\n"}, {"date": 1738796323532, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -121,12 +121,14 @@\n \n \n ## AGREGAR TAGS\n \n-tagID/EPC de los comprados en rollos: 000000001234 (con 8 ceros)\n+TagID/EPC de los comprados\n+15k en 2024 en rollos: 000000001234 (con 8 ceros)\n+10k en 2025 en rollos: 000000000000000000001235 (con 20 ceros)\n \n INSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';\n-=CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000\";A1;\"';\")\n+=CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000000000000000\";A1;\"';\")\n \n INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\n \n UPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';\n"}, {"date": 1738802885749, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -172,38 +172,33 @@\n \n DELETE FROM lecturas WHERE idcontrol IN\n (SELECT idcontrol FROM controles WHERE idetapa IN\n (SELECT idetapa FROM etapas WHERE idcarrera IN\n-#CUIDADO (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979))));\n+ (SELECT idcarrera FROM carreras WHERE idevento IN (2465))));\n \n-DELETE FROM datosxparticipantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n+DELETE FROM datosxparticipantes WHERE idevento IN (2465);\n+DELETE FROM participantes WHERE idevento IN (2465);\n \n-DELETE FROM datosxeventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n+DELETE FROM datosxeventos WHERE idevento IN (2465);\n DELETE FROM categorias WHERE idcarrera IN\n-    (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\n+    (SELECT idcarrera FROM carreras WHERE idevento IN (2465));\n DELETE FROM controles WHERE idetapa IN\n     (SELECT idetapa FROM etapas WHERE idcarrera IN\n-        (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979)));\n+        (SELECT idcarrera FROM carreras WHERE idevento IN (2465)));\n DELETE FROM etapas WHERE idcarrera IN\n-    (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\n-DELETE FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM config_vivo WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM config_organizacion WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM config_cronometraje WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n-DELETE FROM eventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n+    (SELECT idcarrera FROM carreras WHERE idevento IN (2465));\n+DELETE FROM carreras WHERE idevento IN (2465);\n+DELETE FROM config_vivo WHERE idevento IN (2465);\n+DELETE FROM config_organizacion WHERE idevento IN (2465);\n+DELETE FROM config_cronometraje WHERE idevento IN (2465);\n+DELETE FROM eventos WHERE idevento IN (2465);\n \n ## En SaaS\n \n DELETE FROM saas_161.productosxventas WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\n-DELETE FROM saas_161.ventasxventas WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\n-DELETE FROM saas_161.ventasxventas WHERE idrelacion IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n DELETE FROM saas_161.ventasxclientes WHERE idtipoventa > 0 AND id IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\n-DELETE FROM saas_161.servicios WHERE idservicio IN (1915, 1816, 1884, 2006, 1979);\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n \n ## CAMBIAR ID DE EVENTO\n \n ### En Crono\n"}, {"date": 1738802893598, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -197,8 +197,12 @@\n DELETE FROM saas_161.productosxventas WHERE idventa IN\n     (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n DELETE FROM saas_161.ventasxclientes WHERE idtipoventa > 0 AND id IN\n     (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+DELETE FROM saas_161.ventasxventas WHERE idventa IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+DELETE FROM saas_161.ventasxventas WHERE idrelacion IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n \n ## CAMBIAR ID DE EVENTO\n \n ### En Crono\n"}, {"date": 1739577944711, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -129,9 +129,11 @@\n INSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';\n =CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000000000000000\";A1;\"';\")\n \n INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\n+=CONCAT(\"INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\")\n \n+\n UPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';\n \n SELECT codigo, COUNT(*) AS cantidad FROM tags GROUP BY codigo;\n SELECT tagID, COUNT(*) AS cantidad FROM tags GROUP BY tagID;\n"}, {"date": 1739582704588, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -129,9 +129,9 @@\n INSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';\n =CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000000000000000\";A1;\"';\")\n \n INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\n-=CONCAT(\"INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\")\n+=CONCAT(\"INSERT INTO tags SET idevento=2350, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=2350 AND estado != 'eliminado' AND idparticipante = ''), 0));\")\n \n \n UPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';\n \n"}, {"date": 1739584978554, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -129,9 +129,9 @@\n INSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';\n =CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000000000000000\";A1;\"';\")\n \n INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\n-=CONCAT(\"INSERT INTO tags SET idevento=2350, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=2350 AND estado != 'eliminado' AND idparticipante = ''), 0));\")\n+=CONCAT(\"INSERT INTO tags SET idevento=2350, tagID='', estado=1, idorganizacion=519, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=2350 AND estado != 'eliminado' AND idparticipante = ''), 0));\")\n \n \n UPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';\n \n"}, {"date": 1740176280446, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -257,20 +257,19 @@\n \n \n ##### CAMBIAR / MOVER EVENTO DE CLIENTE\n \n-UPDATE cronometrajeinstantaneo.eventos SET idorganizacion = 542 WHERE idevento IN (1880, 2246);\n+UPDATE cronometrajeinstantaneo.eventos SET user_id = 456, idorganizacion = 456 WHERE idevento IN (807);\n \n-UPDATE saas_161.servicios SET idcliente = 542 WHERE idservicio IN (1880, 2246);\n-UPDATE saas_161.ventas SET idcliente = 542 WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246);\n-UPDATE saas_161.ventaspagos SET idcliente = 542 WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246));\n-UPDATE saas_161.ventasxclientes SET idcliente = 542 WHERE idtipoventa = 4 AND id IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246));\n+UPDATE saas_161.ventas SET idcliente = 456 WHERE tiporelacion = 'servicio' AND idrelacion IN (807);\n+UPDATE saas_161.ventaspagos SET idcliente = 456 WHERE idventa IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807));\n+UPDATE saas_161.ventasxclientes SET idcliente = 456 WHERE idtipoventa = 4 AND id IN\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807));\n \n-UPDATE saas_161.ventasxclientes SET idcliente = 542 WHERE idtipoventa = 0 AND id IN\n+UPDATE saas_161.ventasxclientes SET idcliente = 456 WHERE idtipoventa = 0 AND id IN\n     (SELECT idventapago FROM saas_161.ventaspagos WHERE idventa IN\n-        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1880, 2246)));\n+        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807)));\n \n ##### RECUPERAR LECTURAS\n \n UPDATE lecturas_archivadas SET idevento = (SELECT idevento FROM carreras WHERE idcarrera = (SELECT idcarrera FROM etapas WHERE idetapa = (SELECT idetapa FROM controles WHERE controles.idcontrol = lecturas_archivadas.idcontrol)))\n"}, {"date": 1740276398487, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,9 +168,13 @@\n DELETE FROM datosxparticipantes WHERE idinscripcion IN\n     (SELECT idinscripcion FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado');\n DELETE FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado';\n \n+## RESTAR HORAS\n \n+UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n+\n+\n ## En Crono\n \n DELETE FROM lecturas WHERE idcontrol IN\n (SELECT idcontrol FROM controles WHERE idetapa IN\n"}, {"date": 1740659955221, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -316,9 +316,9 @@\n \n \n ## RECUPERAR CONTRASEÑA UPDATE PASSWORD\n https://admin.cronometrajeinstantaneo.com/forgot-password\n-$user = App\\Models\\User::where('id', 403)->update(['password' => Hash::make('bosquemtb')]);\n+$user = App\\Models\\User::where('id', 313)->update(['password' => Hash::make('backup')]);\n \n \n ## HORARIOS DE LARGADAS\n \n"}, {"date": 1741792758983, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -258,9 +258,15 @@\n nacimiento_desde = DATE_ADD(nacimiento_desde, INTERVAL 1 YEAR),\n nacimiento_hasta = DATE_ADD(nacimiento_hasta, INTERVAL 1 YEAR)\n WHERE idevento = 2301;\n \n+#### CONFIGURAR PUNTOS\n \n+UPDATE carreras SET\n+puntaje = 'categorias',\n+puntos = '25,21,18,16,14,13,12,11,10,9,8,7,6,5,4,3,2,1'\n+WHERE idevento = 2522;\n+\n ##### CAMBIAR / MOVER EVENTO DE CLIENTE\n \n UPDATE cronometrajeinstantaneo.eventos SET user_id = 456, idorganizacion = 456 WHERE idevento IN (807);\n \n"}, {"date": 1741816329913, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -178,37 +178,38 @@\n \n DELETE FROM lecturas WHERE idcontrol IN\n (SELECT idcontrol FROM controles WHERE idetapa IN\n (SELECT idetapa FROM etapas WHERE idcarrera IN\n- (SELECT idcarrera FROM carreras WHERE idevento IN (2465))));\n+ (SELECT idcarrera FROM carreras WHERE idevento IN (2043))));\n \n-DELETE FROM datosxparticipantes WHERE idevento IN (2465);\n-DELETE FROM participantes WHERE idevento IN (2465);\n+DELETE FROM datosxparticipantes WHERE idevento IN (2043);\n+DELETE FROM participantes WHERE idevento IN (2043);\n \n-DELETE FROM datosxeventos WHERE idevento IN (2465);\n+DELETE FROM datosxeventos WHERE idevento IN (2043);\n DELETE FROM categorias WHERE idcarrera IN\n-    (SELECT idcarrera FROM carreras WHERE idevento IN (2465));\n+    (SELECT idcarrera FROM carreras WHERE idevento IN (2043));\n DELETE FROM controles WHERE idetapa IN\n     (SELECT idetapa FROM etapas WHERE idcarrera IN\n-        (SELECT idcarrera FROM carreras WHERE idevento IN (2465)));\n+        (SELECT idcarrera FROM carreras WHERE idevento IN (2043)));\n DELETE FROM etapas WHERE idcarrera IN\n-    (SELECT idcarrera FROM carreras WHERE idevento IN (2465));\n-DELETE FROM carreras WHERE idevento IN (2465);\n-DELETE FROM config_vivo WHERE idevento IN (2465);\n-DELETE FROM config_organizacion WHERE idevento IN (2465);\n-DELETE FROM config_cronometraje WHERE idevento IN (2465);\n-DELETE FROM eventos WHERE idevento IN (2465);\n+    (SELECT idcarrera FROM carreras WHERE idevento IN (2043));\n+DELETE FROM carreras WHERE idevento IN (2043);\n+DELETE FROM config_vivo WHERE idevento IN (2043);\n+DELETE FROM config_organizacion WHERE idevento IN (2043);\n+DELETE FROM config_cronometraje WHERE idevento IN (2043);\n+DELETE FROM eventos WHERE idevento IN (2043);\n \n ## En SaaS\n \n DELETE FROM saas_161.productosxventas WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));\n DELETE FROM saas_161.ventasxclientes WHERE idtipoventa > 0 AND id IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));\n DELETE FROM saas_161.ventasxventas WHERE idventa IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));\n DELETE FROM saas_161.ventasxventas WHERE idrelacion IN\n-    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2465));\n+    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));\n+DELETE FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043);\n \n ## CAMBIAR ID DE EVENTO\n \n ### En Crono\n"}, {"date": 1742070616789, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -105,9 +105,9 @@\n ```\n \n ## PORCENTAJE LECTURA CHIPS\n \n-SELECT * FROM `controles` WHERE idevento = 1626;\n+SELECT * FROM `controles` WHERE idevento = 1626 GROUP BY idcontrol;\n SELECT COUNT(*) AS cantidad, timer FROM `lecturas` WHERE idcontrol = 12529 GROUP BY timer;\n SELECT\n (SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0) AS total,\n (SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0 AND timer = 'Chips') AS chips,\n"}, {"date": 1742073299519, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -109,9 +109,9 @@\n SELECT * FROM `controles` WHERE idevento = 1626 GROUP BY idcontrol;\n SELECT COUNT(*) AS cantidad, timer FROM `lecturas` WHERE idcontrol = 12529 GROUP BY timer;\n SELECT\n (SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0) AS total,\n-(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0 AND timer = 'Chips') AS chips,\n+(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0 AND tipo = 'rfid') AS chips,\n (SELECT chips * 100 / total) AS porcentaje;\n \n \n ## VIDEOS\n"}, {"date": 1742310198159, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -149,8 +149,25 @@\n (1894, 13, 8962),\n (1894, 14, 8963),\n (1894, 15, 8979);\n \n+\n+## LISTAR PAGOS TOTALES POR PRECIO Y PLATAFORMA\n+\n+SELECT\n+pagos.fecha,\n+eventos.nombre AS Evento,\n+plataformas.key AS MP_Key,\n+participantes.nombre as Participante,\n+precios.precio as Total\n+FROM `pagos`\n+LEFT JOIN eventos ON pagos.idevento = eventos.idevento\n+LEFT JOIN precios ON pagos.idprecio = precios.idprecio\n+LEFT JOIN plataformas ON precios.idplataforma = plataformas.idplataforma\n+LEFT JOIN participantes ON pagos.idinscripcion = participantes.idinscripcion\n+WHERE pagos.idevento IN (2507, 2494)\n+\n+\n ## TOTALES ANUALES (Cantidades en el año)\n \n SELECT idevento FROM `eventos` WHERE fecha > '2023-01-01' AND fecha < '2024-01-01';\n SELECT\n"}, {"date": 1747512555724, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -139,9 +139,16 @@\n SELECT tagID, COUNT(*) AS cantidad FROM tags GROUP BY tagID;\n \n ## AGREGAR PRECIOS\n \n+INSERT INTO plataformas (idorganizacion, titulo, plataforma, descripcion, `key`, secret) VALUES\n+(593, 'MercadoPago', 'mercadopago', 'Paga con todas las opciones de MercadoPago', 'APP_USR-169bf06c-e54f-4971-a15e-c56ee1a95457', 'APP_USR-7368465733403202-051219-2e58996441707a0da6a5f620924add55-549597078');\n+\n INSERT INTO precios (idprecio, idevento, idplataforma, precio, url) VALUES\n+(NULL, 2679, 19, '40000', ''),\n+(NULL, 2679, 19, '50000', '');\n+\n+INSERT INTO precios (idprecio, idevento, idplataforma, precio, url) VALUES\n (13, 1894, 8, 102.978, '<a class=\"button\" href=\"https://mpago.la/2xpeazK\" target=\"_blank\">Pagar 42km Corredores Nacionales</a>'),\n (14, 1894, 8, 84.409, '<a class=\"button\" href=\"https://mpago.la/2ZauKmm\" target=\"_blank\">Pagar 21km Corredores Nacionales</a>'),\n (15, 1894, 8, 65.855, '<a class=\"button\" href=\"https://mpago.la/1FKy54C\" target=\"_blank\">Pagar 10km Corredores Nacionales</a>');\n \n"}], "date": 1726407825431, "name": "Commit-0", "content": "# AYUDAS TEMPORALES\n\n## Libre Office\n\n=CONCAT(\"INSERT INTO participantes SET idevento = 562, estado='acreditado', idparticipante=\";A57;\", nombre='\";B57;\" \";C57;\"', idcategoria=\";E57;\";\")\n=CONCAT(\"INSERT INTO lecturas SET idevento = 425, idcontrol = 1697, estado = 'ok', tipo = 'app', idparticipante = \";A9;\", tiempo = '2021-02-24 04:00:00';\")\nSELECT idcategoria, nombre FROM participantes WHERE idevento = 565 GROUP BY idcategoria;\n\n## Carreras\n\nSELECT * FROM carreras WHERE idevento =\n\nINSERT INTO carreras (idcarrera, idevento, nombre, largada, observacion, etapas) VALUES\n(NULL, 35, '21K', '', NULL, 1),\n(NULL, 35, '10K', '', NULL, 1),\n(NULL, 35, 'Participativa 5K', '', NULL, 1);\n\n## Categor<PERSON>\n\nSELECT * FROM categorias WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 29)\n\nINSERT INTO categorias (idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n(7412, 1716, 'Caballeros - Juveniles (16-19 años)', 'masculino', '', '2004-02-18', '2008-02-18'),\n(7412, 1716, 'Caballeros - Mayores (20-29 años)', 'masculino', '', '1994-02-18', '2004-02-18'),\n(7412, 1716, 'Caballeros - Pre-veteranos (30-34 años)', 'masculino', '', '1989-02-18', '1994-02-18'),\n(7412, 1716, 'Caballeros - Veteranos (35-39 años)', 'masculino', '', '1984-02-18', '1989-02-18'),\n(7412, 1716, 'Caballeros - Veteranos A (40-44 años)', 'masculino', '', '1979-02-18', '1984-02-18'),\n(7412, 1716, 'Caballeros - Veteranos B (45-49 años)', 'masculino', '', '1974-02-18', '1979-02-18'),\n(7412, 1716, 'Caballeros - Veteranos C (50-54 años)', 'masculino', '', '1969-02-18', '1974-02-18'),\n(7412, 1716, 'Caballeros - Veteranos D (55-59 años)', 'masculino', '', '1964-02-18', '1969-02-18'),\n(7412, 1716, 'Caballeros - Veteranos E (60-64 años)', 'masculino', '', '1959-02-18', '1964-02-18'),\n(7412, 1716, 'Caballeros - Veteranos F (+65 años)', 'masculino', '', '1900-02-18', '1959-02-18'),\n(7412, 1716, 'Damas - Juveniles (16-19 años)', 'femenino', '', '2004-02-18', '2008-02-18'),\n(7412, 1716, 'Damas - Mayores (20-29 años)', 'femenino', '', '1994-02-18', '2004-02-18'),\n(7412, 1716, 'Damas - Pre-veteranos (30-34 años)', 'femenino', '', '1989-02-18', '1994-02-18'),\n(7412, 1716, 'Damas - Veteranos (35-39 años)', 'femenino', '', '1984-02-18', '1989-02-18'),\n(7412, 1716, 'Damas - Veteranos A (40-44 años)', 'femenino', '', '1979-02-18', '1984-02-18'),\n(7412, 1716, 'Damas - Veteranos B (45-49 años)', 'femenino', '', '1974-02-18', '1979-02-18'),\n(7412, 1716, 'Damas - Veteranos C (50-54 años)', 'femenino', '', '1969-02-18', '1974-02-18'),\n(7412, 1716, 'Damas - Veteranos D (55-59 años)', 'femenino', '', '1964-02-18', '1969-02-18'),\n(7412, 1716, 'Damas - Veteranos E (60-64 años)', 'femenino', '', '1959-02-18', '1964-02-18'),\n(7412, 1716, 'Damas - Veteranos F (+65 años)', 'femenino', '', '1900-02-18', '1959-02-18');\n\n\n## Etapas\n\nSELECT * FROM etapas WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 29)\n\nINSERT INTO etapas (idcarrera, idevento, nombre) VALUES\n(35, 100, 'Final 21K'),\n(36, 100, 'Final 10K'),\n(37, 100, 'Final Participativa 5K')\n\n## Controles\n\nSELECT * FROM controles WHERE idetapa IN\n(SELECT idetapa FROM etapas WHERE idcarrera IN\n(SELECT idcarrera FROM carreras WHERE idevento = 40));\n\nINSERT INTO controles (idcontrol, idevento, codigo, idetapa, nombre, tipo, tipolectura) VALUES\n(53, 100, 'BOS1', 38, 'Final', 'final', 'movil')\n\n\n## DatosXEventos\n\nSELECT * FROM datosxeventos WHERE idevento = 29;\n\nINSERT INTO datosxeventos (iddato, idevento, obligatorio, orden) VALUES\n\n## Lecturas\n\nSELECT * FROM lecturas WHERE idcontrol IN\n(SELECT idcontrol FROM controles WHERE idetapa IN\n(SELECT idetapa FROM etapas WHERE idcarrera IN\n(SELECT idcarrera FROM carreras WHERE idevento = 40)));\n\n## Remeras\n\nSELECT dato, COUNT(*) FROM datosxparticipantes WHERE iddato = 'talle' AND idinscripcion IN\n    (SELECT idinscripcion FROM participantes WHERE idcategoria IN\n        (SELECT idcategoria FROM categorias WHERE idevento = 476 AND sexo = 'masculino'))\nGROUP BY dato\n\n## OWA: COPIA DE ficha_ok según DNI\n\n```sql\nSELECT idevento, fecha, nombre, resultados_estilo FROM eventos WHERE idorganizacion = 45 ORDER BY fecha DESC;\n\nUPDATE datosxparticipantes SET dato = REPLACE(dato,'.','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\nUPDATE datosxparticipantes SET dato = REPLACE(dato,' ','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\nUPDATE datosxparticipantes SET dato = REPLACE(dato,'-','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\nUPDATE datosxparticipantes SET dato = REPLACE(dato,'dni','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\nUPDATE datosxparticipantes SET dato = REPLACE(dato,'Dni','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\nUPDATE datosxparticipantes SET dato = REPLACE(dato,'DNI','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);\n\nSELECT dato FROM datosxparticipantes WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762)\n    AND idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762) AND (estado = 'acreditado' OR ficha_ok = 1))\nGROUP BY dato\nORDER BY dato;\n\nUPDATE participantes SET ficha_ok = 1 WHERE idinscripcion IN\n(SELECT idinscripcion FROM datosxparticipantes WHERE idevento = 1473 AND iddato = 'dni' AND dato IN (\n\n))\n```\n\n## PORCENTAJE LECTURA CHIPS\n\nSELECT * FROM `controles` WHERE idevento = 1626;\nSELECT COUNT(*) AS cantidad, timer FROM `lecturas` WHERE idcontrol = 12529 GROUP BY timer;\nSELECT\n(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0) AS total,\n(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0 AND timer = 'Chips') AS chips,\n(SELECT chips * 100 / total) AS porcentaje;\n\n\n## VIDEOS\n\nINSERT INTO videos (`idevento`, `idcontrol`, `nombre`, `tipo`, `url`, `inicio`, `fin`) VALUES\n(1425, 10707, 'Mirá tu llegada', 'youtube', 'https://www.youtube.com/watch?v=yC3cUSfXOFc', '2024-06-09 15:46:27.000', '2024-06-09 16:02:49.000');\n\n\n## AGREGAR TAGS\n\ntagID/EPC de los comprados en rollos: 000000001234 (con 8 ceros)\n\nINSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';\n=CONCAT(\"INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='\";A1;\"', tagID='00000000\";A1;\"';\")\n\nINSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));\n\nUPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';\n\nSELECT codigo, COUNT(*) AS cantidad FROM tags GROUP BY codigo;\nSELECT tagID, COUNT(*) AS cantidad FROM tags GROUP BY tagID;\n\n## AGREGAR PRECIOS\n\nINSERT INTO precios (idprecio, idevento, idplataforma, precio, url) VALUES\n(13, 1894, 8, 102.978, '<a class=\"button\" href=\"https://mpago.la/2xpeazK\" target=\"_blank\">Pagar 42km Corredores Nacionales</a>'),\n(14, 1894, 8, 84.409, '<a class=\"button\" href=\"https://mpago.la/2ZauKmm\" target=\"_blank\">Pagar 21km Corredores Nacionales</a>'),\n(15, 1894, 8, 65.855, '<a class=\"button\" href=\"https://mpago.la/1FKy54C\" target=\"_blank\">Pagar 10km Corredores Nacionales</a>');\n\nINSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n(1894, 13, 8962),\n(1894, 14, 8963),\n(1894, 15, 8979);\n\n## TOTALES ANUALES (Cantidades en el año)\n\nSELECT idevento FROM `eventos` WHERE fecha > '2023-01-01' AND fecha < '2024-01-01';\nSELECT\n(SELECT COUNT(*) FROM lecturas WHERE idevento IN ()) AS lecturas,\n(SELECT COUNT(*) FROM participantes WHERE idevento IN ()) AS participantes,\n(SELECT COUNT(*) FROM eventos WHERE idevento IN ()) AS eventos,\n(SELECT COUNT(DISTINCT idorganizacion) FROM eventos WHERE idevento IN ()) AS organizaciones,\n(SELECT COUNT(DISTINCT iddisciplina) FROM eventos WHERE idevento IN ()) AS disciplinas,\n(SELECT COUNT(DISTINCT idpais) FROM eventos WHERE idevento IN ()) AS paises;\n\n\n## ELIMINAR EVENTO COMPLETO\n\n## Limpiar eliminados\nDELETE FROM datosxparticipantes WHERE idinscripcion IN\n    (SELECT idinscripcion FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado');\nDELETE FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado';\n\n\n## En Crono\n\nDELETE FROM lecturas WHERE idcontrol IN\n(SELECT idcontrol FROM controles WHERE idetapa IN\n(SELECT idetapa FROM etapas WHERE idcarrera IN\n#CUIDADO (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979))));\n\nDELETE FROM datosxparticipantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n\nDELETE FROM datosxeventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM categorias WHERE idcarrera IN\n    (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM controles WHERE idetapa IN\n    (SELECT idetapa FROM etapas WHERE idcarrera IN\n        (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979)));\nDELETE FROM etapas WHERE idcarrera IN\n    (SELECT idcarrera FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM carreras WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM config_vivos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM config_inscripciones WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM config_cronometraje WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\nDELETE FROM eventos WHERE idevento IN (1915, 1816, 1884, 2006, 1979);\n\n## En SaaS\n\nDELETE FROM saas_161.productosxventas WHERE idventa IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM saas_161.ventasxventas WHERE idventa IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM saas_161.ventasxventas WHERE idrelacion IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM saas_161.ventasxclientes WHERE idtipoventa > 0 AND id IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (1915, 1816, 1884, 2006, 1979));\nDELETE FROM saas_161.servicios WHERE idservicio IN (1915, 1816, 1884, 2006, 1979);\n\n## CAMBIAR ID DE EVENTO\n\n### En Crono\n\nUPDATE participantes SET idevento = 527 WHERE idevento = 530;\nUPDATE datosxparticipantes SET idevento = 527 WHERE idevento = 530;\nUPDATE categorias SET idevento = 527 WHERE idevento = 530;\nUPDATE carreras SET idevento = 527 WHERE idevento = 530;\nUPDATE eventos SET idevento = 527 WHERE idevento = 530;\n\n### En SaaS\n\nUPDATE saas_161.ventas SET idrelacion = 614 WHERE tiporelacion = 'servicio' AND idrelacion = 531;\n\n\n## Últimos\n\nSELECT\n    (SELECT idorganizacion FROM organizaciones ORDER BY idorganizacion DESC LIMIT 1) AS idorganizacion,\n    (SELECT idevento FROM eventos ORDER BY idevento DESC LIMIT 1) AS idevento,\n    (SELECT idcarrera FROM carreras ORDER BY idcarrera DESC LIMIT 1) AS idcarrera,\n    (SELECT idcategoria FROM categorias ORDER BY idcategoria DESC LIMIT 1) AS idcategoria,\n    (SELECT idetapa FROM etapas ORDER BY idetapa DESC LIMIT 1) AS idetapa,\n    (SELECT idcontrol FROM controles ORDER BY idcontrol DESC LIMIT 1) AS idcontrol\n\n\n## VARIOS\n\nSELECT idservicio, fechasolicitado, c.nombre AS cliente, titulo, cat.nombre AS categoria,\n    (SELECT SUM(total) FROM ventas WHERE muevesaldo = 1 AND idrelacion = s.idservicio) AS total\nFROM servicios AS s\n    JOIN clientes AS c ON s.idcliente = c.idcliente\n    JOIN categorias_servicios AS cat ON s.idtiposervicio = cat.idtiposervicio\nWHERE idservicio > 10 AND s.estado != 5\nORDER BY idservicio\n\nSELECT idevento, COUNT(*) AS cantidad\nFROM participantes\nWHERE estado IN ('inscripto', 'acreditado', 'descalificado', 'abandono')\nGROUP BY idevento\nORDER BY idevento\n\n\n##### CAMBIAR EVENTO DE CLIENTE\n\nUPDATE cronometrajeinstantaneo.eventos SET idorganizacion = 353 WHERE idevento IN (897, 1419);\n\nUPDATE saas_161.servicios SET idcliente = 353 WHERE idservicio IN (897, 1419);\nUPDATE saas_161.ventas SET idcliente = 353 WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419);\nUPDATE saas_161.ventaspagos SET idcliente = 353 WHERE idventa IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419));\nUPDATE saas_161.ventasxclientes SET idcliente = 353 WHERE idtipoventa = 4 AND id IN\n    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419));\n\nUPDATE saas_161.ventasxclientes SET idcliente = 353 WHERE idtipoventa = 0 AND id IN\n    (SELECT idventapago FROM saas_161.ventaspagos WHERE idventa IN\n        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (897, 1419)));\n\n##### RECUPERAR LECTURAS\n\nUPDATE lecturas_archivadas SET idevento = (SELECT idevento FROM carreras WHERE idcarrera = (SELECT idcarrera FROM etapas WHERE idetapa = (SELECT idetapa FROM controles WHERE controles.idcontrol = lecturas_archivadas.idcontrol)))\n\n\n##### NUEVO USUARIO ####\n\n## Datos para completar\n\nOrganización: DH Chorde\nUsuario: <EMAIL>\nContraseña: Dhchorde\nCronometrador: Ecuador\nPais: Ecuador\nidorganizacion = 388\n\n## En SaaS\n\nINSERT INTO saas_161.clientes SET\n    nombre = 'DH Chorde',\n    mail = '<EMAIL>',\n    contacto = 'Ecuador',\n    idlocalidad = (SELECT idlocalidad FROM saas_161.categorias_localidades WHERE nombre = 'Ecuador' LIMIT 1),\n    obsinterna = '<p>\nUsuario: <EMAIL><br>\nContraseña: Dhchorde\n</p>';\n\n## En Crono\n\nINSERT INTO cronometrajeinstantaneo.organizaciones SET\n    idorganizacion = 388,\n    pass = md5('Dhchorde'),\n    nombre = 'DH Chorde',\n    idpais = (SELECT id FROM cronometrajeinstantaneo.paises WHERE nombre_es = 'Ecuador'),\n    estado = 1,\n    mail = '<EMAIL>';\n\n$new=new App\\Models\\User();$new->id=388;$new->email=\"<EMAIL>\";$new->password=Hash::make(\"Dhchorde\");$new->save();\n\n\n## RECUPERAR CONTRASEÑA UPDATE PASSWORD\nhttps://admin.cronometrajeinstantaneo.com/forgot-password\n$user = App\\Models\\User::where('id', 403)->update(['password' => Hash::make('bosquemtb')]);\n\n\n## HORARIOS DE LARGADAS\n\nPara la que genera sólo el informe, pueden ir a Resultados > Etapas y elegir la etapa de la Qualy y abrir ese informe (puede ser por categorías o no). Luego agregarle al final de la url lo siguiente: &largadas=32400_60_180 que significa 32400 son los segundos del día para las 9 de la mañana, 60 segundos son entre cada participante y 180 segundos entre cada categoría. No está fácil, pero la idea es que se va a generar solo desde una configuración\nPor ej:\nhttps://cronometrajeinstantaneo.com/resultados/baja-chiara-300/etapas?idetapas=13880,13881&largadas=36000_60_60"}]}