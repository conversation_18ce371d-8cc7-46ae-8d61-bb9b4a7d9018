{"sourceFile": "BRAIN/WIKI/Listar Combos.sql.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1737979629578, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1737979674657, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,9 @@\n \n-\n+```mysql\n SELECT idcombo, c.nombre AS combo, idproducto, p.nombre AS producto, cantidad\n FROM productosxcombos\n \tLEFT JOIN productos AS p ON productos.idproducto = productosxcombos.idproducto\n \tLEFT JOIN productos AS c ON productos.idproducto = productosxcombos.idcombo\n WHERE idcombo IN\n\\ No newline at end of file\n-(344, 529, 675, 730, 741, 758, 759, 762, 763, 764, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 777, 778, 779, 780, 782, 784, 785, 788, 790, 793, 795, 799, 800, 801, 837, 905, 906, 907, 908, 909, 911, 912, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 955, 956, 957, 959, 1059, 1090, 1092, 1136, 1137, 1138, 1141, 1142, 1221, 1256, 1287, 1288, 1302, 1311, 1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1351, 1357, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1377, 1381, 1384, 1385, 1389, 1390, 1391, 1392, 1393, 1395, 1399, 1408, 1413, 1421, 1422, 1449, 1450, 1451, 1452, 1453, 1459, 1470, 1478, 1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1490, 1491, 1498, 1502, 1503, 1506, 1508, 1510, 1521, 1522, 1523, 1528, 1559, 1562, 1564, 1565, 1566, 1568, 1569, 1570, 1575, 1578, 1579, 1580, 1582, 1584, 1585, 1586, 1587, 1588, 1590, 1610, 1648, 1649, 1650, 1659, 1660, 1662, 1683, 1684, 1685, 1687, 1713, 1714, 1715, 1726, 1727, 1741, 1742, 1743, 1746, 1747, 1748, 1749, 1750, 1751, 1754, 1765, 1766, 1776, 1777, 1778, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1790, 1805, 1807, 1809, 1811, 1815, 1816, 1825, 1826, 1835, 1836, 1842, 1845, 1847, 1848, 1849, 1850, 1853, 9171, 9173, 9177, 9198, 9201, 9212, 9213, 9214, 9216, 9217, 9218, 9220, 9221, 9222, 9224, 9225, 9226, 9228, 9229, 9230, 9232, 9233, 9234, 9236, 9237, 9238, 9240, 9241, 9242, 9244, 9245, 9246, 9248, 9249, 9250, 9252, 9253, 9254, 9256, 9257, 9258, 9261, 9264, 9268, 9308, 9309, 9310, 9311, 9312, 9313, 9314, 9315, 9316, 9317, 9318, 9319, 9320, 9321, 9322, 9323, 9324, 9325, 9326, 9327, 9328, 9329, 9330, 9331, 9332, 9333, 9334, 9335, 9336, 9337, 9338, 9339, 9340, 9341, 9342, 9343, 9344, 9345, 9346, 9347, 9348, 9349, 9350, 9356, 9366, 9383, 9384, 9385, 9386, 9387, 9388, 9389, 9390, 9391, 9406, 16927, 16929, 16930, 16931, 16932, 16934, 16935, 16936, 16938, 16939, 16948, 16949, 16953, 16954, 16955, 16963, 16980, 16997, 16998, 16999, 17000, 17001, 17003, 17004, 17005, 17006, 17007, 17008, 17009, 17010, 17011, 17012, 17013, 17014, 17015, 17016, 17029, 17030, 17032, 17033, 17041, 17042, 17043, 17044, 17045, 17046, 17047, 17048, 17055, 17068, 17071, 17072, 17073, 17074, 17075, 17087, 17088, 17092, 17105, 17108, 17111, 17114, 17117, 17127, 17128, 17129, 17130, 17131, 17132, 17133, 17134, 17135, 17136, 17137, 17140, 17141, 17142, 17143, 17150, 17151, 17152, 17153, 17154, 17155, 17156, 17157, 17161, 17183, 17184, 17189, 17192, 17193, 17194, 17196, 17198, 17202, 17203, 17204, 17205, 17206, 17207, 17208, 17209, 17214, 17217, 17218, 17219, 17220, 17221, 17222, 17224, 17239, 17240, 17241, 17242, 17244, 17245, 17273, 17274, 17275, 17281)\n+(SELECT idproducto FROM productos WHERE combo = 1)\n+```\n\\ No newline at end of file\n"}, {"date": 1737980029322, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n+# LISTAR COMBOS\n \n ```mysql\n-SELECT idcombo, c.nombre AS combo, idproducto, p.nombre AS producto, cantidad\n-FROM productosxcombos\n-\tLEFT JOIN productos AS p ON productos.idproducto = productosxcombos.idproducto\n-\tLEFT JOIN productos AS c ON productos.idproducto = productosxcombos.idcombo\n-WHERE idcombo IN\n-(SELECT idproducto FROM productos WHERE combo = 1)\n+SELECT idcombo, c.codigo AS codigo_combo, c.nombre AS combo, p.idproducto, p.codigo, p.nombre AS producto, cantidad\n+FROM productosxcombos AS pxc\n+\tLEFT JOIN productos AS p ON p.idproducto = pxc.idproducto\n+\tLEFT JOIN productos AS c ON c.idproducto = pxc.idcombo\n+WHERE c.combo = 1\n ```\n\\ No newline at end of file\n"}], "date": 1737979629578, "name": "Commit-0", "content": "\n\nSELECT idcombo, c.nombre AS combo, idproducto, p.nombre AS producto, cantidad\nFROM productosxcombos\n\tLEFT JOIN productos AS p ON productos.idproducto = productosxcombos.idproducto\n\tLEFT JOIN productos AS c ON productos.idproducto = productosxcombos.idcombo\nWHERE idcombo IN\n(344, 529, 675, 730, 741, 758, 759, 762, 763, 764, 766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 777, 778, 779, 780, 782, 784, 785, 788, 790, 793, 795, 799, 800, 801, 837, 905, 906, 907, 908, 909, 911, 912, 914, 915, 916, 917, 918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936, 937, 938, 939, 940, 941, 942, 943, 944, 945, 946, 947, 948, 949, 950, 951, 952, 953, 955, 956, 957, 959, 1059, 1090, 1092, 1136, 1137, 1138, 1141, 1142, 1221, 1256, 1287, 1288, 1302, 1311, 1312, 1313, 1314, 1315, 1316, 1317, 1318, 1319, 1322, 1323, 1324, 1325, 1326, 1327, 1328, 1329, 1330, 1331, 1332, 1333, 1334, 1335, 1336, 1351, 1357, 1359, 1360, 1361, 1362, 1363, 1364, 1365, 1377, 1381, 1384, 1385, 1389, 1390, 1391, 1392, 1393, 1395, 1399, 1408, 1413, 1421, 1422, 1449, 1450, 1451, 1452, 1453, 1459, 1470, 1478, 1479, 1480, 1481, 1482, 1483, 1484, 1485, 1486, 1487, 1488, 1490, 1491, 1498, 1502, 1503, 1506, 1508, 1510, 1521, 1522, 1523, 1528, 1559, 1562, 1564, 1565, 1566, 1568, 1569, 1570, 1575, 1578, 1579, 1580, 1582, 1584, 1585, 1586, 1587, 1588, 1590, 1610, 1648, 1649, 1650, 1659, 1660, 1662, 1683, 1684, 1685, 1687, 1713, 1714, 1715, 1726, 1727, 1741, 1742, 1743, 1746, 1747, 1748, 1749, 1750, 1751, 1754, 1765, 1766, 1776, 1777, 1778, 1780, 1781, 1782, 1783, 1784, 1785, 1786, 1787, 1790, 1805, 1807, 1809, 1811, 1815, 1816, 1825, 1826, 1835, 1836, 1842, 1845, 1847, 1848, 1849, 1850, 1853, 9171, 9173, 9177, 9198, 9201, 9212, 9213, 9214, 9216, 9217, 9218, 9220, 9221, 9222, 9224, 9225, 9226, 9228, 9229, 9230, 9232, 9233, 9234, 9236, 9237, 9238, 9240, 9241, 9242, 9244, 9245, 9246, 9248, 9249, 9250, 9252, 9253, 9254, 9256, 9257, 9258, 9261, 9264, 9268, 9308, 9309, 9310, 9311, 9312, 9313, 9314, 9315, 9316, 9317, 9318, 9319, 9320, 9321, 9322, 9323, 9324, 9325, 9326, 9327, 9328, 9329, 9330, 9331, 9332, 9333, 9334, 9335, 9336, 9337, 9338, 9339, 9340, 9341, 9342, 9343, 9344, 9345, 9346, 9347, 9348, 9349, 9350, 9356, 9366, 9383, 9384, 9385, 9386, 9387, 9388, 9389, 9390, 9391, 9406, 16927, 16929, 16930, 16931, 16932, 16934, 16935, 16936, 16938, 16939, 16948, 16949, 16953, 16954, 16955, 16963, 16980, 16997, 16998, 16999, 17000, 17001, 17003, 17004, 17005, 17006, 17007, 17008, 17009, 17010, 17011, 17012, 17013, 17014, 17015, 17016, 17029, 17030, 17032, 17033, 17041, 17042, 17043, 17044, 17045, 17046, 17047, 17048, 17055, 17068, 17071, 17072, 17073, 17074, 17075, 17087, 17088, 17092, 17105, 17108, 17111, 17114, 17117, 17127, 17128, 17129, 17130, 17131, 17132, 17133, 17134, 17135, 17136, 17137, 17140, 17141, 17142, 17143, 17150, 17151, 17152, 17153, 17154, 17155, 17156, 17157, 17161, 17183, 17184, 17189, 17192, 17193, 17194, 17196, 17198, 17202, 17203, 17204, 17205, 17206, 17207, 17208, 17209, 17214, 17217, 17218, 17219, 17220, 17221, 17222, 17224, 17239, 17240, 17241, 17242, 17244, 17245, 17273, 17274, 17275, 17281)"}]}