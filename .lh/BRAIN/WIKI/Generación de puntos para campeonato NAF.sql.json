{"sourceFile": "BRAIN/WIKI/Generación de puntos para campeonato NAF.sql", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1725831797617, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1725831797617, "name": "Commit-0", "content": "\nINSERT INTO datosxparticipantes SET iddato = 'puntos', idevento = 2075, dato = '', idinscripcion = '';\n\nSELECT dni, idinscripcion FROM datosxparticipantes WHERE idevento = 2075 AND iddato = 'documento_numero' AND dato = ''\n\n\n# Verifico que sean todos iddato = 'documento_numero'\nSELECT * FROM datosxeventos WHERE idevento IN (1918,1932,1933,2075);\n\n// Obtengo los idinscripciones de los que no tienen dni\nSELECT idinscripcion FROM datosxparticipantes WHERE idevento IN (1918,1932,1933,2075) AND iddato = 'documento_numero' AND dato IN\n(35489665, 39149235, 26271493, 44916647, 41551745, 28867028, 30488280, 65826805, 49604190, 43862944, 2007000, 35947295, 65840409, 42194742, 40817689, 36815316, 40770267, 28941688, 29761556, 62442917, 19759438, 36410982, 41533991, 37783879, 63054137, 46835285, 16594411, 44649642, 39708112, 33095593, 28437758, 31951187, 29981261, 19098357, 51790961, 51052866, 62442898, 30634158, 18866195, 38895061, 55980922, 34239881, 46604692, 51803560, 27687182, 656409509, 20231192, 56839063, 35442803, 47978674, 37018517, 16657930, 16528648, 33060851, 64699499, 48137645, 40130746, 14347574, 41847673, 57234608, 77779167, 49462220, 34994617, 51467221, 51132486, 41091474, 47855163, 34741600, 18783783, 47433268, 33919167, 64870548, 64707913, 18840349, 29190872, 39086879, 41079113, 34218351, 30937009, 30514843, 33148893, 49135886, 35173292, 34166676, 46721096, 16452102, 33939298, 25632470);\n"}]}