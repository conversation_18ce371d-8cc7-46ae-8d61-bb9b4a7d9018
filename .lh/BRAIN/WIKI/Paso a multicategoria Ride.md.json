{"sourceFile": "BRAIN/WIKI/Paso a multicategoria Ride.md", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1726612480459, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726612514744, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,10 @@\n+# OSE + ARG\n+\n+<PERSON> a tener un evento doble, junto con el Argentino. Kittu me debe un excel con las categorías de cada uno (el Argentino tiene menos y se compensan). Voy a configurar 3 carreras: OSE, ARG, OSE + ARG con las categorías compensadas.\n+\n+El día antes del evento, voy a mover a mano las de OSE + ARG a cada uno. Ver si podes preparar los SQL antes.\n+\n idevento: 2175\n idcarreras\n OSE: 10773\n ARG: 10881\n"}, {"date": 1726614257236, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,9 @@\n OSE+ARG: 10882\n \n ## AGREGAR A LA MULTICATEGORIA ANTES DEL EVENTO\n \n+```sql\n INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n     (SELECT 55211, idinscripcion FROM participantes WHERE idcategoria = 55211),\n     (SELECT 55212, idinscripcion FROM participantes WHERE idcategoria = 55212),\n \n@@ -46,9 +47,66 @@\n     (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55230),\n     (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55231),\n     (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55231),\n \n+    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55232),\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55233),\n+    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55234),\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55234),\n \n+    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55235),\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55236),\n+    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55237),\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55238),\n+\n+    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55238),\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55239),\n+    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55240),\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55240),\n+\n+    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55241),\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55242),\n+    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55243),\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55243),\n+\n+    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55244),\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55245),\n+    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55246),\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55246),\n+\n+    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55247),\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55248),\n+    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55249),\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55249),\n+\n+    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55250),\n+    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55251),\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55251),\n+\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55252),\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55253),\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55254),\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55255),\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55256),\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55257),\n+\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55253),\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55254),\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55255),\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55256),\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55257),\n+\n+    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55258),\n+    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55259),\n+    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55259),\n+\n+    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55260),\n+    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55261),\n+    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55261)\n+\n+\n+\n+\n DELETE FROM categorias WHERE idevento = 2175 AND idcarrera = 10773;\n \n INSERT INTO `categorias` (`idcategoria`, `idevento`, `idcarrera`, `orden`, `nombre`, `sexo`, `nacimiento_desde`, `nacimiento_hasta`) VALUES\n (55211, 2175, 10773, 1, 'Debutantes Men', 'masculino', '1910-01-01', '2008-12-31'),\n@@ -79,22 +137,47 @@\n (55229, 2175, 10773, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n (55230, 2175, 10881, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n (55231, 2175, 10882, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n \n+(55232, 2175, 10773, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n+(55233, 2175, 10881, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n+(55234, 2175, 10882, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n \n-(55228, 2175, 10773, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n-(55229, 2175, 10881, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n-(55230, 2175, 10773, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n-(55231, 2175, 10881, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n-(55232, 2175, 10773, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n-(55233, 2175, 10881, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n-(55234, 2175, 10773, 12, 'E-Amateur (16+)', 'mixto', '1910-01-01', '2008-12-31'),\n-(55235, 2175, 10881, 12, 'E-Elite', 'mixto', '1910-01-01', '2008-12-31'),\n-(55236, 2175, 10882, 12, 'E-Amateur (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),\n-(55237, 2175, 10773, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n-(55238, 2175, 10881, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n+(55235, 2175, 10773, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n+(55236, 2175, 10881, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n+(55237, 2175, 10882, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n\\ No newline at end of file\n \n-*(2175, 10773, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),\n-*(2175, 10773, 15, 'E-PRO', 'masculino', '1910-01-01', '2008-12-31'),\n-*(2175, 10773, 17, 'Master PRO (35+)', 'masculino', '1910-01-01', '1989-12-31'),\n-*(2175, 10773, 18, 'Damas PRO', 'femenino', '0000-00-00', '0000-00-00'),\n-*(2175, 10773, 19, 'Elite Pro (19-34)', 'masculino', '1970-01-01', '2005-12-01')\n+(55238, 2175, 10773, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n+(55239, 2175, 10881, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n+(55240, 2175, 10882, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n+\n+(55241, 2175, 10773, 12, 'E-Amateur (16+)', 'mixto', '1910-01-01', '2008-12-31'),\n+(55242, 2175, 10881, 12, 'E-Elite', 'mixto', '1910-01-01', '2008-12-31'),\n+(55243, 2175, 10882, 12, 'E-Amateur (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),\n+\n+(55244, 2175, 10773, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n+(55245, 2175, 10881, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n+(55246, 2175, 10882, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n+\n+(55247, 2175, 10773, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),\n+(55248, 2175, 10881, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),\n+(55249, 2175, 10882, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),\n+\n+(55250, 2175, 10773, 15, 'E-PRO', 'masculino', '1910-01-01', '2008-12-31'),\n+(55251, 2175, 10882, 12, 'E-PRO (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),\n+\n+(55252, 2175, 10773, 17, 'Master PRO (35+)', 'masculino', '1910-01-01', '1989-12-31'),\n+(55253, 2175, 10882, 13, 'Master PRO (OSE) / E-Master B (ARG)', 'masculino', '1975-01-01', '1984-12-31'),\n+(55254, 2175, 10882, 14, 'Master PRO (OSE) / E-Master C (ARG)', 'masculino', '1950-01-01', '1974-12-31'),\n+(55255, 2175, 10882, 9, 'Master PRO (OSE) / Master A (ARG)', 'masculino', '1985-01-01', '1989-12-31'),\n+(55256, 2175, 10882, 8, 'Master PRO (OSE) / Master B (ARG)', 'masculino', '1975-01-01', '1984-12-31'),\n+(55257, 2175, 10882, 7, 'Master PRO (OSE) / Master C (ARG)', 'masculino', '1950-01-01', '1974-12-31'),\n+\n+(55258, 2175, 10773, 18, 'Damas PRO', 'femenino', '0000-00-00', '0000-00-00'),\n+(55259, 2175, 10882, 18, 'Damas PRO (OSE) / Damas Elite (ARG)', 'femenino', '0000-00-00', '0000-00-00'),\n+\n+(55260, 2175, 10773, 19, 'Elite Pro (19-34)', 'masculino', '1970-01-01', '2005-12-01'),\n+(55261, 2175, 10882, 19, 'Elite Pro (OSE) / Elite (ARG)', 'masculino', '1970-01-01', '2005-12-01')\n+\n+\n+\n+```\n"}, {"date": 1726614602290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,100 +13,193 @@\n ## AGREGAR A LA MULTICATEGORIA ANTES DEL EVENTO\n \n ```sql\n INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n-    (SELECT 55211, idinscripcion FROM participantes WHERE idcategoria = 55211),\n-    (SELECT 55212, idinscripcion FROM participantes WHERE idcategoria = 55212),\n+    (SELECT 55211, idinscripcion FROM participantes WHERE idcategoria = 55211);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55212, idinscripcion FROM participantes WHERE idcategoria = 55212);\n \n-    (SELECT 55213, idinscripcion FROM participantes WHERE idcategoria = 55213),\n-    (SELECT 55214, idinscripcion FROM participantes WHERE idcategoria = 55214),\n-    (SELECT 55215, idinscripcion FROM participantes WHERE idcategoria = 55215),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55213, idinscripcion FROM participantes WHERE idcategoria = 55213);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55214, idinscripcion FROM participantes WHERE idcategoria = 55214);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55215, idinscripcion FROM participantes WHERE idcategoria = 55215);\n \n-    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55216),\n-    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55217),\n-    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55218),\n-    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55218),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55216);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55217);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55218);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55218);\n \n-    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55219),\n-    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55220),\n-    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55221),\n-    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55221),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55219);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55220);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55221);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55221);\n \n-    (SELECT 55222, idinscripcion FROM participantes WHERE idcategoria = 55222),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55222, idinscripcion FROM participantes WHERE idcategoria = 55222);\n \n-    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55223),\n-    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55224),\n-    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55225),\n-    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55225),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55223);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55224);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55225);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55225);\n \n-    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55226),\n-    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55227),\n-    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55228),\n-    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55228),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55226);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55227);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55228);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55228);\n \n-    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55229),\n-    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55230),\n-    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55231),\n-    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55231),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55229);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55230);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55231);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55231);\n \n-    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55232),\n-    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55233),\n-    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55234),\n-    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55234),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55232);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55233);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55234);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55234);\n \n-    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55235),\n-    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55236),\n-    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55237),\n-    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55238),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55235);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55236);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55237);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55238);\n \n-    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55238),\n-    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55239),\n-    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55240),\n-    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55240),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55238);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55239);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55240);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55240);\n \n-    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55241),\n-    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55242),\n-    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55243),\n-    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55243),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55241);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55242);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55243);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55243);\n \n-    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55244),\n-    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55245),\n-    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55246),\n-    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55246),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55244);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55245);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55246);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55246);\n \n-    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55247),\n-    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55248),\n-    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55249),\n-    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55249),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55247);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55248);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55249);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55249);\n \n-    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55250),\n-    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55251),\n-    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55251),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55250);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55251);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55251);\n \n-    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55252),\n-    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55253),\n-    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55254),\n-    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55255),\n-    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55256),\n-    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55257),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55252);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55253);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55254);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55255);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55256);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55257);\n \n-    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55253),\n-    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55254),\n-    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55255),\n-    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55256),\n-    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55257),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55253);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55254);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55255);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55256);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55257);\n \n-    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55258),\n-    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55259),\n-    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55259),\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55258);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55259);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55259);\n \n-    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55260),\n-    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55261),\n-    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55261)\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55260);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55261);\n+INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n+    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55261);\n \n+UPDATE participantes SET idcategoria = 55213 WHERE idcategoria = 55215;\n+UPDATE participantes SET idcategoria = 55216 WHERE idcategoria = 55218;\n+UPDATE participantes SET idcategoria = 55219 WHERE idcategoria = 55221;\n+UPDATE participantes SET idcategoria = 55223 WHERE idcategoria = 55225;\n+UPDATE participantes SET idcategoria = 55226 WHERE idcategoria = 55228;\n+UPDATE participantes SET idcategoria = 55229 WHERE idcategoria = 55231;\n+UPDATE participantes SET idcategoria = 55232 WHERE idcategoria = 55234;\n+UPDATE participantes SET idcategoria = 55235 WHERE idcategoria = 55237;\n+UPDATE participantes SET idcategoria = 55238 WHERE idcategoria = 55240;\n+UPDATE participantes SET idcategoria = 55241 WHERE idcategoria = 55243;\n+UPDATE participantes SET idcategoria = 55244 WHERE idcategoria = 55246;\n+UPDATE participantes SET idcategoria = 55247 WHERE idcategoria = 55249;\n+UPDATE participantes SET idcategoria = 55250 WHERE idcategoria = 55251;\n+UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55253;\n+UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55254;\n+UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55255;\n+UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55256;\n+UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55257;\n+UPDATE participantes SET idcategoria = 55258 WHERE idcategoria = 55259;\n+UPDATE participantes SET idcategoria = 55260 WHERE idcategoria = 55261;\n \n+```\n \n+## CONFIGURACIÓN DE LAS CATEGORIAS PARA ABRIR INSCRIPCIONES\n \n+```sql\n+\n DELETE FROM categorias WHERE idevento = 2175 AND idcarrera = 10773;\n \n INSERT INTO `categorias` (`idcategoria`, `idevento`, `idcarrera`, `orden`, `nombre`, `sexo`, `nacimiento_desde`, `nacimiento_hasta`) VALUES\n (55211, 2175, 10773, 1, 'Debutantes Men', 'masculino', '1910-01-01', '2008-12-31'),\n@@ -175,9 +268,7 @@\n (55258, 2175, 10773, 18, 'Damas PRO', 'femenino', '0000-00-00', '0000-00-00'),\n (55259, 2175, 10882, 18, 'Damas PRO (OSE) / Damas Elite (ARG)', 'femenino', '0000-00-00', '0000-00-00'),\n \n (55260, 2175, 10773, 19, 'Elite Pro (19-34)', 'masculino', '1970-01-01', '2005-12-01'),\n-(55261, 2175, 10882, 19, 'Elite Pro (OSE) / Elite (ARG)', 'masculino', '1970-01-01', '2005-12-01')\n+(55261, 2175, 10882, 19, 'Elite Pro (OSE) / Elite (ARG)', 'masculino', '1970-01-01', '2005-12-01');\n \n-\n-\n\\ No newline at end of file\n-```\n+```\n"}], "date": 1726612480459, "name": "Commit-0", "content": "idevento: 2175\nidcarreras\nOSE: 10773\nARG: 10881\nOSE+ARG: 10882\n\n## AGREGAR A LA MULTICATEGORIA ANTES DEL EVENTO\n\nINSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES\n    (SELECT 55211, idinscripcion FROM participantes WHERE idcategoria = 55211),\n    (SELECT 55212, idinscripcion FROM participantes WHERE idcategoria = 55212),\n\n    (SELECT 55213, idinscripcion FROM participantes WHERE idcategoria = 55213),\n    (SELECT 55214, idinscripcion FROM participantes WHERE idcategoria = 55214),\n    (SELECT 55215, idinscripcion FROM participantes WHERE idcategoria = 55215),\n\n    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55216),\n    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55217),\n    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55218),\n    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55218),\n\n    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55219),\n    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55220),\n    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55221),\n    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55221),\n\n    (SELECT 55222, idinscripcion FROM participantes WHERE idcategoria = 55222),\n\n    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55223),\n    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55224),\n    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55225),\n    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55225),\n\n    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55226),\n    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55227),\n    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55228),\n    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55228),\n\n    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55229),\n    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55230),\n    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55231),\n    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55231),\n\n\nDELETE FROM categorias WHERE idevento = 2175 AND idcarrera = 10773;\n\nINSERT INTO `categorias` (`idcategoria`, `idevento`, `idcarrera`, `orden`, `nombre`, `sexo`, `nacimiento_desde`, `nacimiento_hasta`) VALUES\n(55211, 2175, 10773, 1, 'Debutantes Men', 'masculino', '1910-01-01', '2008-12-31'),\n(55212, 2175, 10773, 2, 'Debutantes Women', 'femenino', '1910-01-01', '2008-12-31'),\n\n(55213, 2175, 10773, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),\n(55214, 2175, 10881, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),\n(55215, 2175, 10882, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),\n\n(55216, 2175, 10773, 5, 'Damas Amateur', 'femenino', '0000-00-00', '0000-00-00'),\n(55217, 2175, 10881, 18, 'Damas Elite', 'femenino', '0000-00-00', '0000-00-00'),\n(55218, 2175, 10882, 5, 'Damas Amateur (OSE) / Damas Elite (ARG)', 'femenino', '0000-00-00', '0000-00-00'),\n\n(55219, 2175, 10773, 4, 'Amateur Men (19+)', 'masculino', '1910-01-01', '2005-12-31'),\n(55220, 2175, 10881, 19, 'Elite', 'masculino', '1910-01-01', '2005-12-31'),\n(55221, 2175, 10882, 4, 'Amateur Men (OSE) / Elite (ARG)', 'masculino', '1910-01-01', '2005-12-31'),\n\n(55222, 2175, 10773, 3, 'Infantiles Mixto (11-12)', 'mixto', '2012-01-01', '2013-12-31'),\n\n(55223, 2175, 10773, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),\n(55224, 2175, 10881, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),\n(55225, 2175, 10882, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),\n\n(55226, 2175, 10773, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),\n(55227, 2175, 10881, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),\n(55228, 2175, 10882, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),\n\n(55229, 2175, 10773, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n(55230, 2175, 10881, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n(55231, 2175, 10882, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),\n\n\n(55228, 2175, 10773, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n(55229, 2175, 10881, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),\n(55230, 2175, 10773, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n(55231, 2175, 10881, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),\n(55232, 2175, 10773, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n(55233, 2175, 10881, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),\n(55234, 2175, 10773, 12, 'E-Amateur (16+)', 'mixto', '1910-01-01', '2008-12-31'),\n(55235, 2175, 10881, 12, 'E-Elite', 'mixto', '1910-01-01', '2008-12-31'),\n(55236, 2175, 10882, 12, 'E-Amateur (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),\n(55237, 2175, 10773, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n(55238, 2175, 10881, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),\n\n*(2175, 10773, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),\n*(2175, 10773, 15, 'E-PRO', 'masculino', '1910-01-01', '2008-12-31'),\n*(2175, 10773, 17, 'Master PRO (35+)', 'masculino', '1910-01-01', '1989-12-31'),\n*(2175, 10773, 18, 'Damas PRO', 'femenino', '0000-00-00', '0000-00-00'),\n*(2175, 10773, 19, 'Elite Pro (19-34)', 'masculino', '1970-01-01', '2005-12-01')"}]}