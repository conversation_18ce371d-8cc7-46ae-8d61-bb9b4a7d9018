{"sourceFile": "BRAIN/WIKI/Seguridad en HTTP y Apache.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1726428779542, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726428787620, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,9 @@\n WordPress la concha de tu madre\n \n ## Configurar htpasswd\n \n-1- Configurar el virtual host\n+1 - Configurar el virtual host\n \n ```apache\n <Directory \"/var/www/simplementeviviendo/brain\">\n     Options FollowSymLinks MultiViews\n@@ -32,9 +32,9 @@\n     Require valid-user\n </Directory>\n ```\n \n-1- Después crear el archivo de contraseña:\n+2 - <PERSON><PERSON><PERSON> crear el archivo de contraseña:\n \n ```bash\n sudo htpasswd -c /var/www/simplementeviviendo/brain/.htpasswd andresmaiden\n ```\n"}, {"date": 1726428874507, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,4 +37,17 @@\n \n ```bash\n sudo htpasswd -c /var/www/simplementeviviendo/brain/.htpasswd andresmaiden\n ```\n+\n+3 - Agregar el subdominio a Letsencrypt\n+\n+```bash\n+sudo letsencrypt certonly --apache -d simplementeviviendo.com -d brain.simplementeviviendo.com\n+```\n+\n+4 - Comprobar que todo esté bien y reiniciar Apache2\n+\n+```bash\n+sudo apache2ctl configtest\n+sudo systemctl restart apache2\n+```\n\\ No newline at end of file\n"}], "date": 1726428779542, "name": "Commit-0", "content": "a2enmod headers\n\n# Extra Security Headers\n<IfModule mod_headers.c>\n    Header set X-XSS-Protection \"1; mode=block\"\n    Header always append X-Frame-Options SAMEORIGIN\n    Header set X-Content-Type-Options nosniff\n    Header always set Strict-Transport-Security \"max-age=31536000; includeSubDomains\"\n</IfModule>\n\nContent-Security-Policy: frame-ancestors 'none';\n\n\nUPDATE wp_posts\nSET campo = REPLACE(campo, 'https://cronometrajeinstantaneo.com', 'https://wp.cronometrajeinstantaneo.com')\nWHERE campo LIKE '%https://cronometrajeinstantaneo.com%';\nWordPress la concha de tu madre\n\n## Configurar htpasswd\n\n1- Configurar el virtual host\n\n```apache\n<Directory \"/var/www/simplementeviviendo/brain\">\n    Options FollowSymLinks MultiViews\n    AllowOverride All\n    Order allow,deny\n    allow from all\n\tAuthType Basic\n\tAuthName \"Sector privado\"\n    AuthUserFile /var/www/simplementeviviendo/brain/.htpasswd\n    Require valid-user\n</Directory>\n```\n\n1- Despu<PERSON> crear el archivo de contraseña:\n\n```bash\nsudo htpasswd -c /var/www/simplementeviviendo/brain/.htpasswd andresmaiden\n```\n"}]}