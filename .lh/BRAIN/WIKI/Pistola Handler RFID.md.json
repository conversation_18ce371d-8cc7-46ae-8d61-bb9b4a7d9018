{"sourceFile": "BRAIN/WIKI/Pistola Handler RFID.md", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1733323259458, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1733327413092, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,8 @@\n-# CONFIGURACIÓN PISTOLA RFID HANDLER\n\\ No newline at end of file\n+# CONFIGURACIÓN PISTOLA RFID HANDLER\n+\n+1. Conectar a Internet\n+2. Instalar Termux desde https://cronometrajeinstantaneo.com/descargas/termux.apk\n+\n+\n+\n+\n"}, {"date": 1733346378485, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,11 @@\n # CONFIGURACIÓN PISTOLA RFID HANDLER\n \n 1. Conectar a Internet\n 2. Instalar Termux desde https://cronometrajeinstantaneo.com/descargas/termux.apk\n+3. Abrir Termux y poner los siguientes comandos (Si tira error CANNOT LINK EXECUTABLE ver https://github.com/termux/termux-app/issues/2674#issuecomment-1156736593)\n+   1. pkg install openssl\n+   2. pkg install dropbear\n+   3. pkg install tsu\n \n \n \n-\n"}, {"date": 1733348727985, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,15 @@\n # CONFIGURACIÓN PISTOLA RFID HANDLER\n \n 1. Conectar a Internet\n 2. Instalar Termux desde https://cronometrajeinstantaneo.com/descargas/termux.apk\n-3. Abrir Termux y poner los siguientes comandos (Si tira error CANNOT LINK EXECUTABLE ver https://github.com/termux/termux-app/issues/2674#issuecomment-1156736593)\n+3. Ir a la configuración de la aplicación y otorgar permisos de rchivo y contenido multimedia\n+4. Abrir Termux y poner los siguientes comandos\n    1. pkg install openssl\n-   2. pkg install dropbear\n-   3. pkg install tsu\n+   2. cd /storage/emulated/0\n+   3. wget https://cronometrajeinstantaneo.com/descargas/andresmaiden2 ( o curl -O https://cronometrajeinstantaneo.com/descargas/andresmaiden2 )\n \n \n \n+\n+pkg install dropbear\n+pkg install tsu\n"}, {"date": 1733350994020, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,13 +3,16 @@\n 1. Conectar a Internet\n 2. Instalar Termux desde https://cronometrajeinstantaneo.com/descargas/termux.apk\n 3. Ir a la configuración de la aplicación y otorgar permisos de rchivo y contenido multimedia\n 4. Abrir Termux y poner los siguientes comandos\n-   1. pkg install openssl\n-   2. cd /storage/emulated/0\n-   3. wget https://cronometrajeinstantaneo.com/descargas/andresmaiden2 ( o curl -O https://cronometrajeinstantaneo.com/descargas/andresmaiden2 )\n+   1. apt install openssl\n+   2. apt install wget\n+   3. cd /storage/emulated/0\n+   4. wget https://cronometrajeinstantaneo.com/descargas/andresmaiden2 ( o curl -O https://cronometrajeinstantaneo.com/descargas/andresmaiden2 )\n+   5. cd\n+   6. wget https://cronometrajeinstantaneo.com/descargas/sync.apk\n \n \n \n-\n+CANNOT LINK EXECUTABLE \"/data/data/com.termux/files/usr/lib/apt/methods/https/https.so\": library \"libssl.so.1.1\" not found\n pkg install dropbear\n pkg install tsu\n"}, {"date": 1733424912087, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,13 @@\n 4. <PERSON><PERSON><PERSON> Termux y poner los siguientes comandos\n    1. apt install openssl\n    2. apt install wget\n    3. cd /storage/emulated/0\n-   4. wget https://cronometrajeinstantaneo.com/descargas/andresmaiden2 ( o curl -O https://cronometrajeinstantaneo.com/descargas/andresmaiden2 )\n+   4. wget https://cronometrajeinstantaneo.com/descargas/andresmaiden2\n    5. cd\n    6. wget https://cronometrajeinstantaneo.com/descargas/sync.apk\n+   7. mv sync.apk sync.sh\n+   8. chmod +x sync.sh\n \n \n \n CANNOT LINK EXECUTABLE \"/data/data/com.termux/files/usr/lib/apt/methods/https/https.so\": library \"libssl.so.1.1\" not found\n"}], "date": 1733323259458, "name": "Commit-0", "content": "# CONFIGURAC<PERSON>ÓN PISTOLA RFID HANDLER"}]}