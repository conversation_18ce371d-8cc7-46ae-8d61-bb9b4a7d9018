{"sourceFile": "BRAIN/WIKI/GIT.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1742496546451, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1746443789822, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,8 @@\n-***************************************************************************************\n-  GIT\n-***************************************************************************************\n-RECORDAR COMANDOS\n+# COMANDS Y PROCESOS CON GIT\n+\n+## RECORDAR COMANDOS\n+\n git branch --no-merged\n git branch --merged\n git checkout -b funcionalidad_666 origin/funcionalidad_666\n git push origin :funcionalidad_666\n@@ -13,9 +13,11 @@\n git merge --abort\n git branch -a (muestra incluyendo remotos)\n git fetch -p origin (fetch actualizando cache local)\n \n-DEPLOY CON PUSH\n+\n+## DEPLOY CON PUSH\n+\n mkdir beta\n cd beta\n git init --bare\n nano hooks/post-receive\n@@ -26,22 +28,23 @@\n \n git remote add beta ec2-user@18.231.86.55:/home/<USER>/gits/beta\n \n \n-DESARROLLO EN origin\n+## DESARROLLO EN origin\n+\n git checkout desarrollo\n     Switched to branch 'desarrollo'\n git checkout -b 666\n     Crear branch 666 and switched to this branch\n git push origin 666:666\n     Subo a origin la nueva rama\n \n-    --EL PROGRAMADOR DESIGNADO COMMITEA, HACE UN PUSH Y ME AVISA--\n+--EL PROGRAMADOR DESIGNADO COMMITEA, HACE UN PUSH Y ME AVISA--\n \n git fetch origin 666:666\n     Traigo el trabajo realizado\n \n-    --REVISO EL NUEVO CÓDIGO Y MODIFICO LO QUE HAGA FALTA--\n+--REVISO EL NUEVO CÓDIGO Y MODIFICO LO QUE HAGA FALTA--\n \n git checkout desarrollo\n     Switched to branch 'desarrollo'\n git merge --no-ff 666\n@@ -62,9 +65,10 @@\n git push beta beta:master\n     Deploy on beta\n \n \n-DESARROLLO EN VERSIONES\n+## DESARROLLO EN VERSIONES\n+\n git checkout desarrollo\n     Switched to branch 'desarrollo'\n git checkout -b 666\n     Crear branch 666 and switched to this branch\n@@ -108,9 +112,10 @@\n git push beta beta:master\n     Deploy on beta con los errores que estaban en master también\n \n \n-DESARROLLO EN ERRORES\n+## DESARROLLO EN ERRORES\n+\n git checkout master\n     Switched to branch 'master'\n git checkout -b error_666\n     Crear branch error_666 and switched to this branch\n@@ -125,4 +130,15 @@\n git branch -d error_666\n     Deleted branch error_666 (was 05e9557)\n git push origin master\n     Deploy on origin\n+\n+\n+## ROLLBACK O VOLVER ATRÁS\n+\n+Hay 2 formas principales:\n+\n+- `revert` para mantener el historial de commits (lo cual es recomendable si ya has compartido tu trabajo con otros) con `git revert <commit-hash>`\n+- `reset` para eliminar el commit de la historia completamente (esto no se recomienda si ya has compartido tu trabajo con otros), que a su vez tiene estas opciones:\n+  - Modo suave (Mantiene los cambios en el área de staging) `git reset --soft HEAD~1`\n+  - Modo mixto (Deshace el commit y mantiene los cambios en el directorio de trabajo (no en staging)) `git reset --mixed HEAD~1`\n+  - Modo duro (Elimina el commit y todos los cambios asociados del directorio de trabajo) `git reset --hard HEAD~1`\n\\ No newline at end of file\n"}, {"date": 1746444069181, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,11 +134,13 @@\n \n \n ## ROLLBACK O VOLVER ATRÁS\n \n-Hay 2 formas principales:\n+Hay 2 formas principales: revert y reset\n \n - `revert` para mantener el historial de commits (lo cual es recomendable si ya has compartido tu trabajo con otros) con `git revert <commit-hash>`\n-- `reset` para eliminar el commit de la historia completamente (esto no se recomienda si ya has compartido tu trabajo con otros), que a su vez tiene estas opciones:\n+- `reset` para eliminar el commit de la historia completamente (esto no se recomienda si ya has compartido tu trabajo con otros). A su vez tiene estas opciones:\n   - Modo suave (Mantiene los cambios en el área de staging) `git reset --soft HEAD~1`\n\\ No newline at end of file\n   - Modo mixto (Deshace el commit y mantiene los cambios en el directorio de trabajo (no en staging)) `git reset --mixed HEAD~1`\n-  - Modo duro (Elimina el commit y todos los cambios asociados del directorio de trabajo) `git reset --hard HEAD~1`\n+  - Modo duro (Elimina el commit y todos los cambios asociados del directorio de trabajo) `git reset --hard HEAD~1`\n+\n+Para apuntar a un commit se especifica con <commit-hash> y si se quiere ir pasos atrás del HEAD se hace con `HEAD~1` donde 1 es la cantidad de pasos\n\\ No newline at end of file\n"}, {"date": 1753735104074, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,10 +20,9 @@\n mkdir beta\n cd beta\n git init --bare\n nano hooks/post-receive\n-echo \"#!/bin/sh\" >> hooks/post-receive\n-echo \"GIT_WORK_TREE=/saas/customer/services/beta git checkout -f\" >> hooks/post-receive\n+echo \"GIT_WORK_TREE=/saas/services/beta git checkout -f\" >> hooks/post-receive\n chmod 755 hooks/post-receive\n sudo chown -R apache:ec2-user /saas/customer/services/beta\n \n git remote add beta ec2-user@18.231.86.55:/home/<USER>/gits/beta\n@@ -142,5 +141,5 @@\n   - Modo suave (Mantiene los cambios en el área de staging) `git reset --soft HEAD~1`\n   - Modo mixto (Des<PERSON>ce el commit y mantiene los cambios en el directorio de trabajo (no en staging)) `git reset --mixed HEAD~1`\n   - <PERSON><PERSON> duro (Elimina el commit y todos los cambios asociados del directorio de trabajo) `git reset --hard HEAD~1`\n \n-Para apuntar a un commit se especifica con <commit-hash> y si se quiere ir pasos atrás del HEAD se hace con `HEAD~1` donde 1 es la cantidad de pasos\n\\ No newline at end of file\n+Para apuntar a un commit se especifica con <commit-hash> y si se quiere ir pasos atrás del HEAD se hace con `HEAD~1` donde 1 es la cantidad de pasos\n"}, {"date": 1753735756879, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,9 +20,10 @@\n mkdir beta\n cd beta\n git init --bare\n nano hooks/post-receive\n-echo \"GIT_WORK_TREE=/saas/services/beta git checkout -f\" >> hooks/post-receive\n+echo \"#!/bin/sh\" >> hooks/post-receive\n+echo \"GIT_WORK_TREE=/saas/customer/services/beta git checkout -f\" >> hooks/post-receive\n chmod 755 hooks/post-receive\n sudo chown -R apache:ec2-user /saas/customer/services/beta\n \n git remote add beta ec2-user@18.231.86.55:/home/<USER>/gits/beta\n"}], "date": 1742496546450, "name": "Commit-0", "content": "***************************************************************************************\n  GIT\n***************************************************************************************\nRECORDAR COMANDOS\ngit branch --no-merged\ngit branch --merged\ngit checkout -b funcionalidad_666 origin/funcionalidad_666\ngit push origin :funcionalidad_666\ngit reset --hard ORIG_HEAD\ngit restore <filename>\ngit branch -m <oldname> <newname>\ngit stash branch <branchname> [<stash>]\ngit merge --abort\ngit branch -a (muestra incluyendo remotos)\ngit fetch -p origin (fetch actualizando cache local)\n\nDEPLOY CON PUSH\nmkdir beta\ncd beta\ngit init --bare\nnano hooks/post-receive\necho \"#!/bin/sh\" >> hooks/post-receive\necho \"GIT_WORK_TREE=/saas/customer/services/beta git checkout -f\" >> hooks/post-receive\nchmod 755 hooks/post-receive\nsudo chown -R apache:ec2-user /saas/customer/services/beta\n\ngit remote add beta ec2-user@18.231.86.55:/home/<USER>/gits/beta\n\n\nDESARROLLO EN origin\ngit checkout desarrollo\n    Switched to branch 'desarrollo'\ngit checkout -b 666\n    Crear branch 666 and switched to this branch\ngit push origin 666:666\n    Subo a origin la nueva rama\n\n    --EL PROGRAMADOR DESIGNADO COMMITEA, HACE UN PUSH Y ME AVISA--\n\ngit fetch origin 666:666\n    Traigo el trabajo realizado\n\n    --REVISO EL NUEVO CÓDIGO Y MODIFICO LO QUE HAGA FALTA--\n\ngit checkout desarrollo\n    Switched to branch 'desarrollo'\ngit merge --no-ff 666\n    Updating ea1b82a..05e9557\ngit branch -d 666\n    Deleted branch 666 (was 05e9557)\ngit pull origin --delete 2834\n    Deleted branch 666 on origin\ngit push origin desarrollo:master\n    Actualizo rama en origin\n\ngit checkout beta\n    Switched to branch 'beta'\ngit merge --no-ff desarrollo\n    Updating ea1b82a..05e9557\ngit tag -a 1.2-b1\n    Tag a la versión correspondiente\ngit push beta beta:master\n    Deploy on beta\n\n\nDESARROLLO EN VERSIONES\ngit checkout desarrollo\n    Switched to branch 'desarrollo'\ngit checkout -b 666\n    Crear branch 666 and switched to this branch\n\n  --VARIOS DESARROLLOS CON CADA UNO SU COMMIT CORRESPONDIENTE--\n\ngit commit -a \"666 - Se realizó lo siguiente que se puede copiar desde el servicio\"\n    Commit every step you take\n\ngit checkout desarrollo\n    Switched to branch 'desarrollo'\ngit merge --no-ff 666\n    Updating ea1b82a..05e9557\ngit branch -d 666\n    Deleted branch 666 (was 05e9557)\n\ngit checkout beta\n    Switched to branch 'beta'\ngit merge --no-ff desarrollo\n    Updating ea1b82a..05e9557\ngit tag -a 1.2-b1\n    Tag a la versión correspondiente\ngit push beta beta:master\n    Deploy on beta\n\n  --MENSAJES A BETA TESTERS Y PRUEBAS GENERALES--\n\ngit checkout master\n    Switched to branch 'master'\ngit merge --no-ff beta\n    Updating ea1b82a..05e9557\ngit tag -a 1.2\n    Tag a la versión correspondiente\ngit push produccion master\n    Deploy on produccion\n\ngit diff beta\n    Para ver si hay diferencias entre beta y master\ngit checkout beta\ngit merge --no-ff master\ngit push beta beta:master\n    Deploy on beta con los errores que estaban en master también\n\n\nDESARROLLO EN ERRORES\ngit checkout master\n    Switched to branch 'master'\ngit checkout -b error_666\n    Crear branch error_666 and switched to this branch\ngit commit -am \"error_666 - Error que hacía esa cosa mal\"\n    Commit every step you take\ngit checkout master\n    Switched to branch 'master'\ngit merge --no-ff error_666\n    Updating ea1b82a..05e9557\ngit tag -a 1.2.1\n    Tag a la versión correspondiente con el de modificación menor\ngit branch -d error_666\n    Deleted branch error_666 (was 05e9557)\ngit push origin master\n    Deploy on origin\n"}]}