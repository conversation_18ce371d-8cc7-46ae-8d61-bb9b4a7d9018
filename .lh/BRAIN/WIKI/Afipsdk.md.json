{"sourceFile": "BRAIN/WIKI/Afipsdk.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1748898150531, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1748898150531, "name": "Commit-0", "content": "Documentación: Procesamiento de Facturas con AWS Lambda\nDescripción General\n\nEste sistema utiliza AWS Lambda para procesar facturas electrónicas a través de la API de AFIPSDK. El flujo de trabajo es el siguiente:\n\n- La aplicación principal envía un mensaje a una cola SQS con el formato `idempresa|idventa`\n- La función Lambda afipsdk recibe el mensaje y procesa la factura\n- La función Lambda obtiene los datos de la factura de la base de datos\n- La función Lambda envía los datos a AFIP y recibe la respuesta\n- La función Lambda actualiza el estado de la factura en la base de datos\n\nArquitectura\n\n┌─────────────┐    ┌─────────────┐    ┌─────────────┐    ┌─────────────┐\n│  Aplicación │    │  Cola SQS   │    │   Lambda    │    │    AFIP     │\n│  Principal  │───►│ afipsdk-queue│───►│   afipsdk   │───►│    API     │\n└─────────────┘    └─────────────┘    └──────┬──────┘    └─────────────┘\n                                             │\n                                             ▼\n                                      ┌─────────────┐\n                                      │  Base de    │\n                                      │   Datos     │\n                                      └─────────────┘\n\nComponentes\n1. Cola SQS\n\n    Nombre: afipsdk-queue-{stage} (donde {stage} es dev, alfa, beta o prod)\n    URL: https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-{stage}\n    Formato del mensaje: idempresa|idventa (ambos deben ser números enteros positivos)\n\n2. Función Lambda\n\n    Nombre: arca-{stage}-afipsdk\n    Descripción: Procesador de facturas para AFIP\n    Runtime: PHP 8.3\n    Código fuente: services/lambda/arca/public/afipsdk.php\n    Configuración: services/lambda/arca/serverless.yml\n\n3. Base de Datos\n\n    Se utiliza la clase Database de services/lambda/api/lib/Database.php\n    Conecta a la base de datos de la empresa correspondiente\n    Obtiene los datos de la factura y actualiza su estado\n\nFlujo de Procesamiento\n\n    Recepción del mensaje:\n        La función Lambda recibe un mensaje de la cola SQS\n        Valida que el mensaje tenga el formato correcto (idempresa|idventa)\n    Obtención de datos:\n        Conecta a la base de datos de la empresa\n        Obtiene los datos de la venta y sus detalles\n    Preparación de datos para AFIP:\n        Formatea los datos según los requerimientos de la API de AFIP\n        Genera el JSON para la solicitud\n    Envío a AFIP:\n        Envía los datos a la API de AFIP\n        Recibe la respuesta (CAE, resultado, etc.)\n    Actualización de la base de datos:\n        Actualiza el estado de la factura con el resultado de AFIP\n        Guarda el CAE y otros datos relevantes\n\nComandos Principales\nDespliegue\n\n# Navegar al directorio del proyecto\ncd services/lambda/arca\n\n# Desplegar en el entorno de desarrollo\nserverless deploy --stage=dev\n\n# Desplegar en otros entornos\nserverless deploy --stage=alfa\nserverless deploy --stage=beta\nserverless deploy --stage=prod\n\nMonitoreo y Logs\n\n# Ver los logs de la función Lambda\nserverless logs -f afipsdk\n\n# Ver los logs en tiempo real\nserverless logs -f afipsdk -t\n\n# Ver los logs con AWS CLI\naws logs filter-log-events --log-group-name /aws/lambda/arca-dev-afipsdk --limit 20\n\nPruebas\n\n# Enviar un mensaje de prueba a la cola SQS\naws sqs send-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --message-body \"1234|5678\"\n\n# Invocar la función Lambda directamente (para pruebas)\nserverless invoke -f afipsdk --data '{\"Records\":[{\"body\":\"1234|5678\"}]}'\n\n# Probar la función localmente\nserverless bref:local --function afipsdk --data '{\"Records\":[{\"body\":\"1234|5678\"}]}'\n\nGestión de la Cola SQS\n\n# Ver atributos de la cola\naws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names All\n\n# Ver mensajes en la cola\naws sqs receive-message --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --max-number-of-messages 10\n\n# Purgar la cola (eliminar todos los mensajes)\naws sqs purge-queue --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev\n\nCódigo de Ejemplo\nEnviar un mensaje desde PHP\n\nfunction afipsdk_lambda($idempresa, $idventa)\n{\n    try {\n        $sqs = conectar_aws('sqs');\n\n        $params = [\n            'QueueUrl' => AWS_URL_AFIPSDK_QUEUE,\n            'DelaySeconds' => 10,\n            'MessageBody' => \"$idempresa|$idventa\",\n        ];\n\n        $sqs->sendMessage($params);\n\n    } catch (Exception $e) {\n        mostrar_error('No se envió un mensaje con SQS.<br>'\n            .'Error: '.$e->getMessage().'<br>'\n            .'Parametros: '.json_encode($params), true);\n        return false;\n    }\n    return true;\n}\n\nSolución de Problemas\nEl Lambda no procesa los mensajes\n\n    Verifica que la cola SQS tenga mensajes:\n\n    aws sqs get-queue-attributes --queue-url https://sqs.sa-east-1.amazonaws.com/124561084955/afipsdk-queue-dev --attribute-names ApproximateNumberOfMessages\n\n    Verifica que el Lambda tenga los permisos correctos:\n\n    aws lambda get-policy --function-name arca-dev-afipsdk\n\n    Verifica que el mapeo de eventos esté configurado correctamente:\n\n    aws lambda list-event-source-mappings --function-name arca-dev-afipsdk\n\nErrores en el Lambda\n\n    Revisa los logs para ver el error específico:\n\n    serverless logs -f afipsdk\n\n    Verifica que la conexión a la base de datos funcione correctamente\n    Verifica que los datos de la factura sean correctos\n    Verifica que la conexión con AFIP funcione correctamente\n\nPróximos Pasos\n\n    Implementar la integración real con la API de AFIP usando la librería afipsdk/afip.php\n    Agregar manejo de errores más robusto\n    Implementar reintentos para casos de fallo\n    Agregar notificaciones para errores críticos\n    Implementar monitoreo y alertas\n\nReferencias\n\n    Documentación de AWS Lambda\n    Documentación de AWS SQS\n    Documentación de Bref\n    Documentación de AFIP"}]}