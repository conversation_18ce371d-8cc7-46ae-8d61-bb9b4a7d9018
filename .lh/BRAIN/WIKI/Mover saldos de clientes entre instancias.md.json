{"sourceFile": "BRAIN/WIKI/Mover saldos de clientes entre instancias.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1734442557037, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1734443012485, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,17 @@\n Mover saldos de clientes desde 5548 a 12447\n \n-- [x] Correr el script de saldos\n-- [ ] Duplicar los clientes comparando por ids\n-- [ ] Generar un tipo de venta Saldo positivo\n+# Correr el script de saldos\n+\n+./command.php prod script saldos.php idempresas 5548 clientes\n+\n+# Duplicar los clientes comparando por ids\n+\n+# Generar un tipo de venta Saldo positivo\n+\n+```mysql\n+INSERT INTO categorias_ventas (idcomportamiento, nombre, letra, puntodeventa, discrimina, muevesaldo, operacioninversa) VALUES\n+    (115, 'Saldo Anterior', 'C', 1, 'C', 1, 0),\n+    (116, 'Saldo <PERSON> a favor', 'C', 1, 'C', 1, 1);\n+```\n - [ ] Generar el saldo\n \n"}, {"date": 1734449322575, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,71 @@\n Mover saldos de clientes desde 5548 a 12447\n \n-# Correr el script de saldos\n+1. Correr el script de saldos\n \n+```bash\n ./command.php prod script saldos.php idempresas 5548 clientes\n+```\n \n-# Duplicar los clientes comparando por ids\n+2. Duplicar los clientes comparando por ids\n \n-# Generar un tipo de venta Saldo positivo\n+3. Generar un tipo de venta Saldo positivo (el idtipoventa lo pongo a mano)\n \n ```mysql\n-INSERT INTO categorias_ventas (idcomportamiento, nombre, letra, puntodeventa, discrimina, muevesaldo, operacioninversa) VALUES\n-    (115, '<PERSON>do Anterior', 'C', 1, 'C', 1, 0),\n-    (116, '<PERSON><PERSON> a favor', 'C', 1, 'C', 1, 1);\n+INSERT INTO categorias_ventas (idtipoventa, idcomportamiento, nombre, letra, puntodeventa, discrimina, muevesaldo, operacioninversa) VALUES\n+    (21, 115, 'Saldo Anterior', 'C', 1, 'C', 1, 0),\n+    (22, 116, 'Saldo Anterior a favor', 'C', 1, 'C', 1, 1);\n ```\n-- [ ] Generar el saldo\n+4. Generar un archivo de saldos positivos y otro de saldos negativos\n \n+```mysql\n+SET @correlativo = 12;\n+SELECT @correlativo := @correlativo + 1 AS  idventa, idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion;\n+```\n+5. Generar las queries para agregar las ventas copiando y pegando las values en cada insert into\n+\n+```mysql\n+SET @numero = 0;\n+SET @correlativo = 12;\n+SET @idtipoventa = 21;\n+\n+SELECT CONCAT(\"(\",\n+    @correlativo := @correlativo + 1, \", \",\n+    @idtipoventa, \", \",\n+    @numero := @numero + 1, \", \",\n+    \"NOW()\", \", \",\n+    idrelacion, \", \",\n+    saldo, \", \", saldo, \", \", saldo, \", \",\n+    \"'cerrado', 0, 99, 1, 0, 'Generado automáticamente para migrar saldo anterior'),\") AS insert_value\n+FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n+\n+INSERT INTO (idventa, idtipoventa, numero, fecha, idcliente, subtotal, nogravado, total, estado, idtipoiva, tipodoc, muevesaldo, operacioninversa, observacion) VALUES\n+```\n+\n+```mysql\n+SET @correlativo = 12;\n+\n+SELECT CONCAT(\"(\",\n+    @correlativo := @correlativo + 1, \", \",\n+    saldo, \", \", saldo, \", \",\n+    \"1, 'Saldo anterior'),\") AS insert_value\n+FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n+\n+INSERT INTO productosxventas (idventa, precio, preciofinal, cantidad, nombre) VALUES\n+```\n+\n+```mysql\n+SET @numero = 0;\n+SET @correlativo = 12;\n+SET @idtipoventa = 21;\n+\n+SELECT CONCAT(\"(\",\n+    @idtipoventa, \", \",\n+    idrelacion, \", \",\n+    @correlativo := @correlativo + 1, \", \",\n+    \"NOW()\", \", \",\n+    saldo, \", \",\n+    \"R00001-0000\", @numero := @numero + 1, \"),\") AS insert_value\n+FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n+\n+INSERT INTO ventasxclientes (idtipoventa, idcliente, id, fecha, total, numero) VALUES\n+```\n"}, {"date": 1734471800529, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,9 +37,9 @@\n     saldo, \", \", saldo, \", \", saldo, \", \",\n     \"'cerrado', 0, 99, 1, 0, 'Generado automáticamente para migrar saldo anterior'),\") AS insert_value\n FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n \n-INSERT INTO (idventa, idtipoventa, numero, fecha, idcliente, subtotal, nogravado, total, estado, idtipoiva, tipodoc, muevesaldo, operacioninversa, observacion) VALUES\n+INSERT INTO ventas (idventa, idtipoventa, numero, fecha, idcliente, subtotal, nogravado, total, estado, idtipoiva, tipodoc, m<PERSON><PERSON><PERSON>, operacioninversa, observacion) VALUES\n ```\n \n ```mysql\n SET @correlativo = 12;\n@@ -68,4 +68,12 @@\n FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n \n INSERT INTO ventasxclientes (idtipoventa, idcliente, id, fecha, total, numero) VALUES\n ```\n+\n+6. Hacer lo mismo para los clientes con saldo negativo\n+\n+7. Correr los saldos en la instancia destino\n+\n+```bash\n+./command.php prod script saldos.php idempresas 12447 clientes\n+```\n"}, {"date": 1734472119563, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -63,9 +63,9 @@\n     idrelacion, \", \",\n     @correlativo := @correlativo + 1, \", \",\n     \"NOW()\", \", \",\n     saldo, \", \",\n-    \"R00001-0000\", @numero := @numero + 1, \"),\") AS insert_value\n+    \"'R00001-0000\", @numero := @numero + 1, \"'),\") AS insert_value\n FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;\n \n INSERT INTO ventasxclientes (idtipoventa, idcliente, id, fecha, total, numero) VALUES\n ```\n@@ -76,4 +76,10 @@\n \n ```bash\n ./command.php prod script saldos.php idempresas 12447 clientes\n ```\n+8. Actualizar los últimos números en categorias_ventas y deshabilito\n+\n+```mysql\n+UPDATE categorias_ventas SET estado = 0, ultimonumero = 313 WHERE idtipoventa = 21;\n+UPDATE categorias_ventas SET estado = 0, ultimonumero = 511 WHERE idtipoventa = 22;\n+```\n\\ No newline at end of file\n"}], "date": 1734442557037, "name": "Commit-0", "content": "Mover saldos de clientes desde 5548 a 12447\n\n- [x] Co<PERSON> el script de saldos\n- [ ] Duplicar los clientes comparando por ids\n- [ ] Generar un tipo de venta Saldo positivo\n- [ ] Generar el saldo\n\n"}]}