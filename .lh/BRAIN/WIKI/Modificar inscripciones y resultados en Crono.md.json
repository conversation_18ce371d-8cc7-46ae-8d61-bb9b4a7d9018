{"sourceFile": "BRAIN/WIKI/Modificar inscripciones y resultados en Crono.md", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1725717168452, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726072197781, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -351,5 +351,18 @@\n \r\n .head-info {\r\n   display: none;\r\n }\r\n+```\r\n+\r\n+## INTERCAMBIAR COLUMNAS DE LUGAR\r\n+\r\n+```js\r\n+$('#informe_participantes table tr').each(function() {\r\n+  var $row = $(this);\r\n+  var $cell1 = $row.find('td:eq(8), th:eq(8)');\r\n+  var $cell2 = $row.find('td:eq(9), th:eq(9)');\r\n+  var temp = $cell1.html();\r\n+  $cell1.html($cell2.html());\r\n+  $cell2.html(temp);\r\n+});\r\n ```\n\\ No newline at end of file\n"}, {"date": 1729219659956, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -310,14 +310,13 @@\n \r\n ```js\r\n const apellido1 = document.querySelector('input[name=\"apellido1\"]');\r\n const apellido2 = document.querySelector('input[name=\"apellido2\"]');\r\n-const apellido3 = document.querySelector('input[name=\"apellido3\"]');\r\n const nombre = document.querySelector('input[name=\"nombre\"]');\r\n \r\n-[apellido1, apellido2, apellido3].forEach((apellido) => {\r\n+[apellido1, apellido2].forEach((apellido) => {\r\n   apellido.addEventListener('change', () => {\r\n-    nombre.value = [apellido1.value, apellido2.value, apellido3.value].filter(Boolean).join(', ');\r\n+    nombre.value = [apellido1.value, apellido2.value].filter(Boolean).map(name => name.toUpperCase()).join(' / ');\r\n   });\r\n });\r\n ```\r\n \r\n"}, {"date": 1733629363617, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -131,9 +131,13 @@\n \r\n NO OFICILA:\r\n .no-oficial { display: block; }\r\n \r\n+FONDO CON DEGRADE GRADIENTE:\r\n+    background: linear-gradient(30deg, #000024, #000010);\r\n \r\n+\r\n+\r\n RESULTADOS CON FOTO DE FONDO\r\n body {\r\n     font-family: \"Days One\",sans-serif,\"google\";\r\n     font-size: 14px;\r\n"}, {"date": 1737749997182, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -141,9 +141,9 @@\n body {\r\n     font-family: \"Days One\",sans-serif,\"google\";\r\n     font-size: 14px;\r\n     background-color: white;\r\n-    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/1172/imagen_1.png);\r\n+    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/2397/imagen_1.png);\r\n     background-size: cover;\r\n     background-attachment: fixed;\r\n     background-position: 0 0;\r\n     background-position: center;\r\n@@ -164,10 +164,8 @@\n tr { border-radius: 10px;}\r\n \r\n \r\n \r\n-\r\n-\r\n RESULTADOS SIN FONDO Y COLORES SEGÚN PALETA:\r\n body { background: #FFFFFF; }\r\n header, footer { display:none; }\r\n \r\n"}], "date": 1725717168452, "name": "Commit-0", "content": "CAMBIO DE PALETA DE COLORES DE INSCRIPCIONES:\r\nheader { background-color: #B29513; }\r\nh2, label { color: black; }\r\nform, .form {}\r\ninput[type=submit] { background-color: #B29513; color: #FFFFFF; }\r\nfooter { background-color:#B29513; color: #FFFFFF; }\r\n\r\n\r\nINSCRIPCIONES CON FOTO Y TRANSPARENTE:\r\nbody {\r\n    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/567/imagen_2.jpeg);\r\n    background-attachment: fixed;\r\n    background-size: contain;\r\n    background-position: center;\r\n}\r\nform, .form { padding: 25px; background: white; border-radius: 25px; opacity: 0.9; }\r\nheader { background-color: #008CDE; }\r\nlabel { color: black }\r\nh2 {color: #008CDE; }\r\ninput[type=submit] { background-color: #008CDE; color: #FFFFFF; }\r\nfooter { background-color:#008CDE; color: #FFFFFF; }\r\n\r\n\r\nNUEVO BOTÓN DE MP:\r\nNo hace falta cargar un script desde el sistema, sino copiar así\r\n<p>Pagar 10k: <script src=\"https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js\"\r\ndata-preference-id=\"432553423-ed2921a0-046b-4449-aa45-1b307f9c3fb4\" data-source=\"button\"></script></p>\r\n<p>Pagar 6k Iniciales: <script src=\"https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js\"\r\ndata-preference-id=\"432553423-e030e6ae-2801-45da-bb80-07299a55d56e\" data-source=\"button\"></script></p>\r\n<p>Pagar 6k Avanzados: <script src=\"https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js\"\r\ndata-preference-id=\"432553423-dac490ec-ef82-4769-bb08-44cf57374da0\" data-source=\"button\"></script></p>\r\n\r\nhttps://mpago.la/2swCzDg\r\n\r\n\r\n\r\nBOTÓN DE PAGO ECUADOR:\r\nhttps://docs.livepayphone.com/knowledge-base/express-checkout/\r\n\r\n\r\nDESABILITAR CATEGORIA O CARRERA:\r\n$(function() { $(\"label[for=1225]\").append(\"<font color=red> CUPO COMPLETO</font>\"); $(\"input[value=1225]\").prop(\"disabled\", true); });\r\n\r\n\r\nCAMBIOS DE TEXTOS:\r\nwindow.addEventListener(\"load\", function () {\r\ndocument.body.innerHTML = document.body.innerHTML.replace(/Mostrar todas/g, 'Clasificación General');\r\n});\r\n\r\nwindow.addEventListener(\"load\", function () {\r\n    var contenedor_participantes = document.getElementById(\"informe_participantes\");\r\n    if (contenedor_participantes != null) {\r\n        contenedor_participantes.innerHTML = contenedor_participantes.innerHTML.replace(/Categoría/g, 'Cat.');\r\n        contenedor_participantes.innerHTML = contenedor_participantes.innerHTML.replace(/ - /g, '<br>');\r\n    }\r\n});\r\n\r\n// Para filtros\r\nfunction resultados_js()\r\n{\r\n    var contenedor_resultados = document.getElementById(\"contenedor_resultados\");\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');\r\n    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/ - /g, '<br>');\r\n}\r\n\r\n\r\n\r\nOCULTAR GENERALES EN FILTROS\r\nwindow.addEventListener(\"load\", function () {\r\n$(\".tipo_generales\").hide();\r\nsetTimeout(() => {\r\nselect_etapa('6060');\r\nselect_tipo('categorias');\r\n}, 500);\r\n});\r\n\r\n\r\nAGREGAR DISCIPLINA A DUPLA / POSTA:\r\n$(function() {\r\n$(\".datos h2\")[1].append(' Corredor');\r\n$(\".datos h2\")[2].append(' Ciclista');\r\n$(\".datos\").eq(1).find(\"h2\").html('Datos del PILOTO');\r\n});\r\n\r\nOCULTAR FILTRO DE GENERALES (ÚTIL PARA DH)\r\nwindow.addEventListener(\"load\", function () {\r\n$(\".tipo_generales\").hide();\r\nselect_tipo('categorias');\r\nselect_etapa('6059');\r\n});\r\n\r\nELIMINAR / OCULTAR CUARTO PARTICIPANTE EN POSTAS\r\nCon CSS\r\nhr:nth-of-type(5), .datos:nth-of-type(5).datos {\r\n  display: none;\r\n}\r\nCon JS\r\n$(function() {\r\n$(\".datos\").eq(4).hide();\r\n$(\"hr\").eq(4).hide();\r\ndocument.body.innerHTML = document.body.innerHTML.replace(/(5K Open)/g, '');\r\ndocument.body.innerHTML = document.body.innerHTML.replace(/(5K Equipo)/g, '');\r\ndocument.body.innerHTML = document.body.innerHTML.replace(/()/g, '');\r\n});\r\n\r\n\r\n\r\nOCULTAR\r\n$(function() {\r\n$(function() {  });\r\n});\r\n$(\"label[for=reglamento] label[for=NO]\").hide();\r\n$(\"label[for=reglamento] input[name=reglamento]\").hide();\r\n\r\n\r\nOCULTAR SEXO:\r\n$(function() { $(\"label[for=sexo]\").hide(); });\r\n\r\nOCULTAR CATEGORIA:\r\n$(function() { $(\"label[for=idcategoria]\").hide(); });\r\n\r\nOCULTAR CARRERA O PARTICIPA EN Y SELECCIONAR PRIMERO:\r\n// Carrera sola\r\n$(function() { $(\"label[for=idcarrera]\").hide(); });\r\n// Una carrera de varias\r\n$(function() { $(\"label[for=idcarrera]\").hide(); $(\"input[value=1305]\").prop('checked', true); });\r\n\r\nNO OFICILA:\r\n.no-oficial { display: block; }\r\n\r\n\r\nRESULTADOS CON FOTO DE FONDO\r\nbody {\r\n    font-family: \"Days One\",sans-serif,\"google\";\r\n    font-size: 14px;\r\n    background-color: white;\r\n    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/1172/imagen_1.png);\r\n    background-size: cover;\r\n    background-attachment: fixed;\r\n    background-position: 0 0;\r\n    background-position: center;\r\n    background-repeat: repeat-y;\r\n}\r\n\r\nheader {\r\n    background: none;\r\n    width: 100%;\r\n}\r\n\r\nhr { border: #002D55; }\r\n.head1, .head2 { color: #002D55; }\r\n.tabla_interna { border: #53C1FC; padding: 20px; }\r\n.tabla_interna tbody tr:nth-child(even) td { background: #EEEEEE; color: #000}\r\n.tabla_interna thead { background: #002D55; }\r\n.tabla_interna tbody tr:hover td, .tabla_interna tbody td { background-color: #53C1FC; color: #000; }\r\ntr { border-radius: 10px;}\r\n\r\n\r\n\r\n\r\n\r\nRESULTADOS SIN FONDO Y COLORES SEGÚN PALETA:\r\nbody { background: #FFFFFF; }\r\nheader, footer { display:none; }\r\n\r\nhr { border: #002D55; }\r\n.head1, .head2 { color: #002D55; }\r\n.tabla_interna { border: #53C1FC}\r\n.tabla_interna tbody tr:nth-child(even) td { background: #EEEEEE; }\r\n.tabla_interna tbody tr:nth-child(odd) td { }\r\n.tabla_interna thead { background: #002D55; }\r\n.tabla_interna tbody tr:hover td, .tabla_interna tbody td { background-color: #53C1FC; color: #ffffff; }\r\n\r\n\r\nRESULTADOS CON SPONSORS A TODO LO ANCHO\r\n.sponsors img { width: 100%; }\r\n\r\nOCULTAR / ESCONDER SEXO Y LOCALIDAD EN GENERALES:\r\n#informe_generales .tabla_interna th:nth-child(2),\r\n#informe_generales .tabla_interna td:nth-child(2),\r\n#informe_generales .tabla_interna th:nth-child(7),\r\n#informe_generales .tabla_interna td:nth-child(7) {\r\n    display: none;\r\n}\r\n\r\nMÍNIMO WIDTH EN NOMBRE:\r\n#informe_generales .tabla_interna th:nth-child(3) {\r\n    min-width: 200px;\r\n}\r\n\r\n\r\nMEDIA QUERYS:\r\n /* Extra small devices (phones, 600px and down) */\r\n@media only screen and (max-width: 600px) {...}\r\n\r\n/* Small devices (portrait tablets and large phones, 600px and up) */\r\n@media only screen and (min-width: 600px) {...}\r\n\r\n/* Medium devices (landscape tablets, 768px and up) */\r\n@media only screen and (min-width: 768px) {...}\r\n\r\n/* Large devices (laptops/desktops, 992px and up) */\r\n@media only screen and (min-width: 992px) {...}\r\n\r\n/* Extra large devices (large laptops and desktops, 1200px and up) */\r\n@media only screen and (min-width: 1200px) {...}\r\n\r\n\r\nTICKET:\r\n#informe_ticket {\r\n    background-image: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/465/imagen_5.jpeg);\r\n    background-attachment: fixed;\r\n    background-size: cover;\r\n}\r\n\r\n.ticket_logo img { width: 50%; }\r\n\r\n.ticket_content, .ticket_etapas { background: rgb(255, 255, 255, 0.5); }\r\n\r\n.ticket_etapas { display: block; }\r\n\r\n.contenedor_ticket .tiempo_nro,\r\n.contenedor_ticket .pos_general_nro,\r\n.contenedor_ticket .pos_sexo_nro,\r\n.contenedor_ticket .pos_categoria_nro,\r\n.ticket_etapas .pena_nro {\r\n    color: #ED6B23;\r\n}\r\n\r\n.pos_sexo { display: none; }\r\n\r\n.contenedor_filtros .filtros a {\r\n    font-family: \"PT Sans Narrow\",sans-serif,\"google\";\r\n    font-weight: normal;\r\n    font-style: normal;\r\n    color: #03a9f4;\r\n    background-color: white;\r\n    border-radius: 6px;\r\n    text-transform: uppercase;\r\n}\r\n\r\n.filtro_seleccionado {\r\n    background-color: #026E9F !important;\r\n    color: white !important;\r\n}\r\n\r\n.no-oficial { display: block; }\r\n\r\n## STREAMING VIVO META\r\n\r\n```css\r\n.chroma { background: #000 !important; }\r\n.meta, .podio, .vivo {\r\n  width: 800px;\r\n  max-height: 515px;\r\n  overflow: hidden;\r\n}\r\n\r\n.vueltas {\r\n  display: none;\r\n}\r\n\r\ntable.podio {\r\n  background-color: white;\r\n  color: #0D6777;\r\n  font-size: 25px;\r\n}\r\n\r\nthead tr, tbody tr {\r\n  height: 50px;\r\n}\r\n\r\nthead.bg-naranja, thead.bg-azul {\r\n  background-color: #577149;\r\n  font-size: 25px;\r\n}\r\n\r\ntd.puesto-destacado, table.podio tr:nth-child(2n+1) td {\r\n  background: none;\r\n}\r\n```\r\n\r\n## DISEÑOS LINDOS\r\n\r\nhttps://cronometrajeinstantaneo.com/resultados/carrera-84-aniversario-de-la-creacion-de-gendarmeria-nacional\r\nhttps://cronometrajeinstantaneo.com/resultados/valle-de-los-perdidos-trail/generales\r\n\r\n## ELIMINO CARRERAS SIN CUPO\r\n```js\r\n$(function() {\r\n    $('label:contains(\"SIN CUPO\")').each(function() {\r\n        if ($(this).attr('for') != 'idcarrera')\r\n            $(this).remove();\r\n    });\r\n    $('input[name=\"idcarrera\"]').each(function() {\r\n        $(this).next('br').remove();\r\n        if ($(this).attr('disabled'))\r\n            $(this).remove();\r\n    });\r\n```\r\n\r\n## FORMAR NOMBRE DE EQUIPO CON APELLIDOS\r\n```css\r\nlabel[for=equipo] { visibility: hidden }\r\n```\r\n\r\n```js\r\nconst apellido1 = document.querySelector('input[name=\"apellido1\"]');\r\nconst apellido2 = document.querySelector('input[name=\"apellido2\"]');\r\nconst apellido3 = document.querySelector('input[name=\"apellido3\"]');\r\nconst nombre = document.querySelector('input[name=\"nombre\"]');\r\n\r\n[apellido1, apellido2, apellido3].forEach((apellido) => {\r\n  apellido.addEventListener('change', () => {\r\n    nombre.value = [apellido1.value, apellido2.value, apellido3.value].filter(Boolean).join(', ');\r\n  });\r\n});\r\n```\r\n\r\n## RESULTADOS CON UNA IMAGEN EN TODO EL FONDO\r\n\r\n```js\r\n$(function() {\r\n$('header .head-logo img').attr('src', 'https://storage.googleapis.com/cronometrajeinstantaneo-eventos/2026/imagen_1.jpg');\r\n});\r\n```\r\n\r\n```css\r\nheader {\r\n  display: block;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.head-logo {\r\n  display: flex;\r\n  align-items: center;\r\n  width: 100%;\r\n  padding: 0;\r\n  margin: 0;\r\n}\r\n\r\n.head-logo img {\r\n  width: 100%;\r\n  margin: 0;\r\n}\r\n\r\n.head-info {\r\n  display: none;\r\n}\r\n```"}]}