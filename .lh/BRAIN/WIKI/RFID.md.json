{"sourceFile": "BRAIN/WIKI/RFID.md", "activeCommit": 0, "commits": [{"activePatchIndex": 15, "patches": [{"date": 1730041703335, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1730042757116, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -148,9 +148,9 @@\n Items:\r\n 1 Reader/Writer USB Desktop\r\n 1 Reader Invelion YR8900 (8 ports)\r\n 1 Reader Invelion YR8700 (4 ports)\r\n-4 Antennas Invelión YR9028 (9 dbi)\r\n+4 Antennas Invelion YR9028 (9 dbi)\r\n 4 Antenna Cables (6m, 5m, 4m, 3m)\r\n 500 RFID Disposable Tags (Encoded with the same EPC)\r\n \r\n Name: <PERSON><PERSON>\r\n"}, {"date": 1733063507004, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -123,8 +123,26 @@\n - Modelo del reader de Gaby: Invelion YR8700 8 channel UHF RFID reader\r\n - Modelo del reader de Esquel: Invelion YR8600 4 channel UHF RFID reader\r\n - Modelo de las antenas de Esquel: Invelion- YR9028 (Frequency: 860-960 mhz / Gain: 9.2dBi / VSWR: 1.3)\r\n \r\n+## RECOMENDACIONES DE RUFUS\r\n+\r\n+🏃🏃🏃 Puntos de Cronometraje de Alta Densidad: líneas de inicio y meta para eventos de gran tamaño.\r\n+\r\n+- Zebra FX9600: Diseñado para entornos de alta densidad, el FX9600 es ideal para líneas de salida y meta con grandes multitudes. Con su diseño robusto y alta tasa de lectura, es perfecto para puntos de control en carreras concurridas. Disponible en modelos de 4 y 8 puertos de antena.\r\n+- Impinj R420: El R420 ofrece una solución potente -y más costosa- para eventos con altos volúmenes de participantes, con 4 puertos de antena y tasas de lectura altas, incluso en entornos con ruido o interferencia de RF. \r\n+\r\n+🏃🏃 Puntos de Cronometraje de Densidad Media: puntos de control intermedios o carreras de tamaño medio.\r\n+\r\n+- Zebra FX9500: Un clásico confiable para lecturas en puntos de control en eventos de tamaño medio, equilibrando rendimiento y precio. Es un modelo descontinuado, pero aún se encuentra disponible en el mercado a precios accesibles.\r\n+- Chainway UR4: Una opción económica y versátil, ideal para carreras con volúmenes moderados de participantes. Cuenta con 4 puertos de antena y utiliza el chip RFID Impinj E710, siendo resistente a interferencias electromagnéticas y eficiente en la dispersión de calor.\r\n+ \r\n+🏃 Puntos de Cronometraje de Baja Densidad: carreras de menor tamaño.\r\n+\r\n+- Impinj R220: Eficiente y confiable para eventos pequeños, el R220 gestiona menos tags, pero ofrece lecturas precisas con sus 2 puertos de antena y protocolos anti interferencias.\r\n+- Chainway UR4: Versátil y económico, perfecto para puntos de control de baja densidad sin comprometer la fiabilidad, con una tasa de lectura de más de 900 tags/seg.\r\n+\r\n+\r\n ### CABLES\r\n \r\n Here are the two connectors for the standard antennas and RFID readers:\r\n - RP-TNC male (for reader)\r\n"}, {"date": 1733153095887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -160,8 +160,23 @@\n \r\n \r\n ### Para pedir en China\r\n \r\n+Voy a necesitar que me pases estos datos para que te coticen en China:\r\n+\r\n+Name:\r\n+Email:\r\n+Phone:\r\n+TAX ID:\r\n+Address:\r\n+City:\r\n+Postal Code:\r\n+State:\r\n+Country:\r\n+Pay by: Paypal and Credit Card\r\n+\r\n+\r\n+\r\n Hi <PERSON>, I have a customer from Chile that wants to buy the following:\r\n \r\n Items:\r\n 1 Reader/Writer USB Desktop\r\n"}, {"date": 1733253757381, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -173,10 +173,8 @@\n State:\r\n Country:\r\n Pay by: Paypal and Credit Card\r\n \r\n-\r\n-\r\n Hi <PERSON>, I have a customer from Chile that wants to buy the following:\r\n \r\n Items:\r\n 1 Reader/Writer USB Desktop\r\n"}, {"date": 1737641281338, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -158,9 +158,9 @@\n - De 90%: 11m\r\n - De 80%: 12m, 10m, 8m, 6m\r\n \r\n \r\n-### Para pedir en China\r\n+### PEDIDOS A CHINA\r\n \r\n Voy a necesitar que me pases estos datos para que te coticen en China:\r\n \r\n Name:\r\n@@ -183,8 +183,10 @@\n 4 Antennas Invelion YR9028 (9 dbi)\r\n 4 Antenna Cables (6m, 5m, 4m, 3m)\r\n 500 RFID Disposable Tags (Encoded with the same EPC)\r\n \r\n+### DIRECCIONES DE CLIENTES\r\n+\r\n Name: <PERSON><PERSON> Mi<PERSON>k\r\n Email: <EMAIL>\r\n Phone: +54 ************\r\n TAX ID (CUIT): 20-18787460-3\r\n@@ -193,5 +195,5 @@\n Postal Code: 4010000\r\n State: Neuquén\r\n Country: Argentina\r\n Pay by: Paypal and Credit Card\r\n-Items: 2 Reader 8 ports with Android (YR9910)\r\n+\r\n"}, {"date": 1737643845131, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -182,8 +182,9 @@\n 1 Reader Invelion YR8700 (4 ports)\r\n 4 Antennas Invelion YR9028 (9 dbi)\r\n 4 Antenna Cables (6m, 5m, 4m, 3m)\r\n 500 RFID Disposable Tags (Encoded with the same EPC)\r\n+500 RFID Reusable Tags\r\n \r\n ### DIRECCIONES DE CLIENTES\r\n \r\n Name: <PERSON><PERSON>\r\n@@ -196,4 +197,24 @@\n State: Neuquén\r\n Country: Argentina\r\n Pay by: Paypal and Credit Card\r\n \r\n+Name: <PERSON><PERSON>r <PERSON>\r\n+Email: <EMAIL>\r\n+Phone: +54 ************\r\n+TAX ID (RUT): 18.197.365-k\r\n+Address: El Valle 141\r\n+City: Cajón de Vilcun\r\n+Postal Code: 4010000\r\n+State: Temuco\r\n+Country: Chile\r\n+Pay by: Paypal\r\n+\r\n+Name: <PERSON>\r\n+TAX ID (RUT): 12748315\r\n+Phone: +57 3215460235\r\n+Email: <EMAIL>\r\n+Address: Calle 12 N. 39-42 Mariluz 1\r\n+City: Pasto\r\n+Postal Code: 520001\r\n+State: Departamento Nariño\r\n+Country: Colombia\r\n"}, {"date": 1737643902767, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -182,9 +182,9 @@\n 1 Reader Invelion YR8700 (4 ports)\r\n 4 Antennas Invelion YR9028 (9 dbi)\r\n 4 Antenna Cables (6m, 5m, 4m, 3m)\r\n 500 RFID Disposable Tags (Encoded with the same EPC)\r\n-500 RFID Reusable Tags\r\n+500 RFID Reusable Ankle Tags with Straps\r\n \r\n ### DIRECCIONES DE CLIENTES\r\n \r\n Name: <PERSON><PERSON>\r\n"}, {"date": 1738758178955, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -123,10 +123,17 @@\n - Modelo del reader de Gaby: Invelion YR8700 8 channel UHF RFID reader\r\n - Modelo del reader de Esquel: Invelion YR8600 4 channel UHF RFID reader\r\n - Modelo de las antenas de Esquel: Invelion- YR9028 (Frequency: 860-960 mhz / Gain: 9.2dBi / VSWR: 1.3)\r\n \r\n-## RECOMENDACIONES DE RUFUS\r\n+## TIPOS DE CHIPS\r\n \r\n+- M750 dogbones\r\n+- RP6 chips\r\n+- NXP U9\r\n+\r\n+\r\n+## RECOMENDACIONES DE EQUIPOS RUFUS\r\n+\r\n 🏃🏃🏃 Puntos de Cronometraje de Alta Densidad: líneas de inicio y meta para eventos de gran tamaño.\r\n \r\n - Zebra FX9600: Diseñado para entornos de alta densidad, el FX9600 es ideal para líneas de salida y meta con grandes multitudes. Con su diseño robusto y alta tasa de lectura, es perfecto para puntos de control en carreras concurridas. Disponible en modelos de 4 y 8 puertos de antena.\r\n - Impinj R420: El R420 ofrece una solución potente -y más costosa- para eventos con altos volúmenes de participantes, con 4 puertos de antena y tasas de lectura altas, incluso en entornos con ruido o interferencia de RF. \r\n@@ -184,8 +191,9 @@\n 4 Antenna Cables (6m, 5m, 4m, 3m)\r\n 500 RFID Disposable Tags (Encoded with the same EPC)\r\n 500 RFID Reusable Ankle Tags with Straps\r\n \r\n+\r\n ### DIRECCIONES DE CLIENTES\r\n \r\n Name: Andres Misiak\r\n Email: <EMAIL>\r\n"}, {"date": 1739297738215, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -82,8 +82,12 @@\n EJEMPLO DE COMO LLEGA CON PC 14:\r\n RA00B0189201400156803E95DD1\r\n A0 0B 01 89 58  14 00  15 68 03 E9 55 A1\r\n \r\n+EPSs comprados en 2025: 000000000000000000000400\r\n+EPSs comprados recuperables: E2801191A5030060ACB9F57D\r\n+\r\n+\r\n ********************************************************************************\r\n   NOTA PARA EL BLOG\r\n ********************************************************************************\r\n \r\n"}, {"date": 1739970238669, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,9 +133,13 @@\n - M750 dogbones\r\n - RP6 chips\r\n - NXP U9\r\n \r\n+*Recuperables*\r\n+- Blue HuTag XC-2 USD2.50 each \r\n+- Blue HuTag XC-3 USD3.50 each\r\n \r\n+\r\n ## RECOMENDACIONES DE EQUIPOS RUFUS\r\n \r\n 🏃🏃🏃 Puntos de Cronometraje de Alta Densidad: líneas de inicio y meta para eventos de gran tamaño.\r\n \r\n"}, {"date": 1750816849519, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,9 +134,9 @@\n - RP6 chips\r\n - NXP U9\r\n \r\n *Recuperables*\r\n-- Blue HuTag XC-2 USD2.50 each \r\n+- Blue HuTag XC-2 USD2.50 each\r\n - Blue HuTag XC-3 USD3.50 each\r\n \r\n \r\n ## RECOMENDACIONES DE EQUIPOS RUFUS\r\n@@ -213,8 +213,18 @@\n State: Neuquén\r\n Country: Argentina\r\n Pay by: Paypal and Credit Card\r\n \r\n+Name: <PERSON><PERSON>\r\n+Email: <EMAIL>\r\n+Phone: +54 ************\r\n+TAX ID (CUIT): 27-31240532-1\r\n+Address: <PERSON><PERSON> 820\r\n+City: Neuquén Capital\r\n+Postal Code: 8300\r\n+State: Neuquén\r\n+Country: Argentina\r\n+\r\n Name: <PERSON><PERSON><PERSON>\r\n Email: and<PERSON><PERSON><EMAIL>\r\n Phone: +54 ************\r\n TAX ID (RUT): 18.197.365-k\r\n"}, {"date": 1750817677168, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -208,9 +208,9 @@\n Phone: +54 ************\r\n TAX ID (CUIT): 20-18787460-3\r\n Address: Conrado <PERSON> 820\r\n City: Neuquén Capital\r\n-Postal Code: 4010000\r\n+Postal Code: 8300\r\n State: Neuquén\r\n Country: Argentina\r\n Pay by: Paypal and Credit Card\r\n \r\n"}, {"date": 1752258888437, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -175,8 +175,14 @@\n \r\n \r\n ### PEDIDOS A CHINA\r\n \r\n+Paypal para transferir\r\n+\r\n+sd77 to <EMAIL>\r\n+usd730 to <EMAIL>\r\n+\r\n+\r\n Voy a necesitar que me pases estos datos para que te coticen en China:\r\n \r\n Name:\r\n Email:\r\n"}, {"date": 1754854305289, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,8 +133,10 @@\n - M750 dogbones\r\n - RP6 chips\r\n - NXP U9\r\n \r\n+- We upgraded chips to the new ones from Feibot (U9 core but they also have M830 based chips) are amazing\r\n+\r\n *Recuperables*\r\n - Blue HuTag XC-2 USD2.50 each\r\n - Blue HuTag XC-3 USD3.50 each\r\n \r\n"}, {"date": 1754854862539, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -134,8 +134,9 @@\n - RP6 chips\r\n - NXP U9\r\n \r\n - We upgraded chips to the new ones from Feibot (U9 core but they also have M830 based chips) are amazing\r\n+- The M700 chips were twice as sensitive as the R6 and the M830/Ux9 are close to twice as sensitive as those\r\n \r\n *Recuperables*\r\n - Blue HuTag XC-2 USD2.50 each\r\n - Blue HuTag XC-3 USD3.50 each\r\n"}], "date": 1730041703335, "name": "Commit-0", "content": "********************************************************************************\r\n  DESARROLLO\r\n********************************************************************************\r\nIdeas para próxima antena:\r\n  - Buen seguimiento de los chips por idorganizacion\r\n  - Agregar tipo de lectura en la tabla lecturas, para hacer los porcentajes de lecturas de chips y jugar con esas estadísticas\r\n  - Falta tags en los equipos\r\n  - Para hacer el sistema multi-tag, tengo que meter un bucle en parti_manual y agregar más inputs\r\n  - Probar distintos tipos de lectura según buffer\r\n  - Mostrar el código del tag en lugar del EPC cuando lo tengamos\r\n  - Botón para prender o apagar la lectura\r\n  - Agregar una barra de monitoreo\r\n    - Copiar info desde apps chinas\r\n    - Cantidad de lecturas por antena\r\n    - Temperatura de la antena\r\n    - Firmware de la antena\r\n    - Lecturas por segundo y esos valores\r\n    - Cantidad de chips no reconocidos como participantes\r\n  - Agregar configuraciones posibles:\r\n    - Radio spectrum specification\r\n    - Output power\r\n    - Cantidad de antenas (auto-detección)\r\n    - Tiempo de rechazo de tags\r\n  - Ventana de auto-consulta para entregar chips, con envío de ticket digital por mail\r\n  - Falta una forma de revisar que chips fueron devueltos\r\n\r\n\r\n********************************************************************************\r\n  COMUNICACIÓN HEXA\r\n********************************************************************************\r\n- Reset reader: A0 03 01 70 EC / No responde\r\n- Beep apagado: A0 04 01 7A 00 E1 y responde A0 04 01 7A 10 D1\r\n- Beep por inventory A0 04 01 7A 01 E0 y responde A0 04 01 7A 10 D1\r\n- Beep por detección A0 04 01 7A 02 DF y responde A0 04 01 7A 10 D1\r\n- Get RF Output Power A0 03 01 77 E5 y responde A0 04 01 77 1E C6 para 30 dBm / A0 04 01 77 1C C8 para 28 dBm / A0 04 01 77 01 E3 para 1 dBm / A0 04 01 77 00 E3 para 0 dBm / A0 04 01 76 3C A9 a 60 dBm\r\n- Set RF Output Power a 30 dBm A0 04 01 76 1E C7 y responde A0 04 01 76 10 D5\r\n- Set RF Output Power a 29 dBm A0 04 01 76 1D C8 y responde A0 04 01 76 10 D5\r\n- Set RF Output Power a 28 dBm A0 04 01 76 1C C9 y responde A0 04 01 76 10 D5\r\n- Set RF Output Power a 1 dBm A0 04 01 76 01 E4 y responde A0 04 01 76 10 D5\r\n- Se puede setear RF Output Power hasta 33 dBm como máximo con A0 04 01 76 21 E4 y con los siguientes códigos para set\r\nA0 04 01 76 01 E4\r\nA0 04 01 76 02 E3\r\nA0 04 01 76 03 E2\r\nA0 04 01 76 04 E1\r\nA0 04 01 76 05 E0\r\nA0 04 01 76 06 DF\r\nA0 04 01 76 07 DE\r\nA0 04 01 76 08 DD\r\nA0 04 01 76 09 DC\r\nA0 04 01 76 0A DB\r\nA0 04 01 76 0B DA\r\nA0 04 01 76 0C D9\r\nA0 04 01 76 0D D8\r\nA0 04 01 76 0E D7\r\nA0 04 01 76 0F D6\r\nA0 04 01 76 10 D5\r\nA0 04 01 76 11 D4\r\nA0 04 01 76 12 D3\r\nA0 04 01 76 13 D2\r\nA0 04 01 76 14 D1\r\nA0 04 01 76 15 D0\r\nA0 04 01 76 16 CF\r\nA0 04 01 76 17 CE\r\nA0 04 01 76 18 CD\r\nA0 04 01 76 19 CC\r\nA0 04 01 76 1A CB\r\nA0 04 01 76 1B CA\r\nA0 04 01 76 1C C9\r\nA0 04 01 76 1D C8\r\nA0 04 01 76 1E C7\r\nA0 04 01 76 1F C6\r\nA0 04 01 76 20 C5\r\nA0 04 01 76 21 C4\r\n\r\nEJEMPLOS DE COMO LLEGAN EPCS:\r\nA0 13 01 89 9C (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 93 -EPC) 38 67 (-88dBm / Carrier frecuency 922)\r\nA0 13 01 89 9C (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 83 -EPC) 52 5D (-48dBm / Carrier frecuency 918)\r\nA0 13 01 89 B8 (30 00 -PC) (E2 80 68 90 00 00 00 00 A0 6F 1D 79 -EPC) 37 A5 (-79dBm / Carrier frecuency 926)\r\nA0 13 01 89 B8 (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 83 -EPC) 4A 49 (-62dBm / Carrier frecuency 926)\r\nA0 13 01 89 B8 (34 00 -PC) (E2 80 11 70 00 00 02 0C 21 9E 11 93 -EPC) 38 4B (-67dBm / Carrier frecuency 918)\r\n\r\nEJEMPLO DE COMO LLEGA CON PC 14:\r\nRA00B0189201400156803E95DD1\r\nA0 0B 01 89 58  14 00  15 68 03 E9 55 A1\r\n\r\n********************************************************************************\r\n  NOTA PARA EL BLOG\r\n********************************************************************************\r\n\r\n## DIFERENCIAS ENTRE REALTIME Y FAST SWITCH\r\n\r\n- En Realtime el reader lee una sola antena a la vez, entrega todo lo leído al software y recién ahí pasa a la próxima antena. Esta opción es recomendable cuando los participantes pasan más lentos y/o hay mucha cantidad de participantes pasando por el mismo puesto de lectura.\r\n- En Fast Switch el reader lee una antena y pasa a la siguiente, mientras un segundo CPU dentro del reader va enviando todo lo leído sin parar. Esta opción es recomendable cuando son pocos participantes y/o pasan muy rápido por el puesto de lectura.\r\n- Los readers Chafón no tienen Fast Switch, solo Realtime, por lo que ambas opciones son iguales en estos readers.\r\n\r\n## Agregar videos\r\n\r\nEn algunos eventos es mayor la necesidad de automatizar el cronometraje y para eso no hay nada mejor que usar chips. Delegamos la toma de tiempos a la conocida tecnología RFID (*** COMPLETAR NOMBRE Y ENLACE A WIKIPEDIA). Para eso contamos con un *reader* (lector encargado de enviar, recibir y procesar las señales), las antenas (que las hay de varios tipos y formas), los tags (comunmente llamados chips en el mundo del cronometraje) y el software o programa para gestionar las lecturas. La aplicación de Cronometraje Instantaneo para Windows es completamente compatible con esta tecnología.\r\n\r\nUna de las principales ventajas de utilizar cronometraje con tecnología RFID es la posibilidad de tomar los tiempos de varios participantes pasando al mismo tiempo por nuestro punto de control, algo que se torna difícil cuando se cronometra con cualquier otra tecnología. Otra de las ventajas es la comodidad durante el evento, ya que el trabajo del cronometrador es un poco más relajado con la ayuda de sus chips.\r\n\r\nPero necesitamos conocer bien esta tecnología antes de saber si es la adecuada para nuestro evento. Esta tecnología fue inventada *** BUSCAR HISTORIA Y REFERENCIAS *** y es ampliamente usada en las industria de la logística, la seguridad *** BUSCAR EJEMPLOS ***.\r\n\r\n*** CHECKEAR TODO ESTE PÁRRAFO *** Técnicamente el reader genera una señal de radio frecuencia y la envía por las antenas. Los chips reciben la señal y devuelven\r\n\r\nTipos de chips: pasivos y activos\r\n\r\n---CONTRAS---\r\n- Porcentaje de lecturas: depende del tipo de antenas y su ubicación (más info abajo)\r\n- Costos (más que utilizar la app o tocoelula)\r\n- Tiempo de instalación\r\n- Entrega y devolución de chips\r\n\r\n\r\n- Tipos de Readers y antenas\r\n- Crono es compatible con genéricas (es el único sistema abierto de latinoamerica)\r\n- Donde comprar (ver para tu país específico o en China)\r\n\r\n- Compatible con cualquier marca de antena y cualquier marca de chips (único sistema totalmente abierto de latinoamerica)\r\n- Nombre del driver: Silicon Labs CP210x USB to UART Bridge\r\n- Para usar USB tener prendidas las llaves 7 y 8. Para red las 3 y 4.\r\n- La IP de las antenas es IP address: ************* / Net mask: ************* / Port number: 4001\r\n- Modelo del reader de Gaby: Invelion YR8700 8 channel UHF RFID reader\r\n- Modelo del reader de Esquel: Invelion YR8600 4 channel UHF RFID reader\r\n- Modelo de las antenas de Esquel: Invelion- YR9028 (Frequency: 860-960 mhz / Gain: 9.2dBi / VSWR: 1.3)\r\n\r\n### CABLES\r\n\r\nHere are the two connectors for the standard antennas and RFID readers:\r\n- RP-TNC male (for reader)\r\n- N-male (for antenna)\r\n\r\nMake sure to purchase the right connector size for your cable type (LMR 195/LMR 240/LMR 400). They are different for each size of cable. Here are the standard cables. The larger the cable the lower the losses to the antenna.\r\n- LMR 195 11.1 dB loss per 100 ft @900 mHz (this is the cheaper, thin and flexible cable but has the highest losses.)\r\n- LMR 240 7.6 dB loss per 100 ft @ 900 mHz\r\n- LMR 400 3.9 dB loss per 100 ft @ 900 mHz (this is the thick, expensive cable that has the lowest losses.)\r\n\r\nTengo los siguientes:\r\n- De 100%: 2m, 6m, 6m\r\n- De 90%: 11m\r\n- De 80%: 12m, 10m, 8m, 6m\r\n\r\n\r\n### Para pedir en China\r\n\r\nHi Lisa, I have a customer from Chile that wants to buy the following:\r\n\r\nItems:\r\n1 Reader/Writer USB Desktop\r\n1 Reader Invelion YR8900 (8 ports)\r\n1 Reader Invelion YR8700 (4 ports)\r\n4 Antennas Invelión YR9028 (9 dbi)\r\n4 Antenna Cables (6m, 5m, 4m, 3m)\r\n500 RFID Disposable Tags (Encoded with the same EPC)\r\n\r\nName: Andres Misiak\r\nEmail: <EMAIL>\r\nPhone: +54 ************\r\nTAX ID (CUIT): 20-18787460-3\r\nAddress: Conrado Villegas 820\r\nCity: Neuquén Capital\r\nPostal Code: 4010000\r\nState: Neuquén\r\nCountry: Argentina\r\nPay by: Paypal and Credit Card\r\nItems: 2 Reader 8 ports with Android (YR9910)\r\n"}]}