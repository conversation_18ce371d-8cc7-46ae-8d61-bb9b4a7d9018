{"sourceFile": "BRAIN/WIKI/Wordpress.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1727808486242, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1728945408367, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,11 +26,9 @@\n echo \"define('FS_METHOD', 'direct');\" >> wp-config.php\n \n 1. Crear base de datos y usuario\n CREATE DATABASE travesiadeloscerros;\n-GRANT ALL PRIVILEGES ON travesiadeloscerros.* TO 'travesiadeloscerros'@'localhost' IDENTIFIED BY '68282BC%502da5d9673470c6455b072';\n-FVB8VpZSjMCCataKXCYYXqgqC4q6KtQz\n-\n+GRANT ALL PRIVILEGES ON travesiadeloscerros.* TO 'travesiadeloscerros'@'localhost' IDENTIFIED BY 'FVB-8VpZSjMCCataKXC';\n FLUSH PRIVILEGES;\n \n 1. <PERSON><PERSON>ar dominio temporal o definitivo\n 2. Entrar al sitio, seguir el wizard y configurar usuarios\n"}], "date": 1727808486242, "name": "Commit-0", "content": "\n## INSTALAR WORDPRESS EN SERVER CRONO\n\n1. Instalar Wordpress\n\nsudo su\ncd /var/www/\nmkdir travesiadeloscerros\ncd travesiadeloscerros\nmkdir public\nwget https://es-ar.wordpress.org/latest-es_AR.tar.gz\ntar -zxvf latest-es_AR.tar.gz\nmv wordpress wp\nmkdir wp/wp-content/upgrade\nmkdir wp/wp-content/uploads\nchown -R andresmaiden:www-data wp\nchmod -R 775 wp/wp-content/plugins\nchmod -R 775 wp/wp-content/themes\nchmod -R 775 wp/wp-content/upgrade\nchmod -R 775 wp/wp-content/uploads\nchmod -R 775 wp/wp-content/languages\ncd wp\nhtpasswd -c .htpasswd travesiadeloscerros\nmv wp-config{-sample,}.php\nsed -i 's/put your unique phrase here/frase secreta de travesiadeloscerros/g' wp-config.php\necho \"define('FS_METHOD', 'direct');\" >> wp-config.php\n\n1. Crear base de datos y usuario\nCREATE DATABASE travesiadeloscerros;\nGRANT ALL PRIVILEGES ON travesiadeloscerros.* TO 'travesiadeloscerros'@'localhost' IDENTIFIED BY '68282BC%502da5d9673470c6455b072';\nFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz\n\nFLUSH PRIVILEGES;\n\n1. Delegar dominio temporal o definitivo\n2. Entrar al sitio, seguir el wizard y configurar usuarios\n3. Configurar apache y letsencrypt\n    Instalar WP FREE SSL y activar\n    Generar Certificado\n    Copiar y pegar el contenido del Certificate y el Private key en el Panel del hosting en Ferozo\n    Anotarme mensaje antes de que venza\n\n\n\n\n## TUTORIALES ÚTILES:\nCómo igualar la altura de las imágenes de los productos: https://ayudawp.com/igualar-altura-imagenes-productos/\nCómo limitar el número de pedidos por día, hora o lo que sea: https://ayudawp.com/como-limitar-el-numero-de-pedidos-por-dia-hora-o-lo-que-sea-semanawoocommerce/\nCómo hacer obligatorios u opcionales los campos al finalizar compra en WooCommerce: https://ayudawp.com/campos-obligatorios-opcionales-finalizar-compra-woocommerce/\nhttps://ayudawp.com/redirection/\nWooCommerce: Cómo mostrar productos comprados juntos habitualmente: https://ayudawp.com/productos-comprados-juntos-habitualmente-woocommerce/\nCómo configurar y asignar pagos a varias cuentas de PayPal en WooCommerce: https://ayudawp.com/varias-cuentas-paypal-woocommerce/\nConvierte automáticamente tus entradas de blog en cursos online de Sensei LMS: https://ayudawp.com/convertir-entradas-cursos/\nCómo compensar el desplazamiento a anclajes en el tema Astra si tienes cabecera fija: https://ayudawp.com/como-compensar-el-desplazamiento-a-anclajes-en-el-tema-astra-si-tienes-cabecera-fija/\n\n## CAMBIOS DE CÓDIGO:\n\n// Cambiar h2 por h3 en los widgets de Astra para mejorar SEO\n// Pie de página Widget 1 add_filter( 'astra_advanced_footer_widget_1_args', 'widget_title_footer_1_tag', 10, 1 ); function widget_title_footer_1_tag( $atts ) { $atts['before_title'] = '<h3 class=\"widget-title\">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 2 add_filter( 'astra_advanced_footer_widget_2_args', 'widget_title_footer_2_tag', 10, 1 ); function widget_title_footer_2_tag( $atts ) { $atts['before_title'] = '<h3 class=\"widget-title\">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 3 add_filter( 'astra_advanced_footer_widget_3_args', 'widget_title_footer_3_tag', 10, 1 ); function widget_title_footer_3_tag( $atts ) { $atts['before_title'] = '<h3 class=\"widget-title\">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 4 add_filter( 'astra_advanced_footer_widget_4_args', 'widget_title_footer_4_tag', 10, 1 ); function widget_title_footer_4_tag( $atts ) { $atts['before_title'] = '<h3 class=\"widget-title\">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 5 add_filter( 'astra_advanced_footer_widget_5_args', 'widget_title_footer_5_tag', 10, 1 ); function widget_title_footer_5_tag( $atts ) { $atts['before_title'] = '<h3 class=\"widget-title\">'; $atts['after_title'] = '</h3>'; return $atts; }\n\n\n\n\n### ORDENAR\n\n- La combinación perfecta de plugins SEO gratuitos para un Wordpress: https://ayudawp.com/plugins-seo-woocommerce\n---\n\ndefine('WP_HOME', 'https://misuperweb.com');\ndefine('WP_SITEURL', 'https://misuperweb.com/wordpress');\n\nGRANT ALL PRIVILEGES ON simplementeviviendo.* TO 'simplementeviviendo'@'localhost' IDENTIFIED BY '827e9DEA1a3caadb@287678fae%e1e9fd8$';\nFLUSH PRIVILEGES;\n\n827e9DEA1a3caadb@287678fae%e1e9fd8$\n\nFVB8VpZSjMCCataKXCYYXqgqC4q6KtQz\n\nandresmaiden | 2wqTY$N@CJWsMnD3ya\n\nCompré plantilla en [https://themeforest.net/downloads](https://themeforest.net/downloads) / [https://preview.themeforest.net/item/blacksilver-photography-theme-for-wordpress/full_screen_preview/23717875?_ga=2.23205362.995292608.1584328036-818213767.1578006226](https://preview.themeforest.net/item/blacksilver-photography-theme-for-wordpress/full_screen_preview/23717875?_ga=2.23205362.995292608.1584328036-818213767.1578006226)\n\nEmpresa de India que ofrece trabajos de Wordpress a U$D11 la hora: [https://wayinfotechsolutions.com/](https://wayinfotechsolutions.com/)\n\n# PROCESOS\n\n## REVISAR ERROR\nEl error aparece como Ha habido un error crítico en este sitio\n\ndefine( 'WP_DEBUG', true );\ndefine( 'WP_DEBUG_DISPLAY', true );\ndefine( 'WP_DEBUG_LOG', true );\n\nMás info en: https://kinsta.com/es/base-de-conocimiento/ha-habido-un-error-critico-en-su-sitio-web/\n\n### INSTALAR WOOCOMMERCE DESDE WP-CLI\n\n---\n\n1. Instalar desde WP-CLI\ncurl -O [https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar](https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar)\nchmod +x wp-cli.phar\nsudo mv wp-cli.phar /usr/local/bin/wp\nwp core download\nwp core config --dbname=mydbname --dbuser=mydbuser --dbpass=mydbpass --dbhost=localhost --dbprefix=whebfubwef_ --extra-php <<PHP\ndefine( 'WP_DEBUG', true );\ndefine( 'WP_DEBUG_LOG', true );\nPHP\nwp db create\nwp core install --url=http://siteurl.com --title=SiteTitle --admin_user=username --admin_password=mypassword [--admin_email=<EMAIL>](mailto:--admin_email=<EMAIL>)\n\nwp option update home '[http://example.com](http://example.com/)'\nwp option update siteurl '[http://example.com](http://example.com/)'\nwp search-replace oldstring newstring\n\nwp plugin list\nwp theme install twentyseventeen --activate\nwp plugin install advanced-custom-fields jetpack ninja-forms --activate\n\n### INSTALAR WOOCOMMERCE DESDE SSH\n\n---\n\n1. Instalar Wordpress\nmkdir tienda & cd tienda\nwget [https://es-ar.wordpress.org/latest-es_AR.tar.gz](https://es-ar.wordpress.org/latest-es_AR.tar.gz)\ntar -zxvf latest-es_AR.tar.gz\nmv wordpress public\nsudo mkdir public/wp-content/upgrade\nsudo mkdir public/wp-content/uploads\nsudo chown -R andresmaiden:www-data public\nsudo chmod -R 775 public/wp-content/plugins\nsudo chmod -R 775 public/wp-content/themes\nsudo chmod -R 775 public/wp-content/upgrade\nsudo chmod -R 775 public/wp-content/uploads\nsudo chmod -R 775 public/wp-content/languages\ncd public\n\n    mv wp-config{-sample,}.php\n    echo \"define('FS_METHOD', 'direct');\" >> wp-config.php\n\n2. Crear base de datos y usuario\nCREATE DATABASE wp1;\nGRANT ALL PRIVILEGES ON wp1.* TO 'wp1'@'localhost' IDENTIFIED BY 'B48OqXTx5Y$%3PGosIyIUeQGeCB3ek2D';\nFLUSH PRIVILEGES;\n3. Entrar al sitio, seguir el wizard y configurar usuarios\n4. Configurar apache y letsencrypt\n    Instalar WP FREE SSL y activar\n    Generar Certificado\n    Copiar y pegar el contenido del Certificate y el Private key en el Panel del hosting en Ferozo\n    Anotarme mensaje antes de que venza\n\nPASAR WP-CRON A UNIX-CRON:\n\n- [https://spinupwp.com/doc/understanding-wp-cron/](https://spinupwp.com/doc/understanding-wp-cron/)\n- [https://ayudawp.com/wp-cron/](https://ayudawp.com/wp-cron/)\n- define( 'DISABLE_WP_CRON', true );\n- 0 0 * * * wget --delete-after http://YOUR_SITE_URL/wp-cron.php\n\n## PLUGINS NECESARIOS\n\n---\n\n1. Ver tema templates, diseño, otros plugins, etc.\n2. Para Tiendas:\n    - wooCommerce\n    - MercadoPago\n    - Jetpack\n    - LiteSpeed\n    - WP Mail SMTP by WPForms\n    - Revisar si hacen falta más: [https://blog.hubspot.com/website/must-have-wordpress-plugins](https://blog.hubspot.com/website/must-have-wordpress-plugins) y [https://www.wpbeginner.com/showcase/24-must-have-wordpress-plugins-for-business-websites/](https://www.wpbeginner.com/showcase/24-must-have-wordpress-plugins-for-business-websites/)\n    - Carritos abandonados: [https://es.wordpress.org/plugins/woo-cart-abandonment-recovery/](https://es.wordpress.org/plugins/woo-cart-abandonment-recovery/)\n3. MKT\n    - Yoast SEO o Rank Math SEO\n    - Akismet Anti-spam\n    - Broken Link Checker\n    - AMP for WP Accelerated Mobile Pages\n    - Google Analytics\n    - Pixel de Facebook\n    - Framework SEO\n    - [https://yoast.com/wordpress/plugins/yoast-woocommerce-seo/](https://yoast.com/wordpress/plugins/yoast-woocommerce-seo/)\n4. Mejorar velocidad y cache\n    - [https://deliciousbrains.com/wp-offload-media/](https://deliciousbrains.com/wp-offload-media/)\n    - Plugins para cache por órden de profesionalidad:\n        1. WP Super Cache (obligatorio mínimo, es de Automatic y perfecto para static webs)\n        2. Smush (comprimir imágenes)\n        3. W3 Total cache\n        4. WPRocket\n        5. Batcache\n        6. WP Fastest Cache\n5. Ver salud del sitio, seguridad y configurar sistema de BackUp\n    - All In One WordPress Security and Firewall Plugin (creo que es el mejor)\n    - Wordfence Security o iThemes Security\n    - BackWPUp\n\n### IMPORTACIONES\n\nCategorías:\n\n- Dejar sólo las columnas SKU y Categorías\n- REEMPLAZAR , POR .\n- Revisar que las categorías no tengan comas y que existan exactamente igual\n- Agregar en la segunda línea los nombres de las cat generales\nCONSTRUCCIÓN HÚMEDA\tCONSTRUCCIÓN STEEL FRAMING\tCONSTRUCCIÓN EN MADERA\tHORMIGÓN ELABORADO\tTECHOS\tFERRETERIA. HERRAMIENTAS Y MAQUINARIAS\n- Concatenar cat con:\n=SI(ESBLANCO(C3);\"\";CONCAT(C$2;\" > \";C3;\", \"))\n=CONCAT(I3;J3;K3;L3;M3;N3)\n=SI(LARGO(O3)<3;\"\";REEMPLAZAR(O3;LARGO(O3)-1;2;\"\"))\n- Copiar y pegar sólo texto (borrar columnas usadas temporalmente)\n- Exportar como csv (separado por ;) y listo\n\nImágenes\n\n- Subir imágenes por FTP (especificar bien carpeta, permisos y extensión .jpg)\n- Listar imágenes desde consola y acomodarlas (sólo se puede un SKU por línea separando imágenes con coma ,)\n[https://nodomateriales.com/fotos/11SUPE01_1.jpg](https://nodomateriales.com/fotos/11SUPE01_1.jpg)\n\n# PLUGINS\n\n---\n\n### PLUGINS PUNTUALES PROBADOS\n\n- Instalación automatizada con WP Quick Install: [https://www.wpkube.com/automate-wordpress-installs-setup](https://www.wpkube.com/automate-wordpress-installs-setup)\n- Plugin para duplicar wordpress: [https://www.wpkube.com/move-backup-website-wordpress-duplicator-plugin](https://www.wpkube.com/move-backup-website-wordpress-duplicator-plugin)\n- Easy Google Fonts: [https://www.getdrip.com/deliveries/4w89v6brbhim8mwjbfvi](https://www.getdrip.com/deliveries/4w89v6brbhim8mwjbfvi) [https://wordpress.org/plugins/easy-google-fonts](https://wordpress.org/plugins/easy-google-fonts)\n- Para sincronizar con ML se pueden usar estos plugins: [https://www.woosync.com.ar/](https://www.woosync.com.ar/) y [https://woomelly.com/](https://woomelly.com/)\n- Para traducir templates o plugins: Loco Translate (Mover a carpeta languages/loco antes de traducir)\n- Ordenar productos en woocommerce: [https://wordpress.org/support/topic/impossible-sort-by-sku/](https://wordpress.org/support/topic/impossible-sort-by-sku/)\n- Migrar WP a otro server MIGRATE GURU ES LO MEJOR https://wordpress.org/support/plugin/migrate-guru/\n- Cambiar el dominio: Better Search Replace\n- Para agregar más formas de pago: [https://wpfactory.com/item/custom-payment-gateways-woocommerce/](https://wpfactory.com/item/custom-payment-gateways-woocommerce/)\n\n    Se podría programar con este tutorial: [https://www.skyverge.com/blog/how-to-create-a-simple-woocommerce-payment-gateway/](https://www.skyverge.com/blog/how-to-create-a-simple-woocommerce-payment-gateway/) y este [https://stackoverflow.com/questions/17081483/custom-payment-method-in-woocommerce/37631908](https://stackoverflow.com/questions/17081483/custom-payment-method-in-woocommerce/37631908)\n\n- Para agregar intereses y descuentos por forma de pago: [https://es-ar.wordpress.org/plugins/woocommerce-pay-for-payment/](https://es-ar.wordpress.org/plugins/woocommerce-pay-for-payment/)\n- Para reservas y turnos: [https://ayudawp.com/plugin-reservas-woocommerce/](https://ayudawp.com/plugin-reservas-woocommerce/)\n\n### PLUGINS PARA PROBAR\n\n[https://quadlayers.com/portfolio/woocommerce-direct-checkout](https://quadlayers.com/portfolio/woocommerce-direct-checkout)\n\n[https://wordpress.org/plugins/woocommerce-checkout-manager/](https://wordpress.org/plugins/woocommerce-checkout-manager/)\n\n[https://elementor.com/blog/wordpress-security-plugins/](https://elementor.com/blog/wordpress-security-plugins/)\n\n[https://updraftplus.com/updraftcentral/](https://updraftplus.com/updraftcentral/)\n\n[https://wp-rocket.me/](https://wp-rocket.me/)\n\n[https://premmerce.com/](https://premmerce.com/)\n\nListado de shortcodes [https://es.wordpress.org/plugins/shortcodes-ultimate/](https://es.wordpress.org/plugins/shortcodes-ultimate/)\n\nAdapta RGPD (para páginas con accesos desde Europa)\n\nTable of Contents Plus\n\nManageWp (para manejar varios WPs desde un solo lugar)\n\nWordfence Central (Para manejar seguridad de varios WP [https://www.wordfence.com/try-central/](https://www.wordfence.com/try-central/))\n\nCookie Law Info (para mostrar el cartelito de cookie)\n\nNota sobre Schemas [https://ayudawp.com/desactivar-schema-astra/](https://ayudawp.com/desactivar-schema-astra/)\n\nSchemas [https://es.wordpress.org/plugins/schema-and-structured-data-for-wp/](https://es.wordpress.org/plugins/schema-and-structured-data-for-wp/)\n\nInstragra Feed: [https://spotlightwp.com/](https://spotlightwp.com/)\n\n# MEJORAS\n\n---\n\n### APRENDER E IMPLEMENTAR\n\n### TUTORIALES PARA LEER\n\n- Medición de conversiones: [https://www.wpbeginner.com/beginners-guide/wordpress-conversion-tracking-made-simple-a-step-by-step-guide/](https://www.wpbeginner.com/beginners-guide/wordpress-conversion-tracking-made-simple-a-step-by-step-guide/)\n- Cambiar página Mi Cuenta: [https://ayudawp.com/personalizar-mi-cuenta-woocommerce/](https://ayudawp.com/personalizar-mi-cuenta-woocommerce/)\n- Mejorar Wishlist: [https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/](https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/)\n- Queues: [https://wpshout.com/quick-guides/use-wp_enqueue_script-include-javascript-wordpress-site/](https://wpshout.com/quick-guides/use-wp_enqueue_script-include-javascript-wordpress-site/)\nadd_action('wp_enqueue_scripts', 'qg_enqueue');\nfunction qg_enqueue() {\nwp_enqueue_script(\n'qgjs',\nplugin_dir_url(**FILE**).'quick-guide.js'\n);\n}\n\n### RENOVAR EN WP ENCRPYT\nEstá explicado en Letsencrypt.txt\n\n\n### IMPORTAR YOAST META DATOS PARA PRODUCTOS\nDELETE FROM wp_postmeta WHERE meta_key = '_yoast_wpseo_metadesc';\n=CONCAT(\"UPDATE wp_postmeta SET meta_value = '\";I1;\"' WHERE post_id = \";A1;\" AND meta_key = '_yoast_wpseo_metadesc';\")\n\n### PLUGIN PARA PASAR WP A HTML STATICO\n\nSimply Static"}]}