{"sourceFile": "BRAIN/WIKI/Sincronizar.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1729838227334, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1729838267235, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,1 +1,3 @@\n-Sincronizar\n\\ No newline at end of file\n+Para sincronizar estoy usando un comando que se ejecuta sólo con `unison`.\n+\n+Unison es un programa que sincroniza archivos y directorios entre dos máquinas, de manera que ambos tengan la misma versión de los archivos. Es muy útil para mantener actualizados los archivos en diferentes dispositivos, como un ordenador y un servidor.\n"}], "date": 1729838227334, "name": "Commit-0", "content": "Sincronizar"}]}