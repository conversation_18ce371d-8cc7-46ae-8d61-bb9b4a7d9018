{"sourceFile": "BRAIN/ROADS/NIRVANA/PLAY.md", "activeCommit": 0, "commits": [{"activePatchIndex": 3, "patches": [{"date": 1747310057793, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747311529069, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,10 @@\n+@play Armar carpeta con entradas\n+\n+## ROCOLA\n+\n+- [ ] Armar una tapa completa\n+- [ ] Dibujar posible mesa\n+- [ ] Definir ideas y objetivos\n+- [ ] Evaluar estado del tocadisco\n+- [ ] Programar MVP\n+- [ ] Modern Day Record Player Tutorial (https://www.youtube.com/watch?v=-jGWjFR936o&si=3FkF9Pp9qKitpjFp)\n\\ No newline at end of file\n"}, {"date": 1748286357526, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,9 @@\n-@play Armar carpeta con entradas\n+# ROADS > NIRVANA > PLAY\n \n+- [ ] Armar carpeta con entradas\n+\n+\n ## ROCOLA\n \n - [ ] Armar una tapa completa\n - [ ] Dibujar posible mesa\n"}, {"date": 1748286594280, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-# ROADS > NIRVANA > PLAY\n+# ROADS > NIRVANA > PLAY 🎮\n \n - [ ] Armar carpeta con entradas\n \n \n"}], "date": 1747310057793, "name": "Commit-0", "content": ""}]}