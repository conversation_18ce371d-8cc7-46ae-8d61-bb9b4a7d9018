{"sourceFile": "BRAIN/ROADS/AGENTE/AGENTE.md", "activeCommit": 0, "commits": [{"activePatchIndex": 11, "patches": [{"date": 1742849431505, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1742899281604, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,3 +1,12 @@\n \n+## SELENIUM EN AGENTE.AR\n \n\\ No newline at end of file\n-- [ ] Que se pueda obtener el listado\n+- [x] Instalar selenium y correr un test\n+- [x] Crear repositorio git\n+- [x] Desarrollar un index principal\n+- [x] Logearse en local\n+- [ ] Deploy con GitLab CD\n+- [ ] Prisci con git y deploy\n+- [ ] Que se pueda obtener el listado\n+- [ ] Logs y outputs\n+- [ ] Conexión con n8n\n"}, {"date": 1743545571381, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,11 @@\n - [x] Instalar selenium y correr un test\n - [x] Crear repositorio git\n - [x] Desarrollar un index principal\n - [x] Logearse en local\n+- [x] Prisci con git y deploy\n - [ ] Deploy con GitLab CD\n-- [ ] Prisci con git y deploy\n+- [ ] Logs y outputs\n - [ ] Que se pueda obtener el listado\n\\ No newline at end of file\n-- [ ] Logs y outputs\n-- [ ] Conexión con n8n\n+- [ ] Ejecutar con n8n\n+- [ ] Leer la devolución\n+- [ ] Analizar si se puede guardar sesión\n"}, {"date": 1743545581220, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,4 +11,5 @@\n - [ ] Que se pueda obtener el listado\n - [ ] Ejecutar con n8n\n - [ ] Leer la devolución\n - [ ] Ana<PERSON>zar si se puede guardar sesión\n+- [ ] Pasar la pelota\n"}, {"date": 1748276001386, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,11 +5,26 @@\n - [x] Crear repositorio git\n - [x] Desarrollar un index principal\n - [x] Logearse en local\n - [x] Prisci con git y deploy\n-- [ ] Deploy con GitLab CD\n-- [ ] Logs y outputs\n+- [x] Deploy con GitLab CD\n+- [x] Logs y outputs\n - [ ] Que se pueda obtener el listado\n - [ ] Ejecutar con n8n\n - [ ] Leer la devolución\n\\ No newline at end of file\n - [ ] Analizar si se puede guardar sesión\n-- [ ] Pasar la pelota\n+- [ ] Pasar la pelota\n+\n+\n+Esta idea la estoy trabajando en [AIC.md](./AIC.md)\n+\n+## TOKENS\n+\n+En el archivo CloudAPI.postman_environments.json están los datos\n+\n+**Agente Pri**\n+\n+EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD\n+\n+**Agente Crono**\n+\n+EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD\n\\ No newline at end of file\n"}, {"date": 1752008839452, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,11 @@\n+# 🤖 ROADS > AGENTE\n+-------------------------------------------------------------------------------\n \n+## 📊 PLAN\n+\n+## PRÓXIMOS MILESTONES\n+\n ## SELENIUM EN AGENTE.AR\n \n - [x] Instalar selenium y correr un test\n - [x] Crear repositorio git\n"}, {"date": 1752158719222, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,15 @@\n ## 📊 PLAN\n \n ## PRÓXIMOS MILESTONES\n \n+- [ ] Actualizar n8n\n+- [ ] Instalar Supabase y crear base de datos para usuarios y para Remax\n+- [ ] Actualizar el script de Remax para que migre la información a Supabase\n+- [ ] MPC Servidor propio para consultar propiedades\n+- [ ] Averiguar por múltiples certificados\n+\n+\n ## SELENIUM EN AGENTE.AR\n \n - [x] Instalar selenium y correr un test\n - [x] Crear repositorio git\n"}, {"date": 1752158726612, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n - [ ] Instalar Supabase y crear base de datos para usuarios y para Remax\n - [ ] Actualizar el script de Remax para que migre la información a Supabase\n - [ ] MPC Servidor propio para consultar propiedades\n - [ ] Averiguar por múltiples certificados\n+- [ ] Pensar estrategia de módulos\n \n \n ## SELENIUM EN AGENTE.AR\n \n"}, {"date": 1752161952106, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,11 +27,21 @@\n - [ ] <PERSON><PERSON> la devolución\n - [ ] Analizar si se puede guardar sesión\n - [ ] Pasar la pelota\n \n+## MÚLTIPLES CERTIFICADOS\n \n-Esta idea la estoy trabajando en [AIC.md](./AIC.md)\n+- Los hilos de este tema en la comunidad son:\n+  - https://community.n8n.io/t/access-saved-credentials-from-expressions/857/29\n+  - https://community.n8n.io/t/select-credentials-via-expression/5150/39\n+- Posibles community nodes:\n+  - https://www.npmjs.com/package/n8n-nodes-run-node-with-credentials-x (este ya lo tenemos)\n+  - https://www.npmjs.com/package/n8n-nodes-dynamic-node\n \n+## MÁS TEMAS\n+\n+La idea original la tengo en [AIC.md](./AIC.md)\n+\n ## TOKENS\n \n En el archivo CloudAPI.postman_environments.json están los datos\n \n"}, {"date": 1752161978871, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n ## 📊 PLAN\n \n ## PRÓXIMOS MILESTONES\n \n-- [ ] Actualizar n8n\n+- [x] Actualizar n8n\n - [ ] Instalar Supabase y crear base de datos para usuarios y para Remax\n - [ ] Actualizar el script de Remax para que migre la información a Supabase\n - [ ] MPC Servidor propio para consultar propiedades\n - [ ] Averiguar por múltiples certificados\n@@ -27,8 +27,9 @@\n - [ ] Leer la devolución\n - [ ] <PERSON><PERSON>zar si se puede guardar sesión\n - [ ] Pasar la pelota\n \n+\n ## MÚLTIPLES CERTIFICADOS\n \n - Los hilos de este tema en la comunidad son:\n   - https://community.n8n.io/t/access-saved-credentials-from-expressions/857/29\n@@ -36,8 +37,9 @@\n - Posibles community nodes:\n   - https://www.npmjs.com/package/n8n-nodes-run-node-with-credentials-x (este ya lo tenemos)\n   - https://www.npmjs.com/package/n8n-nodes-dynamic-node\n \n+\n ## MÁS TEMAS\n \n La idea original la tengo en [AIC.md](./AIC.md)\n \n"}, {"date": 1752698105177, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,62 @@\n+# 🤖 ROADS > AGENTE\n+-------------------------------------------------------------------------------\n+\n+## 📊 PLAN\n+\n+## PRÓXIMOS MILESTONES\n+\n+- [x] Actualizar n8n\n+- [ ] Configurar app.agente.ar con un node base\n+- [ ] Generar página para autenticar usuario por whatsapp\n+- [ ] Lograr guardar un log de la página\n+- [ ] Auth Google con permiso a Calendar y guardar token y refresh\n+- [ ] Probar la conexión con el token desde n8n\n+\n+- [ ] Instalar Supabase y crear base de datos para usuarios y para Remax\n+- [ ] Actualizar el script de Remax para que migre la información a Supabase\n+- [ ] MPC Servidor propio para consultar propiedades\n+- [ ] Averiguar por múltiples certificados\n+- [ ] Pensar estrategia de módulos\n+\n+\n+## SELENIUM EN AGENTE.AR\n+\n+- [x] Instalar selenium y correr un test\n+- [x] Crear repositorio git\n+- [x] Desarrollar un index principal\n+- [x] Logearse en local\n+- [x] Prisci con git y deploy\n+- [x] Deploy con GitLab CD\n+- [x] Logs y outputs\n+- [ ] Que se pueda obtener el listado\n+- [ ] Ejecutar con n8n\n+- [ ] Leer la devolución\n+- [ ] Analizar si se puede guardar sesión\n+- [ ] Pasar la pelota\n+\n+\n+## MÚLTIPLES CERTIFICADOS\n+\n+- Los hilos de este tema en la comunidad son:\n+  - https://community.n8n.io/t/access-saved-credentials-from-expressions/857/29\n+  - https://community.n8n.io/t/select-credentials-via-expression/5150/39\n+- Posibles community nodes:\n+  - https://www.npmjs.com/package/n8n-nodes-run-node-with-credentials-x (este ya lo tenemos)\n+  - https://www.npmjs.com/package/n8n-nodes-dynamic-node\n+\n+\n+## MÁS TEMAS\n+\n+La idea original la tengo en [AIC.md](./AIC.md)\n+\n+## TOKENS\n+\n+En el archivo CloudAPI.postman_environments.json están los datos\n+\n+**Agente Pri**\n+\n+EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD\n+\n+**Agente Crono**\n+\n+EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD\n\\ No newline at end of file\n"}, {"date": 1753144181293, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,65 @@\n+# 🤖 ROADS > AGENTE\n+-------------------------------------------------------------------------------\n+\n+## 📊 PLAN\n+\n+## PRÓXIMOS MILESTONES\n+\n+- [x] Actualizar n8n\n+- [ ] Configurar app.agente.ar con un node base\n+- [ ] Generar página para autenticar usuario por whatsapp\n+- [ ] Lograr guardar un log de la página\n+- [ ] Auth Google con permiso a Calendar y guardar token y refresh\n+- [ ] Probar la conexión con el token desde n8n\n+\n+- [ ] Instalar Supabase y crear base de datos para usuarios y para Remax\n+- [ ] Actualizar el script de Remax para que migre la información a Supabase\n+- [ ] MPC Servidor propio para consultar propiedades\n+- [ ] Averiguar por múltiples certificados\n+- [ ] Pensar estrategia de módulos\n+\n+1) https://app.yestoki.com/settings/calendars?source=whatsapp&type=0&bot_name=%2B16502234435&code=ndq5me81wu021v13agez906cijhpvmb9vrwd5638\n+2) https://app.yestoki.com/settings/calendars?source=whatsapp&bot_name=%2B16502234435\n+3) https://app.yestoki.com/connect/calendar/google?redirect_url=https%3A%2F%2Fapp.yestoki.com%2Fsettings%2Fcalendars%3Fsource%3Dwhatsapp%26bot_name%3D%252B16502234435&provider=google&ref=app&source=app&account=andresmaiden%40gmail.com&status=success\n+\n+## SELENIUM EN AGENTE.AR\n+\n+- [x] Instalar selenium y correr un test\n+- [x] Crear repositorio git\n+- [x] Desarrollar un index principal\n+- [x] Logearse en local\n+- [x] Prisci con git y deploy\n+- [x] Deploy con GitLab CD\n+- [x] Logs y outputs\n+- [ ] Que se pueda obtener el listado\n+- [ ] Ejecutar con n8n\n+- [ ] Leer la devolución\n+- [ ] Analizar si se puede guardar sesión\n+- [ ] Pasar la pelota\n+\n+\n+## MÚLTIPLES CERTIFICADOS\n+\n+- Los hilos de este tema en la comunidad son:\n+  - https://community.n8n.io/t/access-saved-credentials-from-expressions/857/29\n+  - https://community.n8n.io/t/select-credentials-via-expression/5150/39\n+- Posibles community nodes:\n+  - https://www.npmjs.com/package/n8n-nodes-run-node-with-credentials-x (este ya lo tenemos)\n+  - https://www.npmjs.com/package/n8n-nodes-dynamic-node\n+\n+\n+## MÁS TEMAS\n+\n+La idea original la tengo en [AIC.md](./AIC.md)\n+\n+## TOKENS\n+\n+En el archivo CloudAPI.postman_environments.json están los datos\n+\n+**Agente Pri**\n+\n+EAAVk1XncURIBO9xXcdmWkQcYAYkzY7KXZC6AZA8I6DuV11SNsyrtulJzkmVySdgKgLBYw7fkZBPgg5I1vF2ZA9Io8uTZAwOSMX4agDOAEnSJbxs9kzAoirT9DjBfQ4YVAdIRPfHODnmoFuoFRPZAJbU1b1DgSAPemEf9SKlmj3Pq9t2hZArTa6cVk1dJVpFSVprOAZDZD\n+\n+**Agente Crono**\n+\n+EAAHC6N82t8MBO0uAmmIGM6ZAP51IWqocxkTfH4Ed6fXKwBMyViWLUciNjq2Ol4Da7UDmscBivCQjZC4f1gAbKOt7hMRbKbgNmKflbS49XHEDDHNNYZCPTXyMUUOK1tqQAp3ZAZBd36NMZAIqEjEPBRijvpoJhwh6ZCmKCpvIvUzHiB6vHqtdRZCDjG5pFCDfxBy1ZCAZDZD\n\\ No newline at end of file\n"}], "date": 1742849431505, "name": "Commit-0", "content": "\n\n- [ ] Que se pueda obtener el listado"}]}