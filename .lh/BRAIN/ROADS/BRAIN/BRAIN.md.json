{"sourceFile": "BRAIN/ROADS/BRAIN/BRAIN.md", "activeCommit": 0, "commits": [{"activePatchIndex": 12, "patches": [{"date": 1726149072261, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726417941224, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,29 @@\n - Herramientas personales para recordar y consultar y compartir\n - Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n \n \n+- Debería tener algo para agregar a BRAIN desde el celu\n+\n+- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n+\n+URGENTE:\n+\n+- Servidor caído\n+- Error que genere inconsistencia\n+\n+IMPORTANTE:\n+\n+- Próxima prioridad de DEV\n+- Próxima prioridad de GROWTH\n+\n+CLIENTES:\n+\n+- Soporte a cliente\n+- Ventas\n+\n+\n+\n ## MEGA BRAIN\n \n \n ## ESTUDIO\n"}, {"date": 1726427872192, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,35 +2,18 @@\n -------------------------------------------------------------------------------\n \n Brain es mi segundo cerebro, sólo para uso y consulta personal.\n \n-- Todo el conocimiento procesado\n-- Todo el estudio para sumar a ese conocimiento, tanto pasado como futuro\n+- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor con unison mediante crontab y la funcion memory_hack\n+- Voy a tener herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n - Herramientas personales para recordar y consultar y compartir\n - Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n \n \n-- Debería tener algo para agregar a BRAIN desde el celu\n-\n - El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n \n-URGENTE:\n \n-- Servidor caído\n-- Error que genere inconsistencia\n \n-IMPORTANTE:\n-\n-- Próxima prioridad de DEV\n-- Próxima prioridad de GROWTH\n-\n-CLIENTES:\n-\n-- Soporte a cliente\n-- Ventas\n-\n-\n-\n ## MEGA BRAIN\n \n \n ## ESTUDIO\n"}, {"date": 1726491088829, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,15 +6,18 @@\n - Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor con unison mediante crontab y la funcion memory_hack\n - Voy a tener herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n - Herramientas personales para recordar y consultar y compartir\n - Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n+- Hago backup en MEGA de todo por las dudas\n \n \n - El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n \n \n \n-## MEGA BRAIN\n+## MEMORY\n \n+- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n+- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n \n ## ESTUDIO\n \n"}, {"date": 1726492171147, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,6 +18,7 @@\n \n - La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n - Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n \n+\n ## ESTUDIO\n \n"}, {"date": 1726495770178, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,13 +12,29 @@\n \n - El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n \n \n-\n ## MEMORY\n \n - La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n - Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n \n \n+## TOOLS\n+\n+Tengo que poder convertir formatos en celu y compu:\n+\n+- COMPU Audio a texto\n+- COMPU Texto a audio\n+- COMPU Texto a imagen\n+- COMPU Imagen a texto\n+- COMPU Texto a QR\n+- CELU Texto a audio\n+- CELU Texto a imagen\n+- CELU Imagen a texto\n+- CELU Texto a QR\n+- CELU QR a texto\n+- CELU Texto a PDF\n+\n+\n ## ESTUDIO\n \n"}, {"date": 1738351264504, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,38 @@\n+# 🧠 ROAD > BRAIN\n+-------------------------------------------------------------------------------\n+\n+Brain es mi segundo cerebro, sólo para uso y consulta personal.\n+\n+- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n+- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n+- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n+- Hago backup en MEGA de todo por las dudas\n+\n+- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n+\n+\n+## MEMORY\n+\n+- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n+- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n+\n+\n+## TOOLS\n+\n+Tengo que poder convertir formatos en celu y compu:\n+\n+- COMPU Audio a texto\n+- COMPU Texto a audio\n+- COMPU Texto a imagen\n+- COMPU Imagen a texto\n+- COMPU Texto a QR\n+- CELU Texto a audio\n+- CELU Texto a imagen\n+- CELU Imagen a texto\n+- CELU Texto a QR\n+- CELU QR a texto\n+- CELU Texto a PDF\n+\n+\n+## ESTUDIO\n+\n"}, {"date": 1747252948445, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,58 @@\n+# 🧠 ROAD > BRAIN\n+-------------------------------------------------------------------------------\n+\n+Brain es mi segundo cerebro, sólo para uso y consulta personal.\n+\n+- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n+- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n+- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n+- Hago backup en MEGA de todo por las dudas\n+\n+- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n+\n+\n+# NUEVO TODO\n+\n+Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:\n+\n+- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)\n+- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)\n+- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)\n+- Planear cada @flow al iniciar con AI 10-80-10\n+\n+\n+## MEMORY\n+\n+- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n+- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n+\n+\n+## PROCESOS Y FRAMEWORKS\n+\n+- Puedo pasar audio a texto con MEGA y `transcribir`\n+\n+\n+## TODO\n+\n+- [ ] Ampliar el mail memory\n+- [ ] Terminar el script para pasar audio a texto\n+- [ ] Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n+- [ ] Ver como puedo ver viajes fácil\n+- [ ] Crear el framework para [[next.md]]\n+- [ ] Los pagos me tienen que quedar cómodos como cada mes\n+- [ ] Buscar forma de utilizar fechas recurrentes\n+- [ ] Terminar de pasar el resto de Todoist\n+\n+Tengo que poder convertir formatos en celu y compu:\n+\n+- COMPU Audio a texto\n+- COMPU Texto a audio\n+- COMPU Texto a imagen\n+- COMPU Imagen a texto\n+- COMPU Texto a QR\n+- CELU Texto a audio\n+- CELU Texto a imagen\n+- CELU Imagen a texto\n+- CELU Texto a QR\n+- CELU QR a texto\n+- CELU Texto a PDF\n"}, {"date": 1747311744675, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -55,4 +55,8 @@\n - CELU Imagen a texto\n - CELU Texto a QR\n - CELU QR a texto\n - CELU Texto a PDF\n+\n+## ESTUDIAR\n+\n+- [ ] Terminar curso de importación (https://importacionestrategicacomoimpo.club.hotmart.com/lesson/gOpdV3nrOJ/2.-protocolos-y-comportamientos)\n"}, {"date": 1747410734431, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,9 +33,9 @@\n \n \n ## TODO\n \n-- [ ] Ampliar el mail memory\n+- [ ] Ampliar el mail memory (y ver que me pasó esto como dato: Frases de los Les Luthier)\n - [ ] Terminar el script para pasar audio a texto\n - [ ] Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n - [ ] Ver como puedo ver viajes fácil\n - [ ] Crear el framework para [[next.md]]\n"}, {"date": 1747516506646, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -35,14 +35,10 @@\n ## TODO\n \n - [ ] Ampliar el mail memory (y ver que me pasó esto como dato: Frases de los Les Luthier)\n - [ ] Terminar el script para pasar audio a texto\n-- [ ] Calendario compartido con hotkey para ver y dar de alta eventos (por ej buscar a Mati y viajes)\n-- [ ] Ver como puedo ver viajes fácil\n - [ ] Crear el framework para [[next.md]]\n-- [ ] Los pagos me tienen que quedar cómodos como cada mes\n - [ ] Buscar forma de utilizar fechas recurrentes\n-- [ ] Terminar de pasar el resto de Todoist\n \n Tengo que poder convertir formatos en celu y compu:\n \n - COMPU Audio a texto\n"}, {"date": 1748286507961, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,58 @@\n+# 🧠 ROAD > BRAIN\n+-------------------------------------------------------------------------------\n+\n+Brain es mi segundo cerebro, sólo para uso y consulta personal.\n+\n+- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n+- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n+- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n+- Hago backup en MEGA de todo por las dudas\n+\n+- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n+\n+\n+# NUEVO TODO\n+\n+Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:\n+\n+- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)\n+- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)\n+- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)\n+- Planear cada @flow al iniciar con AI 10-80-10\n+\n+\n+## MEMORY\n+\n+- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n+- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n+\n+\n+## PROCESOS Y FRAMEWORKS\n+\n+- Puedo pasar audio a texto con MEGA y `transcribir`\n+\n+\n+## TODO\n+\n+- [x] Crear el framework para [[next.md]]\n+- [x] Buscar forma de utilizar fechas recurrentes y calendario\n+- [ ] Ampliar el mail memory (y ver que me pasó esto como dato: Frases de los Les Luthier)\n+- [ ] Terminar el script para pasar audio a texto\n+\n+Tengo que poder convertir formatos en celu y compu:\n+\n+- COMPU Audio a texto\n+- COMPU Texto a audio\n+- COMPU Texto a imagen\n+- COMPU Imagen a texto\n+- COMPU Texto a QR\n+- CELU Texto a audio\n+- CELU Texto a imagen\n+- CELU Imagen a texto\n+- CELU Texto a QR\n+- CELU QR a texto\n+- CELU Texto a PDF\n+\n+## ESTUDIAR\n+\n+- [ ] Terminar curso de importación (https://importacionestrategicacomoimpo.club.hotmart.com/lesson/gOpdV3nrOJ/2.-protocolos-y-comportamientos)\n"}, {"date": 1748286551333, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,5 @@\n # 🧠 ROAD > BRAIN\n--------------------------------------------------------------------------------\n \n Brain es mi segundo cerebro, sólo para uso y consulta personal.\n \n - Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n@@ -55,140 +54,4 @@\n \n ## ESTUDIAR\n \n - [ ] Terminar curso de importación (https://importacionestrategicacomoimpo.club.hotmart.com/lesson/gOpdV3nrOJ/2.-protocolos-y-comportamientos)\n-# 🧠 ROAD > BRAIN\n--------------------------------------------------------------------------------\n-\n-Brain es mi segundo cerebro, sólo para uso y consulta personal.\n-\n-- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n-- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n-- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n-- Hago backup en MEGA de todo por las dudas\n-\n-- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n-\n-\n-# NUEVO TODO\n-\n-Estoy jugando a reformular mis sistema TODO y BRAIN con lo siguiente en mente:\n-\n-- Sólo texto markdown y todo en múltiples archivos (mi lugar es el teclado)\n-- Integrado a VSC y Sincronizado con celular (a través de MEGA y con markdown en celu también)\n-- No más Week, ni deadlines, todos flujos continuos @flow para hacer cuando quiera (estoy orgulloso de poder trabajar sólo cuando tengo ganas y de lo que tengo ganas)\n-- Planear cada @flow al iniciar con AI 10-80-10\n-\n-\n-## MEMORY\n-\n-- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n-- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n-\n-\n-## PROCESOS Y FRAMEWORKS\n-\n-- Puedo pasar audio a texto con MEGA y `transcribir`\n-\n-\n-## TODO\n-\n-- [ ] Ampliar el mail memory (y ver que me pasó esto como dato: Frases de los Les Luthier)\n-- [ ] Terminar el script para pasar audio a texto\n-- [ ] Crear el framework para [[next.md]]\n-- [ ] Buscar forma de utilizar fechas recurrentes\n-\n-Tengo que poder convertir formatos en celu y compu:\n-\n-- COMPU Audio a texto\n-- COMPU Texto a audio\n-- COMPU Texto a imagen\n-- COMPU Imagen a texto\n-- COMPU Texto a QR\n-- CELU Texto a audio\n-- CELU Texto a imagen\n-- CELU Imagen a texto\n-- CELU Texto a QR\n-- CELU QR a texto\n-- CELU Texto a PDF\n-\n-## ESTUDIAR\n-\n-- [ ] Terminar curso de importación (https://importacionestrategicacomoimpo.club.hotmart.com/lesson/gOpdV3nrOJ/2.-protocolos-y-comportamientos)\n-# 🧠 ROAD > BRAIN\n--------------------------------------------------------------------------------\n-\n-Brain es mi segundo cerebro, sólo para uso y consulta personal.\n-\n-- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor manualmente con `rup` y `rdown` y la funcion memory_hack\n-- Tengo herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n-- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n-- Hago backup en MEGA de todo por las dudas\n-\n-- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n-\n-\n-## MEMORY\n-\n-- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n-- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n-\n-\n-## TOOLS\n-\n-Tengo que poder convertir formatos en celu y compu:\n-\n-- COMPU Audio a texto\n-- COMPU Texto a audio\n-- COMPU Texto a imagen\n-- COMPU Imagen a texto\n-- COMPU Texto a QR\n-- CELU Texto a audio\n-- CELU Texto a imagen\n-- CELU Imagen a texto\n-- CELU Texto a QR\n-- CELU QR a texto\n-- CELU Texto a PDF\n-\n-\n-## ESTUDIO\n-\n-# 🧠 ROAD > BRAIN\n--------------------------------------------------------------------------------\n-\n-Brain es mi segundo cerebro, sólo para uso y consulta personal.\n-\n-- Cuenta con una base de conocimiento en archivos Markdown sincronizado a mi servidor con unison mediante crontab y la funcion memory_hack\n-- Voy a tener herramientas para editar y consultar esta base de conocimiento desde VSC, la web y tools con AI\n-- Herramientas personales para recordar y consultar y compartir\n-- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n-- Hago backup en MEGA de todo por las dudas\n-\n-\n-- El responder y ordenar los soportes, es parte del tiempo de soporte (Construcción de clientes), y hay que ir dosificando este tiempo durante la semana en momentos muertos que estés preparado para hacerlo sin stress.\n-\n-\n-## MEMORY\n-\n-- La intensión es traer cosas que quiero recordar en distintas situaciones: me lo mando por mail y lo muestro en la terminal\n-- Empezar a agregar enlaces a los MD para profundizar y preguntas en lugar de texto (sería pasar por LLM antes de armar el mail)\n-\n-\n-## TOOLS\n-\n-Tengo que poder convertir formatos en celu y compu:\n-\n-- COMPU Audio a texto\n-- COMPU Texto a audio\n-- COMPU Texto a imagen\n-- COMPU Imagen a texto\n-- COMPU Texto a QR\n-- CELU Texto a audio\n-- CELU Texto a imagen\n-- CELU Imagen a texto\n-- CELU Texto a QR\n-- CELU QR a texto\n-- CELU Texto a PDF\n-\n-\n-## ESTUDIO\n-\n"}], "date": 1726149072261, "name": "Commit-0", "content": "# 🧠 ROAD > BRAIN\n-------------------------------------------------------------------------------\n\nBrain es mi segundo cerebro, sólo para uso y consulta personal.\n\n- Todo el conocimiento procesado\n- Todo el estudio para sumar a ese conocimiento, tanto pasado como futuro\n- Herramientas personales para recordar y consultar y compartir\n- Incluye herramientas personales para compartir, con AI, y en el sitio web andresmisiak.ar\n\n\n## MEGA BRAIN\n\n\n## ESTUDIO\n\n"}]}