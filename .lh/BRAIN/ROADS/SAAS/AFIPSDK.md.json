{"sourceFile": "BRAIN/ROADS/SAAS/AFIPSDK.md", "activeCommit": 0, "commits": [{"activePatchIndex": 23, "patches": [{"date": 1750365917742, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1750367879464, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,43 @@\n+# AFIPSDK\n+\n+## PLAN GENERAL\n+\n+Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n+\n+- [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n+- [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n+- [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] Deploy de ALFA a BETA con test manual de a poco\n+- [ ] Estudiar todo lo que está dando vuelta en Google Groups\n+  - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n+  - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n+  - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n+  - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n+- [ ] Actualizar Pyafipws con Python 2.x como plan B\n+- [ ] Deploy BETA a PROD\n+\n+\n+## Para pasar a otros issues o evaluar\n+\n+- [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n+- [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n+- Tener en cuenta que hoy da Error de Razón Social con Ñ\n+\n+\n+## Acentar\n+\n+Condiciones de IVA:\n+1   IVA Responsable Inscripto\n+2   IVA Responsable no Inscripto\n+3   IVA no Responsable\n+4   IVA Sujeto Exento\n+5   Consumidor Final\n+6   Responsable Monotributo\n+7   Sujeto no Categorizado\n+8   Proveedor del Exterior\n+9   Cliente del Exterior\n+10  IVA Liberado – Ley Nº 19.640\n+11  IVA Responsable Inscripto – Agente de Percepción\n+12  Pequeño Contribuyente Eventual\n+13  Monotributista Social\n+14  Pequeño Contribuyente Eventual Social\n"}, {"date": 1750367930233, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -40,4 +40,52 @@\n 11  IVA Responsable Inscripto – Agente de Percepción\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n+\n+\n+## PROMPTS\n+\n+Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP. \n+\n+Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n+\n+Necesito:\n+1. Sacar datos hardcodeados a variables de entorno (URLs SQS, bucket S3, credenciales)\n+2. Configurar claramente entorno homologación vs producción\n+3. Variables específicas por ambiente (dev/alpha/beta/prod)\n+\n+El código actual tiene hardcodeados:\n+- Bucket S3: 'saasargentina-wsfe'\n+- Queue SQS: 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue'\n+- Credenciales AWS en ErrorHandler\n+\n+¿Puedes ayudarme a organizarlo correctamente?\n+\n+---\n+\n+Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n+\n+Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n+\n+1. Reintento automático cuando la API de AFIP está caída (reintentar a los 15 min)\n+2. Dead letter queue para mensajes que fallan múltiples veces\n+3. Mecanismo para pausar el procesamiento manualmente (como alternativa al cronjob)\n+4. Timeout y circuit breaker para llamadas a AFIP\n+\n+La función actual está en /home/<USER>/www/saasargentina/services/lambda/arca/\n+\n+¿Cómo implementarías esto con SQS y Lambda?\n+\n+---\n+\n+Necesito implementar un sistema de logging específico para monitorear facturas electrónicas en mi Lambda de AFIP.\n+\n+Requisitos:\n+1. Log estructurado de facturas aprobadas/rechazadas/errores\n+2. Métricas separadas por empresa y tipo de error\n+3. Sistema para revisar manualmente facturas problemáticas\n+4. Alertas para fallos masivos\n+\n+El código actual está en /home/<USER>/www/saasargentina/services/lambda/arca/ y ya tiene un ErrorHandler básico.\n+\n+¿Cómo estructurarías un sistema de logging robusto para esto?\n"}, {"date": 1750367988208, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,9 @@\n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n+- [ ] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n@@ -44,9 +45,9 @@\n \n \n ## PROMPTS\n \n-Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP. \n+Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP.\n \n Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n \n Necesito:\n"}, {"date": 1750433765783, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,10 @@\n Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n \n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n-- [ ] [Lambda ARCA](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n - [ ] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n"}, {"date": 1750513977234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,9 @@\n - [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n \n - [ ] Deploy de ALFA a BETA con test manual de a poco\n-- [ ] Documentar el Lambda\n+- [x] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n@@ -46,26 +46,8 @@\n \n \n ## PROMPTS\n \n-Necesito ayuda para configurar variables de entorno y manejo de ambientes en mi función Lambda PHP para AFIP.\n-\n-Tengo el código actual en /home/<USER>/www/saasargentina/services/lambda/arca/ que procesa facturas electrónicas usando AFIPSDK.\n-\n-Necesito:\n-1. Sacar datos hardcodeados a variables de entorno (URLs SQS, bucket S3, credenciales)\n-2. Configurar claramente entorno homologación vs producción\n-3. Variables específicas por ambiente (dev/alpha/beta/prod)\n-\n-El código actual tiene hardcodeados:\n-- Bucket S3: 'saasargentina-wsfe'\n-- Queue SQS: 'https://sqs.sa-east-1.amazonaws.com/124561084955/email-queue'\n-- Credenciales AWS en ErrorHandler\n-\n-¿Puedes ayudarme a organizarlo correctamente?\n-\n----\n-\n Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n \n Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n \n"}, {"date": 1750517692890, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,33 +43,4 @@\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n \n-\n-## PROMPTS\n-\n-Necesito implementar manejo de reintentos y mecanismo de pausa para mi Lambda de AFIP en PHP.\n-\n-Contexto: Tengo una función Lambda que procesa facturas enviándolas a AFIP, pero necesito:\n-\n-1. Reintento automático cuando la API de AFIP está caída (reintentar a los 15 min)\n-2. Dead letter queue para mensajes que fallan múltiples veces\n-3. Mecanismo para pausar el procesamiento manualmente (como alternativa al cronjob)\n-4. Timeout y circuit breaker para llamadas a AFIP\n-\n-La función actual está en /home/<USER>/www/saasargentina/services/lambda/arca/\n-\n-¿Cómo implementarías esto con SQS y Lambda?\n-\n----\n-\n-Necesito implementar un sistema de logging específico para monitorear facturas electrónicas en mi Lambda de AFIP.\n-\n-Requisitos:\n-1. Log estructurado de facturas aprobadas/rechazadas/errores\n-2. Métricas separadas por empresa y tipo de error\n-3. Sistema para revisar manualmente facturas problemáticas\n-4. Alertas para fallos masivos\n-\n-El código actual está en /home/<USER>/www/saasargentina/services/lambda/arca/ y ya tiene un ErrorHandler básico.\n-\n-¿Cómo estructurarías un sistema de logging robusto para esto?\n"}, {"date": 1750542819887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,4 +43,33 @@\n 12  Pequeño Contribuyente Eventual\n 13  Monotributista Social\n 14  Pequeño Contribuyente Eventual Social\n \n+## PROMPTS\n+\n+Estoy migrando un proceso de aprobación de ventas que son facturas electrónicas de Argentina. Voy a darte todo el contexto y los pasos que quiero realizar. Te pido que analices todo lo que vamos a modificar, pero que vayamos de a un paso a la vez.\n+\n+En prompts anteriores ya ejecutamos los pasos 1, 2, 3, 4 y 13\n+\n+## Ten en cuenta los siguientes temas:\n+\n+- El proceso anterior utiliza una función en php que llama con la función `exec` a una librería que está en Python llamada pyafipws. Puedes ver la función `rece1` y las subfunciones que llama en el archivo #file:funciones_wsfe.php  NO MODIFIQUES NADA DE ESTE ARCHIVO. Para futura referencia más abajo de este prompt, lo voy a llamar \"RECE\"\n+- El proceso nuevo es una función de AWS Lambda que llama a una API con el SDK AFIPSDK. Puedes ver esta función en el archivo #file:afipsdk.php  SI MODIFICA ESTE ARCHIVO. Para futura referencia más abajo de este prompt lo voy a llamar \"LAMBDA\".\n+- La función `rece1` tiene 3 parámetros: `$id` que en el proceso nuevo es el `idventa` y otros 2 que no los vamos a necesitar migrar.\n+\n+## La función `rece1` tiene los siguientes pasos:\n+\n+1- Obtiene información de la venta. Esto hay que replicarlo en el método que ya existe `obtenerVenta` en LAMBDA, sólo modificando la consulta mysql.\n+2- Verifica si corresponde intentar emitir y si hay una factura del mismo tipo esperando CAE. Hay que agregar estas verificaciones en un nuevo método `debeAprobar` en LAMBDA que tenga estas 2 validaciones y si corresponde actualiza el registro y termina el proceso con el log correspondiente.\n+3- Descarga y bloquea el certificado con la función `bloquear_wsfe` de RECE y ya tenemos esto funcionando en `descargarCertificados` de LAMBDA, así que esto no hay que modificarlo.\n+4- Buscamos el número que dice ARCA que fue el último. Este proceso en RECE se hace con el método `rece1_ultimonumero` y hay que aplicarlo en un método de LAMBDA llamada `ultimoNumero` que se conecte con la API de AFIPSDK según la documentación que puedes ver en https://docs.afipsdk.com/siguientes-pasos/web-services/factura-electronica . En este mismo método, quiero aplicar también lo que se realiza en RECE `analizar_salida_ultimonumero` que pueden ser respuestas que vengan de AFIPSDK o no. También hay que agregar todas las verificaciones que tenemos en la función `rece1_verificar`. Hazlo simple.\n+5- Sigue actualizar número de venta. Esto es delicado, por lo que quiero que analices la funcion `actualizar_numero_venta` de RECE y que lo apliques en un nuevo método `actualizarNumero` pero con mucho cuidado de que funcione igual. Puedes revisar la consulta para ver si se puede mejorar su performance, pero es impresindible que esta función quede bien. Puedes meter ahí también el próximo paso que es verificar que la anterior esté ok\n+6- El próximo paso es Verifico que no tenga No Aplica si es RI y hay que replicarlo igual, este es fácil.\n+7- Hay un proceso que es `sin_control_numeracion` que su código es `return in_array($idempresa, [8905, 8980, 11597, 11854]);` . Este hay que migrarlo igual a un método `sinControlNumeracion` y aplicarlo en los mismos lugares.\n+8- Verifica y actualiza la fecha. Este proceso también hay que replicarlo en un método `actualizarFecha`\n+9- Luego tenemos el llamado a `generar_entrada_wsfe` que es el `prepararComprobante` en LAMBDA. Por el momento no toquemos este método.\n+10- Ahí si tenemos el llamado con el `exec` que ya está en el método `enviarFacturaAfip` que por ahora no lo toquemos.\n+11- La función `leer_salida_wsfe` de RECE es simplemente lo que devuelve `enviarFacturaAfip` así que no la necesitamos. Pero si la función `analizar_salida_wsfe` la tenemos que pasar con mucho cuidado de que funcione bien y quede bien registrada la devolución. Ahí hay un retorno de la función al array `$return` que quiero evitar, tiene que quedar más prolijo.\n+12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.\n+13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.\n+14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.\n+\n"}, {"date": 1750772407446, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,12 +4,15 @@\n \n Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n \n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n-- [ ] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n+- [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n+- [ ] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n+- [ ] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n+- [ ] Deploy de ALFA a BETA con test manual de a poco\n+\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n \n-- [ ] Deploy de ALFA a BETA con test manual de a poco\n - [x] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n"}, {"date": 1750818027072, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,13 +13,16 @@\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n \n - [x] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n+  - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n+  - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n - [ ] Actualizar Pyafipws con Python 2.x como plan B\n+- [ ] Agregar opción para envio la cotizacion correcta\n - [ ] Deploy BETA a PROD\n \n \n ## Para pasar a otros issues o evaluar\n"}, {"date": 1750818037002, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,9 +20,9 @@\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n - [ ] Actualizar Pyafipws con Python 2.x como plan B\n-- [ ] Agregar opción para envio la cotizacion correcta\n+- [ ] Agregar opción para envio la cotizacion correcta consultando la oficial\n - [ ] Deploy BETA a PROD\n \n \n ## Para pasar a otros issues o evaluar\n"}, {"date": 1750818054662, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,9 +20,9 @@\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n - [ ] Actualizar Pyafipws con Python 2.x como plan B\n-- [ ] Agregar opción para envio la cotizacion correcta consultando la oficial\n+- [ ] Agregar opción para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n - [ ] Deploy BETA a PROD\n \n \n ## Para pasar a otros issues o evaluar\n"}, {"date": 1750890818075, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,9 +23,11 @@\n - [ ] Actualizar Pyafipws con Python 2.x como plan B\n - [ ] Agregar opción para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n - [ ] Deploy BETA a PROD\n \n+- [ ] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n \n+\n ## Para pasar a otros issues o evaluar\n \n - [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n - [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n"}, {"date": 1750900606620, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,22 @@\n \n - [ ] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n \n \n+25-06-2025 22:14:22;874;Inicio afipsdk_ultimonumero\n+25-06-2025 22:14:22;874;Inicio getLastVoucher\n+25-06-2025 22:14:24;874;Fin afipsdk_ultimonumero: 11591\n+25-06-2025 22:14:24;874;Volvio de analizar ultimonumero: []\n+25-06-2025 22:14:24;874;Inicio afipsdk_get\n+25-06-2025 22:14:26;874;afipsdk_get: {\"Concepto\":1,\"DocTipo\":96,\"DocNro\":43454743,\"CbteDesde\":11591,\"CbteHasta\":11591,\"CbteFch\":\"20250625\",\"ImpTotal\":227501,\"ImpTotConc\":0,\"ImpNeto\":205883.26000000001,\"ImpOpEx\":0,\"ImpTrib\":0,\"ImpIVA\":21617.740000000002,\"FchServDesde\":\"\",\"FchServHasta\":\"\",\"FchVtoPago\":\"\",\"MonId\":\"PES\",\"MonCotiz\":1,\"Iva\":{\"AlicIva\":[{\"Id\":4,\"BaseImp\":205883.26000000001,\"Importe\":21617.740000000002}]},\"Resultado\":\"A\",\"CodAutorizacion\":\"75267034833819\",\"EmisionTipo\":\"CAE\",\"FchVto\":\"20250705\",\"FchProceso\":\"20250625175102\",\"Observaciones\":{\"Obs\":[{\"Code\":10245,\"Msg\":\"El campo Condicion Frente al IVA del receptor resultara obligatorio conforme lo reglamentado por la Resolucion General Nro 5616. Para mas informacion consular metodo FEParamGetCondicionIvaReceptor\"}]},\"PtoVta\":10,\"CbteTipo\":6}\n+25-06-2025 22:14:26;874;Fin afipsdk_get: {\"cae\":\"75267034833819\",\"cbt_desde\":11591,\"cbt_hasta\":11591,\"imp_total\":227501,\"fecha_cbte\":\"20250625\",\"fch_venc_cae\":\"20250705\",\"tipo_doc\":96,\"nro_doc\":43454743,\"cuit\":0,\"dni\":43454743,\"tipodoc\":96,\"numero\":11591,\"total\":227501,\"fecha\":\"2025-06-25\",\"vencimientocae\":\"2025-07-05\"}\n+25-06-2025 22:14:26;874;Inicio llamar_afipsdk\n+25-06-2025 22:14:26;874;Data para AFIPSDK: {\"CantReg\":1,\"PtoVta\":10,\"CbteTipo\":6,\"Concepto\":1,\"DocTipo\":96,\"DocNro\":42654105,\"CbteDesde\":11592,\"CbteHasta\":11592,\"CbteFch\":20250625,\"ImpTotal\":102000,\"ImpTotConc\":0,\"ImpNeto\":92307.690000000002,\"ImpOpEx\":0,\"ImpIVA\":9692.3099999999995,\"ImpTrib\":0,\"MonId\":\"PES\",\"MonCotiz\":1,\"Iva\":[{\"Id\":4,\"BaseImp\":92307.690000000002,\"Importe\":9692.3099999999995}]}\n+25-06-2025 22:14:27;874;Fin llamar_afipsdk: {\"FeCabResp\":{\"Cuit\":30717970787,\"PtoVta\":10,\"CbteTipo\":6,\"FchProceso\":\"20250625221427\",\"CantReg\":1,\"Resultado\":\"A\",\"Reproceso\":\"N\"},\"FeDetResp\":{\"FECAEDetResponse\":{\"Concepto\":1,\"DocTipo\":96,\"DocNro\":42654105,\"CbteDesde\":11592,\"CbteHasta\":11592,\"CbteFch\":\"20250625\",\"Resultado\":\"A\",\"Observaciones\":{\"Obs\":[{\"Code\":10245,\"Msg\":\"El campo Condicion Frente al IVA del receptor resultara obligatorio conforme lo reglamentado por la Resoluci\\u00c3\\u00b3n General Nro 5616. Para mas informacion consular metodo FEParamGetCondicionIvaReceptor\"}]},\"CAE\":\"75267069023336\",\"CAEFchVto\":\"20250705\"}},\"Events\":{\"Evt\":[{\"Code\":39,\"Msg\":\"IMPORTANTE: El dia 6 de abril de 2025, se actualizo la version del Web Service (WS) que permite enviar, de forma opcional, el campo Condicion Frente al IVA del receptor. Cabe destacar que la Resolucion General Nro 5616 indica que ese dato debe enviarse de manera obligatoria a partir del 15\\/04\\/2025. No obstante, se mantendra como un dato no excluyente hasta el 31\\/07\\/2025, inclusive. A partir del 1\\/08\\/2025 se rechazaran las solicitudes de emision de comprobantes sin este dato. Para mas informacion, consultar el manual en: https:\\/\\/www.arca.gob.ar\\/fe\\/ayuda\\/webservice.asp, https:\\/\\/www.arca.gob.ar\\/ws\\/documentacion\\/ws-factura-electronica.asp\"}]}}\n+\n+\n+\n+\n ## Para pasar a otros issues o evaluar\n \n - [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n - [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n"}, {"date": 1750946455212, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n - [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [ ] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n - [ ] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n+  - 161,874,9589,10798,11166\n \n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n \n - [x] Documentar el Lambda\n"}, {"date": 1750949961728, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,44 +5,30 @@\n Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n \n - [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n - [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n-- [ ] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n-- [ ] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n+- [x] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n+- [x] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n+- [x] Documentar el Lambda\n - [ ] Deploy de ALFA a BETA con test manual de a poco\n   - 161,874,9589,10798,11166\n+- [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n \n-- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n-\n-- [x] Documentar el Lambda\n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n-- [ ] Actualizar Pyafipws con Python 2.x como plan B\n-- [ ] Agregar opción para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+\n+- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] Actualizar Pyafipws con Python 2.x como plan C\n - [ ] Deploy BETA a PROD\n \n-- [ ] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n \n \n-25-06-2025 22:14:22;874;Inicio afipsdk_ultimonumero\n-25-06-2025 22:14:22;874;Inicio getLastVoucher\n-25-06-2025 22:14:24;874;Fin afipsdk_ultimonumero: 11591\n-25-06-2025 22:14:24;874;Volvio de analizar ultimonumero: []\n-25-06-2025 22:14:24;874;Inicio afipsdk_get\n-25-06-2025 22:14:26;874;afipsdk_get: {\"Concepto\":1,\"DocTipo\":96,\"DocNro\":43454743,\"CbteDesde\":11591,\"CbteHasta\":11591,\"CbteFch\":\"20250625\",\"ImpTotal\":227501,\"ImpTotConc\":0,\"ImpNeto\":205883.26000000001,\"ImpOpEx\":0,\"ImpTrib\":0,\"ImpIVA\":21617.740000000002,\"FchServDesde\":\"\",\"FchServHasta\":\"\",\"FchVtoPago\":\"\",\"MonId\":\"PES\",\"MonCotiz\":1,\"Iva\":{\"AlicIva\":[{\"Id\":4,\"BaseImp\":205883.26000000001,\"Importe\":21617.740000000002}]},\"Resultado\":\"A\",\"CodAutorizacion\":\"75267034833819\",\"EmisionTipo\":\"CAE\",\"FchVto\":\"20250705\",\"FchProceso\":\"20250625175102\",\"Observaciones\":{\"Obs\":[{\"Code\":10245,\"Msg\":\"El campo Condicion Frente al IVA del receptor resultara obligatorio conforme lo reglamentado por la Resolucion General Nro 5616. Para mas informacion consular metodo FEParamGetCondicionIvaReceptor\"}]},\"PtoVta\":10,\"CbteTipo\":6}\n-25-06-2025 22:14:26;874;Fin afipsdk_get: {\"cae\":\"75267034833819\",\"cbt_desde\":11591,\"cbt_hasta\":11591,\"imp_total\":227501,\"fecha_cbte\":\"20250625\",\"fch_venc_cae\":\"20250705\",\"tipo_doc\":96,\"nro_doc\":43454743,\"cuit\":0,\"dni\":43454743,\"tipodoc\":96,\"numero\":11591,\"total\":227501,\"fecha\":\"2025-06-25\",\"vencimientocae\":\"2025-07-05\"}\n-25-06-2025 22:14:26;874;Inicio llamar_afipsdk\n-25-06-2025 22:14:26;874;Data para AFIPSDK: {\"CantReg\":1,\"PtoVta\":10,\"CbteTipo\":6,\"Concepto\":1,\"DocTipo\":96,\"DocNro\":42654105,\"CbteDesde\":11592,\"CbteHasta\":11592,\"CbteFch\":20250625,\"ImpTotal\":102000,\"ImpTotConc\":0,\"ImpNeto\":92307.690000000002,\"ImpOpEx\":0,\"ImpIVA\":9692.3099999999995,\"ImpTrib\":0,\"MonId\":\"PES\",\"MonCotiz\":1,\"Iva\":[{\"Id\":4,\"BaseImp\":92307.690000000002,\"Importe\":9692.3099999999995}]}\n-25-06-2025 22:14:27;874;Fin llamar_afipsdk: {\"FeCabResp\":{\"Cuit\":30717970787,\"PtoVta\":10,\"CbteTipo\":6,\"FchProceso\":\"20250625221427\",\"CantReg\":1,\"Resultado\":\"A\",\"Reproceso\":\"N\"},\"FeDetResp\":{\"FECAEDetResponse\":{\"Concepto\":1,\"DocTipo\":96,\"DocNro\":42654105,\"CbteDesde\":11592,\"CbteHasta\":11592,\"CbteFch\":\"20250625\",\"Resultado\":\"A\",\"Observaciones\":{\"Obs\":[{\"Code\":10245,\"Msg\":\"El campo Condicion Frente al IVA del receptor resultara obligatorio conforme lo reglamentado por la Resoluci\\u00c3\\u00b3n General Nro 5616. Para mas informacion consular metodo FEParamGetCondicionIvaReceptor\"}]},\"CAE\":\"75267069023336\",\"CAEFchVto\":\"20250705\"}},\"Events\":{\"Evt\":[{\"Code\":39,\"Msg\":\"IMPORTANTE: El dia 6 de abril de 2025, se actualizo la version del Web Service (WS) que permite enviar, de forma opcional, el campo Condicion Frente al IVA del receptor. Cabe destacar que la Resolucion General Nro 5616 indica que ese dato debe enviarse de manera obligatoria a partir del 15\\/04\\/2025. No obstante, se mantendra como un dato no excluyente hasta el 31\\/07\\/2025, inclusive. A partir del 1\\/08\\/2025 se rechazaran las solicitudes de emision de comprobantes sin este dato. Para mas informacion, consultar el manual en: https:\\/\\/www.arca.gob.ar\\/fe\\/ayuda\\/webservice.asp, https:\\/\\/www.arca.gob.ar\\/ws\\/documentacion\\/ws-factura-electronica.asp\"}]}}\n-\n-\n-\n-\n ## Para pasar a otros issues o evaluar\n \n - [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n - [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n"}, {"date": 1750972938266, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,10 +8,9 @@\n - [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n - [x] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n - [x] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n - [x] Documentar el Lambda\n-- [ ] Deploy de ALFA a BETA con test manual de a poco\n-  - 161,874,9589,10798,11166\n+- [x] Deploy de ALFA a BETA con test manual de a poco: 161,874,9589,10798,11166\n - [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n \n - [ ] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n@@ -20,12 +19,13 @@\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n - [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+- [ ] Deploy BETA a PROD\n \n+- [ ] Agregar QR también con AFIPSDK\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n - [ ] Actualizar Pyafipws con Python 2.x como plan C\n-- [ ] Deploy BETA a PROD\n \n \n \n ## Para pasar a otros issues o evaluar\n"}, {"date": 1750974477061, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,9 +21,9 @@\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n - [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n - [ ] Deploy BETA a PROD\n \n-- [ ] Agregar QR también con AFIPSDK\n+- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/)\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n - [ ] Actualizar Pyafipws con Python 2.x como plan C\n \n \n"}, {"date": 1750975238504, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,12 +18,13 @@\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n-- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n - [ ] Deploy BETA a PROD\n+- [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n \n-- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/)\n+- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n - [ ] Actualizar Pyafipws con Python 2.x como plan C\n \n \n"}, {"date": 1752008204637, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,86 @@\n+# AFIPSDK\n+\n+## PLAN GENERAL\n+\n+Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n+\n+- [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n+- [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n+- [x] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n+- [x] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n+- [x] Documentar el Lambda\n+- [x] Deploy de ALFA a BETA con test manual de a poco: 161,874,9589,10798,11166\n+- [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n+- [x] Deploy BETA a PROD\n+\n+- [ ] Estudiar todo lo que está dando vuelta en Google Groups\n+  - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n+  - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n+  - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n+  - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n+  - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n+  - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n+- [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n+- [ ] [ARCA Lambda PyAFIPws](https://gitlab.com/saasargentina/app/-/issues/2166)\n+\n+- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n+- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n+- [ ] Actualizar Pyafipws con Python 2.x como plan C\n+\n+\n+\n+## Para pasar a otros issues o evaluar\n+\n+- [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n+- [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n+- Tener en cuenta que hoy da Error de Razón Social con Ñ\n+\n+\n+## Acentar\n+\n+Condiciones de IVA:\n+1   IVA Responsable Inscripto\n+2   IVA Responsable no Inscripto\n+3   IVA no Responsable\n+4   IVA Sujeto Exento\n+5   Consumidor Final\n+6   Responsable Monotributo\n+7   Sujeto no Categorizado\n+8   Proveedor del Exterior\n+9   Cliente del Exterior\n+10  IVA Liberado – Ley Nº 19.640\n+11  IVA Responsable Inscripto – Agente de Percepción\n+12  Pequeño Contribuyente Eventual\n+13  Monotributista Social\n+14  Pequeño Contribuyente Eventual Social\n+\n+## PROMPTS\n+\n+Estoy migrando un proceso de aprobación de ventas que son facturas electrónicas de Argentina. Voy a darte todo el contexto y los pasos que quiero realizar. Te pido que analices todo lo que vamos a modificar, pero que vayamos de a un paso a la vez.\n+\n+En prompts anteriores ya ejecutamos los pasos 1, 2, 3, 4 y 13\n+\n+## Ten en cuenta los siguientes temas:\n+\n+- El proceso anterior utiliza una función en php que llama con la función `exec` a una librería que está en Python llamada pyafipws. Puedes ver la función `rece1` y las subfunciones que llama en el archivo #file:funciones_wsfe.php  NO MODIFIQUES NADA DE ESTE ARCHIVO. Para futura referencia más abajo de este prompt, lo voy a llamar \"RECE\"\n+- El proceso nuevo es una función de AWS Lambda que llama a una API con el SDK AFIPSDK. Puedes ver esta función en el archivo #file:afipsdk.php  SI MODIFICA ESTE ARCHIVO. Para futura referencia más abajo de este prompt lo voy a llamar \"LAMBDA\".\n+- La función `rece1` tiene 3 parámetros: `$id` que en el proceso nuevo es el `idventa` y otros 2 que no los vamos a necesitar migrar.\n+\n+## La función `rece1` tiene los siguientes pasos:\n+\n+1- Obtiene información de la venta. Esto hay que replicarlo en el método que ya existe `obtenerVenta` en LAMBDA, sólo modificando la consulta mysql.\n+2- Verifica si corresponde intentar emitir y si hay una factura del mismo tipo esperando CAE. Hay que agregar estas verificaciones en un nuevo método `debeAprobar` en LAMBDA que tenga estas 2 validaciones y si corresponde actualiza el registro y termina el proceso con el log correspondiente.\n+3- Descarga y bloquea el certificado con la función `bloquear_wsfe` de RECE y ya tenemos esto funcionando en `descargarCertificados` de LAMBDA, así que esto no hay que modificarlo.\n+4- Buscamos el número que dice ARCA que fue el último. Este proceso en RECE se hace con el método `rece1_ultimonumero` y hay que aplicarlo en un método de LAMBDA llamada `ultimoNumero` que se conecte con la API de AFIPSDK según la documentación que puedes ver en https://docs.afipsdk.com/siguientes-pasos/web-services/factura-electronica . En este mismo método, quiero aplicar también lo que se realiza en RECE `analizar_salida_ultimonumero` que pueden ser respuestas que vengan de AFIPSDK o no. También hay que agregar todas las verificaciones que tenemos en la función `rece1_verificar`. Hazlo simple.\n+5- Sigue actualizar número de venta. Esto es delicado, por lo que quiero que analices la funcion `actualizar_numero_venta` de RECE y que lo apliques en un nuevo método `actualizarNumero` pero con mucho cuidado de que funcione igual. Puedes revisar la consulta para ver si se puede mejorar su performance, pero es impresindible que esta función quede bien. Puedes meter ahí también el próximo paso que es verificar que la anterior esté ok\n+6- El próximo paso es Verifico que no tenga No Aplica si es RI y hay que replicarlo igual, este es fácil.\n+7- Hay un proceso que es `sin_control_numeracion` que su código es `return in_array($idempresa, [8905, 8980, 11597, 11854]);` . Este hay que migrarlo igual a un método `sinControlNumeracion` y aplicarlo en los mismos lugares.\n+8- Verifica y actualiza la fecha. Este proceso también hay que replicarlo en un método `actualizarFecha`\n+9- Luego tenemos el llamado a `generar_entrada_wsfe` que es el `prepararComprobante` en LAMBDA. Por el momento no toquemos este método.\n+10- Ahí si tenemos el llamado con el `exec` que ya está en el método `enviarFacturaAfip` que por ahora no lo toquemos.\n+11- La función `leer_salida_wsfe` de RECE es simplemente lo que devuelve `enviarFacturaAfip` así que no la necesitamos. Pero si la función `analizar_salida_wsfe` la tenemos que pasar con mucho cuidado de que funcione bien y quede bien registrada la devolución. Ahí hay un retorno de la función al array `$return` que quiero evitar, tiene que quedar más prolijo.\n+12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.\n+13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.\n+14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.\n+\n"}, {"date": 1752763746481, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,18 +19,23 @@\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n+\n - [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n+  MS: Poder utilizar la librería AFIPSDK con los cambios en BETA\n+- [ ] [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n+  MS: Pasar lo que ya está en ALFA para poder esperar aprobación con LAMBDA\n - [ ] [ARCA Lambda PyAFIPws](https://gitlab.com/saasargentina/app/-/issues/2166)\n-\n+  MS: Tener un plan B con PyAFIPws por si algo sale mal con AFIPSDK\n - [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n+  MS: Poder facturar en dólares\n - [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n+  MS: Poder sacar pyafipws anterior y quizás Python completo\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n-- [ ] Actualizar Pyafipws con Python 2.x como plan C\n+  MS: Tener todo funcionando en Lambdas\n \n \n-\n ## Para pasar a otros issues o evaluar\n \n - [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n - [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n@@ -83,89 +88,4 @@\n 12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.\n 13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.\n 14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.\n \n-# AFIPSDK\n-\n-## PLAN GENERAL\n-\n-Necesito un archivo a parte para llevar todo este trabajo que es grande. A nivel general los pasos son:\n-\n-- [x] Generar posibilidad de distintas librerías [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n-- [x] Evaluar el plan de deploy, si va las librerías a BETA o ALFA\n-- [x] Pasar en BETA el plan C de AFIPSDK en funciones_wsfe (escribir plan y deployar a BETA)\n-- [x] [ARCA AFIPSDK en app](https://gitlab.com/saasargentina/app/-/issues/2159) funcionando en ALFA\n-- [x] Documentar el Lambda\n-- [x] Deploy de ALFA a BETA con test manual de a poco: 161,874,9589,10798,11166\n-- [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n-\n-- [ ] Estudiar todo lo que está dando vuelta en Google Groups\n-  - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n-  - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n-  - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n-  - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n-  - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n-  - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n-- [ ] Deploy BETA a PROD\n-- [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n-\n-- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n-- [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n-- [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n-- [ ] Actualizar Pyafipws con Python 2.x como plan C\n-\n-\n-\n-## Para pasar a otros issues o evaluar\n-\n-- [ ] Agregar una ventana para poder ejecutar el antiafip manualmente y presente el informe todo dentro del módulo SaaS, con el mensaje de AFIP completo y toda la info necesaria para saber que está pasando.\n-- [ ] Evaluar si conviene cambiar los procesos de generación de certificados\n-- Tener en cuenta que hoy da Error de Razón Social con Ñ\n-\n-\n-## Acentar\n-\n-Condiciones de IVA:\n-1   IVA Responsable Inscripto\n-2   IVA Responsable no Inscripto\n-3   IVA no Responsable\n-4   IVA Sujeto Exento\n-5   Consumidor Final\n-6   Responsable Monotributo\n-7   Sujeto no Categorizado\n-8   Proveedor del Exterior\n-9   Cliente del Exterior\n-10  IVA Liberado – Ley Nº 19.640\n-11  IVA Responsable Inscripto – Agente de Percepción\n-12  Pequeño Contribuyente Eventual\n-13  Monotributista Social\n-14  Pequeño Contribuyente Eventual Social\n-\n-## PROMPTS\n-\n-Estoy migrando un proceso de aprobación de ventas que son facturas electrónicas de Argentina. Voy a darte todo el contexto y los pasos que quiero realizar. Te pido que analices todo lo que vamos a modificar, pero que vayamos de a un paso a la vez.\n-\n-En prompts anteriores ya ejecutamos los pasos 1, 2, 3, 4 y 13\n-\n-## Ten en cuenta los siguientes temas:\n-\n-- El proceso anterior utiliza una función en php que llama con la función `exec` a una librería que está en Python llamada pyafipws. Puedes ver la función `rece1` y las subfunciones que llama en el archivo #file:funciones_wsfe.php  NO MODIFIQUES NADA DE ESTE ARCHIVO. Para futura referencia más abajo de este prompt, lo voy a llamar \"RECE\"\n-- El proceso nuevo es una función de AWS Lambda que llama a una API con el SDK AFIPSDK. Puedes ver esta función en el archivo #file:afipsdk.php  SI MODIFICA ESTE ARCHIVO. Para futura referencia más abajo de este prompt lo voy a llamar \"LAMBDA\".\n-- La función `rece1` tiene 3 parámetros: `$id` que en el proceso nuevo es el `idventa` y otros 2 que no los vamos a necesitar migrar.\n-\n-## La función `rece1` tiene los siguientes pasos:\n-\n-1- Obtiene información de la venta. Esto hay que replicarlo en el método que ya existe `obtenerVenta` en LAMBDA, sólo modificando la consulta mysql.\n-2- Verifica si corresponde intentar emitir y si hay una factura del mismo tipo esperando CAE. Hay que agregar estas verificaciones en un nuevo método `debeAprobar` en LAMBDA que tenga estas 2 validaciones y si corresponde actualiza el registro y termina el proceso con el log correspondiente.\n-3- Descarga y bloquea el certificado con la función `bloquear_wsfe` de RECE y ya tenemos esto funcionando en `descargarCertificados` de LAMBDA, así que esto no hay que modificarlo.\n-4- Buscamos el número que dice ARCA que fue el último. Este proceso en RECE se hace con el método `rece1_ultimonumero` y hay que aplicarlo en un método de LAMBDA llamada `ultimoNumero` que se conecte con la API de AFIPSDK según la documentación que puedes ver en https://docs.afipsdk.com/siguientes-pasos/web-services/factura-electronica . En este mismo método, quiero aplicar también lo que se realiza en RECE `analizar_salida_ultimonumero` que pueden ser respuestas que vengan de AFIPSDK o no. También hay que agregar todas las verificaciones que tenemos en la función `rece1_verificar`. Hazlo simple.\n-5- Sigue actualizar número de venta. Esto es delicado, por lo que quiero que analices la funcion `actualizar_numero_venta` de RECE y que lo apliques en un nuevo método `actualizarNumero` pero con mucho cuidado de que funcione igual. Puedes revisar la consulta para ver si se puede mejorar su performance, pero es impresindible que esta función quede bien. Puedes meter ahí también el próximo paso que es verificar que la anterior esté ok\n-6- El próximo paso es Verifico que no tenga No Aplica si es RI y hay que replicarlo igual, este es fácil.\n-7- Hay un proceso que es `sin_control_numeracion` que su código es `return in_array($idempresa, [8905, 8980, 11597, 11854]);` . Este hay que migrarlo igual a un método `sinControlNumeracion` y aplicarlo en los mismos lugares.\n-8- Verifica y actualiza la fecha. Este proceso también hay que replicarlo en un método `actualizarFecha`\n-9- Luego tenemos el llamado a `generar_entrada_wsfe` que es el `prepararComprobante` en LAMBDA. Por el momento no toquemos este método.\n-10- Ahí si tenemos el llamado con el `exec` que ya está en el método `enviarFacturaAfip` que por ahora no lo toquemos.\n-11- La función `leer_salida_wsfe` de RECE es simplemente lo que devuelve `enviarFacturaAfip` así que no la necesitamos. Pero si la función `analizar_salida_wsfe` la tenemos que pasar con mucho cuidado de que funcione bien y quede bien registrada la devolución. Ahí hay un retorno de la función al array `$return` que quiero evitar, tiene que quedar más prolijo.\n-12- La función `desbloquear_wsfe` no tiene que pasarse porque ya limpiamos los certificados descargados y no hay que desbloquear nada.\n-13- Luego tenemos un par de líneas que analizan y actualizan la venta. Eso creo que puedes dejarlo bien prolijo en LAMBDA en el método `actualizarVentaExitosa`, que ya que estás cambia el nombre a `actualizarVenta` y que pueda actualizarse el estadocae a `rechazado` cuando corresponde. También hay que corregir los nombres de los campos que están mal y agregar `obscae` que es importante.\n-14- Por último quiero que se envíe el mail de error que está en ErrorHandler cuando la venta es rechazada o hay algún error.\n-\n"}, {"date": 1752977810075, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,9 +22,9 @@\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n \n - [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n   MS: Poder utilizar la librería AFIPSDK con los cambios en BETA\n-- [ ] [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n+- [x] [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n   MS: Pasar lo que ya está en ALFA para poder esperar aprobación con LAMBDA\n - [ ] [ARCA Lambda PyAFIPws](https://gitlab.com/saasargentina/app/-/issues/2166)\n   MS: Tener un plan B con PyAFIPws por si algo sale mal con AFIPSDK\n - [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n"}, {"date": 1753035010806, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,24 +12,25 @@\n - [x] Deploy de ALFA a BETA con test manual de a poco: 161,874,9589,10798,11166\n - [x] Controlar los logs: 'rfce_mi_pyme.csv', 'afip_caido.csv'\n - [x] Deploy BETA a PROD\n \n-- [ ] Estudiar todo lo que está dando vuelta en Google Groups\n+- [x] Estudiar todo lo que está dando vuelta en Google Groups\n   - https://groups.google.com/g/pyafipws/c/Gc-QCxeQZ4s/m/XnD4qAb2BQAJ\n   - https://groups.google.com/g/pyafipws/c/Yy5M1zwNaWw/m/bD_tyNQmCQAJ\n   - https://groups.google.com/g/pyafipws/c/k9RnGpBQfR0/m/fqDThC6ECgAJ\n   - https://groups.google.com/g/pyafipws/c/YxzEf29xoEo/m/MmFIjZ-FCgAJ\n   - https://groups.google.com/g/pyafipws/c/dZoYlU4VyXo/m/t9tP-juWCAAJ\n   - https://groups.google.com/g/pyafipws/c/UyjEFACH_eI/m/iPvDpG2wEAAJ\n \n-- [ ] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n+- [x] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n   MS: Poder utilizar la librería AFIPSDK con los cambios en BETA\n - [x] [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n   MS: Pasar lo que ya está en ALFA para poder esperar aprobación con LAMBDA\n+- [ ] [ARCA AFIPSDK Cotización](https://gitlab.com/saasargentina/app/-/issues/2171)\n+  MS: Poder facturar en dólares con la cotización oficial\n+\n - [ ] [ARCA Lambda PyAFIPws](https://gitlab.com/saasargentina/app/-/issues/2166)\n   MS: Tener un plan B con PyAFIPws por si algo sale mal con AFIPSDK\n-- [ ] Agregar issue para envio la cotizacion correcta consultando la oficial (Importe_Cotizacion = WSFE.ParamGetCotizacion(MonedaID,fecha))\n-  MS: Poder facturar en dólares\n - [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n   MS: Poder sacar pyafipws anterior y quizás Python completo\n - [ ] [ARCA Lambda AFIPSDK](https://gitlab.com/saasargentina/app/-/issues/2088) funcionando en ALFA\n   MS: Tener todo funcionando en Lambdas\n"}, {"date": 1753966618062, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,11 +24,15 @@\n - [x] [ARCA Actualizar condiciones](https://gitlab.com/saasargentina/app/-/issues/2160)\n   MS: Poder utilizar la librería AFIPSDK con los cambios en BETA\n - [x] [ARCA Distintas librerías](https://gitlab.com/saasargentina/app/-/issues/2114)\n   MS: Pasar lo que ya está en ALFA para poder esperar aprobación con LAMBDA\n+\n+Antes de seguir, aprovecho para actualizar PHP8\n+- [ ] [Nueva EC2 con PHP8 y Python3](https://gitlab.com/saasargentina/app/-/issues/1981)\n+\n+\n - [ ] [ARCA AFIPSDK Cotización](https://gitlab.com/saasargentina/app/-/issues/2171)\n   MS: Poder facturar en dólares con la cotización oficial\n-\n - [ ] [ARCA Lambda PyAFIPws](https://gitlab.com/saasargentina/app/-/issues/2166)\n   MS: Tener un plan B con PyAFIPws por si algo sale mal con AFIPSDK\n - [ ] Agregar QR también con AFIPSDK (https://afipsdk.com/blog/crear-qr-de-afip-en-php/) y buscar que otros métodos hay que migrar\n   MS: Poder sacar pyafipws anterior y quizás Python completo\n"}], "date": 1750365917742, "name": "Commit-0", "content": ""}]}