{"sourceFile": "BRAIN/ROADS/SAAS/DEPLOY.md", "activeCommit": 0, "commits": [{"activePatchIndex": 22, "patches": [{"date": 1725454943286, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1725455493534, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,9 @@\n <li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n <li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li>\n <li>Agregamos la opción de multimoneda a todos los informes.</li>\n <li>Nueva integración con el sistema Más Pedidos (https://maspedidos.com.ar).</li>\n+<li>Agregamos la posibilidad de configurar y facturar con varios tipos de documentos nuevos y en distintas monedas.</li>\n <li>Agregamos varios campos solicitados por usuarios a distintos informes.</li>\n <li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li>\n <li>Realizamos mejoras en el importador para que sea más eficiente.</li>\n </ul>\n"}, {"date": 1725455514800, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,11 +5,11 @@\n \n <h4>Versión 3.17 (14-11-2024)</h4><ul>\n <li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n <li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li>\n-<li>Agregamos la opción de multimoneda a todos los informes.</li>\n <li>Nueva integración con el sistema Más Pedidos (https://maspedidos.com.ar).</li>\n <li>Agregamos la posibilidad de configurar y facturar con varios tipos de documentos nuevos y en distintas monedas.</li>\n+<li>Agregamos la opción de multimoneda a todos los informes.</li>\n <li>Agregamos varios campos solicitados por usuarios a distintos informes.</li>\n <li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li>\n <li>Realizamos mejoras en el importador para que sea más eficiente.</li>\n </ul>\n"}, {"date": 1725456520840, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,15 @@\n ***************************************************************************************\n   NEXT DEPLOY\n ***************************************************************************************\n \n+## TODO EN DEPLOY\n \n+- Sacar el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n+\n+\n+## MENSAJES\n+\n <h4>Versión 3.17 (14-11-2024)</h4><ul>\n <li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n <li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li>\n <li>Nueva integración con el sistema Más Pedidos (https://maspedidos.com.ar).</li>\n"}, {"date": 1725490921979, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,9 @@\n ***************************************************************************************\n \n ## TODO EN DEPLOY\n \n-- Sacar el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n+- Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n \n \n ## MENSAJES\n \n"}, {"date": 1726158237595, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,12 @@\n ***************************************************************************************\n   NEXT DEPLOY\n ***************************************************************************************\n \n-## TODO EN DEPLOY\n+## TODO EN DEPLOY EN PROD\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n+- Agregar en abonos.php el campo de tipo de documento\n \n \n ## MENSAJES\n \n"}, {"date": 1726158267163, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n \n ## TODO EN DEPLOY EN PROD\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n-- Agregar en abonos.php el campo de tipo de documento\n+- Agregar en abonos.php el campo de tipo de documento (buscar `INSERT INTO ventas`)\n \n \n ## MENSAJES\n \n"}, {"date": 1726158292025, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n \n ## TODO EN DEPLOY EN PROD\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n-- Agregar en abonos.php el campo de tipo de documento (buscar `INSERT INTO ventas`)\n+- Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n \n \n ## MENSAJES\n \n"}, {"date": 1726158338297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n ## TODO EN DEPLOY EN PROD\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n - Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n+- Descomentar en exportar.php y exportar_saas.php\n \n \n ## MENSAJES\n \n"}, {"date": 1727786303724, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n - Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n - Descomentar en exportar.php y exportar_saas.php\n+- Actualizar scripts de abonos, especialmente el de saas, para que pongan tipodoc\n \n \n ## MENSAJES\n \n"}, {"date": 1727960213264, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,12 @@\n - Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n - Descomentar en exportar.php y exportar_saas.php\n - Actualizar scripts de abonos, especialmente el de saas, para que pongan tipodoc\n \n+## TODO EN DEPLOY ALFA EN BETA\n \n+- Avisar al que pidió unificar productos en ventas\n+\n ## MENSAJES\n \n <h4>Versión 3.17 (14-11-2024)</h4><ul>\n <li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n"}, {"date": 1728612156556, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,29 @@\n ***************************************************************************************\n   NEXT DEPLOY\n ***************************************************************************************\n \n+- [ ] Revisar las clases nuevo-menu\n+- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [ ] Actualizar el archivo version\n+- [ ] Actualizar saas_hash\n+\n+- [ ] Tag a la versión y merge donde corresponda\n+- [ ] Deploy app\n+- [ ] DEPLOY INFORMES\n+- [ ] Ejecutar el script de MIGRATIONS\n+\n+- [ ] Mover los issues en el board\n+\n+- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n+- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n+\n+- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n+- [ ] Mandar información a Pablo para marketing\n+- [ ] Escribir en mis redes como estuvo el sprint\n+\n+\n ## TODO EN DEPLOY EN PROD\n \n - Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n - Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n@@ -26,9 +47,9 @@\n <li>Realizamos mejoras en el importador para que sea más eficiente.</li>\n </ul>\n <hr>\n \n-./command.php beta migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n+./command.php prod migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n \n \n ## EN INFORMES MODIFICAR EL .ENV\n \n"}, {"date": 1728613479587, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,78 +1,20 @@\n ***************************************************************************************\n   NEXT DEPLOY\n ***************************************************************************************\n \n-- [ ] Revisar las clases nuevo-menu\n-- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [ ] Actualizar el archivo version\n-- [ ] Actualizar saas_hash\n \n-- [ ] Tag a la versión y merge donde corresponda\n-- [ ] Deploy app\n-- [ ] DEPLOY INFORMES\n-- [ ] Ejecutar el script de MIGRATIONS\n-\n-- [ ] Mover los issues en el board\n-\n-- [ ] Escribir el mensaje de actualización con capturas de pantalla\n-- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n-- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n-\n-- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Mandar información a Pablo para marketing\n-- [ ] Escribir en mis redes como estuvo el sprint\n-\n-\n-## TODO EN DEPLOY EN PROD\n-\n-- Sacar en API el $this->beta, asignar a $cliente['tipodoc'] y cambiar el sql por\n-- Agregar en abonos.php, numeracion.php y en scripts empresas el campo de tipo de documento (buscar `INSERT INTO ventas`)\n-- Descomentar en exportar.php y exportar_saas.php\n-- Actualizar scripts de abonos, especialmente el de saas, para que pongan tipodoc\n-\n ## TODO EN DEPLOY ALFA EN BETA\n \n - Avisar al que pidió unificar productos en ventas\n \n+\n ## MENSAJES\n \n-<h4>Versión 3.17 (14-11-2024)</h4><ul>\n-<li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n-<li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li>\n-<li>Nueva integración con el sistema Más Pedidos (https://maspedidos.com.ar).</li>\n-<li>Agregamos la posibilidad de configurar y facturar con varios tipos de documentos nuevos y en distintas monedas.</li>\n-<li>Agregamos la opción de multimoneda a todos los informes.</li>\n-<li>Agregamos varios campos solicitados por usuarios a distintos informes.</li>\n-<li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li>\n-<li>Realizamos mejoras en el importador para que sea más eficiente.</li>\n-</ul>\n-<hr>\n-\n ./command.php prod migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n \n \n-## EN INFORMES MODIFICAR EL .ENV\n \n-# URLs varias sin / al final\n-URL_SAAS=https://app.saasargentina.com\n-URL_BETA=https://beta.saasargentina.com\n-URL_ALFA=https://alfa.saasargentina.com\n-\n-URL_INFORMES=https://informes.saasargentina.com\n-URL_INFORMES_BETA=https://informes-beta.saasargentina.com\n-URL_INFORMES_ALFA=https://informes-alfa.saasargentina.com\n-\n-URL_ESTILOS=https://app.saasargentina.com/estilos\n-URL_ESTILOS_BETA=https://beta.saasargentina.com/estilos\n-URL_ESTILOS_ALFA=https://alfa.saasargentina.com/estilos\n-\n-URL_ERROR=https://saasargentina.com/error\n-URL_S3=https://s3-sa-east-1.amazonaws.com/saasargentina/\n-\n-APP_ENV=beta\n-php artisan optimize\n-\n ***************************************************************************************\n   DEPLOY SAAS\n ***************************************************************************************\n - [ ] Revisar las clases nuevo-menu\n"}, {"date": 1728616764063, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,8 @@\n \n ./command.php prod migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n \n \n-\n ***************************************************************************************\n   DEPLOY SAAS\n ***************************************************************************************\n - [ ] Revisar las clases nuevo-menu\n"}, {"date": 1734032130119, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -59,10 +59,11 @@\n <p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n <hr>\n \n ```mysql\n-UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.13 (14-11-2024)</h4><ul>', texto) WHERE id = 4;\n+UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n ```\n+<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n \n ***************************************************************************************\n   BASH ONLINE\n ***************************************************************************************\n"}, {"date": 1747237726930, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,15 +3,32 @@\n ***************************************************************************************\n \n \n ## TODO EN DEPLOY ALFA EN BETA\n+- [ ] Revisar las clases nuevo-menu\n+- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [ ] Actualizar el archivo version\n+- [ ] Actualizar saas_hash\n \n-- Avisar al que pidió unificar productos en ventas\n+- [ ] Tag a la versión y merge donde corresponda\n+- [ ] Deploy app\n+- [ ] DEPLOY INFORMES\n+- [ ] Ejecutar el script de MIGRATIONS\n \n+- [ ] Mover los issues en el board\n \n+- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n+- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n+\n+- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n+- [ ] Mandar información a Pablo para marketing\n+- [ ] Escribir en mis redes como estuvo el sprint\n+\n+\n ## MENSAJES\n \n-./command.php prod migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n+./command.php prod migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n \n \n ***************************************************************************************\n   DEPLOY SAAS\n"}, {"date": 1747237735614, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,107 @@\n+***************************************************************************************\n+  NEXT DEPLOY\n+***************************************************************************************\n+\n+\n+## TODO EN DEPLOY ALFA EN BETA\n+- [x] Revisar las clases nuevo-menu\n+- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [ ] Actualizar el archivo version\n+- [ ] Actualizar saas_hash\n+\n+- [ ] Tag a la versión y merge donde corresponda\n+- [ ] Deploy app\n+- [ ] DEPLOY INFORMES\n+- [ ] Ejecutar el script de MIGRATIONS\n+\n+- [ ] Mover los issues en el board\n+\n+- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n+- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n+\n+- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n+- [ ] Mandar información a Pablo para marketing\n+- [ ] Escribir en mis redes como estuvo el sprint\n+\n+\n+## MENSAJES\n+\n+./command.php prod migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n+\n+\n+***************************************************************************************\n+  DEPLOY SAAS\n+***************************************************************************************\n+- [ ] Revisar las clases nuevo-menu\n+- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [ ] Actualizar el archivo version\n+- [ ] Actualizar saas_hash\n+\n+- [ ] Tag a la versión y merge donde corresponda\n+- [ ] Deploy app\n+- [ ] DEPLOY INFORMES\n+- [ ] Ejecutar el script de MIGRATIONS\n+\n+- [ ] Mover los issues en el board\n+\n+- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n+- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n+\n+- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n+- [ ] Mandar información a Pablo para marketing\n+- [ ] Escribir en mis redes como estuvo el sprint\n+\n+\n+***************************************************************************************\n+  MENSAJES\n+***************************************************************************************\n+PROD\n+./command.php prod migrate mensaje\n+'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n+</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n+\n+\n+BETA\n+./command.php beta migrate mensaje\n+'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>\n+</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href=\"mailto:<EMAIL>\"><EMAIL></a></i></p>'\n+\n+\n+MENSAJE PARA HISTORIAL\n+<h4>Versión 3.13 (14-11-2024)</h4><ul>\n+<li></li>\n+</ul>\n+<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n+<hr>\n+\n+```mysql\n+UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n+```\n+<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n+\n+***************************************************************************************\n+  BASH ONLINE\n+***************************************************************************************\n+alias l=\"ls -lha\"\n+alias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\n+alias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\n+alias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n+\n+alias saas=\"cd /saas/customer/services\"\n+alias acc=\"cd /saas/customer/services/acc\"\n+alias api=\"cd /saas/customer/services/api\"\n+alias app=\"cd /saas/customer/services/app\"\n+alias informes=\"cd /saas/customer/services/informes\"\n+alias login=\"cd /saas/customer/services/login\"\n+alias scripts=\"cd /saas/customer/services/scripts\"\n+alias www=\"cd /saas/customer/services/www\"\n+\n+function deploy () {\n+  sudo su\n+  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64\n+  chmod +x /saas/customer/services/acc/command.php\n+  exit\n+}\n+\n"}, {"date": 1747254099782, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,138 +4,31 @@\n \n \n ## TODO EN DEPLOY ALFA EN BETA\n - [x] Revisar las clases nuevo-menu\n-- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [ ] Actualizar el archivo version\n+- [x] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [x] Actualizar el archivo version\n - [ ] Actualizar saas_hash\n \n-- [ ] Tag a la versión y merge donde corresponda\n-- [ ] Deploy app\n-- [ ] DEPLOY INFORMES\n-- [ ] Ejecutar el script de MIGRATIONS\n+- [x] Tag a la versión y merge donde corresponda\n \n-- [ ] Mover los issues en el board\n-\n-- [ ] Escribir el mensaje de actualización con capturas de pantalla\n-- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n-- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n-\n-- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Mandar información a Pablo para marketing\n-- [ ] Escribir en mis redes como estuvo el sprint\n-\n-\n-## MENSAJES\n-\n-./command.php prod migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n-\n-\n-***************************************************************************************\n-  DEPLOY SAAS\n-***************************************************************************************\n-- [ ] Revisar las clases nuevo-menu\n-- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [ ] Actualizar el archivo version\n-- [ ] Actualizar saas_hash\n-\n-- [ ] Tag a la versión y merge donde corresponda\n - [ ] Deploy app\n - [ ] DEPLOY INFORMES\n - [ ] Ejecutar el script de MIGRATIONS\n-\n - [ ] Mover los issues en el board\n \n-- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [x] Escribir el mensaje de actualización con capturas de pantalla\n - [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n-- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n \n-- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Mandar información a Pablo para marketing\n-- [ ] Escribir en mis redes como estuvo el sprint\n-\n-\n-***************************************************************************************\n-  MENSAJES\n-***************************************************************************************\n-PROD\n-./command.php prod migrate mensaje\n-'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n-</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n-\n-\n-BETA\n-./command.php beta migrate mensaje\n-'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>\n-</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href=\"mailto:<EMAIL>\"><EMAIL></a></i></p>'\n-\n-\n-MENSAJE PARA HISTORIAL\n-<h4>Versión 3.13 (14-11-2024)</h4><ul>\n-<li></li>\n-</ul>\n-<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n-<hr>\n-\n-```mysql\n-UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n-```\n-<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n-\n-***************************************************************************************\n-  BASH ONLINE\n-***************************************************************************************\n-alias l=\"ls -lha\"\n-alias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\n-alias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\n-alias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n-\n-alias saas=\"cd /saas/customer/services\"\n-alias acc=\"cd /saas/customer/services/acc\"\n-alias api=\"cd /saas/customer/services/api\"\n-alias app=\"cd /saas/customer/services/app\"\n-alias informes=\"cd /saas/customer/services/informes\"\n-alias login=\"cd /saas/customer/services/login\"\n-alias scripts=\"cd /saas/customer/services/scripts\"\n-alias www=\"cd /saas/customer/services/www\"\n-\n-function deploy () {\n-  sudo su\n-  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64\n-  chmod +x /saas/customer/services/acc/command.php\n-  exit\n-}\n-\n-***************************************************************************************\n-  NEXT DEPLOY\n-***************************************************************************************\n-\n-\n-## TODO EN DEPLOY ALFA EN BETA\n-- [ ] Revisar las clases nuevo-menu\n-- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [ ] Actualizar el archivo version\n-- [ ] Actualizar saas_hash\n-\n-- [ ] Tag a la versión y merge donde corresponda\n-- [ ] Deploy app\n-- [ ] DEPLOY INFORMES\n-- [ ] Ejecutar el script de MIGRATIONS\n-\n-- [ ] Mover los issues en el board\n-\n-- [ ] Escribir el mensaje de actualización con capturas de pantalla\n-- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n - [ ] Mailing desde dentro de SaaS con capturas de pantalla\n-\n - [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Mandar información a Pablo para marketing\n+- [ ] Preparar información para MKT\n - [ ] Escribir en mis redes como estuvo el sprint\n \n \n ## MENSAJES\n \n-./command.php prod migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n+./command.php beta migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n \n \n ***************************************************************************************\n   DEPLOY SAAS\n@@ -156,9 +49,9 @@\n - [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n - [ ] Mailing desde dentro de SaaS con capturas de pantalla\n \n - [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Mandar información a Pablo para marketing\n+- [ ] Preparar información para MKT\n - [ ] Escribir en mis redes como estuvo el sprint\n \n \n ***************************************************************************************\n"}, {"date": 1747256552372, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,7 @@\n-***************************************************************************************\n+*******************************************************************************\n   NEXT DEPLOY\n-***************************************************************************************\n+*******************************************************************************\n \n \n ## TODO EN DEPLOY ALFA EN BETA\n - [x] Revisar las clases nuevo-menu\n"}, {"date": 1747256746103, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,9 @@\n-*******************************************************************************\n-  NEXT DEPLOY\n-*******************************************************************************\n+---\n+# DEPLOY\n+---\n \n+# NEXT DEPLOY\n \n ## TODO EN DEPLOY ALFA EN BETA\n - [x] Revisar las clases nuevo-menu\n - [x] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n@@ -30,10 +31,11 @@\n ./command.php beta migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n \n \n ***************************************************************************************\n-  DEPLOY SAAS\n-***************************************************************************************\n+\n+# DEPLOY SAAS\n+\n - [ ] Revisar las clases nuevo-menu\n - [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n - [ ] Actualizar el archivo version\n - [ ] Actualizar saas_hash\n"}, {"date": 1747256791251, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,9 @@\n \n # NEXT DEPLOY\n \n ## TODO EN DEPLOY ALFA EN BETA\n+\n - [x] Revisar las clases nuevo-menu\n - [x] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n - [x] Actualizar el archivo version\n - [ ] Actualizar saas_hash\n@@ -56,10 +57,11 @@\n - [ ] Escribir en mis redes como estuvo el sprint\n \n \n ***************************************************************************************\n-  MENSAJES\n-***************************************************************************************\n+\n+# MENSAJES\n+\n PROD\n ./command.php prod migrate mensaje\n '<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n </ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n@@ -82,11 +84,13 @@\n UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n ```\n <li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n \n+\n ***************************************************************************************\n-  BASH ONLINE\n-***************************************************************************************\n+\n+# BASH ONLINE\n+\n alias l=\"ls -lha\"\n alias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\n alias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\n alias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n"}, {"date": 1747272191416, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,90 @@\n+---\n+# DEPLOY\n+---\n+\n+# NEXT DEPLOY\n+\n+\n+## MENSAJES\n+\n+\n+\n+***************************************************************************************\n+\n+# DEPLOY SAAS\n+\n+- [ ] Revisar las clases nuevo-menu\n+- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n+- [ ] Actualizar el archivo version\n+- [ ] Actualizar saas_hash\n+\n+- [ ] Tag a la versión y merge donde corresponda\n+- [ ] Deploy app\n+- [ ] DEPLOY INFORMES\n+- [ ] Ejecutar el script de MIGRATIONS\n+\n+- [ ] Mover los issues en el board\n+\n+- [ ] Escribir el mensaje de actualización con capturas de pantalla\n+- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n+- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n+\n+- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n+- [ ] Preparar información para MKT\n+- [ ] Escribir en mis redes como estuvo el sprint\n+\n+\n+***************************************************************************************\n+\n+# MENSAJES\n+\n+PROD\n+./command.php prod migrate mensaje\n+'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n+</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n+\n+\n+BETA\n+./command.php beta migrate mensaje\n+'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>\n+</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href=\"mailto:<EMAIL>\"><EMAIL></a></i></p>'\n+\n+\n+MENSAJE PARA HISTORIAL\n+<h4>Versión 3.13 (14-11-2024)</h4><ul>\n+<li></li>\n+</ul>\n+<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n+<hr>\n+\n+```mysql\n+UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n+```\n+<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n+\n+\n+***************************************************************************************\n+\n+# BASH ONLINE\n+\n+alias l=\"ls -lha\"\n+alias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\n+alias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\n+alias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n+\n+alias saas=\"cd /saas/customer/services\"\n+alias acc=\"cd /saas/customer/services/acc\"\n+alias api=\"cd /saas/customer/services/api\"\n+alias app=\"cd /saas/customer/services/app\"\n+alias informes=\"cd /saas/customer/services/informes\"\n+alias login=\"cd /saas/customer/services/login\"\n+alias scripts=\"cd /saas/customer/services/scripts\"\n+alias www=\"cd /saas/customer/services/www\"\n+\n+function deploy () {\n+  sudo su\n+  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64\n+  chmod +x /saas/customer/services/acc/command.php\n+  exit\n+}\n+\n"}, {"date": 1748437169040, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,126 +3,14 @@\n ---\n \n # NEXT DEPLOY\n \n+- Avisar a Fernando de idempresa 12541 cuando esté lo de sincronizar productos con idlista y iddeposito (https://gitlab.com/saasargentina/app/-/issues/2147)\n \n-## MENSAJES\n \n-\n-\n-***************************************************************************************\n-\n-# DEPLOY SAAS\n-\n-- [ ] Revisar las clases nuevo-menu\n-- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [ ] Actualizar el archivo version\n-- [ ] Actualizar saas_hash\n-\n-- [ ] Tag a la versión y merge donde corresponda\n-- [ ] Deploy app\n-- [ ] DEPLOY INFORMES\n-- [ ] Ejecutar el script de MIGRATIONS\n-\n-- [ ] Mover los issues en el board\n-\n-- [ ] Escribir el mensaje de actualización con capturas de pantalla\n-- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n-- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n-\n-- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Preparar información para MKT\n-- [ ] Escribir en mis redes como estuvo el sprint\n-\n-\n-***************************************************************************************\n-\n-# MENSAJES\n-\n-PROD\n-./command.php prod migrate mensaje\n-'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n-</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n-\n-\n-BETA\n-./command.php beta migrate mensaje\n-'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>\n-</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href=\"mailto:<EMAIL>\"><EMAIL></a></i></p>'\n-\n-\n-MENSAJE PARA HISTORIAL\n-<h4>Versión 3.13 (14-11-2024)</h4><ul>\n-<li></li>\n-</ul>\n-<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n-<hr>\n-\n-```mysql\n-UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;\n-```\n-<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>\n-\n-\n-***************************************************************************************\n-\n-# BASH ONLINE\n-\n-alias l=\"ls -lha\"\n-alias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\n-alias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\n-alias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n-\n-alias saas=\"cd /saas/customer/services\"\n-alias acc=\"cd /saas/customer/services/acc\"\n-alias api=\"cd /saas/customer/services/api\"\n-alias app=\"cd /saas/customer/services/app\"\n-alias informes=\"cd /saas/customer/services/informes\"\n-alias login=\"cd /saas/customer/services/login\"\n-alias scripts=\"cd /saas/customer/services/scripts\"\n-alias www=\"cd /saas/customer/services/www\"\n-\n-function deploy () {\n-  sudo su\n-  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64\n-  chmod +x /saas/customer/services/acc/command.php\n-  exit\n-}\n-\n----\n-# DEPLOY\n----\n-\n-# NEXT DEPLOY\n-\n-## TODO EN DEPLOY ALFA EN BETA\n-\n-- [x] Revisar las clases nuevo-menu\n-- [x] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n-- [x] Actualizar el archivo version\n-- [ ] Actualizar saas_hash\n-\n-- [x] Tag a la versión y merge donde corresponda\n-\n-- [ ] Deploy app\n-- [ ] DEPLOY INFORMES\n-- [ ] Ejecutar el script de MIGRATIONS\n-- [ ] Mover los issues en el board\n-\n-- [x] Escribir el mensaje de actualización con capturas de pantalla\n-- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n-\n-- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n-- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n-- [ ] Preparar información para MKT\n-- [ ] Escribir en mis redes como estuvo el sprint\n-\n-\n ## MENSAJES\n \n-./command.php beta migrate mensaje '<h4>Versión 3.19 (14-05-2025)</h4><ul><li><b>Nuevo filtro de fechas</b>. Agregamos un nuevo filtro de fechas a los informes para que sea más fácil filtrar por los períodos más usados.</li><li>Nueva funcionalidad de <b>Unificar productos</b>. Ahora en las ventas se puede activar para que los productos iguales sean agrupados en una sóla línea.</li><li>Aplicamos varios cambios para cumplir con las nuevas resoluciones de ARCA</li><li>Convertimos nuestro sincronizador entre instancias para que ahora sea también multi-depósito.</li><li>Mejoramos nuestro informe de clientes deudores para que puedas ver las deudas con múltiples monedas discriminadas.</li><li>Agregamos notificaciones para stock mínimo de productos</li><li>Mejoramos la subida de archivos en nuestra versión para celular, para que puedas subir capturas y fotos directamente a cualquier cliente, producto, servicio, etc.</li></ul><hr>'\n \n-\n ***************************************************************************************\n \n # DEPLOY SAAS\n \n"}], "date": 1725454943286, "name": "Commit-0", "content": "***************************************************************************************\n  NEXT DEPLOY\n***************************************************************************************\n\n\n<h4>Versión 3.17 (14-11-2024)</h4><ul>\n<li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li>\n<li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li>\n<li>Agregamos la opción de multimoneda a todos los informes.</li>\n<li>Nueva integración con el sistema Más Pedidos (https://maspedidos.com.ar).</li>\n<li>Agregamos varios campos solicitados por usuarios a distintos informes.</li>\n<li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li>\n<li>Realizamos mejoras en el importador para que sea más eficiente.</li>\n</ul>\n<hr>\n\n./command.php beta migrate mensaje '<h4>Versión 3.17 (14-11-2024)</h4><ul><li><b>Nuevo módulo de Multimoneda</b>. Agregamos un módulo para gestionar múltiples monedas con sus cotizaciones.</li><li><b>Nuevo módulo de Cotizaciones en transacciones</b>. Ahora cada transacción que se realiza en cualquier parte del sistema, es convertida al valor correspondiente con la cotización actual de la moneda configurada.</li><li>Agregamos la opción de multimoneda a todos los informes.</li><li>Agregamos varios campos solicitados por usuarios a distintos informes.</li><li>Agregamos la opción de <b>Usar costos actuales</b> en las compras para actualizar el costo de los productos en la compras.</li><li>Realizamos mejoras en el importador para que sea más eficiente.</li></ul><hr>'\n\n\n## EN INFORMES MODIFICAR EL .ENV\n\n# URLs varias sin / al final\nURL_SAAS=https://app.saasargentina.com\nURL_BETA=https://beta.saasargentina.com\nURL_ALFA=https://alfa.saasargentina.com\n\nURL_INFORMES=https://informes.saasargentina.com\nURL_INFORMES_BETA=https://informes-beta.saasargentina.com\nURL_INFORMES_ALFA=https://informes-alfa.saasargentina.com\n\nURL_ESTILOS=https://app.saasargentina.com/estilos\nURL_ESTILOS_BETA=https://beta.saasargentina.com/estilos\nURL_ESTILOS_ALFA=https://alfa.saasargentina.com/estilos\n\nURL_ERROR=https://saasargentina.com/error\nURL_S3=https://s3-sa-east-1.amazonaws.com/saasargentina/\n\nAPP_ENV=beta\nphp artisan optimize\n\n***************************************************************************************\n  DEPLOY SAAS\n***************************************************************************************\n- [ ] Revisar las clases nuevo-menu\n- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql\n- [ ] Actualizar el archivo version\n- [ ] Actualizar saas_hash\n\n- [ ] Tag a la versión y merge donde corresponda\n- [ ] Deploy app\n- [ ] DEPLOY INFORMES\n- [ ] Ejecutar el script de MIGRATIONS\n\n- [ ] Mover los issues en el board\n\n- [ ] Escribir el mensaje de actualización con capturas de pantalla\n- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)\n- [ ] Mailing desde dentro de SaaS con capturas de pantalla\n\n- [ ] Informar en chat de desarrollo de la actualización a modo informativo\n- [ ] Mandar información a Pablo para marketing\n- [ ] Escribir en mis redes como estuvo el sprint\n\n\n***************************************************************************************\n  MENSAJES\n***************************************************************************************\nPROD\n./command.php prod migrate mensaje\n'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>\n</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p>'\n\n\nBETA\n./command.php beta migrate mensaje\n'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>\n</ul><p>Puede ver el <a href=\"ayudas.php?a=actualizaciones\">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href=\"mailto:<EMAIL>\"><EMAIL></a></i></p>'\n\n\nMENSAJE PARA HISTORIAL\n<h4>Versión 3.13 (14-11-2024)</h4><ul>\n<li></li>\n</ul>\n<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>\n<hr>\n\n```mysql\nUPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.13 (14-11-2024)</h4><ul>', texto) WHERE id = 4;\n```\n\n***************************************************************************************\n  BASH ONLINE\n***************************************************************************************\nalias l=\"ls -lha\"\nalias wsfe=\"cd /saas/customer/services/acc/empresas/wsfe/\"\nalias rece=\"sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py\"\nalias antiafip=\"sudo php -f /saas/customer/services/scripts/crontab/antiafip.php\"\n\nalias saas=\"cd /saas/customer/services\"\nalias acc=\"cd /saas/customer/services/acc\"\nalias api=\"cd /saas/customer/services/api\"\nalias app=\"cd /saas/customer/services/app\"\nalias informes=\"cd /saas/customer/services/informes\"\nalias login=\"cd /saas/customer/services/login\"\nalias scripts=\"cd /saas/customer/services/scripts\"\nalias www=\"cd /saas/customer/services/www\"\n\nfunction deploy () {\n  sudo su\n  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64\n  chmod +x /saas/customer/services/acc/command.php\n  exit\n}\n\n"}]}