{"sourceFile": "BRAIN/ROADS/SAAS/DOC.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1742902948675, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1742903316097, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,7 +4,13 @@\n ## INDICE CAMIÓN\n \n ## STACK\n \n+- <PERSON>stán completamente divididos los entornos beta y prod en las bases de datos, y las carpeta. Faltaría dividir en 2 ECs diferentes\n+- Estoy dividiendo en Lambda\n+\n ## GITS\n \n+- Los repos son los mismos, tenemos distintas ramas\n+-\n+\n ## DEPLOY\n"}], "date": 1742902948675, "name": "Commit-0", "content": "# 📦 ROADS > SAAS > DOC\n-------------------------------------------------------------------------------\n\n## INDICE CAMIÓN\n\n## STACK\n\n## GITS\n\n## DEPLOY\n"}]}