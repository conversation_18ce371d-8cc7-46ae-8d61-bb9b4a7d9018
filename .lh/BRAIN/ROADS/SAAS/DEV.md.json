{"sourceFile": "BRAIN/ROADS/SAAS/DEV.md", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1724937543108, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1724957553900, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,7 @@\n # 📦 ROADS > SAAS > DEV\n -------------------------------------------------------------------------------\n \n-\"No compres más de esto, porque es al pedo\"\n-\n-\n De <PERSON><PERSON><PERSON><PERSON>: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.\n \n Tipo de Doc avisar a Gaming\n \n"}, {"date": 1724972602449, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,7 @@\n # 📦 ROADS > SAAS > DEV\n -------------------------------------------------------------------------------\n \n-De Agust<PERSON>: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.\n \n Tipo de Doc avisar a Gaming\n \n -------------------------------------------------------------------------------\n@@ -166,9 +165,11 @@\n - Poder re-ordenar los productos dentro de una venta/compra (10624)\n - Ordenar fotos de los productos (Lucas Duxton)\n - En mi sistema de Proveedores – Cuentas a Pagar .. la pantalla de carga de comprobantes permite leer el código QR de un comprobante, y mediante el des encriptado es posible obtener el contenido del String. Descomponiendo el contenido me permite precargar la pantalla de ingreso de comprobantes con valores como Fechas, Ptos de Venta, Nro de Comprobantes, tomar el CUIT y verificar si existe en base de proveedores o realizar altas automáticas usando Consulta de Padrón A5. Para finalmente poder además constatar el comprobante en AFIP.. recuerden este método.\n - BackUp de Listas de precios en S3 exportable para cuando se cae SaaS (Diego NetPatagon)\n+- De Agustín Suarez: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.\n \n+\n ### SUGERENCIAS VARIAS YA PLANEADAS\n \n - Encontré otro sistema que factura automáticamente las compras de mercadolibre y se las adjunta al cliente en la compra. Me ahorra mucho tiempo eso. Con ustedes tenía que generar la factura, descargarla y adjuntarla yo en cada venta de ML\n - Múltiples remitos por factura\n"}, {"date": 1725207235732, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,11 @@\n # 📦 ROADS > SAAS > DEV\n -------------------------------------------------------------------------------\n \n+- [ ] En venta exportar aparece todavía el DNI\n+- [ ] Ver alguna forma de actualizar los precios al cambiar el dólar\n+- [ ] Tipo de Doc avisar a Gaming\n \n-Tipo de Doc avisar a Gaming\n \n -------------------------------------------------------------------------------\n ## NUEVOS ISSUES\n \n"}, {"date": 1725243801736, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n -------------------------------------------------------------------------------\n \n - [ ] En venta exportar aparece todavía el DNI\n - [ ] Ver alguna forma de actualizar los precios al cambiar el dólar\n+- [ ] Revisar el inventario que no pone valores\n - [ ] Tipo de Doc avisar a Gaming\n \n \n -------------------------------------------------------------------------------\n"}, {"date": 1725280515550, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -54,8 +54,13 @@\n - Crear eventos base para: llamado a la API, cerrar venta, desde cron. El evento puede ser solamente generar un log y luego enviarme un mail (ejecuta otro SQS)\n - Documentar posibilidades en una Nota para poder compartir a clientes que requieran scripts personalizados en Lambda (van a tener costo de desarrollo)\n - Publicar actualización para que el usuario final pueda entender cómo funciona y que beneficios le puede aportar\n \n+Documentación\n+\n+- Generar una documentación de como funciona y cómo solicitar una automatización\n+- Generar una documentación de cómo crear una automatización en PHP y enviarla por mail a desarrollo. Debería haber un listado de las variables ya cargadas y alguna forma de testearla en local\n+\n En un segundo issue\n \n - Crear un sistema de logs para los eventos en Cloudwatch\n - Crear una automatización que sólo sean notificar (por mail, por sistema y en un futuro por Whatsapp)\n"}, {"date": 1725281509149, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,8 +18,9 @@\n - ISSUE API V2 Listar ventas: Necesito sumar las ventas de una instancia en otra y recuperar los remitos para controlar la carga de camiones con colectores por tipo y nro de comprobante, fecha, cantidad, desde hasta\n - ISSUE API ML Envío de ML como producto: Listar las distintas formas de envío de ML y poder configurarlas como productos existentes, si no se especifica ningún producto no se carga el envío en el pedido\n - ISSUE API ML Pedido de ML como factura: Poder configurar que los pedidos de ML se carguen como facturas electrónicas\n - ISSUE API ML Datos de entrega: Agregar a las ventas (ver si también clientes) los datos de entrega de ML. En el caso de Uriel, la provincia a la que se envía es la que utilizan para contabilizar los impuestos de rentas\n+- ISSUE API ML Depósito de mercadería usada: Para que si una venta se hace con un producto que esté usado, salga de otro depósito que no sea el mismo que los productos nuevos\n - ISSUE API V2 Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito\n - ISSUE Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí\n - ISSUE API V2 Cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante\n - ISSUE de Comprobantes por Sucursal: Limitar los comprobantes que se pueden emitir por sucursal\n"}, {"date": 1725281526591, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -161,8 +161,11 @@\n \n Gezatek:\n - ISSUE de Sucursales: Limitar los comprobantes que se pueden emitir por sucursal\n \n+Gaming:\n+- ISSUE API ML Depósito de mercadería usada\n+\n <PERSON> Opentrace:\n - Por otro lado, tenemos automatizado la generación de facturas dado que se utiliza otra forma de facturar\n Por ejemplo, tenemos precios diferentes dependiendo de la cantidad del producto\n Y también esta el tema de acumular el mismo producto en un solo renglón si se ingresa varias veces al facturar\n"}, {"date": 1725286499416, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,11 @@\n \n - [ ] En venta exportar aparece todavía el DNI\n - [ ] Ver alguna forma de actualizar los precios al cambiar el dólar\n - [ ] Revisar el inventario que no pone valores\n+\n - [ ] Tipo de Doc avisar a Gaming\n+- [ ] Al modificar una venta de un servicio, no se ve el nombre del servicio\n \n \n -------------------------------------------------------------------------------\n ## NUEVOS ISSUES\n"}, {"date": 1725317255267, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n \n - [ ] En venta exportar aparece todavía el DNI\n - [ ] Ver alguna forma de actualizar los precios al cambiar el dólar\n - [ ] Revisar el inventario que no pone valores\n+- [ ] Al generar una venta relacionada en otra moneda, no se actualizan los precios\n \n - [ ] Tipo de Doc avisar a Gaming\n - [ ] Al modificar una venta de un servicio, no se ve el nombre del servicio como en ventas\n \n"}, {"date": 1725371240665, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,205 @@\n+# 📦 ROADS > SAAS > DEV\n+-------------------------------------------------------------------------------\n+\n+\n+- [x] Error en ventaspagos_altamod\n+- [ ] Mostrar el tipo de documento\n+- [ ] No se ejecutó el CRONTAB\n+\n+- [ ] Ver lo que pasé de ALFA a BETA para poner en Deploy y para avisar\n+- [ ] Tipo de Doc avisar a Gaming\n+- [ ] Ordenar feedback 10\n+\n+- [ ] En venta exportar aparece todavía el DNI\n+- [ ] Ver alguna forma de actualizar los precios al cambiar el dólar\n+- [ ] Revisar el inventario que no pone valores\n+- [ ] Al generar una venta relacionada en otra moneda, no se actualizan los precios\n+\n+- [ ] Al modificar una venta de un servicio, no se ve el nombre del servicio como en ventas\n+\n+\n+-------------------------------------------------------------------------------\n+## NUEVOS ISSUES\n+\n+- Re ordenar TODOS los issues según prioridad anual\n+- Ver como generar una API Beta de ML\n+- Ver como renombrar los recursos en la API V2\n+- ISSUE Cambiar máscara: https://beholdr.github.io/maska/v3/\n+- ISSUE API V2 Composiciones: Para poder listar, modificar, armar, o desarmar combos.\n+- ISSUE API V2 Listar ventas: Necesito sumar las ventas de una instancia en otra y recuperar los remitos para controlar la carga de camiones con colectores por tipo y nro de comprobante, fecha, cantidad, desde hasta\n+- ISSUE API ML Envío de ML como producto: Listar las distintas formas de envío de ML y poder configurarlas como productos existentes, si no se especifica ningún producto no se carga el envío en el pedido\n+- ISSUE API ML Pedido de ML como factura: Poder configurar que los pedidos de ML se carguen como facturas electrónicas\n+- ISSUE API ML Datos de entrega: Agregar a las ventas (ver si también clientes) los datos de entrega de ML. En el caso de Uriel, la provincia a la que se envía es la que utilizan para contabilizar los impuestos de rentas\n+- ISSUE API ML Depósito de mercadería usada: Para que si una venta se hace con un producto que esté usado, salga de otro depósito que no sea el mismo que los productos nuevos\n+- ISSUE API V2 Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito\n+- ISSUE Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí\n+- ISSUE API V2 Cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante\n+- ISSUE de Comprobantes por Sucursal: Limitar los comprobantes que se pueden emitir por sucursal\n+- ISSUE API V2 para seleccionar el tipo de venta en la API: Para que se pueda seleccionar el tipo de venta en la API y que se pueda hacer remitos, facturas y A o B según CUIT\n+- ISSUE API V2 para cargar pagos: Para que se puedan cargar pagos en la API, hay que ver que configuraciones se agregar para limitar o predeterminar la caja, forma de pago, etc.\n+- ISSUE de AUTOMATIZACIONES 1: con la info de acá abajo\n+- ISSUE de AUTOMATIZACIONES 2: con info de abajo\n+- ISSUE Partner MP: ahora ofrece toda una configuración para Partners que nos puede servir: https://http2.mlstatic.com/frontend-assets/partners-associations/templates/asociacion_bilateral.pdf\n+\n+\n+\n+-------------------------------------------------------------------------------\n+## MILESTONES DEV\n+\n+**Multimoneda**\n+\n+**Scripts**\n+\n+OBJETIVO: Poder agregar funcionalidades chicas pero útiles siendo ejecutadas por distintos eventos, todo automatizado y dentro de un módulo de automatizaciones. La tabla ya existe desde 2009 que me está esperando, pero ahora con AI, Zapier, nuestra API y todo lo que están pidiendo los clientes tiene lógica de hacerlo con serverles y con el paradigma \"Event-driven programming\".\n+\n+Los casos a tener en cuenta:\n+\n+- Ejecutar desde el cierre de una venta, la carga de un pago o cualquier acción de un usuario dentro del sistema\n+- Ejecutar desde scripts personalizados desde crontab o desde un clic manual\n+- Ejecutar desde un evento de la API (tanto V2 como ML)\n+- Ejecutar desde un pedido por chat o AI en el futuro\n+- Puede tener criterios, por ej. al vender x producto, al bajar el stock, al generarse una factura A desde la API a clientes de x categoría, etc.\n+\n+- Actualizar la tabla automatizaciones con los cambios para cubrir el Event-driven programming y los nuevos casos\n+- Cargar las automatizaciones en variables de sesión\n+- Crear la función despachar evento que revisa si está registrado el evento en la tabla automatizaciones y si está lo manda al Queue que corresponda, el cual se procesará en un Lambda\n+- Crear eventos base para: llamado a la API, cerrar venta, desde cron. El evento puede ser solamente generar un log y luego enviarme un mail (ejecuta otro SQS)\n+- Documentar posibilidades en una Nota para poder compartir a clientes que requieran scripts personalizados en Lambda (van a tener costo de desarrollo)\n+- Publicar actualización para que el usuario final pueda entender cómo funciona y que beneficios le puede aportar\n+\n+Documentación\n+\n+- Generar una documentación de como funciona y cómo solicitar una automatización\n+- Generar una documentación de cómo crear una automatización en PHP y enviarla por mail a desarrollo. Debería haber un listado de las variables ya cargadas y alguna forma de testearla en local\n+\n+En un segundo issue\n+\n+- Crear un sistema de logs para los eventos en Cloudwatch\n+- Crear una automatización que sólo sean notificar (por mail, por sistema y en un futuro por Whatsapp)\n+- Crear una automatización que sea contactar una API externa con parámetros configurables\n+- Crear tests con AI para estas automatizaciones y una documentación de como crearlos. Puede ser útil ejecutar los tests en local y en la nube desde el módulo de scripts\n+- Un evento debe poder llamar a otro en una cadena de eventos\n+\n+\n+**Sincronizar ML y API ML**\n+\n+OBJETIVO: Empezar a recuperar el mercado de empresas que se integran con MercadoLibre.\n+\n+\n+**Mejoras en nuestra API V2**\n+\n+OBJETIVO: Poder mejorar la API para que sea más fácil de programar para los programadores, más fácil de dar soporte para mí y más completa.\n+\n+\n+## VARIOS PARA PENSAR\n+\n+- Que se pueda ejecutar un script personalizado de crontab, también manualmente\n+\n+\n+-------------------------------------------------------------------\n+## PROBLEMAS ZUMBANDO\n+\n+### MAIL:\n+\n+Nombre de usuario de IAM: ses-smtp-user.20240702-164504\n+Nombre de usuario de SMTP: ********************\n+Contraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n+\n+NEW IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n+\n+EX IAM PARA SES\n+AWS_KEY: ********************\n+AWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n+\n+\n+### LAMDA:\n+\n+Acceso: https://saasargentina.signin.aws.amazon.com/console\n+User: empresa_9589_script_1\n+Password: qZbquV3&\n+Region: San Pablo (sa-east-1)\n+Lambda >> Functions: empresa_9589_script_1\n+\n+### EMPRESAS EN BETA Y ALFA\n+(98, 161, 2629)\n+(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, , 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664)\n+\n+\n+\n+\n+-------------------------------------------------------------------------------\n+# SUGERENCIAS DE USUARIO E INTERNAS\n+-------------------------------------------------------------------------------\n+\n+### VARIOS OTROS TEMAS PARA ISSUES FUTUROS:\n+\n+- Automatizaciones\n+- Sector para contadores: Listado de informes útiles, herramientas con archivos de AFIP, herramientas para balances y stock con inventario a fecha anterior\n+- Integración con Asisteclick tanto para nuestra atención, como para nuestros clientes\n+- Vendedores 2° etapa\n+  - Comisiones de vendedores\n+  - Ver los vendedores en la lista de Pedidos pendientes (cuando se activa a ver todos los usuarios), quizás puede ser configurable las columnas\n+- Trazabilidad\n+- Variaciones (incluyendo integración con ML)\n+- Sucursales: es agregar una dirección, agrupar usuarios (vendedores), cajas, depósitos y puntos de ventas. Se puede usar por una cuestión de permisos y de informes separados. Uriel pidió limitar los comprobantes según sucursal: https://mail.google.com/mail/u/0/#inbox/********************************\n+- App con las ventanas actuales\n+- Indicadores\n+- Agregar los pagos en la exportación de ventas: Recibimos\n+- Reflotar métricas\n+- Chatgpt con la ayuda de saas, luego como buscador y luego entendiendo comandos ( https://mail.google.com/mail/u/0/#inbox/******************************** )\n+\n+- Informe con fecha de última compra\n+- Para onboarding: https://mail.google.com/mail/u/0/#inbox/******************************** https://mail.google.com/mail/u/0/#search/openphone/FMfcgzGsltLlqHVVrQbLfxTlwRzKsDWR https://mail.google.com/mail/u/0/#inbox/******************************** https://mail.google.com/mail/u/0/#inbox/********************************\n+- Mejorar filtros de fechas\n+- Inteligencia en campos usados\n+- Configuración de notificaciones\n+- Archivar ventas viejas, especialmente a Uriel\n+- Contabilidad\n+\n+### ISSUES CON USUARIOS ESPERANDO\n+\n+Uriel:\n+- Informe o forma de descargar las recetas de los combos\n+- ISSUE para Envio de ML como producto: Cuando tengamos esto, ahí volvemos a ver si te cierra que se auto-cargue los pagos de MP\n+- ISSUE para Pedido de ML como factura: Lo que charlamos de que sea directamente una factura y se apruebe automáticamente\n+- ISSUE para Enviar por mail pedidos de ML: Hoy ya lo hace pero tenemos que enviarlo con la factura adjunta post-aprobación de AFIP\n+- ISSUE para Datos de entrega en todas las ventas: con esto resolveríamos el tema de la jurisdicción para rentas y si metemos algún script para mejorar el proceso de los envíos a Misiones\n+- ISSUE para Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito\n+- ISSUE para Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí\n+- ISSUE para cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante\n+\n+Gezatek:\n+- ISSUE de Sucursales: Limitar los comprobantes que se pueden emitir por sucursal\n+\n+Gaming:\n+- ISSUE API ML Depósito de mercadería usada\n+\n+Fernando Poggio Opentrace:\n+- Por otro lado, tenemos automatizado la generación de facturas dado que se utiliza otra forma de facturar\n+Por ejemplo, tenemos precios diferentes dependiendo de la cantidad del producto\n+Y también esta el tema de acumular el mismo producto en un solo renglón si se ingresa varias veces al facturar\n+Con tu sistema tendríamos una factura de muchos renglones dado que cada producto leído con el codigo de barras lo pone en un renglón individual\n+- ISSUE para listar ventas en la API\n+\n+### IDEAS DE CLIENTES SIN PLANEAR\n+\n+- Poder re-ordenar los productos dentro de una venta/compra (10624)\n+- Ordenar fotos de los productos (Lucas Duxton)\n+- En mi sistema de Proveedores – Cuentas a Pagar .. la pantalla de carga de comprobantes permite leer el código QR de un comprobante, y mediante el des encriptado es posible obtener el contenido del String. Descomponiendo el contenido me permite precargar la pantalla de ingreso de comprobantes con valores como Fechas, Ptos de Venta, Nro de Comprobantes, tomar el CUIT y verificar si existe en base de proveedores o realizar altas automáticas usando Consulta de Padrón A5. Para finalmente poder además constatar el comprobante en AFIP.. recuerden este método.\n+- BackUp de Listas de precios en S3 exportable para cuando se cae SaaS (Diego NetPatagon)\n+- De Agustín Suarez: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.\n+\n+\n+### SUGERENCIAS VARIAS YA PLANEADAS\n+\n+- Encontré otro sistema que factura automáticamente las compras de mercadolibre y se las adjunta al cliente en la compra. Me ahorra mucho tiempo eso. Con ustedes tenía que generar la factura, descargarla y adjuntarla yo en cada venta de ML\n+- Múltiples remitos por factura\n+- 10318 sugiere  \"si es posible agregar un link para tomar imagen desde camara directamente de tal manera de poder subirdirectamente laimagen yno tener que hacer el traspaso de imagenes cuando tenemos que subir muchas fotos\"\n+\n+### IDEAS PARA UN AI\n+\n+- Que detecte que productos se venden juntos y que sugiera combos\n+- Que detecte que productos no se están vendiendo o no se vendieron nunca y que sugiera bajar el precio o hacer una promoción\n+\n"}], "date": 1724937543108, "name": "Commit-0", "content": "# 📦 ROADS > SAAS > DEV\n-------------------------------------------------------------------------------\n\n\"No compres más de esto, porque es al pedo\"\n\n\nDe Agustín Suarez: Existe la posibilidad de que me quede siempre tildado este tilde, porque todas las fotos que subo son para enlace publico, asi no nos queda olvidado en la carga de imágenes.\n\nTipo de Doc avisar a Gaming\n\n-------------------------------------------------------------------------------\n## NUEVOS ISSUES\n\n- Re ordenar TODOS los issues según prioridad anual\n- Ver como generar una API Beta de ML\n- Ver como renombrar los recursos en la API V2\n- ISSUE Cambiar máscara: https://beholdr.github.io/maska/v3/\n- ISSUE API V2 Composiciones: Para poder listar, modificar, armar, o desarmar combos.\n- ISSUE API V2 Listar ventas: Necesito sumar las ventas de una instancia en otra y recuperar los remitos para controlar la carga de camiones con colectores por tipo y nro de comprobante, fecha, cantidad, desde hasta\n- ISSUE API ML Envío de ML como producto: Listar las distintas formas de envío de ML y poder configurarlas como productos existentes, si no se especifica ningún producto no se carga el envío en el pedido\n- ISSUE API ML Pedido de ML como factura: Poder configurar que los pedidos de ML se carguen como facturas electrónicas\n- ISSUE API ML Datos de entrega: Agregar a las ventas (ver si también clientes) los datos de entrega de ML. En el caso de Uriel, la provincia a la que se envía es la que utilizan para contabilizar los impuestos de rentas\n- ISSUE API V2 Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito\n- ISSUE Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí\n- ISSUE API V2 Cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante\n- ISSUE de Comprobantes por Sucursal: Limitar los comprobantes que se pueden emitir por sucursal\n- ISSUE API V2 para seleccionar el tipo de venta en la API: Para que se pueda seleccionar el tipo de venta en la API y que se pueda hacer remitos, facturas y A o B según CUIT\n- ISSUE API V2 para cargar pagos: Para que se puedan cargar pagos en la API, hay que ver que configuraciones se agregar para limitar o predeterminar la caja, forma de pago, etc.\n- ISSUE de AUTOMATIZACIONES 1: con la info de acá abajo\n- ISSUE de AUTOMATIZACIONES 2: con info de abajo\n- ISSUE Partner MP: ahora ofrece toda una configuración para Partners que nos puede servir: https://http2.mlstatic.com/frontend-assets/partners-associations/templates/asociacion_bilateral.pdf\n\n\n\n-------------------------------------------------------------------------------\n## MILESTONES DEV\n\n**Multimoneda**\n\n**Scripts**\n\nOBJETIVO: Poder agregar funcionalidades chicas pero útiles siendo ejecutadas por distintos eventos, todo automatizado y dentro de un módulo de automatizaciones. La tabla ya existe desde 2009 que me está esperando, pero ahora con AI, Zapier, nuestra API y todo lo que están pidiendo los clientes tiene lógica de hacerlo con serverles y con el paradigma \"Event-driven programming\".\n\nLos casos a tener en cuenta:\n\n- Ejecutar desde el cierre de una venta, la carga de un pago o cualquier acción de un usuario dentro del sistema\n- Ejecutar desde scripts personalizados desde crontab o desde un clic manual\n- Ejecutar desde un evento de la API (tanto V2 como ML)\n- Ejecutar desde un pedido por chat o AI en el futuro\n- Puede tener criterios, por ej. al vender x producto, al bajar el stock, al generarse una factura A desde la API a clientes de x categoría, etc.\n\n- Actualizar la tabla automatizaciones con los cambios para cubrir el Event-driven programming y los nuevos casos\n- Cargar las automatizaciones en variables de sesión\n- Crear la función despachar evento que revisa si está registrado el evento en la tabla automatizaciones y si está lo manda al Queue que corresponda, el cual se procesará en un Lambda\n- Crear eventos base para: llamado a la API, cerrar venta, desde cron. El evento puede ser solamente generar un log y luego enviarme un mail (ejecuta otro SQS)\n- Documentar posibilidades en una Nota para poder compartir a clientes que requieran scripts personalizados en Lambda (van a tener costo de desarrollo)\n- Publicar actualización para que el usuario final pueda entender cómo funciona y que beneficios le puede aportar\n\nEn un segundo issue\n\n- Crear un sistema de logs para los eventos en Cloudwatch\n- Crear una automatización que sólo sean notificar (por mail, por sistema y en un futuro por Whatsapp)\n- Crear una automatización que sea contactar una API externa con parámetros configurables\n- Crear tests con AI para estas automatizaciones y una documentación de como crearlos. Puede ser útil ejecutar los tests en local y en la nube desde el módulo de scripts\n- Un evento debe poder llamar a otro en una cadena de eventos\n\n\n**Sincronizar ML y API ML**\n\nOBJETIVO: Empezar a recuperar el mercado de empresas que se integran con MercadoLibre.\n\n\n**Mejoras en nuestra API V2**\n\nOBJETIVO: Poder mejorar la API para que sea más fácil de programar para los programadores, más fácil de dar soporte para mí y más completa.\n\n\n## VARIOS PARA PENSAR\n\n- Que se pueda ejecutar un script personalizado de crontab, también manualmente\n\n\n-------------------------------------------------------------------\n## PROBLEMAS ZUMBANDO\n\n### MAIL:\n\nNombre de usuario de IAM: ses-smtp-user.20240702-164504\nNombre de usuario de SMTP: ********************\nContraseña de SMTP: BAoe6XTojDuUn/CjayV5Cl3ZNe3UbassCeBJ7ZrKDtrg\n\nNEW IAM PARA SES\nAWS_KEY: ********************\nAWS_SECRET: BgCOVPS6y7qyz0XgcFJLTMZb9ItcMUKVI2jox2h6\n\nEX IAM PARA SES\nAWS_KEY: ********************\nAWS_SECRET: ZqjwYDTNLxLXikqSEyOuBEH5f1Co8ENrMQEKGEO+\n\n\n### LAMDA:\n\nAcceso: https://saasargentina.signin.aws.amazon.com/console\nUser: empresa_9589_script_1\nPassword: qZbquV3&\nRegion: San Pablo (sa-east-1)\nLambda >> Functions: empresa_9589_script_1\n\n### EMPRESAS EN BETA Y ALFA\n(98, 161, 2629)\n(168, 215, 761, 874, 901, 1387, 1548, 1991, 2030, , 2142, 2178, 2216, 3242, 3692, 3983, 4837, 5027, 6149, 6282, 6801, 7160, 7801, 9432, 9589, 9746, 9988, 10047, 10552, 10645, 10762, 10798, 11061, 11166, 11219, 11220, 11664)\n\n\n\n\n-------------------------------------------------------------------------------\n# SUGERENCIAS DE USUARIO E INTERNAS\n-------------------------------------------------------------------------------\n\n### VARIOS OTROS TEMAS PARA ISSUES FUTUROS:\n\n- Automatizaciones\n- Sector para contadores: Listado de informes útiles, herramientas con archivos de AFIP, herramientas para balances y stock con inventario a fecha anterior\n- Integración con Asisteclick tanto para nuestra atención, como para nuestros clientes\n- Vendedores 2° etapa\n  - Comisiones de vendedores\n  - Ver los vendedores en la lista de Pedidos pendientes (cuando se activa a ver todos los usuarios), quizás puede ser configurable las columnas\n- Trazabilidad\n- Variaciones (incluyendo integración con ML)\n- Sucursales: es agregar una dirección, agrupar usuarios (vendedores), cajas, depósitos y puntos de ventas. Se puede usar por una cuestión de permisos y de informes separados. Uriel pidió limitar los comprobantes según sucursal: https://mail.google.com/mail/u/0/#inbox/********************************\n- App con las ventanas actuales\n- Indicadores\n- Agregar los pagos en la exportación de ventas: Recibimos\n- Reflotar métricas\n- Chatgpt con la ayuda de saas, luego como buscador y luego entendiendo comandos ( https://mail.google.com/mail/u/0/#inbox/******************************** )\n\n- Informe con fecha de última compra\n- Para onboarding: https://mail.google.com/mail/u/0/#inbox/******************************** https://mail.google.com/mail/u/0/#search/openphone/FMfcgzGsltLlqHVVrQbLfxTlwRzKsDWR https://mail.google.com/mail/u/0/#inbox/******************************** https://mail.google.com/mail/u/0/#inbox/********************************\n- Mejorar filtros de fechas\n- Inteligencia en campos usados\n- Configuración de notificaciones\n- Archivar ventas viejas, especialmente a Uriel\n- Contabilidad\n\n### ISSUES CON USUARIOS ESPERANDO\n\nUriel:\n- Informe o forma de descargar las recetas de los combos\n- ISSUE para Envio de ML como producto: Cuando tengamos esto, ahí volvemos a ver si te cierra que se auto-cargue los pagos de MP\n- ISSUE para Pedido de ML como factura: Lo que charlamos de que sea directamente una factura y se apruebe automáticamente\n- ISSUE para Enviar por mail pedidos de ML: Hoy ya lo hace pero tenemos que enviarlo con la factura adjunta post-aprobación de AFIP\n- ISSUE para Datos de entrega en todas las ventas: con esto resolveríamos el tema de la jurisdicción para rentas y si metemos algún script para mejorar el proceso de los envíos a Misiones\n- ISSUE para Actualización de productos por API con mejor datos y especificando la lista de precios y el depósito\n- ISSUE para Depósito de Mercadería en tránsito o reservada: para que el stock de los traslados y de reserva de productos quede ahí\n- ISSUE para cancelar ML: para que se cancele automáticamente la venta en SaaS con otro comprobante\n\nGezatek:\n- ISSUE de Sucursales: Limitar los comprobantes que se pueden emitir por sucursal\n\nFernando Poggio Opentrace:\n- Por otro lado, tenemos automatizado la generación de facturas dado que se utiliza otra forma de facturar\nPor ejemplo, tenemos precios diferentes dependiendo de la cantidad del producto\nY también esta el tema de acumular el mismo producto en un solo renglón si se ingresa varias veces al facturar\nCon tu sistema tendríamos una factura de muchos renglones dado que cada producto leído con el codigo de barras lo pone en un renglón individual\n- ISSUE para listar ventas en la API\n\n### IDEAS DE CLIENTES SIN PLANEAR\n\n- Poder re-ordenar los productos dentro de una venta/compra (10624)\n- Ordenar fotos de los productos (Lucas Duxton)\n- En mi sistema de Proveedores – Cuentas a Pagar .. la pantalla de carga de comprobantes permite leer el código QR de un comprobante, y mediante el des encriptado es posible obtener el contenido del String. Descomponiendo el contenido me permite precargar la pantalla de ingreso de comprobantes con valores como Fechas, Ptos de Venta, Nro de Comprobantes, tomar el CUIT y verificar si existe en base de proveedores o realizar altas automáticas usando Consulta de Padrón A5. Para finalmente poder además constatar el comprobante en AFIP.. recuerden este método.\n- BackUp de Listas de precios en S3 exportable para cuando se cae SaaS (Diego NetPatagon)\n\n### SUGERENCIAS VARIAS YA PLANEADAS\n\n- Encontré otro sistema que factura automáticamente las compras de mercadolibre y se las adjunta al cliente en la compra. Me ahorra mucho tiempo eso. Con ustedes tenía que generar la factura, descargarla y adjuntarla yo en cada venta de ML\n- Múltiples remitos por factura\n- 10318 sugiere  \"si es posible agregar un link para tomar imagen desde camara directamente de tal manera de poder subirdirectamente laimagen yno tener que hacer el traspaso de imagenes cuando tenemos que subir muchas fotos\"\n\n### IDEAS PARA UN AI\n\n- Que detecte que productos se venden juntos y que sugiera combos\n- Que detecte que productos no se están vendiendo o no se vendieron nunca y que sugiera bajar el precio o hacer una promoción\n\n"}]}