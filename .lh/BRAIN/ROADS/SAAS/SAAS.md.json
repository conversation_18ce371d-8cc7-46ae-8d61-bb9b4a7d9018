{"sourceFile": "BRAIN/ROADS/SAAS/SAAS.md", "activeCommit": 0, "commits": [{"activePatchIndex": 5, "patches": [{"date": 1735965272776, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748287053396, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,12 +16,9 @@\n - MKT de micro nicho\n - Integraciones: nueva API, Logs, ML, etc\n \n \n-\n ## 🏄‍♂️ BOARDS\n \n-| Dev                                                                | Growth                                                        | Soporte                                                        |\n-| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n-| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n-| [BOARD](https://gitlab.com/saasargentina/app/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2318317139) | [TODO](https://app.todoist.com/app/project/soporte-2317711574) |\n-| [TODO](https://app.todoist.com/app/project/dev-2317711540)         |                                                               |                                                                |\n+[DEV](./DEV.md)\n+[GROWTH](./GROWTH.md)\n+[SOPORTE](./SOPORTE.md)\n"}, {"date": 1748287063893, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,6 +19,8 @@\n \n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md)\n+\n [GROWTH](./GROWTH.md)\n+\n [SOPORTE](./SOPORTE.md)\n"}, {"date": 1748615911742, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,9 +18,9 @@\n \n \n ## 🏄‍♂️ BOARDS\n \n-[DEV](./DEV.md)\n+[DEV](./DEV.md#milestones-dev)\n \n [GROWTH](./GROWTH.md)\n \n [SOPORTE](./SOPORTE.md)\n"}, {"date": 1752014085521, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,32 @@\n+# 📦 ROADS > SAAS\n+-------------------------------------------------------------------------------\n+\n+## 📊 PLAN\n+\n+[PLAN](./PLAN.md)\n+_En el plan se escribe la visión, misión y objetivos generales pero sin describir las tareas o prioridades_\n+\n+[KPIs](./KPIs.md)\n+_En los KPIs se escribe todo lo que se va a medir y como se va a medir_\n+\n+## PRÓXIMOS MILESTONES\n+\n+Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:\n+\n+**INTEGRACIONES**\n+OBJETIVO: Tener toda la información de las integraciones, un framework de como agregar una nueva y landigns de todas\n+\n+**API ARCA**\n+OBJETIVO: Poder integrar las nuevas obligaciones de ARCA y lograr independizarnos de PyAFIPws por el uso Python 3\n+\n+**SEPARADOR DE MILES**\n+OBJETIVO: Recuperar la comodidad de cargar números. Buscar librería de mascara, ver si html alcanza y preguntar a AI\n+\n+\n+## 🏄‍♂️ BOARDS\n+\n+[DEV](./DEV.md#milestones-dev)\n+\n+[GROWTH](./GROWTH.md)\n+\n+[SOPORTE](./SOPORTE.md)\n"}, {"date": 1752080864379, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,18 +12,13 @@\n ## PRÓXIMOS MILESTONES\n \n Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:\n \n-**INTEGRACIONES**\n-OBJETIVO: Tener toda la información de las integraciones, un framework de como agregar una nueva y landigns de todas\n+- [ ] INTEGRACIONES Tiendas #2152: Terminar las integraciones y landings\n+- [ ] ARCA Lambda AFIPSDK #2088: Pasar a producción todo lo necesario de ARCA\n+- [ ] AYUDA Ideas para actualizarla #1162: Actualizar la ayuda con vídeos\n \n-**API ARCA**\n-OBJETIVO: Poder integrar las nuevas obligaciones de ARCA y lograr independizarnos de PyAFIPws por el uso Python 3\n \n-**SEPARADOR DE MILES**\n-OBJETIVO: Recuperar la comodidad de cargar números. Buscar librería de mascara, ver si html alcanza y preguntar a AI\n-\n-\n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md#milestones-dev)\n \n"}], "date": 1735965272776, "name": "Commit-0", "content": "# 📦 ROADS > SAAS\n-------------------------------------------------------------------------------\n\n## 📊 PLAN\n\n[PLAN](./PLAN.md)\n\n[KPIs](./KPIs.md)\n\n\n## PRÓXIMOS MILESTONES\n\nLas 3 prioridades van a ser\n\n- Automatizaciones\n- MKT de micro nicho\n- Integraciones: nueva API, Logs, ML, etc\n\n\n\n## 🏄‍♂️ BOARDS\n\n| Dev                                                                | Growth                                                        | Soporte                                                        |\n| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n| [BOARD](https://gitlab.com/saasargentina/app/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2318317139) | [TODO](https://app.todoist.com/app/project/soporte-2317711574) |\n| [TODO](https://app.todoist.com/app/project/dev-2317711540)         |                                                               |                                                                |\n"}]}