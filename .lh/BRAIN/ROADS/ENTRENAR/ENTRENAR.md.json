{"sourceFile": "BRAIN/ROADS/ENTRENAR/ENTRENAR.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1748287797190, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1748287819586, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-# ENTRENAR\n+# 🏊‍♂️ 🏃 🏋️‍♂️ ROADS > ENTRENAR > ENTRENAR\n \n ## ORDENAR\n \n - La prioridad es calistenia con Mati\n"}, {"date": 1748287942648, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-# 🏊‍♂️ 🏃 🏋️‍♂️ ROADS > ENTRENAR > ENTRENAR\n+# 🏋️‍♂️ ROADS > ENTRENAR > ENTRENAR\n \n ## ORDENAR\n \n - La prioridad es calistenia con Mati\n"}], "date": 1748287797190, "name": "Commit-0", "content": "# ENTRENAR\n\n## ORDENAR\n\n- La prioridad es calistenia con Mati\n- Llevar archivo con plan y seguimiento de cada ejercicio\n- Por 3 meses no cambiar el plan\n- 3 veces por semana calistenia y 2 correr o nadar. Pero empiezo de a poco por un mes creando el hábito y midiendo sin avanzar.\n- Elijo los ejercicios de calistenia para la plaza o casa, primero los básicos en 3 dias según grupo muscular\n- Hago con YouTube a modo de recompensa\n- El horario puede ser a las 11 AM, sino a las 15 y sino a las 19 si o si\n\n\n## ELONGAR 🤸‍♂️\n\n1. Adho Mukha Virasana con estiramientos laterales\n1. Marjaryasana + Bitilasana con respiración controlada\n2. Squad con rotaciones torácicas y empuje con puños\n2. Sentadilla asiática con rotación al suelo y al cielo\n3. Ardha Uttanasana con mesa/pared y isquiotibiales al frente\n3. <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, gemelos y brazos\n4. Estiramientos natación: homb<PERSON>, espalda y manguito rotador con bandas ( https://www.youtube.com/watch?v=GIUow-xGDaU )\n4. Colgarse"}]}