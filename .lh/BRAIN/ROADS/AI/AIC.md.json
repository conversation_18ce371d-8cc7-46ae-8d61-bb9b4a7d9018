{"sourceFile": "BRAIN/ROADS/AI/AIC.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1735251447394, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1735251447394, "name": "Commit-0", "content": "# INTRODUCCIÓN\n\n- Todo va a cambiar\n\n## Tipos de comercio\n\n### Comercio tradicional\n\nCliente -> Charl<PERSON> | Proveedor <-> ERP\n\n### Comercio electrónico ( e-commerce )\n\nCliente <-> Tienda | ERP <-> Proveedor\n\n### Comercio electrónico con Chatbot\n\nCliente <-> Cha<PERSON>bot <-> Tienda | ERP <-> Proveedor\n\n### Comercio electrónico con Chatbot y AI\n\nCliente <-> AI <-> Tienda | ERP <-> AI <-> Proveedor\n\n## Infraestructura\n\n### Comercio tradicional\n\n- ERP instalado en la PC o a los sumo servidor local\n\n### Comercio electrónico ( e-commerce )\n\n- ERP en la nube con modalidad SaaS\n\n### Comercio electrónico con AI\n\n- ERP 100% serverless\n\n## Tipo de cobro\n\n### Comercio tradicional\n\n- Se cobra una licencia y luego actualizaciones\n\n### Comercio electrónico ( e-commerce )\n\n- Se cobra un abono mensual\n\n### Comercio electrónico con AI\n\n- Se cobra por uso o por cierre\n\n## Más temas para ampliar\n\n- Debería ser automático también el soporte, no hace falta vídeos ni manual\n- Se van a utilizar cada vez menos las interfaces gráficas, entre el Vision Pro de Apple y Neuralink de Musk, se van a poder hacer muchas cosas sin tocar nada\n- Whatsapp va a terminar como Wechat en China, donde se puede hacer de todo, desde pagar impuestos hasta pedir un taxi, por ende también como sistema de gestión\n- Yo arranco haciendo una prueba de concepto con las inscripciones de crono y una ayuda para SaaS. Pero creo que hay oportunidad para generar otra empresa sólo con esto.\n- Sin usuario y contraseña, se valida el mail o número de whatsapp y listo\n- Existe un sistema de embeddings en vector databases para coordinar el contexto y los datos van todos a No-SQL\n- Para no quedar pegado a ChatGPT, en el futuro hay que desarrollar conexiones a Brad u otros sistemas de AI. Aunque está claro que el futuro es ofrecer un servicio de AI a través de APIs\n- Se puede armar sistemas para distintos mercados, solamente adaptando el modelo de lenguaje y la base de datos para cada uno\n\n## Temas para ver\n\nhttps://www.cliengo.com/chatbot-gpt"}]}