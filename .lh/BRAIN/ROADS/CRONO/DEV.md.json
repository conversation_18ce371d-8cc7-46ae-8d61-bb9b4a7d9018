{"sourceFile": "BRAIN/ROADS/CRONO/DEV.md", "activeCommit": 0, "commits": [{"activePatchIndex": 148, "patches": [{"date": 1724702222253, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1724711060305, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,20 +2,12 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n-- [ ] Se está procesando otro informe de este evento\n-- [ ] Tengo un error en Sentry +1 del pico de ayer: https://cronometraje-instantaneo.sentry.io/issues/5721694387/?referrer=alert_email&alert_type=email&alert_timestamp=1724458545264&alert_rule_id=11588895&notification_uuid=acfdd964-1be0-4b41-8b0f-46cf507186d3&environment=production\n-- [ ] Limpiar lecturas con idevento o idcontrol = 0\n \n-### ITRA\n+### ORDENAR\n \n - [ ] Armar itra\n-- [ ] En duplas no se debería permitir tener dos nombres de equipos iguales\n-- [ ] Cuando se elimina un campo y se exporta se corren del Excel todos los campos de lo que se elimino\n-\n-### ORDENAR\n-\n - [ ] Pagos de Colombia también\n \n \n ### ADMIN Filament\n"}, {"date": 1724715004589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n \n \n ### ORDENAR\n \n+- [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Armar itra\n - [ ] Pagos de Colombia también\n \n \n"}, {"date": 1724723484243, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n \n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Armar itra\n - [ ] Pagos de Colombia también\n+- [ ] Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)\n \n \n ### ADMIN Filament\n ### NOVEDADES:\n"}, {"date": 1725287395522, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n+- [ ] Sigue habiendo Sentry\n \n ### ORDENAR\n \n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n"}, {"date": 1725310497027, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,10 +2,11 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n-- [ ] Sigue habiendo Sentry\n+- [ ] Apellidos y géneros\n \n+\n ### ORDENAR\n \n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Armar itra\n@@ -24,8 +25,9 @@\n - Reloj con múltiples parciales en vivo\n - Marcas de autos y motos\n - Mejorar diseño base ticket impreso\n - Reader Chafón\n+- Apellidos y géneros\n \n - Backup en vídeo\n - Datos extras tipo archivo\n - Velocidad promedio\n@@ -39,15 +41,9 @@\n \n - No puedo mover un evento a una organización, da error 500\n \n \n-### NUEVOS ERRORES\n \n-- Revisar que ayer aparecía mucho lo del cache con el cambio de\n-- El listado de categorías en las inscripciones tiene que también respetar el orden (ver en TeleQ Bike Fest)\n-- Hay de utf en Sentry https://cronometraje-instantaneo.sentry.io/issues/5647946960/?referrer=regression_activity-email&notification_uuid=551694aa-5be5-4e79-8245-49ca497ab07a\n-\n-\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Automatizar cuentas**\n"}, {"date": 1725311242412, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,9 @@\n - Marcas de autos y motos\n - Mejorar diseño base ticket impreso\n - Reader Chafón\n - Apellidos y géneros\n+- Selección del nombre de la Localidad\n \n - Backup en vídeo\n - Datos extras tipo archivo\n - Velocidad promedio\n"}, {"date": 1725391413589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n \n ## AHORA\n \n - [ ] Apellidos y géneros\n+- [ ] En los podios NO deberían aparecer los DNF\n \n \n ### ORDENAR\n \n"}, {"date": 1725491462821, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,21 +2,22 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n-- [ ] Apellidos y géneros\n+- [ ] Apellidos y géneros #247\n+- [ ] Ver si podemos poner las fotos en los tickets #292\n+- [ ] Armar itra\n - [ ] En los podios NO deberían aparecer los DNF\n+- [ ] Limpiar lecturas con idevento o idcontrol = 0\n+- [ ] Actualizar Novedades\n \n+## Terminar Nuevas Inscripciones\n \n-### ORDENAR\n+- [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n+- [ ] TV Toti y Costa Rica: uso los 2 widgets que ya tenemos uno abajo del otro 💪\n+- [ ] Terminar Oauth MP y generar documentación y MKT #264\n \n-- [ ] Limpiar lecturas con idevento o idcontrol = 0\n-- [ ] Armar itra\n-- [ ] Pagos de Colombia también\n-- [ ] Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)\n \n-\n-### ADMIN Filament\n ### NOVEDADES:\n \n - Nuevo Módulo de Inscripciones (buscar bien todo)\n - Hora de apertura y cierre de inscripciones\n@@ -33,13 +34,9 @@\n - Backup en vídeo\n - Datos extras tipo archivo\n - Velocidad promedio\n \n-### ORDENAR ÚLTIMO\n \n-- Ya se me ocurrió como hacerlo, uso los 2 widgets que ya tenemos uno abajo del otro 💪\n-\n-\n ### ADMIN Filament\n \n - No puedo mover un evento a una organización, da error 500\n \n@@ -134,4 +131,8 @@\n \n ### IDEAS MENORES\n \n Hay un listado de varias ideas sueltas para ordenar en [DEV Ideas Menores](./DEV%20Ideas%20Menores.md).\n+\n+### PEDIDOS PUNTUALES DE CLIENTES\n+\n+- OWA: Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)\n"}, {"date": 1725491471013, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Actualizar Novedades\n \n+\n ## Terminar Nuevas Inscripciones\n \n - [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n - [ ] TV Toti y Costa Rica: uso los 2 widgets que ya tenemos uno abajo del otro 💪\n"}, {"date": 1725493899541, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,11 @@\n \n ## AHORA\n \n - [ ] Apellidos y géneros #247\n+    - Test con cambios\n+    - Test en equipos\n+    - Test sin cambios\n - [ ] Ver si podemos poner las fotos en los tickets #292\n - [ ] Armar itra\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n"}, {"date": 1726069835544, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,19 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+# REFORMULAR TU ACEPTACIÓN DE PRIORIDADES\n+\n+- No puedo prometer funcionalidades, hay que resolverlo con lo que ya está\n+- La prioridad del otro, no es la mía\n+\n+\n ## AHORA\n \n+- [ ] Revisar error DH Andres Colombia\n+- [ ] Localidad para SARR\n+- [ ] Arrancar con cobros Colombia\n+\n - [ ] Apellidos y géneros #247\n     - Test con cambios\n     - Test en equipos\n     - Test sin cambios\n"}, {"date": 1726071266914, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,10 +53,13 @@\n ### ADMIN Filament\n \n - No puedo mover un evento a una organización, da error 500\n \n+### ERRORES O MINORS\n \n+- Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n \n+\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Automatizar cuentas**\n"}, {"date": 1726094425460, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,49 +8,27 @@\n \n \n ## AHORA\n \n-- [ ] Revisar error DH Andres Colombia\n-- [ ] Localidad para SARR\n-- [ ] Arrancar con cobros Colombia\n-\n - [ ] Apellidos y géneros #247\n     - Test con cambios\n     - Test en equipos\n     - Test sin cambios\n-- [ ] Ver si podemos poner las fotos en los tickets #292\n - [ ] Armar itra\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n-- [ ] Actualizar Novedades\n \n \n ## Terminar Nuevas Inscripciones\n \n - [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n - [ ] TV Toti y Costa Rica: uso los 2 widgets que ya tenemos uno abajo del otro 💪\n - [ ] Terminar <PERSON>h MP y generar documentación y MKT #264\n \n+- [ ] Ver si podemos poner las fotos en los tickets #292\n \n-### NOVEDADES:\n \n-- Nuevo Módulo de Inscripciones (buscar bien todo)\n-- Hora de apertura y cierre de inscripciones\n-- Fecha hasta en los eventos\n-- Agregamos estados a los eventos\n-- Agregamos estados a los pagos\n-- Reloj con múltiples parciales en vivo\n-- Marcas de autos y motos\n-- Mejorar diseño base ticket impreso\n-- Reader Chafón\n-- Apellidos y géneros\n-- Selección del nombre de la Localidad\n \n-- Backup en vídeo\n-- Datos extras tipo archivo\n-- Velocidad promedio\n-\n-\n ### ADMIN Filament\n \n - No puedo mover un evento a una organización, da error 500\n \n"}, {"date": 1726094785647, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,8 +15,9 @@\n     - Test sin cambios\n - [ ] Armar itra\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n+- [ ] Ocultar la opción de género que no existen en el sistema\n \n \n ## Terminar Nuevas Inscripciones\n \n"}, {"date": 1726179522419, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,9 @@\n - [ ] Apellidos y géneros #247\n     - Test con cambios\n     - Test en equipos\n     - Test sin cambios\n-- [ ] Armar itra\n+- [ ] Armar itra (Agregué columna de género, nacionalidad y marca como palabras)\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n"}, {"date": 1726256796344, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,19 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n+## PAGOS\n \n+- [ ] Escribir los pasos para generar el permiso\n+- [ ] Escribir las opciones de pagos que vas a haceptar\n+- [ ] Desarrollar los POSTS para Nequi\n+- [ ] Desarrollar la consulta de estado por POST de MP\n+- [ ] Desarrollar los POSTS para DLocalGo\n+- [ ] Desarrollar los POSTS para Paypal\n+- [ ] Desarrollar los POSTS para PayU\n+\n+\n ## Terminar Nuevas Inscripciones\n \n - [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n - [ ] TV Toti y Costa Rica: uso los 2 widgets que ya tenemos uno abajo del otro 💪\n"}, {"date": 1726257017469, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,8 +23,9 @@\n - [ ] Escribir los pasos para generar el permiso\n - [ ] Escribir las opciones de pagos que vas a haceptar\n - [ ] Desarrollar los POSTS para Nequi\n - [ ] Desarrollar la consulta de estado por POST de MP\n+- [ ] Pensar si podemos usar la misma tabla pagos para los eventos\n - [ ] Desarrollar los POSTS para DLocalGo\n - [ ] Desarrollar los POSTS para Paypal\n - [ ] Desarrollar los POSTS para PayU\n \n"}, {"date": 1726257062554, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,9 +23,9 @@\n - [ ] Escribir los pasos para generar el permiso\n - [ ] Escribir las opciones de pagos que vas a haceptar\n - [ ] Desarrollar los POSTS para Nequi\n - [ ] Desarrollar la consulta de estado por POST de MP\n-- [ ] Pensar si podemos usar la misma tabla pagos para los eventos\n+- [ ] Pensar si podemos usar la misma tabla pagos para los eventos y el mismo módulo de inscripciones\n - [ ] Desarrollar los POSTS para DLocalGo\n - [ ] Desarrollar los POSTS para Paypal\n - [ ] Desarrollar los POSTS para PayU\n \n"}, {"date": 1726266956780, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,8 +23,9 @@\n - [ ] Escribir los pasos para generar el permiso\n - [ ] Escribir las opciones de pagos que vas a haceptar\n - [ ] Desarrollar los POSTS para Nequi\n - [ ] Desarrollar la consulta de estado por POST de MP\n+\n - [ ] Pensar si podemos usar la misma tabla pagos para los eventos y el mismo módulo de inscripciones\n - [ ] Desarrollar los POSTS para DLocalGo\n - [ ] Desarrollar los POSTS para Paypal\n - [ ] Desarrollar los POSTS para PayU\n"}, {"date": 1726278533934, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -47,8 +47,9 @@\n \n ### ERRORES O MINORS\n \n - Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n+- Agregar el enlace de privado a los informes que son públicos\n \n \n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n"}, {"date": 1726278807100, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -46,10 +46,10 @@\n - No puedo mover un evento a una organización, da error 500\n \n ### ERRORES O MINORS\n \n-- Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n-- Agregar el enlace de privado a los informes que son públicos\n+- [x] Agregar el enlace de privado a los informes que son públicos\n+- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n \n \n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n"}, {"date": 1726329891116, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,8 +48,9 @@\n ### ERRORES O MINORS\n \n - [x] Ag<PERSON>gar el enlace de privado a los informes que son públicos\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n+- [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n \n \n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n"}, {"date": 1726406300466, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,9 +20,10 @@\n \n ## PAGOS\n \n - [ ] Escribir los pasos para generar el permiso\n-- [ ] Escribir las opciones de pagos que vas a haceptar\n+- [ ] Escribir las opciones de pagos que vas a aceptar\n+  - Acomodar los botones <a class=\"button\" href=\"https://mpago.la/2z9b8h9\" target=\"_blank\">Pagar</a>\n - [ ] Desarrollar los POSTS para Nequi\n - [ ] Desarrollar la consulta de estado por POST de MP\n \n - [ ] Pensar si podemos usar la misma tabla pagos para los eventos y el mismo módulo de inscripciones\n"}, {"date": 1726430946485, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,8 +48,18 @@\n \n ### ERRORES O MINORS\n \n - [x] Ag<PERSON>gar el enlace de privado a los informes que son públicos\n+- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+- [ ] Warnings\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n+\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n \n \n"}, {"date": 1726495363198, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,8 +62,13 @@\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n \n \n+## ORDENAR\n+\n+- Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n+\n+\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Automatizar cuentas**\n"}, {"date": 1726514390422, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,27 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-# REFORMULAR TU ACEPTACIÓN DE PRIORIDADES\n+### ERRORES O MINORS\n \n-- No puedo prometer funcionalidades, hay que resolverlo con lo que ya está\n-- La prioridad del otro, no es la mía\n+- [x] Agregar el enlace de privado a los informes que son públicos\n+- [ ] Tengo un montón de Sentry\n+- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+- [ ] Warnings\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n+- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n+- [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n \n-## AHORA\n \n+## TERMINAR APELLIDOS\n+\n - [ ] Apellidos y géneros #247\n     - Test con cambios\n     - Test en equipos\n     - Test sin cambios\n@@ -17,8 +29,20 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n+\n+### ADMIN Filament\n+\n+- No puedo mover un evento a una organización, da error 500\n+\n+\n+## ORDENAR\n+\n+- [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n+- [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n+\n+\n ## PAGOS\n \n - [ ] Escribir los pasos para generar el permiso\n - [ ] Escribir las opciones de pagos que vas a aceptar\n@@ -33,42 +57,13 @@\n \n \n ## Terminar Nuevas Inscripciones\n \n+- [ ] Terminar Oauth MP y generar documentación y MKT #264\n - [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n-- [ ] TV Toti y Costa Rica: uso los 2 widgets que ya tenemos uno abajo del otro 💪\n-- [ ] Terminar Oauth MP y generar documentación y MKT #264\n-\n - [ ] Ver si podemos poner las fotos en los tickets #292\n \n \n-\n-### ADMIN Filament\n-\n-- No puedo mover un evento a una organización, da error 500\n-\n-### ERRORES O MINORS\n-\n-- [x] Agregar el enlace de privado a los informes que son públicos\n-- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n-- [ ] Warnings\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n-\n-- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n-- [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n-\n-\n-## ORDENAR\n-\n-- Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n-\n-\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Automatizar cuentas**\n"}, {"date": 1726675840739, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,8 +16,9 @@\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] <PERSON>car \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n+- [ ] Filtrar el widget de podio por sexo\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1726748380050, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,8 +17,9 @@\n \n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Filtrar el widget de podio por sexo\n+- [ ] Agregar los {{nombre}} denuevos en las inscripciones\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1726749957665, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,8 +18,9 @@\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Filtrar el widget de podio por sexo\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n+- [ ] Agregar código de identificación en los MP como pidió Kittu\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1727275845443, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,9 @@\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Tengo un montón de Sentry\n+- [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n - [ ] Warnings\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n"}, {"date": 1727488670768, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,8 +21,9 @@\n - [ ] Filtrar el widget de podio por sexo\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n \n+- [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n \n ## TERMINAR APELLIDOS\n \n - [ ] Apellidos y géneros #247\n"}, {"date": 1727531663180, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,9 +37,14 @@\n \n \n ### ADMIN Filament\n \n+- Re-pensar el tema de la pertenencia de eventos\n+- No mostrar los menues si no tenes permisos\n - No puedo mover un evento a una organización, da error 500\n+- Filtrar por cronometrador\n+- Poder avisar pagos\n+- Pensar las comisiones\n \n \n ## ORDENAR\n \n"}, {"date": 1727531691165, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,9 +38,9 @@\n \n ### ADMIN Filament\n \n - Re-pensar el tema de la pertenencia de eventos\n-- No mostrar los menues si no tenes permisos\n+- Agregar los carteles para invitar a convertirte en organizador o cronometrador\n - No puedo mover un evento a una organización, da error 500\n - Filtrar por cronometrador\n - Poder avisar pagos\n - Pensar las comisiones\n"}, {"date": 1727531886505, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,14 +37,15 @@\n \n \n ### ADMIN Filament\n \n-- Re-pensar el tema de la pertenencia de eventos\n-- Agregar los carteles para invitar a convertirte en organizador o cronometrador\n-- No puedo mover un evento a una organización, da error 500\n-- Filtrar por cronometrador\n-- Poder avisar pagos\n-- Pensar las comisiones\n+- [ ] Re-pensar el tema de la pertenencia de eventos\n+- [ ] Actualizar Laravel y Filament\n+- [ ] Agregar los carteles para invitar a convertirte en organizador o cronometrador\n+- [ ] No puedo mover un evento a una organización, da error 500\n+- [ ] Filtrar por cronometrador\n+- [ ] Poder avisar pagos\n+- [ ] Pensar las comisiones para Ecuador y Andres\n \n \n ## ORDENAR\n \n"}, {"date": 1727537368573, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,13 +37,19 @@\n \n \n ### ADMIN Filament\n \n+\n+- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n+\n - [ ] Re-pensar el tema de la pertenencia de eventos\n-- [ ] Actualizar <PERSON> y Filament\n+\n - [ ] Agregar los carteles para invitar a convertirte en organizador o cronometrador\n - [ ] No puedo mover un evento a una organización, da error 500\n - [ ] Filtrar por cronometrador\n+\n+- [ ] Actualizar <PERSON> y Filament\n+\n - [ ] Poder avisar pagos\n - [ ] Pensar las comisiones para Ecuador y Andres\n \n \n@@ -66,49 +72,50 @@\n - [ ] Desarrollar los POSTS para Paypal\n - [ ] Desarrollar los POSTS para PayU\n \n \n-## Terminar Nuevas Inscripciones\n \n-- [ ] Terminar Oauth MP y generar documentación y MKT #264\n-- [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n-- [ ] Ver si podemos poner las fotos en los tickets #292\n \n-\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n-**Automatizar cuentas**\n-\n-OBJETIVO: automatizar los pagos, sincronizar SaaS. Todavía no entran usuarios finales, pero ya no hago nada manual\n-\n-- EVENTOS Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n-- AUTO-ADMIN #132: acomodamos todos los scripts\n-- PAGOS Automatizar #242: Terminar lo que ya avanzaste de DLocalGo\n-- EVENTOS Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n-\n-\n **Actualizar framework y mejorar diseño**\n \n OBJETIVO: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales\n \n-- [ ] FILAMENT Admin #276: poder generar el panel eventos y Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos\n+- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n - [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n-- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n+- [ ] FILAMENT Eventos: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos)\n+- [ ] FILAMENT Admin #276: poder generar el panel eventos y accesos rápidos a inscripciones, resultados y pagos\n \n **Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n \n - Limpiar los issues de features\n \n+**Automatizar cuentas**\n+\n+OBJETIVO: automatizar los pagos, sincronizar SaaS. Todavía no entran usuarios finales, pero ya no hago nada manual\n+\n+- EVENTOS Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n+- AUTO-ADMIN #132: acomodamos todos los scripts\n+- PAGOS Automatizar #242: Terminar lo que ya avanzaste de DLocalGo\n+- EVENTOS Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n+\n **Generar sector de Eventos en el sitio**\n \n OBJETIVO: Sector de eventos, ayudar configuración con equipo de Crono\n \n - EVENTOS Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos\n - EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos\n \n+**Terminar Nuevas Inscripciones**\n+\n+- [ ] Terminar Oauth MP y generar documentación y MKT #264\n+- [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n+- [ ] Ver si podemos poner las fotos en los tickets #292\n+\n **Métricas, Dashboard y KPIs automáticos con AI 📈**\n \n OBJETIVO: tener un dashboard con las métricas más importantes automatizadas\n \n"}, {"date": 1727537399641, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -84,9 +84,9 @@\n \n - [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n - [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n - [ ] FILAMENT Eventos: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos)\n-- [ ] FILAMENT Admin #276: poder generar el panel eventos y accesos rápidos a inscripciones, resultados y pagos\n+- [ ] FILAMENT Admin #276: poder generar el acceso al nuevo módulo de eventos\n \n **Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n"}, {"date": 1727537468896, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,41 +41,24 @@\n \n - [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n \n - [ ] Re-pensar el tema de la pertenencia de eventos\n+- [ ] Pensar las comisiones para Ecuador y Andres\n \n - [ ] Agregar los carteles para invitar a convertirte en organizador o cronometrador\n - [ ] No puedo mover un evento a una organización, da error 500\n - [ ] Filtrar por cronometrador\n \n - [ ] Actualizar Lara<PERSON> y Filament\n \n-- [ ] Poder avisar pagos\n-- [ ] Pensar las comisiones para Ecuador y Andres\n \n-\n ## ORDENAR\n \n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n \n \n-## PAGOS\n \n-- [ ] Escribir los pasos para generar el permiso\n-- [ ] Escribir las opciones de pagos que vas a aceptar\n-  - Acomodar los botones <a class=\"button\" href=\"https://mpago.la/2z9b8h9\" target=\"_blank\">Pagar</a>\n-- [ ] Desarrollar los POSTS para Nequi\n-- [ ] Desarrollar la consulta de estado por POST de MP\n-\n-- [ ] Pensar si podemos usar la misma tabla pagos para los eventos y el mismo módulo de inscripciones\n-- [ ] Desarrollar los POSTS para DLocalGo\n-- [ ] Desarrollar los POSTS para Paypal\n-- [ ] Desarrollar los POSTS para PayU\n-\n-\n-\n-\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Actualizar framework y mejorar diseño**\n"}, {"date": 1727537502117, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,9 +37,8 @@\n \n \n ### ADMIN Filament\n \n-\n - [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n \n - [ ] Re-pensar el tema de la pertenencia de eventos\n - [ ] Pensar las comisiones para Ecuador y Andres\n"}, {"date": 1727537542972, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,16 +37,13 @@\n \n \n ### ADMIN Filament\n \n-- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n-\n-- [ ] Re-pensar el tema de la pertenencia de eventos\n+- [ ] Re-pensar el tema de la pertenencia de eventos y de los módulos\n - [ ] Pensar las comisiones para Ecuador y Andres\n \n-- [ ] Agregar los carteles para invitar a convertirte en organizador o cronometrador\n - [ ] No puedo mover un evento a una organización, da error 500\n-- [ ] Filtrar por cronometrador\n+- [ ] Filtrar por cronometrador, organizador o por usuario\n \n - [ ] Actualizar <PERSON> y Filament\n \n \n"}, {"date": 1727616911022, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,12 @@\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Tengo un montón de Sentry\n+- [ ] Algo está generando picos ¿cómo puedo saber que es?\n+- [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+- [ ] Agregar el nombre de la etapa en el modificar tiempos\n+- [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumana\n - [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n - [ ] Warnings\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n"}, {"date": 1727616924431, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,9 @@\n - [ ] Tengo un montón de Sentry\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n-- [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumana\n+- [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n - [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n - [ ] Warnings\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n"}, {"date": 1727617582852, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,13 +41,11 @@\n \n \n ### ADMIN Filament\n \n-- [ ] Re-pensar el tema de la pertenencia de eventos y de los módulos\n-- [ ] Pensar las comisiones para Ecuador y Andres\n+- [ ] Generar módulo de\n+- [ ]\n \n-- [ ] No puedo mover un evento a una organización, da error 500\n-- [ ] Filtrar por cronometrador, organizador o por usuario\n \n - [ ] Actualizar Laravel y Filament\n \n \n@@ -64,12 +62,12 @@\n **Actualizar framework y mejorar diseño**\n \n OBJETIVO: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales\n \n-- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n+- [x] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n+- [ ] FILAMENT Admin #276: poder generar el acceso al nuevo módulo de eventos\n+- [ ] FILAMENT ABM Eventos #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n - [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n-- [ ] FILAMENT Eventos: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos)\n-- [ ] FILAMENT Admin #276: poder generar el acceso al nuevo módulo de eventos\n \n **Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n"}, {"date": 1727625884403, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,9 @@\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Tengo un montón de Sentry\n+- [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n"}, {"date": 1727632930848, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,8 +20,9 @@\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n+- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Filtrar el widget de podio por sexo\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n"}, {"date": 1727651316694, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,17 +41,8 @@\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n \n-### ADMIN Filament\n-\n-- [ ] Generar módulo de\n-- [ ]\n-\n-\n-- [ ] Actualizar Laravel y Filament\n-\n-\n ## ORDENAR\n \n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n"}, {"date": 1727723840005, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n"}, {"date": 1727736388580, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -46,8 +46,9 @@\n ## ORDENAR\n \n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n+- [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n \n \n -------------------------------------------------------------------------------\n"}, {"date": 1727794089098, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,14 @@\n \n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n+- [ ] Inscripciones con multicategoria\n+\n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Pasar chips al cronometrador o al organizador con un in\n+\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n"}, {"date": 1727875599867, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,8 +4,9 @@\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Inscripciones con multicategoria\n+- [ ] Largada y llegada mismo código\n \n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Pasar chips al cronometrador o al organizador con un in\n"}, {"date": 1727893370870, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Inscripciones con multicategoria\n - [ ] Largada y llegada mismo código\n+- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n \n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Pasar chips al cronometrador o al organizador con un in\n"}, {"date": 1727913300143, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,17 +4,18 @@\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [ ] Inscripciones con multicategoria\n+- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largada y llegada mismo código\n-- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n \n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n-- [ ] Pasar chips al cronometrador o al organizador con un in\n-\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+\n+- [ ] Pasar chips al cronometrador o al organizador con un in\n+- [ ] Sacar el genero Otro de Mixtos\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n - [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n"}, {"date": 1728052084778, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -47,9 +47,15 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n+## TERMINAR MULTI-CATEGORIA\n \n+- [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n+- [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n+- [ ] En el ticket digital se debería mostrar todos los resultados de todas las carreras o tener distintos tickets\n+\n+\n ## ORDENAR\n \n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n"}, {"date": 1728052094384, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,8 +36,9 @@\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n \n+\n ## TERMINAR APELLIDOS\n \n - [ ] Apellidos y géneros #247\n     - Test con cambios\n@@ -47,8 +48,9 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n+\n ## TERMINAR MULTI-CATEGORIA\n \n - [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n - [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n"}, {"date": 1728052625398, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,10 @@\n \n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n-- [ ] Inscripciones con multicategoria\n+- [x] Inscripciones con multicategoria\n+- [x] Sacar el genero Otro de Mixtos\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largada y llegada mismo código\n \n - [ ] Tengo un montón de Sentry\n@@ -13,9 +14,8 @@\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n \n - [ ] Pasar chips al cronometrador o al organizador con un in\n-- [ ] Sacar el genero Otro de Mixtos\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n - [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n"}, {"date": 1728065450585, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -35,8 +35,9 @@\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n+- [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1728324997323, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,10 @@\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largada y llegada mismo código\n+- [ ] Poner Nombre y Apellido al listado de participantes\n+- [ ] Largadas en las etapas y/o en las carreras\n \n - [ ] Tengo un montón de Sentry\n - [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n"}, {"date": 1728426879535, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n+- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n - [ ] Largada y llegada mismo código\n - [ ] Poner Nombre y Apellido al listado de participantes\n - [ ] Largadas en las etapas y/o en las carreras\n \n"}, {"date": 1728603148682, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,24 +5,30 @@\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n-- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n-- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n-- [ ] Largada y llegada mismo código\n-- [ ] Poner Nombre y Apellido al listado de participantes\n+\n - [ ] Largadas en las etapas y/o en las carreras\n \n+- [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+- [ ] Pasar chips al cronometrador o al organizador con un in\n+- [ ] Texto de inscripciones para carreras para Gaby\n+- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+\n+- [ ] Poner Nombre y Apellido al listado de participantes\n+- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n+\n - [ ] Tengo un montón de Sentry\n-- [ ] Avisar a capacitadores la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n-- [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n \n-- [ ] Pasar chips al cronometrador o al organizador con un in\n+- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n+\n+- [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+\n+- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n-- [ ] Sume 2 en lugar de 4 en FIM que en realidad no se sumaba\n-- [ ] Texto de inscripciones para carreras para Gaby\n-- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+- [ ] Largada y llegada mismo código\n+\n - [ ] Warnings\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n@@ -30,14 +36,14 @@\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n-- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Filtrar el widget de podio por sexo\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n+- [ ] El filtro de etapas no funciona si no está iniciado el evento\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n"}, {"date": 1728668949048, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,8 +42,9 @@\n - [ ] Filtrar el widget de podio por sexo\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n+- [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n"}, {"date": 1728753641592, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,11 @@\n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n \n+- [ ] Agrego estado a los precios\n+    ALTER TABLE `precios` ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`;\n+\n - [ ] Largadas en las etapas y/o en las carreras\n \n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n"}, {"date": 1728754503805, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,13 @@\n \n - [ ] Agrego estado a los precios\n     ALTER TABLE `precios` ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`;\n \n+ALTER TABLE `plataformas` CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n+UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n+UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n+\n+\n - [ ] Largadas en las etapas y/o en las carreras\n \n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n"}, {"date": 1728759310122, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -47,8 +47,9 @@\n \n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Filtrar el widget de podio por sexo\n+- [ ] Si pones un dato único que no está entre los datosxevento salta error\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n"}, {"date": 1728759889629, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,11 @@\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n \n - [ ] Agrego estado a los precios\n-    ALTER TABLE `precios` ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`;\n+    ALTER TABLE `precios`\n+    ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n+    ADD `titulo` VARCHAR(100) NULL DEFAULT NULL AFTER `estado`;\n \n ALTER TABLE `plataformas` CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n"}, {"date": 1728762101624, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,13 +7,21 @@\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n \n - [ ] Agrego estado a los precios\n-    ALTER TABLE `precios`\n+\n+ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n     ADD `titulo` VARCHAR(100) NULL DEFAULT NULL AFTER `estado`;\n \n-ALTER TABLE `plataformas` CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n+ALTER TABLE `carreras`\n+    ADD `inscripciones_texto` TEXT NULL DEFAULT NULL AFTER `suma_puntos`,\n+    ADD `inscripciones_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_texto`,\n+    ADD `mail_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_preinscripto`;\n+\n+ALTER TABLE `plataformas`\n+    CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n+\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n"}, {"date": 1728826393887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,27 @@\n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n \n+- [ ] Error que no se actualiza categorías cuando llegan todo el tiempo es por la ecuación\n+echo 'cache_vencido: '.($cache_vencido ? 'true' : 'false').'<br>';\n+echo 'timestamp: '.$timestamp.'<br>';\n+echo 'filemtime: '.filemtime($cache_file).'<br>';\n+echo 'time: '.time().'<br>';\n+echo 'delay: '.$delay.'<br>';\n+\n+\n+timestamp: 1728826143\n+filemtime: 1728825955\n+time:      1728826146\n+delay: 20\n+\n+file_exists($cache_file)\n+ ($timestamp > filemtime($cache_file)) && ((time() - $timestamp) > $delay)\n+\n+\n+\n+\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -18,8 +37,9 @@\n     ADD `inscripciones_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_texto`,\n     ADD `mail_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_preinscripto`;\n \n ALTER TABLE `plataformas`\n+    ADD `titulo` VARCHAR(100) NULL DEFAULT NULL AFTER `estado`;\n     CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n \n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n"}, {"date": 1728906929106, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -81,8 +81,9 @@\n - [ ] Si pones un dato único que no está entre los datosxevento salta error\n - [ ] Agregar los {{nombre}} denuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n+- [ ] Ver sponsors en las inscripciones\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n"}, {"date": 1729113701673, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,9 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n-- [ ] Largadas en las etapas y/o en las carreras\n+- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n \n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Texto de inscripciones para carreras para Gaby\n@@ -105,8 +105,12 @@\n \n - [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n - [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n - [ ] En el ticket digital se debería mostrar todos los resultados de todas las carreras o tener distintos tickets\n+- [ ] Probar de poner 2 categorias de la misma carrera a ver que pasa y si lo habilitamos o bloqueamos\n+- [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n+- [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n+- [ ] Ver como hacer las estadísticas con multicategoria\n \n \n ## ORDENAR\n \n"}, {"date": 1729209169391, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,11 +22,9 @@\n \n file_exists($cache_file)\n  ($timestamp > filemtime($cache_file)) && ((time() - $timestamp) > $delay)\n \n-\n-\n-\n+- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -44,46 +42,42 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n-- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n-\n - [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Texto de inscripciones para carreras para Gaby\n-- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n \n - [ ] Poner Nombre y Apellido al listado de participantes\n - [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n-\n - [ ] Tengo un montón de Sentry\n+- [ ] Warnings\n - [ ] Algo está generando picos ¿cómo puedo saber que es?\n+- [ ] Agregar el nombre de la etapa en el modificar tiempos\n+- [ ] Ver sponsors en las inscripciones\n \n+\n+### MEJORAS PEDIDAS\n+\n+- [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n+- [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n-\n+- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n \n-- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n-- [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Largada y llegada mismo código\n+- [ ] Largada por categorías para Quatro Vientos\n \n-- [ ] Warnings\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n+### SÓLO SI LLEGO DESPUÉS\n+\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n-- [ ] Filtrar el widget de podio por sexo\n - [ ] Si pones un dato único que no está entre los datosxevento salta error\n-- [ ] Agregar los {{nombre}} denuevos en las inscripciones\n+- [ ] Agregar los {{nombre}} de nuevos en las inscripciones\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n-- [ ] Ver sponsors en las inscripciones\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n \n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n@@ -118,9 +112,20 @@\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n \n+## WARNINGS\n \n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n+PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n+PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n+PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n+\n+\n+\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Actualizar framework y mejorar diseño**\n"}, {"date": 1729218838816, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,10 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+\n+\n+\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n@@ -123,9 +126,12 @@\n PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n \n \n+HYBRID INSCRIPCION INDIVIDUAL: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/TVY0VmllcFk0WXVLaHhvWWxWK2E2bkt1Um03STJadk84ZGRsbUdiZUcwb2kwMEFHSG1GeDFpK1VOenFMa2FnVw%3D%3D\n+HYBRID INSCRIPCION DUPLA: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/QWxRSUlyQ1BjNmJrYmQ5dVFRVWpZSnZ1ZWgxbHpWckt0dHdPVllXN01rRHFIZ2VpSXZ4dExCd3RwbExpWmthVg%3D%3D\n \n+\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Actualizar framework y mejorar diseño**\n"}, {"date": 1729256789340, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,8 @@\n -------------------------------------------------------------------------------\n \n \n \n-\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n"}, {"date": 1729338933360, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,25 +7,9 @@\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n-\n-- [ ] Error que no se actualiza categorías cuando llegan todo el tiempo es por la ecuación\n-echo 'cache_vencido: '.($cache_vencido ? 'true' : 'false').'<br>';\n-echo 'timestamp: '.$timestamp.'<br>';\n-echo 'filemtime: '.filemtime($cache_file).'<br>';\n-echo 'time: '.time().'<br>';\n-echo 'delay: '.$delay.'<br>';\n-\n-\n-timestamp: 1728826143\n-filemtime: 1728825955\n-time:      1728826146\n-delay: 20\n-\n-file_exists($cache_file)\n- ($timestamp > filemtime($cache_file)) && ((time() - $timestamp) > $delay)\n-\n+- [x] Error que no se actualiza categorías cuando llegan todo el tiempo es por la ecuación\n - [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n@@ -109,8 +93,9 @@\n \n \n ## ORDENAR\n \n+- [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n"}, {"date": 1729347224950, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,20 @@\n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n - [x] Sacar el genero Otro de Mixtos\n - [x] Error que no se actualiza categorías cuando llegan todo el tiempo es por la ecuación\n-- [ ] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+- [x] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n+- [x] Warnings\n+- [x] Algo está generando picos ¿cómo puedo saber que es?\n+- [x] Tengo un montón de Sentry\n+- [x] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+\n+- [ ] Poner Nombre y Apellido al listado de participantes\n+- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n+- [ ] Agregar el nombre de la etapa en el modificar tiempos\n+- [ ] Ver sponsors en las inscripciones\n+\n+\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -28,21 +39,12 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n-- [ ] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+- [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Pasar chips al cronometrador o al organizador con un in\n-- [ ] Texto de inscripciones para carreras para Gaby\n \n-- [ ] Poner Nombre y Apellido al listado de participantes\n-- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n-- [ ] Tengo un montón de Sentry\n-- [ ] Warnings\n-- [ ] Algo está generando picos ¿cómo puedo saber que es?\n-- [ ] Agregar el nombre de la etapa en el modificar tiempos\n-- [ ] Ver sponsors en las inscripciones\n \n-\n ### MEJORAS PEDIDAS\n \n - [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n@@ -99,19 +101,8 @@\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n \n-## WARNINGS\n-\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 631;\n-PHP message: PHP Warning:  Undefined array key \"94748-20\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 640;\n-PHP message: PHP Warning:  Undefined array key \"94748-1\" in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 828;\n-PHP message: PHP Warning:  Trying to access array offset on null in /var/www/cronometrajeinstantaneo/code/resultados/informes/generales.php on line 881;\n-\n-\n HYBRID INSCRIPCION INDIVIDUAL: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/TVY0VmllcFk0WXVLaHhvWWxWK2E2bkt1Um03STJadk84ZGRsbUdiZUcwb2kwMEFHSG1GeDFpK1VOenFMa2FnVw%3D%3D\n HYBRID INSCRIPCION DUPLA: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/QWxRSUlyQ1BjNmJrYmQ5dVFRVWpZSnZ1ZWgxbHpWckt0dHdPVllXN01rRHFIZ2VpSXZ4dExCd3RwbExpWmthVg%3D%3D\n \n \n"}, {"date": 1729347271243, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,10 +18,9 @@\n - [ ] Poner Nombre y Apellido al listado de participantes\n - [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Ver sponsors en las inscripciones\n-\n-\n+- [ ] Texto de inscripciones para carreras para Gaby\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -39,14 +38,12 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n-- [ ] Texto de inscripciones para carreras para Gaby\n-- [ ] Pasar chips al cronometrador o al organizador con un in\n \n-\n ### MEJORAS PEDIDAS\n \n+- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n"}, {"date": 1729351301912, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,9 +18,9 @@\n - [ ] Poner Nombre y Apellido al listado de participantes\n - [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [ ] Agregar el nombre de la etapa en el modificar tiempos\n - [ ] Ver sponsors en las inscripciones\n-- [ ] Texto de inscripciones para carreras para Gaby\n+- [x] Texto de inscripciones para carreras para Gaby\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n"}, {"date": 1729364819702, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+- [ ] Texto de inscripciones para carreras para Gaby\n \n \n ### ERRORES O MINORS\n \n@@ -13,14 +14,13 @@\n - [x] Warnings\n - [x] Algo está generando picos ¿cómo puedo saber que es?\n - [x] Tengo un montón de Sentry\n - [x] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n+- [x] Poner Nombre y Apellido al listado de participantes\n+- [x] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n+- [x] Agregar el nombre de la etapa en el modificar tiempos\n+- [x] Ver sponsors en las inscripciones\n \n-- [ ] Poner Nombre y Apellido al listado de participantes\n-- [ ] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n-- [ ] Agregar el nombre de la etapa en el modificar tiempos\n-- [ ] Ver sponsors en las inscripciones\n-- [x] Texto de inscripciones para carreras para Gaby\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -38,9 +38,31 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n+## TERMINAR APELLIDOS\n \n+- [ ] Apellidos y géneros #247\n+    - Test con cambios\n+    - Test en equipos\n+    - Test sin cambios\n+- [ ] Armar itra (Agregué columna de género, nacionalidad y marca como palabras)\n+- [ ] En los podios NO deberían aparecer los DNF\n+- [ ] Limpiar lecturas con idevento o idcontrol = 0\n+- [ ] Ocultar la opción de género que no existen en el sistema\n+\n+\n+## TERMINAR MULTI-CATEGORIA\n+\n+- [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n+- [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n+- [ ] En el ticket digital se debería mostrar todos los resultados de todas las carreras o tener distintos tickets\n+- [ ] Probar de poner 2 categorias de la misma carrera a ver que pasa y si lo habilitamos o bloqueamos\n+- [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n+- [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n+- [ ] Ver como hacer las estadísticas con multicategoria\n+\n+\n ### MEJORAS PEDIDAS\n \n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n@@ -48,9 +70,8 @@\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n-\n - [ ] Largada y llegada mismo código\n - [ ] Largada por categorías para Quatro Vientos\n \n \n@@ -67,31 +88,9 @@\n - [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n \n-## TERMINAR APELLIDOS\n \n-- [ ] Apellidos y géneros #247\n-    - Test con cambios\n-    - Test en equipos\n-    - Test sin cambios\n-- [ ] Armar itra (Agregué columna de género, nacionalidad y marca como palabras)\n-- [ ] En los podios NO deberían aparecer los DNF\n-- [ ] Limpiar lecturas con idevento o idcontrol = 0\n-- [ ] Ocultar la opción de género que no existen en el sistema\n-\n-\n-## TERMINAR MULTI-CATEGORIA\n-\n-- [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n-- [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n-- [ ] En el ticket digital se debería mostrar todos los resultados de todas las carreras o tener distintos tickets\n-- [ ] Probar de poner 2 categorias de la misma carrera a ver que pasa y si lo habilitamos o bloqueamos\n-- [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n-- [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n-- [ ] Ver como hacer las estadísticas con multicategoria\n-\n-\n ## ORDENAR\n \n - [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n"}, {"date": 1729364845365, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -49,9 +49,8 @@\n - [ ] En los podios NO deberían aparecer los DNF\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n-\n ## TERMINAR MULTI-CATEGORIA\n \n - [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n - [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n@@ -60,9 +59,11 @@\n - [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n - [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n - [ ] Ver como hacer las estadísticas con multicategoria\n \n+- [ ] ACTUALIZAR NOVEDADES\n \n+\n ### MEJORAS PEDIDAS\n \n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n"}, {"date": 1729438838959, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,8 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-- [ ] Texto de inscripciones para carreras para Gaby\n \n-\n ### ERRORES O MINORS\n \n - [x] Agregar el enlace de privado a los informes que son públicos\n - [x] Inscripciones con multicategoria\n@@ -19,8 +17,9 @@\n - [x] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [x] Agregar el nombre de la etapa en el modificar tiempos\n - [x] Ver sponsors en las inscripciones\n \n+- [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n"}, {"date": 1729521568656, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,9 +36,13 @@\n \n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n+- [ ] Pasar chips al cronometrador o al organizador con un in\n+    Necesito los DT000 en <NAME_EMAIL>\n+    Y los ES00000  en <EMAIL>\n \n+\n ## TERMINAR APELLIDOS\n \n - [ ] Apellidos y géneros #247\n     - Test con cambios\n@@ -63,9 +67,8 @@\n \n \n ### MEJORAS PEDIDAS\n \n-- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n"}, {"date": 1729541731622, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,8 +36,11 @@\n \n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n+\n+- [ ] Hay más Sentry\n+- [ ] Los totales de las estadísticas tienen algo raro\n - [ ] Pasar chips al cronometrador o al organizador con un in\n     Necesito los DT000 en <NAME_EMAIL>\n     Y los ES00000  en <EMAIL>\n \n"}, {"date": 1729543402406, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,8 +42,9 @@\n - [ ] Los totales de las estadísticas tienen algo raro\n - [ ] Pasar chips al cronometrador o al organizador con un in\n     Necesito los DT000 en <NAME_EMAIL>\n     Y los ES00000  en <EMAIL>\n+- [ ] QR para Claudio y para mí\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1729548355671, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,16 @@\n - [x] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [x] Agregar el nombre de la etapa en el modificar tiempos\n - [x] Ver sponsors en las inscripciones\n \n+- [x] Hay más Sentry\n+- [x] Los totales de las estadísticas tienen algo raro\n+\n+- [ ] Ver lo de Toti\n+- [ ] QR para Claudio y para mí\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n+- [ ] Ver lo de las pistolas para Ushuaia\n+- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -37,14 +44,8 @@\n UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n \n \n-- [ ] Hay más Sentry\n-- [ ] Los totales de las estadísticas tienen algo raro\n-- [ ] Pasar chips al cronometrador o al organizador con un in\n-    Necesito los DT000 en <NAME_EMAIL>\n-    Y los ES00000  en <EMAIL>\n-- [ ] QR para Claudio y para mí\n \n \n ## TERMINAR APELLIDOS\n \n"}, {"date": 1729603136404, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -68,8 +68,9 @@\n - [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n - [ ] Ver como hacer las estadísticas con multicategoria\n \n - [ ] ACTUALIZAR NOVEDADES\n+  - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n \n \n ### MEJORAS PEDIDAS\n \n"}, {"date": 1729606171582, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,13 +16,13 @@\n - [x] Poner Nombre y Apellido al listado de participantes\n - [x] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n - [x] Agregar el nombre de la etapa en el modificar tiempos\n - [x] Ver sponsors en las inscripciones\n-\n-- [x] Hay más Sentry\n - [x] Los totales de las estadísticas tienen algo raro\n+- [x] Ver lo de Toti\n \n-- [ ] Ver lo de Toti\n+- [ ] Hay más Sentry\n+- [ ] Ticket con cache al buscar\n - [ ] QR para Claudio y para mí\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n - [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Pasar chips al cronometrador o al organizador con un in\n"}, {"date": 1729606209071, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,8 +25,9 @@\n - [ ] QR para Claudio y para mí\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n - [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Pasar chips al cronometrador o al organizador con un in\n+- [ ] Ordenar llegada según timer\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n"}, {"date": 1729615718226, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,14 +18,19 @@\n - [x] Agregar el nombre de la etapa en el modificar tiempos\n - [x] Ver sponsors en las inscripciones\n - [x] Los totales de las estadísticas tienen algo raro\n - [x] Ver lo de Toti\n+- [x] Si pones un dato único que no está entre los datosxevento salta error\n \n-- [ ] Hay más Sentry\n-- [ ] Ticket con cache al buscar\n+- [x] Ticket con cache al buscar\n+- [x] Hay más Sentry\n - [ ] QR para Claudio y para mí\n+    - [ ] Generar qr\n+    - [ ] Chequear router de nube\n+    - [ ] Video de grabar chips para Macsha\n+    - [ ] Sumador de remeras en duplas en Duatlon\n+- [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n-- [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Ordenar llegada según timer\n - [ ] Agrego estado a los precios\n \n@@ -55,8 +60,9 @@\n     - Test en equipos\n     - Test sin cambios\n - [ ] Armar itra (Agregué columna de género, nacionalidad y marca como palabras)\n - [ ] En los podios NO deberían aparecer los DNF\n+- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n - [ ] Limpiar lecturas con idevento o idcontrol = 0\n - [ ] Ocultar la opción de género que no existen en el sistema\n \n ## TERMINAR MULTI-CATEGORIA\n@@ -68,42 +74,41 @@\n - [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n - [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n - [ ] Ver como hacer las estadísticas con multicategoria\n \n-- [ ] ACTUALIZAR NOVEDADES\n-  - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n+## ACTUALIZAR NOVEDADES\n \n+- [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n+- [ ] Apellidos y Géneros (Ver que ya debe estar)\n+- [ ] Multi-categoría\n \n+\n ### MEJORAS PEDIDAS\n \n-- [ ] Filtrar el widget de podio por sexo o lo que necesite Toti\n+- [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n-- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n-- [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n-- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n - [ ] Largada y llegada mismo código\n - [ ] Largada por categorías para Quatro Vientos\n+- [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n \n ### SÓLO SI LLEGO DESPUÉS\n \n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n-- [ ] Si pones un dato único que no está entre los datosxevento salta error\n - [ ] Agregar los {{nombre}} de nuevos en las inscripciones\n-- [ ] Agregar código de identificación en los MP como pidió Kittu\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n+- [ ] Agregar código de identificación en los MP como pidió Kittu\n \n-- [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n-- [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n \n \n-\n ## ORDENAR\n \n - [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n+- [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n"}, {"date": 1729627830463, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,12 +23,11 @@\n \n - [x] Ticket con cache al buscar\n - [x] Hay más Sentry\n - [ ] QR para Claudio y para mí\n-    - [ ] Generar qr\n-    - [ ] Chequear router de nube\n-    - [ ] Video de grabar chips para Macsha\n-    - [ ] Sumador de remeras en duplas en Duatlon\n+    - [x] Generar qr\n+    - [x] Borrar prueba 2024-09-22 Prueba huinganco\n+    - [x] Sumador de remeras en duplas en Duatlon\n - [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Ordenar llegada según timer\n"}, {"date": 1729628095532, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,9 +29,8 @@\n     - [x] Sumador de remeras en duplas en Duatlon\n - [ ] Ver lo de las pistolas para Ushuaia\n - [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n - [ ] Pasar chips al cronometrador o al organizador con un in\n-- [ ] Ordenar llegada según timer\n - [ ] Agrego estado a los precios\n \n ALTER TABLE `precios`\n     ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n@@ -83,8 +82,9 @@\n \n ### MEJORAS PEDIDAS\n \n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+- [ ] Ordenar llegada según timer\n - [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largada y llegada mismo código\n"}, {"date": 1729635352339, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,69 +1,7 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-\n-### ERRORES O MINORS\n-\n-- [x] Agregar el enlace de privado a los informes que son públicos\n-- [x] Inscripciones con multicategoria\n-- [x] Sacar el genero Otro de Mixtos\n-- [x] Error que no se actualiza categorías cuando llegan todo el tiempo es por la ecuación\n-- [x] Hay un error generando https://cronometrajeinstantaneo.com/resultados/4ta-valida-tco-gran-final-tco-kids-2024-xcc/categorias\n-- [x] Warnings\n-- [x] Algo está generando picos ¿cómo puedo saber que es?\n-- [x] Tengo un montón de Sentry\n-- [x] No hace backup de lecturas ni descarga (me pasó con Mendoza)\n-- [x] Poner Nombre y Apellido al listado de participantes\n-- [x] No funcionan los enlaces de inscripción con barra al final ( https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/ )\n-- [x] Agregar el nombre de la etapa en el modificar tiempos\n-- [x] Ver sponsors en las inscripciones\n-- [x] Los totales de las estadísticas tienen algo raro\n-- [x] Ver lo de Toti\n-- [x] Si pones un dato único que no está entre los datosxevento salta error\n-\n-- [x] Ticket con cache al buscar\n-- [x] Hay más Sentry\n-- [ ] QR para Claudio y para mí\n-    - [x] Generar qr\n-    - [x] Borrar prueba 2024-09-22 Prueba huinganco\n-    - [x] Sumador de remeras en duplas en Duatlon\n-- [ ] Ver lo de las pistolas para Ushuaia\n-- [ ] Texto de inscripciones para carreras para Gaby (Ver que funcione bien en Paso Austral)\n-- [ ] Pasar chips al cronometrador o al organizador con un in\n-- [ ] Agrego estado a los precios\n-\n-ALTER TABLE `precios`\n-    ADD `estado` TINYINT(1) NOT NULL DEFAULT '1' AFTER `idevento`\n-    ADD `titulo` VARCHAR(100) NULL DEFAULT NULL AFTER `estado`;\n-\n-ALTER TABLE `carreras`\n-    ADD `inscripciones_texto` TEXT NULL DEFAULT NULL AFTER `suma_puntos`,\n-    ADD `inscripciones_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_texto`,\n-    ADD `mail_preinscripto` TEXT NULL DEFAULT NULL AFTER `inscripciones_preinscripto`;\n-\n-ALTER TABLE `plataformas`\n-    ADD `titulo` VARCHAR(100) NULL DEFAULT NULL AFTER `estado`;\n-    CHANGE `plataforma` `plataforma` SET('efectivo','transferencia','paypal','payu','mercadopago','boton-mercadopago') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL;\n-\n-UPDATE `plataformas` SET `plataforma` = 'boton-mercadopago' WHERE `plataforma` = 'mercadopago';\n-UPDATE `plataformas` SET `plataforma` = 'mercadopago' WHERE `plataforma` = '';\n-\n-\n-\n-\n-## TERMINAR APELLIDOS\n-\n-- [ ] Apellidos y géneros #247\n-    - Test con cambios\n-    - Test en equipos\n-    - Test sin cambios\n-- [ ] Armar itra (Agregué columna de género, nacionalidad y marca como palabras)\n-- [ ] En los podios NO deberían aparecer los DNF\n-- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n-- [ ] Limpiar lecturas con idevento o idcontrol = 0\n-- [ ] Ocultar la opción de género que no existen en el sistema\n-\n ## TERMINAR MULTI-CATEGORIA\n \n - [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n - [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n@@ -72,8 +10,9 @@\n - [ ] Analizar cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n - [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n - [ ] Ver como hacer las estadísticas con multicategoria\n \n+\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n - [ ] Apellidos y Géneros (Ver que ya debe estar)\n@@ -81,8 +20,12 @@\n \n \n ### MEJORAS PEDIDAS\n \n+- [ ] Limpiar lecturas con idevento o idcontrol = 0\n+- [ ] En los podios NO deberían aparecer los DNF\n+- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n+- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Ordenar llegada según timer\n - [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n"}, {"date": 1729635510791, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,23 +1,11 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## TERMINAR MULTI-CATEGORIA\n \n-- [ ] En los textos de pantalla y mail se tienen que mostrar todas las categorías (en inscripciones y en el resultado)\n-- [ ] En el listado de participantes se tiene que mostrar los participantes en todas las carreras y categorías\n-- [ ] En el ticket digital se debería mostrar todos los resultados de todas las carreras o tener distintos tickets\n-- [ ] Probar de poner 2 categorias de la misma carrera a ver que pasa y si lo habilitamos o bloqueamos\n-- [ ] <PERSON><PERSON><PERSON> cambiar el botón de inscripción desde \"Agregar categoría\" por \"Agregar carrera y categoría\"\n-- [ ] Generar listado de participantes por carrera, duplicando el participante si está en varias categorías\n-- [ ] Ver como hacer las estadísticas con multicategoria\n-\n-\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n-- [ ] Apellidos y Géneros (Ver que ya debe estar)\n-- [ ] Multi-categoría\n \n \n ### MEJORAS PEDIDAS\n \n"}, {"date": 1729709205722, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,16 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## ADMIN\n \n+- Ver como estamos en FILAMENT y que me costaría menos\n+- Cambiar el informar por el sistema a informar por Whatsapp\n+- Pasar toda la aprobación al sistema (Filament o Admin)\n+- Hoy ya se tiene que poder ver todo desde Admin\n+\n+\n+\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n \n"}, {"date": 1729709300915, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,8 @@\n - Pasar toda la aprobación al sistema (Filament o Admin)\n - Hoy ya se tiene que poder ver todo desde Admin\n \n \n-\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n \n"}, {"date": 1729710039485, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,14 +2,25 @@\n -------------------------------------------------------------------------------\n \n ## ADMIN\n \n-- Ver como estamos en FILAMENT y que me costaría menos\n+- Me voy por Filament directamente\n - Cambiar el informar por el sistema a informar por Whatsapp\n-- Pasar toda la aprobación al sistema (Filament o Admin)\n-- Hoy ya se tiene que poder ver todo desde Admin\n+- Pasar toda la aprobación al sistema en Filament (Juli aprueba y mira todo ahí)\n+- Juli tiene acceso a los bancos y/o recibe redirectos los mails de los pagos\n+- Los usuarios ven la info desde Admin\n+- La carga en SaaS es automática\n \n+- [ ] Poder entrar desde el celular\n+- [ ] Bloquear lo que no está terminado\n+- [ ] Ver las CCs según el usuario posta (ver quien debe)\n+- [ ] Agregar descuentos en usuario y que se vea en Filament\n+- [ ] Agregar estado del pago parcial y en revisión\n+- [ ] Agregar los botones para aprobar los pagos\n+- [ ] Agregar mail de aprobación de los pagos\n+- [ ] Agregar aprobación automáticamente de los pagos\n \n+\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n \n"}, {"date": 1729722014486, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,18 +9,62 @@\n - Juli tiene acceso a los bancos y/o recibe redirectos los mails de los pagos\n - Los usuarios ven la info desde Admin\n - La carga en SaaS es automática\n \n-- [ ] Poder entrar desde el celular\n+UPDATE eventos SET user_id = 84, idcronometrador = 84 WHERE idorganizacion IN\n+(353, 355, 357, 358, 367, 374, 379, 422);\n+\n+INSERT IGNORE INTO user_organizacion (user_id, idorganizacion) VALUES\n+(84, 353), (84, 355), (84, 357), (84, 358), (84, 367), (84, 374), (84, 379), (84, 422);\n+\n+\n+## PRE-FILAMENT\n+\n+- [x] Mejorar visualización en celular\n+- [ ] Bloquear que no haya acceso a idevento sin permiso (en ambos paneles)\n - [ ] Bloquear lo que no está terminado\n+- [ ] Asinar organizadores de Esquel y probar todo (también Gaby, Zapala y Ecuador)\n+- [ ] Filtrar por cronometrador, organizador o por usuario y de esta semana (aunque tenga que cargar fecha)\n+\n+- [ ] Agregar estado_pago a los eventos y descuento a los usuarios\n+- [ ] Copiar descuento según usuario a los eventos\n+- [ ] Agregar información del estado pago en el Historial y en el Panel del Participante\n+- [ ] Agregar sector del pago en Filament y forma de modificarlo\n+- [ ] Enviar mails al modificar un pago para cargarlo en SaaS\n+- [ ] Generar <NAME_EMAIL> y re-direccionar todo lo referente a pagos ahí\n+- [ ] Acceso al banco a Juli\n+- [ ] Modificar Informe de pago para que también sea por Whatsapp\n+\n+\n+- [ ] AMB de Cronometradores\n+\n+\n+\n+\n+- [x] En SaaS: cambiar que el api token sea script token y que se pueda ejecutar scripts con el token. Luego que todo se haga en SaaS sin mi clic\n+- [ ] Fullsearch y saldos en SaaS automático después de los scripts\n+- [ ] Los scripts se ejecutan y después avisan por mail sólo para control\n+- [ ] Acomodar el valor de pago real en todos los eventos\n+- [ ] Agregar campo para cantidad de eventos que pueden estar en CC y sacar el parche actual\n+- [ ] Agregar campo descuento de forma predeterminada (con un campo en el organizador, más las ecuaciones acordadas)\n+- [ ] Pago intermedio para Ecuador\n+- [ ] Agregar campo de aviso de Pago enviado, esperando aprobación\n+- [ ] Agregar evento terminado y que lo termine en SaaS también. No dejar modificar eventos de más de 7 días de cerrados\n+- [ ] Agregar que el usuario de Gaby no paga\n+\n+- [x] Poder entrar desde el celular\n - [ ] Ver las CCs según el usuario posta (ver quien debe)\n+- [ ] Agregar estado del pago parcial y en revisión\n - [ ] Agregar descuentos en usuario y que se vea en Filament\n-- [ ] Agregar estado del pago parcial y en revisión\n-- [ ] Agregar los botones para aprobar los pagos\n-- [ ] Agregar mail de aprobación de los pagos\n-- [ ] Agregar aprobación automáticamente de los pagos\n \n \n+\n+- [ ] Generar vista del evento con toda la info y botones\n+- [ ] Generar paneles para: Sitio Web, Inscripciones, Cronometraje y de Vivos\n+- [ ] Revisión general (sólo navegación y explicaciones)\n+\n+\n+\n ## ACTUALIZAR NOVEDADES\n \n - [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n \n"}, {"date": 1729732821340, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,27 +15,68 @@\n \n INSERT IGNORE INTO user_organizacion (user_id, idorganizacion) VALUES\n (84, 353), (84, 355), (84, 357), (84, 358), (84, 367), (84, 374), (84, 379), (84, 422);\n \n+http://beta.cronometrajeinstantaneo.lan/inscripciones/1868/participantes\n+http://beta.cronometrajeinstantaneo.lan/inscripciones/2043/participantes\n \n+    9 => 2, // CI Zapala\n+    20 => -1, // Francisco Ecuador\n+    26 => 10, // Ride SRL\n+    36 => 10, // APE\n+    41 => 10, // CI Colombia\n+    45 => 10, // OWA\n+    84 => 10, // Esquel\n+    89 => 4, // SARR\n+    99 => 3, // Chronos Racing\n+    110 => 10, // Medallas para tu evento\n+    114 => 3, // Comisión Nacional de Rallies México A.C.\n+    117 => 0, // MTB Carreras\n+    151 => 10, // Enduro Series Perú\n+    173 => 6, // Ruta Activa\n+    178 => 10, // Ecuador <EMAIL>\n+    207 => 3, // Cronolar\n+    208 => 10, // Ecuador Super Enduro\n+    219 => 10, // Puñalica DH Ecuador\n+    229 => 10, // Ecuador Rally Manabi\n+    223 => 3, // Rally Bs. As.\n+    236 => 2, // Entrenamientos Cronometrados\n+    250 => 3, // FullRunners Panamá\n+    257 => 3, // Smart Timing Venezuela\n+    279 => 3, // Federación Deportiva Peruana de Ciclismo\n+    308 => 3, // Andres Mendoza\n+    340 => -1, // Ecuador Capi Automovilismo\n+    344 => 3, // Litoral Cronometraje\n+    352 => 3, // TakeARun\n+    354 => -1, // Ecuador\n+    378 => 10, // La Carrera Panamericana México\n+    412 => 10, // Hugo aguas abiertas Chile\n+    427 => 5, // Orozco México\n+\n+\n ## PRE-FILAMENT\n \n - [x] Mejorar visualización en celular\n-- [ ] Bloquear que no haya acceso a idevento sin permiso (en ambos paneles)\n-- [ ] Bloquear lo que no está terminado\n-- [ ] Asinar organizadores de Esquel y probar todo (también Gaby, Zapala y Ecuador)\n-- [ ] Filtrar por cronometrador, organizador o por usuario y de esta semana (aunque tenga que cargar fecha)\n+- [x] Bloquear que no haya acceso a idevento sin permiso (en ambos paneles)\n+- [x] Bloquear lo que no está terminado\n+- [x] Filtrar por cronometrador, organizador o por usuario y de esta semana (aunque tenga que cargar fecha)\n \n-- [ ] Agregar estado_pago a los eventos y descuento a los usuarios\n-- [ ] Copiar descuento según usuario a los eventos\n-- [ ] Agregar información del estado pago en el Historial y en el Panel del Participante\n+- [x] Agregar estado_pago a los eventos y descuento a los usuarios\n+- [x] Copiar descuento según usuario a los eventos\n+- [x] Agregar información del estado pago en el Historial y en el Panel del Participante\n+\n - [ ] Agregar sector del pago en Filament y forma de modificarlo\n - [ ] Enviar mails al modificar un pago para cargarlo en SaaS\n+\n+- [ ] Asignar organizadores de Esquel y probar todo (también Gaby, Zapala y Ecuador)\n+- [ ] Asignar los descuentos y las ccs en los usuarios\n+\n - [ ] Generar <NAME_EMAIL> y re-direccionar todo lo referente a pagos ahí\n - [ ] Acceso al banco a Juli\n - [ ] Modificar Informe de pago para que también sea por Whatsapp\n \n \n+\n - [ ] AMB de Cronometradores\n \n \n \n"}, {"date": 1729733188582, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,15 +62,15 @@\n \n - [x] Agregar estado_pago a los eventos y descuento a los usuarios\n - [x] Copiar descuento según usuario a los eventos\n - [x] Agregar información del estado pago en el Historial y en el Panel del Participante\n-\n - [ ] Agregar sector del pago en Filament y forma de modificarlo\n-- [ ] Enviar mails al modificar un pago para cargarlo en SaaS\n \n - [ ] Asignar organizadores de Esquel y probar todo (también Gaby, Zapala y Ecuador)\n - [ ] Asignar los descuentos y las ccs en los usuarios\n+- [ ] Asignar los eventos gratis y los pagos intermedios\n \n+- [ ] Enviar mails al modificar un pago para cargarlo en SaaS\n - [ ] Generar <NAME_EMAIL> y re-direccionar todo lo referente a pagos ahí\n - [ ] Acceso al banco a Juli\n - [ ] Modificar Informe de pago para que también sea por Whatsapp\n \n"}, {"date": 1730043645710, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,12 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## ERRORES\n+\n+- Gaby no puede eliminar una etapa\n+\n+\n ## ADMIN\n \n - Me voy por Filament directamente\n - Cambiar el informar por el sistema a informar por Whatsapp\n"}, {"date": 1730227846270, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,16 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## ERRORES\n+## REGULARIDAD\n \n-- Gaby no puede eliminar una etapa\n+- [ ] Probar un informe unificado\n+- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\" o \"Regularidad\"\n+- [ ] Agregar velocidad promedio a las etapas para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa.\n+- [ ] Agregar un campo para especificar el tiempo de la etapa para Regularidad\n+- [ ] Generar el calculo de velocidad promedio, diferencia con la etapa de regularidad y el total de diferencia, tiene que ir antes del ordenar\n+- [ ] Ordenar por Regularidad\n+- [ ] Convertir el mejor vuelta o mejor etapa a mejores x vueltas o etapas\n \n \n ## ADMIN\n \n@@ -132,9 +138,8 @@\n \n \n ### SÓLO SI LLEGO DESPUÉS\n \n-- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\"\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Agregar los {{nombre}} de nuevos en las inscripciones\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n@@ -144,9 +149,8 @@\n \n ## ORDENAR\n \n - [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n-- [ ] Agregar velocidad promedio a las etapas (sería ideal unificar informes ahora) para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n \n"}, {"date": 1730232290399, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,17 +2,22 @@\n -------------------------------------------------------------------------------\n \n ## REGULARIDAD\n \n-- [ ] Probar un informe unificado\n - [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\" o \"Regularidad\"\n - [ ] Agregar velocidad promedio a las etapas para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa.\n - [ ] Agregar un campo para especificar el tiempo de la etapa para Regularidad\n - [ ] Generar el calculo de velocidad promedio, diferencia con la etapa de regularidad y el total de diferencia, tiene que ir antes del ordenar\n - [ ] Ordenar por Regularidad\n - [ ] Convertir el mejor vuelta o mejor etapa a mejores x vueltas o etapas\n \n \n+ALTER TABLE `etapas`\n+    ADD `tiempo_minimo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `velocidad_promedio`,\n+    ADD `tiempo_maximo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_minimo`,\n+    ADD `tiempo_regularidad` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_maximo`;\n+\n+\n ## ADMIN\n \n - Me voy por Filament directamente\n - Cambiar el informar por el sistema a informar por Whatsapp\n@@ -138,8 +143,9 @@\n \n \n ### SÓLO SI LLEGO DESPUÉS\n \n+- [ ] Probar un informe unificado\n - [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n - [ ] Agregar los {{nombre}} de nuevos en las inscripciones\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n"}, {"date": 1730236656899, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,9 +13,9 @@\n \n ALTER TABLE `etapas`\n     ADD `tiempo_minimo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `velocidad_promedio`,\n     ADD `tiempo_maximo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_minimo`,\n-    ADD `tiempo_regularidad` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_maximo`;\n+    ADD `tiempo_ideal` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_maximo`;\n \n \n ## ADMIN\n \n"}, {"date": 1730244222448, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,16 +2,8 @@\n -------------------------------------------------------------------------------\n \n ## REGULARIDAD\n \n-- [ ] Convertir el ordenar_ultima_etapa y el $mejor etapa en un sólo select \"Clasificar por\": \"Tiempo total\", \"Mejor etapa\" o \"Última etapa\" o \"Regularidad\"\n-- [ ] Agregar velocidad promedio a las etapas para usar en filtros: https://cronometrajeinstantaneo.com/resultados/51-rally-de-coronel-suarez/generales. También la class velocidad promedio que se pueda especificar de una etapa.\n-- [ ] Agregar un campo para especificar el tiempo de la etapa para Regularidad\n-- [ ] Generar el calculo de velocidad promedio, diferencia con la etapa de regularidad y el total de diferencia, tiene que ir antes del ordenar\n-- [ ] Ordenar por Regularidad\n-- [ ] Convertir el mejor vuelta o mejor etapa a mejores x vueltas o etapas\n-\n-\n ALTER TABLE `etapas`\n     ADD `tiempo_minimo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `velocidad_promedio`,\n     ADD `tiempo_maximo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_minimo`,\n     ADD `tiempo_ideal` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_maximo`;\n"}, {"date": 1730252117289, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -150,8 +150,9 @@\n - [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n+- [ ] https://www.honeybadger.io/tour/logging-observability/?utm_source=laravelnews&utm_medium=paid&utm_campaign=2024-10-21-laravelnews-sponsored-post\n \n \n HYBRID INSCRIPCION INDIVIDUAL: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/TVY0VmllcFk0WXVLaHhvWWxWK2E2bkt1Um03STJadk84ZGRsbUdiZUcwb2kwMEFHSG1GeDFpK1VOenFMa2FnVw%3D%3D\n HYBRID INSCRIPCION DUPLA: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/QWxRSUlyQ1BjNmJrYmQ5dVFRVWpZSnZ1ZWgxbHpWckt0dHdPVllXN01rRHFIZ2VpSXZ4dExCd3RwbExpWmthVg%3D%3D\n"}, {"date": 1732027147868, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,12 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## VER\n+\n+- Hacer más fácil la configuración de las categorías\n+\n+\n ## REGULARIDAD\n \n ALTER TABLE `etapas`\n     ADD `tiempo_minimo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `velocidad_promedio`,\n"}, {"date": 1732031742252, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n \n ## VER\n \n - <PERSON>cer más fácil la configuración de las categorías\n+- En el ticket los parciales se ven como hora\n \n \n ## REGULARIDAD\n \n"}, {"date": 1733163234127, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,12 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## AHORA\n+\n+- La carrera de Dani Esquel sale mal el listado de datos extras\n+\n+\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733167758272, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,12 +1,12 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## AHORA\n+## ERRORES AHORA\n \n-- La carrera de Dani Esquel sale mal el listado de datos extras\n+- [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n+- [ ] Se cuelga la exportación de Mendoza Maribel\n \n-\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733275459156, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,12 @@\n ## ERRORES AHORA\n \n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n - [ ] Se cuelga la exportación de Mendoza Maribel\n+- [ ] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n+- [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n \n+\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733515794662, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,8 +12,10 @@\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n+- Gestión de staff y prensa en la misma tabla pero con otros tipo\n+- Impresión de tarjetas de prensa (buscar otro nombre)\n \n \n ## REGULARIDAD\n \n"}, {"date": 1733616480853, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n - [ ] Se cuelga la exportación de Mendoza Maribel\n - [ ] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n+- [ ] Agregar marca Sherco\n \n \n ## VER\n \n"}, {"date": 1733629156246, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,9 +8,13 @@\n - [ ] Revisar estadís<PERSON>, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n - [ ] Agregar marca Sherco\n \n+* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales -O /saas/customer/services/scripts/public/wsm-generales.html\n+* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias -O /saas/customer/services/scripts/public/wsm-categorias.html\n+* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios -O /saas/customer/services/scripts/public/wsm-podios.html\n \n+\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733629601269, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,22 @@\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales -O /saas/customer/services/scripts/public/wsm-generales.html\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias -O /saas/customer/services/scripts/public/wsm-categorias.html\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios -O /saas/customer/services/scripts/public/wsm-podios.html\n \n+*Listado Participantes*\n+https://cronometrajeinstantaneo.com/resultados/participantes\n \n+*Resultados Generales*\n+https://cronometrajeinstantaneo.com/resultados/generales\n+\n+*Resultados por Categoría*\n+https://cronometrajeinstantaneo.com/resultados/categorias\n+\n+*Sólo Podios*\n+https://cronometrajeinstantaneo.com/resultados/podios\n+\n+\n+\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733630162356, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,22 +12,25 @@\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales -O /saas/customer/services/scripts/public/wsm-generales.html\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias -O /saas/customer/services/scripts/public/wsm-categorias.html\n * * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios -O /saas/customer/services/scripts/public/wsm-podios.html\n \n-*Listado Participantes*\n-https://cronometrajeinstantaneo.com/resultados/participantes\n+https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/extras?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n-*Resultados Generales*\n-https://cronometrajeinstantaneo.com/resultados/generales\n \n-*Resultados por Categoría*\n-https://cronometrajeinstantaneo.com/resultados/categorias\n+*EXPORTAR Listado Participantes*\n+https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/participantes?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n-*Sólo Podios*\n-https://cronometrajeinstantaneo.com/resultados/podios\n+*EXPORTAR Resultados Generales*\n+https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n+*EXPORTAR Resultados por Categoría*\n+https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n+*EXPORTAR Sólo Podios*\n+https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n+\n+\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1733950971395, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,29 +8,10 @@\n - [ ] Rev<PERSON><PERSON> estad<PERSON>, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n - [ ] Agregar marca Sherco\n \n-* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales -O /saas/customer/services/scripts/public/wsm-generales.html\n-* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias -O /saas/customer/services/scripts/public/wsm-categorias.html\n-* * * * * wget https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios -O /saas/customer/services/scripts/public/wsm-podios.html\n \n-https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/extras?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n \n-\n-*EXPORTAR Listado Participantes*\n-https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/participantes?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n-\n-*EXPORTAR Resultados Generales*\n-https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/generales?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n-\n-*EXPORTAR Resultados por Categoría*\n-https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/categorias?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n-\n-*EXPORTAR Sólo Podios*\n-https://cronometrajeinstantaneo.com/resultados/world-skate-marathon-tour-2024/podios?t=fb36aa3b01d4d1a3a5157a305702a9482467c498e6aa63eae2fc5c6ecdabef41&exportar=planilla\n-\n-\n-\n ## VER\n \n - Hacer más fácil la configuración de las categorías\n - En el ticket los parciales se ven como hora\n"}, {"date": 1734306221512, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,13 +2,15 @@\n -------------------------------------------------------------------------------\n \n ## ERRORES AHORA\n \n+- [x] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n - [ ] Se cuelga la exportación de Mendoza Maribel\n-- [ ] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n+- [ ] Tengo varios Sentry\n+- [ ] Se colgó todos los últimos días (ver si es por vivos, poner logs o pruebas)\n+- [ ] Agregar marca Sherco\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n-- [ ] Agregar marca Sherco\n \n \n \n ## VER\n"}, {"date": 1734306379538, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,14 @@\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n - [ ] Se cuelga la exportación de Mendoza Maribel\n - [ ] Tengo varios Sentry\n - [ ] Se colgó todos los últimos días (ver si es por vivos, poner logs o pruebas)\n+  - https://cronometraje-instantaneo.sentry.io/issues/5672824260/?alert_rule_id=11588895&alert_timestamp=1734274021617&alert_type=email&environment=production&notification_uuid=391d9081-085a-47c9-9ba0-eb4dd273cb82&project=6169710&referrer=alert_email\n+  - https://cronometraje-instantaneo.sentry.io/issues/6119820220/?alert_rule_id=11588895&alert_timestamp=1733658371653&alert_type=email&notification_uuid=5e669e62-f936-4ba6-911a-a4ddace3c7a5&project=6169710&referrer=alert_email\n+  - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n - [ ] Agregar marca Sherco\n+- [ ] Error en comparación\n+  - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n \n \n \n"}, {"date": 1734306810283, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,9 +13,11 @@\n   - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n - [ ] Agregar marca Sherco\n - [ ] Error en comparación\n   - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n+  - https://cronometraje-instantaneo.sentry.io/issues/5974813285/?alert_rule_id=11588895&alert_timestamp=1734282544633&alert_type=email&environment=prod&notification_uuid=e56f7c48-50f8-4426-b698-2882d09a9358&project=6169710&referrer=alert_email\n - [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n+- [ ] En mobile han intentado acceder con el codigo KACH\n \n \n \n ## VER\n"}, {"date": 1734560285793, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,21 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## NUEVOS PRECIOS\n+\n+Tabla formas de pago: idformapago, moneda, nombre, cotización en dólares\n+- Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n+- Descuentos para Cronometrador oficial:\n+  - 3 eventos en los últimos 6 meses 20% de descuento\n+  - 9 eventos en los últimos 6 meses 30% de descuento\n+\n+- Descuento pago por crypto: 10% de descuento\n+- Descuento por pago por adelantado (lo mismo que si es cronometrador oficial)\n+- Descuento adicional especial con código\n+- Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n+\n+\n ## ERRORES AHORA\n \n - [x] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n"}, {"date": 1734560298978, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,10 @@\n -------------------------------------------------------------------------------\n \n ## NUEVOS PRECIOS\n \n+https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/227\n+\n Tabla formas de pago: idformapago, moneda, nombre, cotización en dólares\n - Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n - Descuentos para Cronometrador oficial:\n   - 3 eventos en los últimos 6 meses 20% de descuento\n"}, {"date": 1734561472826, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,18 +6,11 @@\n https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/227\n \n Tabla formas de pago: idformapago, moneda, nombre, cotización en dólares\n - Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n-- Descuentos para Cronometrador oficial:\n-  - 3 eventos en los últimos 6 meses 20% de descuento\n-  - 9 eventos en los últimos 6 meses 30% de descuento\n-\n-- Descuento pago por crypto: 10% de descuento\n-- Descuento por pago por adelantado (lo mismo que si es cronometrador oficial)\n-- Descuento adicional especial con código\n - Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n+- Ver y pensar que cambios meter según [PRECIOS](./PRECIOS.md)\n \n-\n ## ERRORES AHORA\n \n - [x] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n"}, {"date": 1734613420131, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,11 @@\n - Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n - Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n - Ver y pensar que cambios meter según [PRECIOS](./PRECIOS.md)\n \n+- Debería facturar automáticamente por ARCA en ARG y por la LLC en el exterior\n+\n+\n ## ERRORES AHORA\n \n - [x] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n - [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n"}, {"date": 1734639974796, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,9 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+- Baja Tiktok\n+\n ## NUEVOS PRECIOS\n \n https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/227\n \n"}, {"date": 1734720113313, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,12 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n - Baja Tiktok\n+- Sacar totales y paises para posteo\n+- Sacar listado de cronometradores para vídeos\n \n+\n ## NUEVOS PRECIOS\n \n https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/227\n \n"}, {"date": 1734726074580, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,23 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## NECESITO ORDENARME\n+\n+AHORA\n+\n+- ABM Completo de Eventos especialmente con el tema usuarios\n+- Gestión de pagos y deudas de eventos\n+- Aplicar nuevo sistema de precios\n+- Limpiar integración con SaaS y pasarle la gestión a Juli\n+- Hacer más fácil la gestión de deuda con Ecuador y de deuda de cronometradores con distintos usuarios\n+\n+- Generar los nuevos módulos por separado: micrositio, inscripciones, cronometraje, vivo, chips\n+\n+\n+\n+## TAREAS CHICAS\n+\n - Baja Tiktok\n - Sacar totales y paises para posteo\n - Sacar listado de cronometradores para vídeos\n \n@@ -197,10 +213,8 @@\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n - [ ] https://www.honeybadger.io/tour/logging-observability/?utm_source=laravelnews&utm_medium=paid&utm_campaign=2024-10-21-laravelnews-sponsored-post\n \n \n-HYBRID INSCRIPCION INDIVIDUAL: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/TVY0VmllcFk0WXVLaHhvWWxWK2E2bkt1Um03STJadk84ZGRsbUdiZUcwb2kwMEFHSG1GeDFpK1VOenFMa2FnVw%3D%3D\n-HYBRID INSCRIPCION DUPLA: https://cronometrajeinstantaneo.com/inscripciones/the-hybrid-race-2024/QWxRSUlyQ1BjNmJrYmQ5dVFRVWpZSnZ1ZWgxbHpWckt0dHdPVllXN01rRHFIZ2VpSXZ4dExCd3RwbExpWmthVg%3D%3D\n \n \n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n@@ -209,32 +223,31 @@\n \n OBJETIVO: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales\n \n - [x] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n-- [ ] FILAMENT Admin #276: poder generar el acceso al nuevo módulo de eventos\n-- [ ] FILAMENT ABM Eventos #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n-- [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n+- [ ] FILAMENT Gestión Eventos y Usuarios #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n+- [ ] FILAMENT Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n \n **Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n \n-- Limpiar los issues de features\n+- [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n+- [ ] Limpiar los issues de features\n \n **Automatizar cuentas**\n \n-OBJETIVO: automatizar los pagos, sincronizar SaaS. Todavía no entran usuarios finales, pero ya no hago nada manual\n+OBJETIVO: Reemplazar el Historial y automatizar pagos. Entran usuarios finales y tienen control total de sus eventos y datos.\n \n-- EVENTOS Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n-- AUTO-ADMIN #132: acomodamos todos los scripts\n-- PAGOS Automatizar #242: Terminar lo que ya avanzaste de DLocalGo\n-- EVENTOS Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n+- FILAMENT Alta #263: permitimos que los usuarios nuevos generen eventos (Sin ver el tema pagos todavía)\n+- FILAMENT Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos\n+- FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n+- FILAMENT Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n \n **Generar sector de Eventos en el sitio**\n \n OBJETIVO: Sector de eventos, ayudar configuración con equipo de Crono\n \n-- EVENTOS Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos\n - EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos\n \n **Terminar Nuevas Inscripciones**\n \n@@ -254,14 +267,8 @@\n OBJETIVO: Potenciar las inscripciones a la altura del mercado\n \n - Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente\n \n-**Generar ventanas para configurar de eventos, organizadores y cronometradores**\n-\n-OBJETIVO: Reemplazar el Historial. Entran usuarios finales y tienen control total de sus eventos y datos.\n-\n-- EVENTOS Alta #263: permitimos que los usuarios nuevos generen eventos (Sin ver el tema pagos todavía)\n-\n **Generar un sector de Cursos con AMB para los Admins**\n \n **Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**\n \n"}, {"date": 1734726303124, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -232,8 +232,9 @@\n OBJETIVO: No configurar funcionalidades manualmente yo\n \n - [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n - [ ] Limpiar los issues de features\n+- [ ] DEPORTES #165: generar sistemas para cada deporte\n \n **Automatizar cuentas**\n \n OBJETIVO: Reemplazar el Historial y automatizar pagos. Entran usuarios finales y tienen control total de sus eventos y datos.\n@@ -248,43 +249,39 @@\n OBJETIVO: Sector de eventos, ayudar configuración con equipo de Crono\n \n - EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos\n \n+**Generar un sector de Cursos con AMB para los Admins**\n+\n **Terminar Nuevas Inscripciones**\n \n+OBJETIVO: Potenciar las inscripciones a la altura del mercado\n+\n - [ ] Terminar Oauth MP y generar documentación y MKT #264\n - [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n - [ ] Ver si podemos poner las fotos en los tickets #292\n+- [ ] Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente\n \n+**Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**\n+\n+**Crono sin errores**\n+\n+- Panel del participante con vídeo y tiempos\n+- Modificar tiempos nuevo\n+- Logs de TODO para recuperar información y para que lo vea el responsable\n+\n **Métricas, Dashboard y KPIs automáticos con AI 📈**\n \n OBJETIVO: tener un dashboard con las métricas más importantes automatizadas\n \n - PAGOS Descuentos y Adicionales #268: poder ofrecer descuentos adicionales\n - MÉTRICAS Automáticas con AI #279: poder ver por fin las métricas en Filament u afuera con AI\n \n-**Generar procesos con pasarelas de pagos automatizadas**\n \n-OBJETIVO: Potenciar las inscripciones a la altura del mercado\n-\n-- Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente\n-\n-**Generar un sector de Cursos con AMB para los Admins**\n-\n-**Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**\n-\n-**Crono sin errores**\n-\n-- Panel del participante con vídeo y tiempos\n-- Modificar tiempos nuevo\n-- Logs de TODO para recuperar información y para que lo vea el responsable\n-\n ### SIGUEN\n \n - Métricas y KPIs automáticos con AI 📈\n - Automatización y conexión con AI\n-- Rediseñar sistema con Deportes e Idiomas\n-- Generar sector de Deportes en el sitio\n - Agregar una función de Kiosko en la app\n - Agregar compatibilidad para los equipos: Macsha xFTP y probar wireshark\n - Agregar compatibilidad para Chafón x2\n - Migrar base de datos relacional a Cloud MySQL\n"}, {"date": 1734727605545, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,8 +14,38 @@\n - Generar los nuevos módulos por separado: micrositio, inscripciones, cronometraje, vivo, chips\n \n \n \n+- [ ] FILAMENT ABM Eventos #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n+\n+\n+- [ ] Agregar un tipo de usuario 'admin' junto a los ya existentes: 'organizador', 'cronometrador', admin, superadmin, ecuador, '' (participante)\n+- [ ] Revisar el correcto\n+\n+\n+\n+- Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n+- Descuentos para Cronometrador oficial:\n+  - 3 eventos en los últimos 6 meses 20% de descuento\n+  - 9 eventos en los últimos 6 meses 30% de descuento\n+- Descuento pago por crypto: 10% de descuento\n+- Descuento por pago por adelantado (lo mismo que si es cronometrador oficial)\n+- Descuento adicional especial con código\n+- Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n+\n+\n+- [ ] Agregar la opción de crear un evento nuevo en el listado de eventos. Debe tener un selector de deporte, un selector de país y un selector de tipo de sistema: Micrositio, Inscripciones, etc.\n+- [ ] Agregar la opción de poder modificarlo\n+- [ ] Administrar la posibilidad de cambiar el slug o no\n+- [ ] Agregar la opción para que un super usuario pueda habilitar eventos a tener inscripciones o cronometraje\n+\n+\n+\n+\n+\n+\n+\n+\n ## TAREAS CHICAS\n \n - Baja Tiktok\n - Sacar totales y paises para posteo\n"}, {"date": 1734735883452, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,241 +1,29 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## NECESITO ORDENARME\n \n-AHORA\n \n-- ABM Completo de Eventos especialmente con el tema usuarios\n-- Gestión de pagos y deudas de eventos\n-- Aplicar nuevo sistema de precios\n-- Limpiar integración con SaaS y pasarle la gestión a Juli\n-- Hacer más fácil la gestión de deuda con Ecuador y de deuda de cronometradores con distintos usuarios\n \n-- Generar los nuevos módulos por separado: micrositio, inscripciones, cronometraje, vivo, chips\n \n \n \n-- [ ] FILAMENT ABM Eventos #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n+------------------------------------------------------------------------------\n \n+### MEJORAS MENORES VARIAS\n \n-- [ ] Agregar un tipo de usuario 'admin' junto a los ya existentes: 'organizador', 'cronometrador', admin, superadmin, ecuador, '' (participante)\n-- [ ] Revisar el correcto\n-\n-\n-\n-- Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n-- Descuentos para Cronometrador oficial:\n-  - 3 eventos en los últimos 6 meses 20% de descuento\n-  - 9 eventos en los últimos 6 meses 30% de descuento\n-- Descuento pago por crypto: 10% de descuento\n-- Descuento por pago por adelantado (lo mismo que si es cronometrador oficial)\n-- Descuento adicional especial con código\n-- Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n-\n-\n-- [ ] Agregar la opción de crear un evento nuevo en el listado de eventos. Debe tener un selector de deporte, un selector de país y un selector de tipo de sistema: Micrositio, Inscripciones, etc.\n-- [ ] Agregar la opción de poder modificarlo\n-- [ ] Administrar la posibilidad de cambiar el slug o no\n-- [ ] Agregar la opción para que un super usuario pueda habilitar eventos a tener inscripciones o cronometraje\n-\n-\n-\n-\n-\n-\n-\n-\n-## TAREAS CHICAS\n-\n-- Baja Tiktok\n-- Sacar totales y paises para posteo\n-- Sacar listado de cronometradores para vídeos\n-\n-\n-## NUEVOS PRECIOS\n-\n-https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/227\n-\n-Tabla formas de pago: idformapago, moneda, nombre, cotización en dólares\n-- Valor del sistema según país: en Arg hasta tal fecha x valor ($58k), si no hay después poner lo de consultar. En Ecuador un mensaje y el resto a Paypal\n-- Ecuador tiene doble sistema de pago para que ellos también puedan gestionar en el sistema (estaría bueno para todos)\n-- Ver y pensar que cambios meter según [PRECIOS](./PRECIOS.md)\n-\n-- Debería facturar automáticamente por ARCA en ARG y por la LLC en el exterior\n-\n-\n-## ERRORES AHORA\n-\n-- [x] Revisar estadísticas, solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n-- [ ] La carrera de Dani Esquel sale mal el listado de datos extras\n-- [ ] Se cuelga la exportación de Mendoza Maribel\n-- [ ] Tengo varios Sentry\n-- [ ] Se colgó todos los últimos días (ver si es por vivos, poner logs o pruebas)\n-  - https://cronometraje-instantaneo.sentry.io/issues/5672824260/?alert_rule_id=11588895&alert_timestamp=1734274021617&alert_type=email&environment=production&notification_uuid=391d9081-085a-47c9-9ba0-eb4dd273cb82&project=6169710&referrer=alert_email\n-  - https://cronometraje-instantaneo.sentry.io/issues/6119820220/?alert_rule_id=11588895&alert_timestamp=1733658371653&alert_type=email&notification_uuid=5e669e62-f936-4ba6-911a-a4ddace3c7a5&project=6169710&referrer=alert_email\n-  - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n-- [ ] Agregar marca Sherco\n-- [ ] Error en comparación\n-  - https://cronometraje-instantaneo.sentry.io/issues/6120260881/?referrer=alert_email&alert_type=email&alert_timestamp=1733683371389&alert_rule_id=11588895&notification_uuid=6da10dee-2af4-4aa2-b31a-9fed49051216&environment=production\n-  - https://cronometraje-instantaneo.sentry.io/issues/5974813285/?alert_rule_id=11588895&alert_timestamp=1734282544633&alert_type=email&environment=prod&notification_uuid=e56f7c48-50f8-4426-b698-2882d09a9358&project=6169710&referrer=alert_email\n-- [ ] Script comprobar las 3 inscripciones en Campeonato Neuquíno\n-- [ ] En mobile han intentado acceder con el codigo KACH\n-\n-\n-\n-## VER\n-\n-- Hacer más fácil la configuración de las categorías\n-- En el ticket los parciales se ven como hora\n-- Gestión de staff y prensa en la misma tabla pero con otros tipo\n-- Impresión de tarjetas de prensa (buscar otro nombre)\n-\n-\n-## REGULARIDAD\n-\n-ALTER TABLE `etapas`\n-    ADD `tiempo_minimo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `velocidad_promedio`,\n-    ADD `tiempo_maximo` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_minimo`,\n-    ADD `tiempo_ideal` INT UNSIGNED NOT NULL DEFAULT '0' AFTER `tiempo_maximo`;\n-\n-\n-## ADMIN\n-\n-- Me voy por Filament directamente\n-- Cambiar el informar por el sistema a informar por Whatsapp\n-- Pasar toda la aprobación al sistema en Filament (Juli aprueba y mira todo ahí)\n-- Juli tiene acceso a los bancos y/o recibe redirectos los mails de los pagos\n-- Los usuarios ven la info desde Admin\n-- La carga en SaaS es automática\n-\n-UPDATE eventos SET user_id = 84, idcronometrador = 84 WHERE idorganizacion IN\n-(353, 355, 357, 358, 367, 374, 379, 422);\n-\n-INSERT IGNORE INTO user_organizacion (user_id, idorganizacion) VALUES\n-(84, 353), (84, 355), (84, 357), (84, 358), (84, 367), (84, 374), (84, 379), (84, 422);\n-\n-http://beta.cronometrajeinstantaneo.lan/inscripciones/1868/participantes\n-http://beta.cronometrajeinstantaneo.lan/inscripciones/2043/participantes\n-\n-    9 => 2, // CI Zapala\n-    20 => -1, // Francisco Ecuador\n-    26 => 10, // Ride SRL\n-    36 => 10, // APE\n-    41 => 10, // CI Colombia\n-    45 => 10, // OWA\n-    84 => 10, // Esquel\n-    89 => 4, // SARR\n-    99 => 3, // Chronos Racing\n-    110 => 10, // Medallas para tu evento\n-    114 => 3, // Comisión Nacional de Rallies México A.C.\n-    117 => 0, // MTB Carreras\n-    151 => 10, // Enduro Series Perú\n-    173 => 6, // Ruta Activa\n-    178 => 10, // Ecuador <EMAIL>\n-    207 => 3, // Cronolar\n-    208 => 10, // Ecuador Super Enduro\n-    219 => 10, // Puñalica DH Ecuador\n-    229 => 10, // Ecuador Rally Manabi\n-    223 => 3, // Rally Bs. As.\n-    236 => 2, // Entrenamientos Cronometrados\n-    250 => 3, // FullRunners Panamá\n-    257 => 3, // Smart Timing Venezuela\n-    279 => 3, // Federación Deportiva Peruana de Ciclismo\n-    308 => 3, // Andres Mendoza\n-    340 => -1, // Ecuador Capi Automovilismo\n-    344 => 3, // Litoral Cronometraje\n-    352 => 3, // TakeARun\n-    354 => -1, // Ecuador\n-    378 => 10, // La Carrera Panamericana México\n-    412 => 10, // Hugo aguas abiertas Chile\n-    427 => 5, // Orozco México\n-\n-\n-## PRE-FILAMENT\n-\n-- [x] Mejorar visualización en celular\n-- [x] Bloquear que no haya acceso a idevento sin permiso (en ambos paneles)\n-- [x] Bloquear lo que no está terminado\n-- [x] Filtrar por cronometrador, organizador o por usuario y de esta semana (aunque tenga que cargar fecha)\n-\n-- [x] Agregar estado_pago a los eventos y descuento a los usuarios\n-- [x] Copiar descuento según usuario a los eventos\n-- [x] Agregar información del estado pago en el Historial y en el Panel del Participante\n-- [ ] Agregar sector del pago en Filament y forma de modificarlo\n-\n-- [ ] Asignar organizadores de Esquel y probar todo (también Gaby, Zapala y Ecuador)\n-- [ ] Asignar los descuentos y las ccs en los usuarios\n-- [ ] Asignar los eventos gratis y los pagos intermedios\n-\n-- [ ] Enviar mails al modificar un pago para cargarlo en SaaS\n-- [ ] Generar <NAME_EMAIL> y re-direccionar todo lo referente a pagos ahí\n-- [ ] Acceso al banco a Juli\n-- [ ] Modificar Informe de pago para que también sea por Whatsapp\n-\n-\n-\n-- [ ] AMB de Cronometradores\n-\n-\n-\n-\n-- [x] En SaaS: cambiar que el api token sea script token y que se pueda ejecutar scripts con el token. Luego que todo se haga en SaaS sin mi clic\n-- [ ] Fullsearch y saldos en SaaS automático después de los scripts\n-- [ ] Los scripts se ejecutan y después avisan por mail sólo para control\n-- [ ] Acomodar el valor de pago real en todos los eventos\n-- [ ] Agregar campo para cantidad de eventos que pueden estar en CC y sacar el parche actual\n-- [ ] Agregar campo descuento de forma predeterminada (con un campo en el organizador, más las ecuaciones acordadas)\n-- [ ] Pago intermedio para Ecuador\n-- [ ] Agregar campo de aviso de Pago enviado, esperando aprobación\n-- [ ] Agregar evento terminado y que lo termine en SaaS también. No dejar modificar eventos de más de 7 días de cerrados\n-- [ ] Agregar que el usuario de Gaby no paga\n-\n-- [x] Poder entrar desde el celular\n-- [ ] Ver las CCs según el usuario posta (ver quien debe)\n-- [ ] Agregar estado del pago parcial y en revisión\n-- [ ] Agregar descuentos en usuario y que se vea en Filament\n-\n-\n-\n-- [ ] Generar vista del evento con toda la info y botones\n-- [ ] Generar paneles para: Sitio Web, Inscripciones, Cronometraje y de Vivos\n-- [ ] Revisión general (sólo navegación y explicaciones)\n-\n-\n-\n-## ACTUALIZAR NOVEDADES\n-\n-- [ ] Cambios en el css y en cantidad para Toti y para poder diseñar diferente cada informe (x sexo por ej.)\n-\n-\n-### MEJORAS PEDIDAS\n-\n-- [ ] Limpiar lecturas con idevento o idcontrol = 0\n-- [ ] En los podios NO deberían aparecer los DNF\n-- [ ] Mover el DNS a donde está el puesto, y/o ocultar los puestos de los DNS/DNF, poner esas líneas de otro color de fondo\n-- [ ] Pasar chips al cronometrador o al organizador con un in\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Ordenar llegada según timer\n-- [ ] Largadas en las etapas y/o en las carreras (lo pidió alguien, creo que Jaime de Chile)\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n - [ ] Largada y llegada mismo código\n - [ ] Largada por categorías para Quatro Vientos\n - [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n-\n-\n-### SÓLO SI LLEGO DESPUÉS\n-\n-- [ ] Probar un informe unificado\n-- [ ] Sacar \"En mobile han intentado acceder con el codigo KACH\" y revisar que responda algo que sea intuitivo que no se guarda ese tiempo\n-- [ ] Agregar los {{nombre}} de nuevos en las inscripciones\n - [ ] El filtro de etapas no funciona si no está iniciado el evento\n - [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n - [ ] Agregar código de identificación en los MP como pidió Kittu\n \n \n-\n ## ORDENAR\n \n - [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n - [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n@@ -243,10 +31,8 @@\n - [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n - [ ] https://www.honeybadger.io/tour/logging-observability/?utm_source=laravelnews&utm_medium=paid&utm_campaign=2024-10-21-laravelnews-sponsored-post\n \n \n-\n-\n -------------------------------------------------------------------------------\n ## MILESTONES DEV\n \n **Actualizar framework y mejorar diseño**\n@@ -262,9 +48,11 @@\n OBJETIVO: No configurar funcionalidades manualmente yo\n \n - [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n - [ ] Limpiar los issues de features\n+- [ ] Edades en las categorías #236: Hacer más fácil la configuración de las categorías\n - [ ] DEPORTES #165: generar sistemas para cada deporte\n+- [ ] CHIPS #298: dejar de gestionar chips a mano\n \n **Automatizar cuentas**\n \n OBJETIVO: Reemplazar el Historial y automatizar pagos. Entran usuarios finales y tienen control total de sus eventos y datos.\n"}, {"date": 1734876955051, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -47,9 +47,9 @@\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n \n - [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n-- [ ] Limpiar los issues de features\n+- [ ] Limpiar los issues de features. UTILIZAR ACCIONES PARA TODA LA LÓGICA DE NEGOCIOS!\n - [ ] Edades en las categorías #236: Hacer más fácil la configuración de las categorías\n - [ ] DEPORTES #165: generar sistemas para cada deporte\n - [ ] CHIPS #298: dejar de gestionar chips a mano\n \n"}, {"date": 1734876976597, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -47,9 +47,9 @@\n \n OBJETIVO: No configurar funcionalidades manualmente yo\n \n - [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n-- [ ] Limpiar los issues de features. UTILIZAR ACCIONES PARA TODA LA LÓGICA DE NEGOCIOS!\n+- [ ] Limpiar los issues de features. UTILIZAR ACCIONES PARA TODA LA LÓGICA DE NEGOCIOS PENSANDO EN AI!\n - [ ] Edades en las categorías #236: Hacer más fácil la configuración de las categorías\n - [ ] DEPORTES #165: generar sistemas para cada deporte\n - [ ] CHIPS #298: dejar de gestionar chips a mano\n \n"}, {"date": 1735568038602, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,8 +10,9 @@\n ------------------------------------------------------------------------------\n \n ### MEJORAS MENORES VARIAS\n \n+- [ ] Limitar el vivo\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Ordenar llegada según timer\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n"}, {"date": 1735568078756, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,9 @@\n \n ### MEJORAS MENORES VARIAS\n \n - [ ] Limitar el vivo\n+- [ ] No funcionó el enlace de Mejor Etapa para el evento de Perú del último finde del 2024\n - [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n - [ ] Ordenar llegada según timer\n - [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n - [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n"}, {"date": 1735569958214, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,10 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+## ADMIN CONSULTAS\n \n+- <PERSON><PERSON> los eventos que están con usuario en 0\n \n \n \n \n"}, {"date": 1735570122847, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,14 +2,16 @@\n -------------------------------------------------------------------------------\n \n ## ADMIN CONSULTAS\n \n+- Modificar el estado del pago (al aprobar se tiene que mandar un mail)\n+- Modiciar país\n+- Poder gestionar los módulos que tienen habilitados\n+\n - Buscar los eventos que están con usuario en 0\n \n \n \n-\n-\n ------------------------------------------------------------------------------\n \n ### MEJORAS MENORES VARIAS\n \n"}, {"date": 1735570461598, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,19 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## ADMIN CONSULTAS\n+## ADMIN DEV\n \n - Modificar el estado del pago (al aprobar se tiene que mandar un mail)\n - Modiciar país\n - Poder gestionar los módulos que tienen habilitados\n+- En admin agregar país y disciplina tanto en historial como en panel\n \n+## ADMIN CONSULTAS\n+\n - Buscar los eventos que están con usuario en 0\n+- Asignar bien los organizadores y los cronometradores\n+-\n \n \n \n ------------------------------------------------------------------------------\n"}, {"date": 1735572030288, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,18 +2,22 @@\n -------------------------------------------------------------------------------\n \n ## ADMIN DEV\n \n-- Modificar el estado del pago (al aprobar se tiene que mandar un mail)\n-- Modiciar país\n-- Poder gestionar los módulos que tienen habilitados\n-- En admin agregar país y disciplina tanto en historial como en panel\n+- [ ] Modificar los módulos a: sitio, organizacion, cronometraje, vivo\n+- [ ] Modificar el estado del pago (al aprobar se tiene que mandar un mail)\n+- [ ] Modiciar país\n+- [ ] Poder gestionar los módulos que tienen habilitados\n+- [ ] En admin agregar al historial: país, disciplina, módulos, estado de evento, estado de pago\n+- [ ] En admin agregar al panel: país, disciplina, estado del evento, estado de pago\n \n ## ADMIN CONSULTAS\n \n-- Buscar los eventos que están con usuario en 0\n-- Asignar bien los organizadores y los cronometradores\n--\n+- [ ] Buscar los eventos que están con usuario en 0\n+- [ ] Asignar bien los organizadores y los cronometradores\n+- [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n+- [ ] Actualizar todos los estados de pagos de todos los eventos\n+- [ ] Terminar todos los eventos del 2024\n \n \n \n ------------------------------------------------------------------------------\n"}, {"date": 1735572251840, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n - [ ] Modiciar país\n - [ ] Poder gestionar los módulos que tienen habilitados\n - [ ] En admin agregar al historial: país, disciplina, módulos, estado de evento, estado de pago\n - [ ] En admin agregar al panel: país, disciplina, estado del evento, estado de pago\n+- [ ] Cambiar organizacion por organizador\n \n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n"}, {"date": 1735822252114, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,7 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-## ADMIN DEV\n-\n-- [ ] Modificar los módulos a: sitio, organizacion, cronometraje, vivo\n-- [ ] Modificar el estado del pago (al aprobar se tiene que mandar un mail)\n-- [ ] Modiciar país\n-- [ ] Poder gestionar los módulos que tienen habilitados\n-- [ ] En admin agregar al historial: país, disciplina, módulos, estado de evento, estado de pago\n-- [ ] En admin agregar al panel: país, disciplina, estado del evento, estado de pago\n-- [ ] Cambiar organizacion por organizador\n-\n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n - [ ] Asignar bien los organizadores y los cronometradores\n"}, {"date": 1735827330934, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,10 @@\n - [ ] Asignar bien los organizadores y los cronometradores\n - [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n - [ ] Actualizar todos los estados de pagos de todos los eventos\n - [ ] Terminar todos los eventos del 2024\n+- [ ] Cargar los pagos pendientes que tengo en soporte\n+- [ ] Cerrar Cajas\n \n \n \n ------------------------------------------------------------------------------\n"}, {"date": 1736088160168, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,10 +10,14 @@\n - [ ] Terminar todos los eventos del 2024\n - [ ] Cargar los pagos pendientes que tengo en soporte\n - [ ] Cerrar Cajas\n \n+## COORDINAR EN SAAS\n \n+- [ ] No se generan más servicios\n+- [ ] Los pagos en pesos que se carguen en la moneda correcta\n \n+\n ------------------------------------------------------------------------------\n \n ### MEJORAS MENORES VARIAS\n \n"}, {"date": 1736193804440, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n - [ ] Actualizar todos los estados de pagos de todos los eventos\n - [ ] Terminar todos los eventos del 2024\n - [ ] Cargar los pagos pendientes que tengo en soporte\n - [ ] Cerrar Cajas\n+- [ ] Los eventos con cronometraje también tienen organizacion\n \n ## COORDINAR EN SAAS\n \n - [ ] No se generan más servicios\n"}, {"date": 1736193862632, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,9 +10,11 @@\n - [ ] Terminar todos los eventos del 2024\n - [ ] Cargar los pagos pendientes que tengo en soporte\n - [ ] Cerrar <PERSON>ajas\n - [ ] Los eventos con cronometraje también tienen organizacion\n+- [ ] Todos son publicos menos si en el nombre dicen \"entrenamiento\", \"prueba\" o \"test\", o tienen 0 inscriptos\n \n+\n ## COORDINAR EN SAAS\n \n - [ ] No se generan más servicios\n - [ ] Los pagos en pesos que se carguen en la moneda correcta\n"}, {"date": 1736533913695, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,10 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+\n+\n+\n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n - [ ] Asignar bien los organizadores y los cronometradores\n"}, {"date": 1736707887062, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,10 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n+ALTER TABLE `users` CHANGE `tipo` `tipo` ENUM('','organizador','cronometrador','auto','admin','superadmin','ecuador') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT ''; \n \n \n-\n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n - [ ] Asignar bien los organizadores y los cronometradores\n"}, {"date": 1736709372819, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,14 @@\n # 🥇 CRONO > DEV\n -------------------------------------------------------------------------------\n \n-ALTER TABLE `users` CHANGE `tipo` `tipo` ENUM('','organizador','cronometrador','auto','admin','superadmin','ecuador') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_520_ci NOT NULL DEFAULT ''; \n+ALTER TABLE `users` CHANGE `tipo` `tipo` ENUM('','organizador','cronometrador','auto','admin','superadmin','ecuador') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '';\n \n+ALTER TABLE eventos\n+CHANGE COLUMN created_at created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n+CHANGE COLUMN updated_at updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n \n+\n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n - [ ] Asignar bien los organizadores y los cronometradores\n"}, {"date": 1736715722916, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,8 +10,9 @@\n \n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n+- [ ] Copiar los nombre de las organizaciones a los usuarios\n - [ ] Asignar bien los organizadores y los cronometradores\n - [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n - [ ] Actualizar todos los estados de pagos de todos los eventos\n - [ ] Terminar todos los eventos del 2024\n"}, {"date": 1736724195147, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,21 @@\n ALTER TABLE eventos\n CHANGE COLUMN created_at created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n CHANGE COLUMN updated_at updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n \n+SELECT DISTINCT eventos.* \n+FROM eventos\n+LEFT JOIN user_organizacion ON eventos.idorganizacion = user_organizacion.idorganizacion \n+    AND user_organizacion.user_id = 123  -- reemplazar 123 por el ID del usuario\n+LEFT JOIN user_organizacion as uo_crono ON eventos.idcronometrador = uo_crono.idorganizacion \n+    AND uo_crono.user_id = 123  -- reemplazar 123 por el ID del usuario\n+WHERE \n+    user_organizacion.user_id IS NOT NULL \n+    OR uo_crono.user_id IS NOT NULL \n+    OR eventos.user_id = 123;  -- reemplazar 123 por el ID del usuario\n \n+\n+    \n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n - [ ] Copiar los nombre de las organizaciones a los usuarios\n"}, {"date": 1736861429086, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,24 +6,25 @@\n ALTER TABLE eventos\n CHANGE COLUMN created_at created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n CHANGE COLUMN updated_at updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n \n-SELECT DISTINCT eventos.* \n+SELECT DISTINCT eventos.*\n FROM eventos\n-LEFT JOIN user_organizacion ON eventos.idorganizacion = user_organizacion.idorganizacion \n+LEFT JOIN user_organizacion ON eventos.idorganizacion = user_organizacion.idorganizacion\n     AND user_organizacion.user_id = 123  -- reemplazar 123 por el ID del usuario\n-LEFT JOIN user_organizacion as uo_crono ON eventos.idcronometrador = uo_crono.idorganizacion \n+LEFT JOIN user_organizacion as uo_crono ON eventos.idcronometrador = uo_crono.idorganizacion\n     AND uo_crono.user_id = 123  -- reemplazar 123 por el ID del usuario\n-WHERE \n-    user_organizacion.user_id IS NOT NULL \n-    OR uo_crono.user_id IS NOT NULL \n+WHERE\n+    user_organizacion.user_id IS NOT NULL\n+    OR uo_crono.user_id IS NOT NULL\n     OR eventos.user_id = 123;  -- reemplazar 123 por el ID del usuario\n \n \n-    \n+\n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n+- [ ] Copiar las observaciones de los clientes desde SaaS a Crono (sacando la contraseña)\n - [ ] Copiar los nombre de las organizaciones a los usuarios\n - [ ] Asignar bien los organizadores y los cronometradores\n - [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n - [ ] Actualizar todos los estados de pagos de todos los eventos\n"}, {"date": 1736861709768, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -22,9 +22,9 @@\n \n ## ADMIN CONSULTAS\n \n - [ ] Buscar los eventos que están con usuario en 0\n-- [ ] Copiar las observaciones de los clientes desde SaaS a Crono (sacando la contraseña)\n+- [ ] Copiar las observaciones de los clientes y los eventos desde SaaS a Crono (sacando la contraseña)\n - [ ] Copiar los nombre de las organizaciones a los usuarios\n - [ ] Asignar bien los organizadores y los cronometradores\n - [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n - [ ] Actualizar todos los estados de pagos de todos los eventos\n@@ -34,8 +34,9 @@\n - [ ] Los eventos con cronometraje también tienen organizacion\n - [ ] Todos son publicos menos si en el nombre dicen \"entrenamiento\", \"prueba\" o \"test\", o tienen 0 inscriptos\n \n \n+\n ## COORDINAR EN SAAS\n \n - [ ] No se generan más servicios\n - [ ] Los pagos en pesos que se carguen en la moneda correcta\n"}, {"date": 1737380343998, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -163,5 +163,9 @@\n - Generar sector de cada Hardware en el sitio\n \n ### IDEAS MENORES\n \n-Hay un listado de va\n\\ No newline at end of file\n+Hay un listado de varias ideas sueltas para ordenar en [DEV Ideas Menores](./DEV%20Ideas%20Menores.md).\n+\n+### PEDIDOS PUNTUALES DE CLIENTES\n+\n+- OWA: Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)\n"}, {"date": 1737720468947, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,172 @@\n+# 🥇 CRONO > DEV\n+-------------------------------------------------------------------------------\n+\n+ALTER TABLE `users` CHANGE `tipo` `tipo` ENUM('','organizador','cronometrador','auto','admin','superadmin','ecuador') CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci NOT NULL DEFAULT '';\n+\n+ALTER TABLE eventos\n+CHANGE COLUMN created_at created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n+<PERSON>AN<PERSON> COLUMN updated_at updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP;\n+\n+SELECT DISTINCT eventos.*\n+FROM eventos\n+LEFT JOIN user_organizacion ON eventos.idorganizacion = user_organizacion.idorganizacion\n+    AND user_organizacion.user_id = 123  -- reemplazar 123 por el ID del usuario\n+LEFT JOIN user_organizacion as uo_crono ON eventos.idcronometrador = uo_crono.idorganizacion\n+    AND uo_crono.user_id = 123  -- reemplazar 123 por el ID del usuario\n+WHERE\n+    user_organizacion.user_id IS NOT NULL\n+    OR uo_crono.user_id IS NOT NULL\n+    OR eventos.user_id = 123;  -- reemplazar 123 por el ID del usuario\n+\n+## ERRORES NUEVOS\n+\n+- Error en las categorías de Acuatlón\n+\n+## ISSUE ORGANIZADOR\n+\n+- Módulo para premiación: Ordenar textos, sorteos y generales en el medio del podio\n+- Módulo para staff y gestión de tareas\n+- Caja con salidas y entradas de dinero\n+- Ver que hay eventos del tipo entrenamiento\n+\n+## ADMIN CONSULTAS\n+\n+- [ ] Buscar los eventos que están con usuario en 0\n+- [ ] Copiar las observaciones de los clientes y los eventos desde SaaS a Crono (sacando la contraseña)\n+- [ ] Copiar los nombre de las organizaciones a los usuarios\n+- [ ] Asignar bien los organizadores y los cronometradores\n+- [ ] Cerrar todos los eventos anteriores con los estados correspondientes\n+- [ ] Actualizar todos los estados de pagos de todos los eventos\n+- [ ] Terminar todos los eventos del 2024\n+- [ ] Cargar los pagos pendientes que tengo en soporte\n+- [ ] Cerrar Cajas\n+- [ ] Los eventos con cronometraje también tienen organizacion\n+- [ ] Todos son publicos menos si en el nombre dicen \"entrenamiento\", \"prueba\" o \"test\", o tienen 0 inscriptos\n+\n+\n+\n+## COORDINAR EN SAAS\n+\n+- [ ] No se generan más servicios\n+- [ ] Los pagos en pesos que se carguen en la moneda correcta\n+\n+\n+------------------------------------------------------------------------------\n+\n+### MEJORAS MENORES VARIAS\n+\n+- [ ] Limitar el vivo\n+- [ ] No funcionó el enlace de Mejor Etapa para el evento de Perú del último finde del 2024\n+- [ ] Avisar con alguna marca en los resultados y/o en el modificador de tiempos, que la prioridad del rfid y que va a estar la de fotocélulas en la pŕoxima versión\n+- [ ] Ordenar llegada según timer\n+- [ ] Nombre de equipo desde apellidos (lo pidió Quatro Vientos)\n+- [ ] Descuentos y adicionales por datos extras (valor y % en 2 campos separados)\n+- [ ] Largada y llegada mismo código\n+- [ ] Largada por categorías para Quatro Vientos\n+- [ ] Ver si se puede poner una carrera gratis con una pasarela de pago sin precio\n+- [ ] El filtro de etapas no funciona si no está iniciado el evento\n+- [ ] En el evento Vuelta Ciclística a Ibarra 2024 dio tiempos negativos, o sea que en largadas individuales puede estar viendose los negativos, que no deberían\n+- [ ] Agregar código de identificación en los MP como pidió Kittu\n+\n+\n+## ORDENAR\n+\n+- [ ] Entran miles de visitas por día, hay que optimizar la carga de los resultados desde html\n+- [ ] Puedes hacer una herramienta de ordenar columnas para crono y nombre de las columnas. Y activado o desactivado. Puede quedar algo bastante fácil y después eso armás un array y lo procesás en todos los resultados.\n+- [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n+- [ ] Poder importar a SaaS el listado de participantes de Crono, poner un valor y un producto y que te genere todas las facturas (en un futuro que lo mande por mail)\n+- [ ] https://www.honeybadger.io/tour/logging-observability/?utm_source=laravelnews&utm_medium=paid&utm_campaign=2024-10-21-laravelnews-sponsored-post\n+\n+\n+-------------------------------------------------------------------------------\n+## MILESTONES DEV\n+\n+**Actualizar framework y mejorar diseño**\n+\n+OBJETIVO: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales\n+\n+- [x] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n+- [ ] FILAMENT Gestión Eventos y Usuarios #294: poder generar eventos y modificarlos (la intensión es matar el historial y la generación de eventos para nosotros)\n+- [ ] FILAMENT Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n+\n+**Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n+\n+OBJETIVO: No configurar funcionalidades manualmente yo\n+\n+- [ ] FILAMENT Admin y Módulos #276: poder generar el acceso al nuevo módulo de eventos\n+- [ ] Limpiar los issues de features. UTILIZAR ACCIONES PARA TODA LA LÓGICA DE NEGOCIOS PENSANDO EN AI!\n+- [ ] Edades en las categorías #236: Hacer más fácil la configuración de las categorías\n+- [ ] DEPORTES #165: generar sistemas para cada deporte\n+- [ ] CHIPS #298: dejar de gestionar chips a mano\n+\n+**Automatizar cuentas**\n+\n+OBJETIVO: Reemplazar el Historial y automatizar pagos. Entran usuarios finales y tienen control total de sus eventos y datos.\n+\n+- FILAMENT Alta #263: permitimos que los usuarios nuevos generen eventos (Sin ver el tema pagos todavía)\n+- FILAMENT Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos\n+- FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n+- FILAMENT Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n+\n+**Generar sector de Eventos en el sitio**\n+\n+OBJETIVO: Sector de eventos, ayudar configuración con equipo de Crono\n+\n+- EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos\n+\n+**Generar un sector de Cursos con AMB para los Admins**\n+\n+**Terminar Nuevas Inscripciones**\n+\n+OBJETIVO: Potenciar las inscripciones a la altura del mercado\n+\n+- [ ] Terminar Oauth MP y generar documentación y MKT #264\n+- [ ] Agregar pagos Colombia (crear issue dividiendo #272)\n+- [ ] Ver si podemos poner las fotos en los tickets #292\n+- [ ] Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente\n+\n+**Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**\n+\n+**Crono sin errores**\n+\n+- Panel del participante con vídeo y tiempos\n+- Modificar tiempos nuevo\n+- Logs de TODO para recuperar información y para que lo vea el responsable\n+\n+**Métricas, Dashboard y KPIs automáticos con AI 📈**\n+\n+OBJETIVO: tener un dashboard con las métricas más importantes automatizadas\n+\n+- PAGOS Descuentos y Adicionales #268: poder ofrecer descuentos adicionales\n+- MÉTRICAS Automáticas con AI #279: poder ver por fin las métricas en Filament u afuera con AI\n+\n+\n+### SIGUEN\n+\n+- Métricas y KPIs automáticos con AI 📈\n+- Automatización y conexión con AI\n+- Agregar una función de Kiosko en la app\n+- Agregar compatibilidad para los equipos: Macsha xFTP y probar wireshark\n+- Agregar compatibilidad para Chafón x2\n+- Migrar base de datos relacional a Cloud MySQL\n+- Definí usar Cloud MySQL para los eventos y participantes. Luego para las lecturas y resultados, pasar a Firestore\n+- Generar Cloud Run con Containers\n+- Generar los procesos en Cloud Functions para los datos estáticos\n+- Generar los procesos para exportar los resultados a CDN\n+- Lograr primera etapa de CronoChat para la gestión de eventos y micrositios\n+- Desarrollar toda la gestión de pagos dentro de CronoChat\n+- Re-programar todo lo de los chips en la versión de la APP 3.x\n+- Conectar CronoChat a Whatsapp\n+- Lograr segunda etapa de CronoChat para las inscripciones de participantes y sus pagos\n+- Empezar con pruebas reales con Beacons\n+- Agregar un sector para administrar Chips y Beacons\n+- Agregar compatibilidad para: Pistola RFID, Chips Activos\n+- Generar sector de cada Hardware en el sitio\n+\n+### IDEAS MENORES\n+\n+Hay un listado de varias ideas sueltas para ordenar en [DEV Ideas Menores](./DEV%20Ideas%20Menores.md).\n+\n+### PEDIDOS PUNTUALES DE CLIENTES\n+\n+- OWA: Conectar Crono con SaaS (tener en cuenta que quiere tener distintos CUITs y distintos eventos facturarlos con esos CUITs)\n"}], "date": 1724702222253, "name": "Commit-0", "content": "# 🥇 CRONO > DEV\n-------------------------------------------------------------------------------\n\n## AHORA\n\n- [ ] Se está procesando otro informe de este evento\n- [ ] Tengo un error en Sentry +1 del pico de ayer: https://cronometraje-instantaneo.sentry.io/issues/5721694387/?referrer=alert_email&alert_type=email&alert_timestamp=1724458545264&alert_rule_id=11588895&notification_uuid=acfdd964-1be0-4b41-8b0f-46cf507186d3&environment=production\n- [ ] Limpiar lecturas con idevento o idcontrol = 0\n\n### ITRA\n\n- [ ] Armar itra\n- [ ] En duplas no se debería permitir tener dos nombres de equipos iguales\n- [ ] Cuando se elimina un campo y se exporta se corren del Excel todos los campos de lo que se elimino\n\n### ORDENAR\n\n- [ ] Pagos de Colombia también\n\n\n### ADMIN Filament\n### NOVEDADES:\n\n- Nuevo Módulo de Inscripciones (buscar bien todo)\n- Hora de apertura y cierre de inscripciones\n- Fecha hasta en los eventos\n- Agregamos estados a los eventos\n- Agregamos estados a los pagos\n- Reloj con múltiples parciales en vivo\n- Marcas de autos y motos\n- Mejorar diseño base ticket impreso\n- Reader Chafón\n\n- Backup en vídeo\n- Datos extras tipo archivo\n- Velocidad promedio\n\n### ORDENAR ÚLTIMO\n\n- Ya se me ocurrió como hacerlo, uso los 2 widgets que ya tenemos uno abajo del otro 💪\n\n\n### ADMIN Filament\n\n- No puedo mover un evento a una organización, da error 500\n\n\n### NUEVOS ERRORES\n\n- Revisar que ayer aparecía mucho lo del cache con el cambio de\n- El listado de categorías en las inscripciones tiene que también respetar el orden (ver en TeleQ Bike Fest)\n- Hay de utf en Sentry https://cronometraje-instantaneo.sentry.io/issues/5647946960/?referrer=regression_activity-email&notification_uuid=551694aa-5be5-4e79-8245-49ca497ab07a\n\n\n-------------------------------------------------------------------------------\n## MILESTONES DEV\n\n**Automatizar cuentas**\n\nOBJETIVO: automatizar los pagos, sincronizar SaaS. Todavía no entran usuarios finales, pero ya no hago nada manual\n\n- EVENTOS Precio #193: agregar tabla descuentos, tipos de eventos y precios final (por ahora en Admin viejo)\n- AUTO-ADMIN #132: acomodamos todos los scripts\n- PAGOS Automatizar #242: Terminar lo que ya avanzaste de DLocalGo\n- EVENTOS Pagos #227: todo lo de las pasarelas para que paguen automáticamente\n\n\n**Actualizar framework y mejorar diseño**\n\nOBJETIVO: el equipo de Crono pueda gestionar todo. Todavía no entran usuarios finales\n\n- [ ] FILAMENT Admin #276: poder generar el panel eventos y Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos\n- [ ] FILAMENT Terminar Login #120: poder loguearse, generar usuarios y cambiar contraseñas\n- [ ] FILAMENT Roles #154: Definimos ya la realidad de los usuarios, que funcione en FILAMENT, pero mantener compatibilidad con admin\n\n**Terminar funcionalidades: vídeos, puntos, velocidad promedio, datos extras de archivos**\n\nOBJETIVO: No configurar funcionalidades manualmente yo\n\n- Limpiar los issues de features\n\n**Generar sector de Eventos en el sitio**\n\nOBJETIVO: Sector de eventos, ayudar configuración con equipo de Crono\n\n- EVENTOS Estados #104: Permite gestionar mejor lo pendiente (pendiente, iniciado, terminado, postergado (sin fecha), cancelado) y muestra la información que va a salir en /eventos\n- EVENTOS Listado #46: Empezamos a tener el script y el listado de eventos\n\n**Métricas, Dashboard y KPIs automáticos con AI 📈**\n\nOBJETIVO: tener un dashboard con las métricas más importantes automatizadas\n\n- PAGOS Descuentos y Adicionales #268: poder ofrecer descuentos adicionales\n- MÉTRICAS Automáticas con AI #279: poder ver por fin las métricas en Filament u afuera con AI\n\n**Generar procesos con pasarelas de pagos automatizadas**\n\nOBJETIVO: Potenciar las inscripciones a la altura del mercado\n\n- Pagos Precios #264: Terminar una primera etapa con el botón y lo hecho actualmente\n\n**Generar ventanas para configurar de eventos, organizadores y cronometradores**\n\nOBJETIVO: Reemplazar el Historial. Entran usuarios finales y tienen control total de sus eventos y datos.\n\n- EVENTOS Alta #263: permitimos que los usuarios nuevos generen eventos (Sin ver el tema pagos todavía)\n\n**Generar un sector de Cursos con AMB para los Admins**\n\n**Generar ventanas para la creación de Micrositios (sólo para usuarios al principio)**\n\n**Crono sin errores**\n\n- Panel del participante con vídeo y tiempos\n- Modificar tiempos nuevo\n- Logs de TODO para recuperar información y para que lo vea el responsable\n\n### SIGUEN\n\n- Métricas y KPIs automáticos con AI 📈\n- Automatización y conexión con AI\n- Rediseñar sistema con Deportes e Idiomas\n- Generar sector de Deportes en el sitio\n- Agregar una función de Kiosko en la app\n- Agregar compatibilidad para los equipos: Macsha xFTP y probar wireshark\n- Agregar compatibilidad para Chafón x2\n- Migrar base de datos relacional a Cloud MySQL\n- Definí usar Cloud MySQL para los eventos y participantes. Luego para las lecturas y resultados, pasar a Firestore\n- Generar Cloud Run con Containers\n- Generar los procesos en Cloud Functions para los datos estáticos\n- Generar los procesos para exportar los resultados a CDN\n- Lograr primera etapa de CronoChat para la gestión de eventos y micrositios\n- Desarrollar toda la gestión de pagos dentro de CronoChat\n- Re-programar todo lo de los chips en la versión de la APP 3.x\n- Conectar CronoChat a Whatsapp\n- Lograr segunda etapa de CronoChat para las inscripciones de participantes y sus pagos\n- Empezar con pruebas reales con Beacons\n- Agregar un sector para administrar Chips y Beacons\n- Agregar compatibilidad para: Pistola RFID, Chips Activos\n- Generar sector de cada Hardware en el sitio\n\n### IDEAS MENORES\n\nHay un listado de varias ideas sueltas para ordenar en [DEV Ideas Menores](./DEV%20Ideas%20Menores.md).\n"}]}