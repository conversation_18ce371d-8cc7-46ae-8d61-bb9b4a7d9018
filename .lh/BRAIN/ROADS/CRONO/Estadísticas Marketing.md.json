{"sourceFile": "BRAIN/ROADS/CRONO/Estadísticas Marketing.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1753222382819, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1753222382819, "name": "Commit-0", "content": "# 📊 Estadísticas Marketing - Documentación Técnica\n\n## 🏗️ Implementación Actual\n\n### Estructura de Base de Datos\n\n#### Tabla `marketing_tracking`\n```sql\nCREATE TABLE marketing_tracking (\n    idtracking INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,\n    idevento INT UNSIGNED NOT NULL,\n    idinscripcion INT UNSIGNED NULL,\n    utm_source VARCHAR(100) NULL,\n    utm_medium VARCHAR(100) NULL,\n    utm_campaign VARCHAR(100) NULL,\n    conversion_step ENUM('event_visit', 'form_complete', 'payment_complete') NOT NULL,\n    conversion_value DECIMAL(10,2) NULL,\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    INDEX idx_evento (idevento),\n    INDEX idx_inscripcion (idinscripcion),\n    INDEX idx_conversion (conversion_step),\n    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE,\n    FOREIGN KEY (idinscripcion) REFERENCES participantes(idinscripcion) ON DELETE SET NULL\n);\n```\n\n#### Tabla `marketing_pixels`\n```sql\nCREATE TABLE marketing_pixels (\n    idpixel INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,\n    idevento INT UNSIGNED NOT NULL,\n    platform ENUM('meta', 'google') NOT NULL,\n    pixel_id VARCHAR(100) NOT NULL,\n    conversion_step ENUM('event_visit', 'form_complete', 'payment_complete') NOT NULL,\n    enabled TINYINT DEFAULT 1,\n    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,\n    INDEX idx_evento (idevento),\n    INDEX idx_platform (platform),\n    INDEX idx_step (conversion_step),\n    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE\n);\n```\n\n### Clases Implementadas\n\n#### MarketingTracking.php\n- **Optimización**: Solo se inicializa si `eventos.marketing = 1`\n- **Lógica**: Solo guarda registros si hay UTM o es paso de conversión importante\n- **Tracking**: Basado en `idinscripcion` (no session_id)\n\n#### MarketingPixels.php\n- **Carga dinámica**: Pixels desde base de datos\n- **Configuración granular**: Por paso de conversión\n- **Código reutilizable**: Meta Pixel en archivo JS externo\n\n## 🎛️ Ventanas de Configuración (Futuras)\n\n### 1. Panel de Configuración de Evento\n\n#### Sección: Marketing\n```\n☑️ Habilitar tracking de marketing para este evento\n\n📊 Pixels de Conversión\n┌─────────────────────────────────────────────────────────┐\n│ Plataforma │ ID del Pixel │ Paso de Conversión │ Estado │\n├─────────────────────────────────────────────────────────┤\n│ Meta       │ 729777596... │ Pago completado    │ ✅     │\n│ Google     │ G-XXXXXXXXX  │ Formulario complet │ ✅     │\n│ Meta       │ 729777596... │ Visita al evento  │ ❌     │\n└─────────────────────────────────────────────────────────┘\n\n[+ Agregar Pixel]\n```\n\n#### Campos por Pixel:\n- **Plataforma**: Dropdown (Meta, Google)\n- **ID del Pixel**: Input text (validación por formato)\n- **Paso de Conversión**: Dropdown (event_visit, form_complete, payment_complete)\n- **Estado**: Toggle (enabled/disabled)\n- **Valor de Conversión**: Checkbox (incluir valor del pago)\n\n\n## 📈 Reportes y Estadísticas\n\n### 1. Funnel de Conversión\n\n#### Query Principal:\n```sql\nSELECT \n    idevento,\n    conversion_step,\n    COUNT(*) as total,\n    COUNT(DISTINCT idinscripcion) as inscripciones_unicas,\n    AVG(conversion_value) as valor_promedio,\n    SUM(conversion_value) as valor_total\nFROM marketing_tracking \nWHERE idevento = ? \nGROUP BY conversion_step\nORDER BY \n    CASE conversion_step \n        WHEN 'event_visit' THEN 1\n        WHEN 'form_complete' THEN 2\n        WHEN 'payment_complete' THEN 3\n    END;\n```\n\n#### Métricas a Mostrar:\n- **Visitas**: Total de visitas al formulario\n- **Formularios Completados**: Tasa de conversión (form/visitas)\n- **Pagos Completados**: Tasa de conversión (pagos/form)\n- **Valor Total**: Suma de todos los pagos\n- **Valor Promedio**: Promedio por inscripción\n\n### 2. Análisis por Canal (UTM)\n\n#### Query:\n```sql\nSELECT \n    utm_source,\n    utm_medium,\n    utm_campaign,\n    COUNT(*) as total_visitas,\n    COUNT(CASE WHEN conversion_step = 'form_complete' THEN 1 END) as formularios,\n    COUNT(CASE WHEN conversion_step = 'payment_complete' THEN 1 END) as pagos,\n    AVG(conversion_value) as valor_promedio,\n    SUM(conversion_value) as valor_total\nFROM marketing_tracking \nWHERE idevento = ? AND utm_source IS NOT NULL\nGROUP BY utm_source, utm_medium, utm_campaign\nORDER BY pagos DESC;\n```\n\n#### Métricas por Canal:\n- **Costo por Inscripción**: (Costo campaña / Inscripciones)\n- **ROI**: (Valor total - Costo campaña) / Costo campaña\n- **Tasa de Conversión**: Inscripciones / Visitas\n- **Valor por Visita**: Valor total / Visitas\n\n### 3. Tendencias Temporales\n\n#### Query:\n```sql\nSELECT \n    DATE(created_at) as fecha,\n    conversion_step,\n    COUNT(*) as total,\n    SUM(conversion_value) as valor_total\nFROM marketing_tracking \nWHERE idevento = ? \n    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)\nGROUP BY DATE(created_at), conversion_step\nORDER BY fecha DESC;\n```\n\n#### Gráficos:\n- **Línea temporal**: Visitas por día\n- **Stacked bars**: Pasos de conversión por día\n- **Heatmap**: Actividad por hora del día\n\n### 4. Comparación entre Eventos\n\n#### Query:\n```sql\nSELECT \n    e.nombre as evento,\n    e.fecha,\n    COUNT(mt.idtracking) as total_tracking,\n    COUNT(DISTINCT mt.idinscripcion) as inscripciones,\n    AVG(mt.conversion_value) as valor_promedio,\n    SUM(mt.conversion_value) as valor_total\nFROM eventos e\nLEFT JOIN marketing_tracking mt ON e.idevento = mt.idevento\nWHERE e.marketing = 1\nGROUP BY e.idevento\nORDER BY e.fecha DESC;\n```\n\n## 🎨 Interfaz de Usuario\n\n### 1. Dashboard Principal\n\n#### Layout:\n```\n┌─────────────────────────────────────────────────────────┐\n│ 📊 Funnel de Conversión                                │\n│ Visitas: 1,234 | Formularios: 456 | Pagos: 123        │\n│ Tasa: 37% → 27% | Valor Total: $12,345                │\n└─────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────┐\n│ 🎯 Canales de Marketing                               │\n│ Facebook: 45 inscripciones | Google: 32 | Email: 28   │\n│ ROI: 340% | 280% | 420%                               │\n└─────────────────────────────────────────────────────────┘\n\n┌─────────────────────────────────────────────────────────┐\n│ 📈 Tendencias (Últimos 30 días)                       │\n│ [Gráfico de líneas]                                   │\n└─────────────────────────────────────────────────────────┘\n```\n\n### 2. Reportes Detallados\n\n#### Filtros Disponibles:\n- **Rango de fechas**: Date picker\n- **Paso de conversión**: Multi-select\n- **Canal UTM**: Dropdown con opciones\n- **Valor mínimo**: Input numérico\n\n#### Exportación:\n- **CSV**: Datos crudos para análisis externo\n- **PDF**: Reporte formateado\n- **Excel**: Con gráficos y fórmulas\n\n### 3. Configuración de Pixels\n\n#### Interfaz:\n```\n┌─────────────────────────────────────────────────────────┐\n│ ⚙️ Configuración de Pixels                            │\n│                                                        │\n│ Meta Pixel: [729777596302705] [✅ Habilitado]        │\n│ Google Analytics: [G-XXXXXXXXXX] [✅ Habilitado]     │\n│                                                        │\n│ Eventos a trackear:                                   │\n│ ☑️ Visita al formulario                              │\n│ ☑️ Formulario completado                             │\n│ ☑️ Pago completado                                   │\n└─────────────────────────────────────────────────────────┘\n```\n\n## 🔧 Implementación Técnica\n\n### 1. Nuevas Clases Necesarias\n\n#### MarketingReports.php\n```php\nclass MarketingReports {\n    public function getFunnelData($idevento, $fecha_inicio, $fecha_fin);\n    public function getChannelData($idevento, $fecha_inicio, $fecha_fin);\n    public function getTrendData($idevento, $dias);\n    public function getEventComparison($organizador_id);\n}\n```\n\n#### MarketingConfig.php\n```php\nclass MarketingConfig {\n    public function savePixel($idevento, $platform, $pixel_id, $steps);\n    public function getPixels($idevento);\n    public function updatePixelStatus($idpixel, $enabled);\n    public function deletePixel($idpixel);\n}\n```\n\n### 2. Endpoints API\n\n#### GET /api/marketing/funnel/{idevento}\n```json\n{\n    \"event_visit\": {\"total\": 1234, \"rate\": 100},\n    \"form_complete\": {\"total\": 456, \"rate\": 37},\n    \"payment_complete\": {\"total\": 123, \"rate\": 27, \"value\": 12345}\n}\n```\n\n#### GET /api/marketing/channels/{idevento}\n```json\n[\n    {\n        \"utm_source\": \"facebook\",\n        \"utm_medium\": \"social\",\n        \"visits\": 234,\n        \"conversions\": 45,\n        \"value\": 4500,\n        \"roi\": 340\n    }\n]\n```\n\n### 3. Integración con Frontend\n\n#### JavaScript para Gráficos:\n```javascript\n// Chart.js para gráficos\nconst funnelChart = new Chart(ctx, {\n    type: 'doughnut',\n    data: {\n        labels: ['Visitas', 'Formularios', 'Pagos'],\n        datasets: [{\n            data: [1234, 456, 123],\n            backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384']\n        }]\n    }\n});\n```\n\n## 🚀 Roadmap de Desarrollo\n\n### Fase 1: Configuración UI\n- [ ] Panel de configuración de pixels\n- [ ] Validación de IDs de pixels\n- [ ] Gestión de estados (enabled/disabled)\n\n### Fase 2: Reportes Básicos\n- [ ] Dashboard con métricas principales\n- [ ] Funnel de conversión\n- [ ] Análisis por canal\n\n### Fase 3: Reportes Avanzados\n- [ ] Tendencias temporales\n- [ ] Comparación entre eventos\n- [ ] Exportación de datos\n\n### Fase 4: Optimizaciones\n- [ ] Caching de reportes\n- [ ] Filtros avanzados\n- [ ] Alertas automáticas\n\n## 📊 KPIs Importantes\n\n### Para Organizadores:\n- **Costo por Inscripción**: < $10\n- **Tasa de Conversión**: > 25%\n- **ROI de Marketing**: > 300%\n\n### Para la Plataforma:\n- **Eventos con Marketing**: > 20%\n- **Datos de Tracking**: > 10,000 registros/mes\n- **Uso de Pixels**: > 80% de eventos activos\n\n---\n\n**Nota**: Esta documentación debe actualizarse conforme se implementen las nuevas funcionalidades. "}]}