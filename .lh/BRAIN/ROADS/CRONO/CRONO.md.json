{"sourceFile": "BRAIN/ROADS/CRONO/CRONO.md", "activeCommit": 0, "commits": [{"activePatchIndex": 10, "patches": [{"date": 1724679000477, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726575016123, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,11 +9,11 @@\n \n \n ## PRÓXIMOS MILESTONES\n \n-- Terminar Nuevas Inscripciones: Lograr que los usuarios puedan inscribirse en eventos con los pagos automatizados\n - Auto Admin: Lograr la administración automática completa\n - Filament: Llegar hasta que el equipo de Crono pueda gestionar todo (especialmente el nuevo sistema de inscripciones)\n+- Terminar Nuevas Inscripciones: Lograr que los usuarios puedan inscribirse en eventos con los pagos automatizados\n \n - MKT y Mediciones con AI: Generar contenido mensual y tener configurado el sistema de mediciones con AI\n - Micrositios: Poder ofrecer micrositios a los usuarios que se registran y configuran todo sin soporte\n \n"}, {"date": 1735965845604, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,14 +9,13 @@\n \n \n ## PRÓXIMOS MILESTONES\n \n-- Auto Admin: Lograr la administración automática completa\n-- Filament: <PERSON><PERSON>ar hasta que el equipo de Crono pueda gestionar todo (especialmente el nuevo sistema de inscripciones)\n-- Terminar Nuevas Inscripciones: Lograr que los usuarios puedan inscribirse en eventos con los pagos automatizados\n+Las 3 prioridades van a ser\n \n-- MKT y Mediciones con AI: Generar contenido mensual y tener configurado el sistema de mediciones con AI\n-- Micrositios: Poder ofrecer micrositios a los usuarios que se registran y configuran todo sin soporte\n+- Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\n+- MKT de micro nicho por deporte y con mucho vídeo\n+- Integración con Hardware en App v3.x\n \n \n ## 🏄‍♂️ BOARDS\n \n"}, {"date": 1743681707524, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,9 +15,24 @@\n - Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\n - MKT de micro nicho por deporte y con mucho vídeo\n - Integración con Hardware en App v3.x\n \n+Queremos terminar con esta completa gama de servicios\n \n+```\n+La solución completa para eventos de Rally cuenta con:\n+- Micrositio Web para el evento\n+- Micrositio Web para la organización\n+- Sistema para organizadores de Rally\n+- Sistema para cronometradores de Rally\n+- Sistema para pilotos de Rally\n+- Equipos para cronometraje de Rally\n+- Cursos y capacitación personalizada a todo el personal\n+- Soporte técnico y acompañamiento durante el evento\n+- Agente de Inteligencia Artificial\n+```\n+\n+\n ## 🏄‍♂️ BOARDS\n \n | Dev                                                                | Growth                                                        | Soporte                                                        |\n | ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n"}, {"date": 1747318730187, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-# 🥇 ROADS > CRONO\n+# 🏅 ROADS > CRONO\n -------------------------------------------------------------------------------\n \n ## 📊 PLAN\n \n"}, {"date": 1748287023252, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,13 +33,12 @@\n \n \n ## 🏄‍♂️ BOARDS\n \n-| Dev                                                                | Growth                                                        | Soporte                                                        |\n-| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n-| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n-| [BOARD](https://gitlab.com/cronometrajeinstantaneo/admin/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2270118842) | [TODO](https://app.todoist.com/app/project/soporte-2270118848) |\n-| [TODO](https://app.todoist.com/app/project/dev-2279370760)         |                                                               |                                                                |\n+[DEV](./DEV.md)\n+[GROWTH](./GROWTH.md)\n+[SOPORTE](./SOPORTE.md)\n \n+\n ## 🏃🏊‍♂️ INRACE\n \n [INRACE](./INRACE.md)\n\\ No newline at end of file\n"}, {"date": 1748287070594, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,11 @@\n \n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md)\n+\n [GROWTH](./GROWTH.md)\n+\n [SOPORTE](./SOPORTE.md)\n \n \n ## 🏃🏊‍♂️ INRACE\n"}, {"date": 1752008843963, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,15 +3,17 @@\n \n ## 📊 PLAN\n \n [PLAN](./PLAN.md)\n+_En el plan se escribe la visión, misión y objetivos generales pero sin describir las tareas o prioridades_\n \n [KPIs](./KPIs.md)\n+_En los KPIs se escribe todo lo que se va a medir y como se va a medir_\n \n \n ## PRÓXIMOS MILESTONES\n \n-Las 3 prioridades van a ser\n+Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:\n \n - Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\n - MKT de micro nicho por deporte y con mucho vídeo\n - Integración con Hardware en App v3.x\n"}, {"date": 1752016545080, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,28 +13,22 @@\n ## PRÓXIMOS MILESTONES\n \n Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:\n \n+**MOVER GROWTH**\n+OBJETIVO: Empezar a mover todo el MKT a Growth y generar un hábito\n+- [ ] Charlas de cronometraje\n+- [ ] Reunión Andres Colombia y Preparar Contrato\n+- [ ] Actualizar novedades y recuperar newsletter\n+- [ ] Vídeo de llegadas muy juntas en DH @mobile (Puedo hacer pruebas de chips para demostrar que no es preciso)\n+\n+\n - Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\n - MKT de micro nicho por deporte y con mucho vídeo\n - Integración con Hardware en App v3.x\n \n-Queremos terminar con esta completa gama de servicios\n \n-```\n-La solución completa para eventos de Rally cuenta con:\n-- Micrositio Web para el evento\n-- Micrositio Web para la organización\n-- Sistema para organizadores de Rally\n-- Sistema para cronometradores de Rally\n-- Sistema para pilotos de Rally\n-- Equipos para cronometraje de Rally\n-- Cursos y capacitación personalizada a todo el personal\n-- Soporte técnico y acompañamiento durante el evento\n-- Agente de Inteligencia Artificial\n-```\n \n-\n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md)\n \n"}, {"date": 1752079324007, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,9 +29,9 @@\n \n \n ## 🏄‍♂️ BOARDS\n \n-[DEV](./DEV.md)\n+[DEV](./DEV.md#milestones-dev)\n \n [GROWTH](./GROWTH.md)\n \n [SOPORTE](./SOPORTE.md)\n"}, {"date": 1752086688515, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,22 +13,21 @@\n ## PRÓXIMOS MILESTONES\n \n Las próximas prioridades, extraidas de los 3 boards, incluyendo DEV, GROWTH y SOPORTE, son:\n \n-**MOVER GROWTH**\n-OBJETIVO: Empezar a mover todo el MKT a Growth y generar un hábito\n+@growth\n+MS: Empezar a mover todo el MKT a Growth y generar un hábito\n - [ ] Charlas de cronometraje\n - [ ] Reunión Andres Colombia y Preparar Contrato\n - [ ] Actualizar novedades y recuperar newsletter\n - [ ] Vídeo de llegadas muy juntas en DH @mobile (Puedo hacer pruebas de chips para demostrar que no es preciso)\n \n+@dev \n+- [ ] Transferencias Inteligentes #303: poder transferir dinero a un evento y adjuntar el comprobante\n+- [ ] Pixels de MKT #306: poder agregar pixels de MKT a los eventos\n+- [ ] PAGOS Oauth MP #307: Terminar Oauth de MercadoLibre\n \n-- Toda la gestión y muestra de eventos (Con nueva admin y micrositio)\n-- MKT de micro nicho por deporte y con mucho vídeo\n-- Integración con Hardware en App v3.x\n \n-\n-\n ## 🏄‍♂️ BOARDS\n \n [DEV](./DEV.md#milestones-dev)\n \n"}], "date": 1724679000477, "name": "Commit-0", "content": "# 🥇 ROADS > CRONO\n-------------------------------------------------------------------------------\n\n## 📊 PLAN\n\n[PLAN](./PLAN.md)\n\n[KPIs](./KPIs.md)\n\n\n## PRÓXIMOS MILESTONES\n\n- Terminar Nuevas Inscripciones: Lograr que los usuarios puedan inscribirse en eventos con los pagos automatizados\n- Auto Admin: Lograr la administración automática completa\n- Filament: L<PERSON>ar hasta que el equipo de Crono pueda gestionar todo (especialmente el nuevo sistema de inscripciones)\n\n- MKT y Mediciones con AI: Generar contenido mensual y tener configurado el sistema de mediciones con AI\n- Micrositios: Poder ofrecer micrositios a los usuarios que se registran y configuran todo sin soporte\n\n\n## 🏄‍♂️ BOARDS\n\n| Dev                                                                | Growth                                                        | Soporte                                                        |\n| ------------------------------------------------------------------ | ------------------------------------------------------------- | -------------------------------------------------------------- |\n| [DEV](./DEV.md)                                                    | [GROWTH](./GROWTH.md)                                         | [SOPORTE](./SOPORTE.md)                                        |\n| [BOARD](https://gitlab.com/cronometrajeinstantaneo/admin/-/boards) | [TODO](https://app.todoist.com/app/project/growth-2270118842) | [TODO](https://app.todoist.com/app/project/soporte-2270118848) |\n| [TODO](https://app.todoist.com/app/project/dev-2279370760)         |                                                               |                                                                |\n\n## 🏃🏊‍♂️ INRACE\n\n[INRACE](./INRACE.md)"}]}