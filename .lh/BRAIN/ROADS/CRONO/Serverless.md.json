{"sourceFile": "BRAIN/ROADS/CRONO/Serverless.md", "activeCommit": 0, "commits": [{"activePatchIndex": 1, "patches": [{"date": 1736692956849, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1736701917119, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,29 @@\n+## DESCRIPCIÓN\n+\n+Nuestra empresa brinda servicios de gestión de participantes, cronometraje y resultados en eventos deportivos. Tenemos un uso moderado de lunes a viernes, principalmente de configuración de eventos y de inscripciones de participantes. Los fines de semana, tenemos un uso intensivo de cronometraje y resultados. Trabajamos en todo América Latina en varios deportes diferentes, entre ellos hay muchos puntos de trabajo offline con nuestras aplicaciones que luego se van sincronizando.\n+\n+Los sábados y principalmnete los domingos, tenemos varios miles de tomas de tiempo en nuestras aplicaciones desde distintos países, que llegan a nuestra API y se guardan en una tabla de MySQL. Luego son procesados por queues para generar los tiempos y los resultados.\n+\n+El problema que estamos teniendo es que la base de datos y el servidor se satura y los resultados se demoran. Por eso, queremos migrar a una arquitectura serverless para poder escalar automáticamente y no tener problemas de saturación.\n+\n+Somos una empresa muy chica y soy el único desarrollador. Por eso, necesito la ayuda de ustedes en seleccionar las tecnologías correctas, ya que no puedo darme el lujo de equivocarme, ni de hacer pruebas en distintas tecnologías.\n+\n+## SERVIDOR ACTUAL\n+\n+Hoy tengo una instancia en Google Cloud Engine con un clásico LAMP (Ubuntu + PHP 8.3 + Mysql + Apache), y uso mucho el framework Laravel. Para todas nuestras herramientas de gestión, este servidor es suficiente, funciona perfecto y no tengo intensiones de cambiar la arquitectura. Pero para los tiempos y los resultados, no lo es.\n+\n+## IDEAS\n+\n+Estas son algunas de las ideas que estoy planeando:\n+\n+*Cloud MySQL*: para los datos relacionados. Si bien sería lo lógico, vimos que no me da un beneficio significativo y si me eleva muchísimo el costo. Por el momento quiero mantener el Mysql en nuestra instancia y que se pueda conectar desde Cloud Functions.\n+\n+*Firestore Realtime Database*: Si pasamos las tablas de tomas de tiempos y resultados a esta base de datos, podríamos modificar nuestras aplicaciones para que la sincronización sea más automática (hoy es todo código propio). Luego los procesos de colas y generación de resultados se podrían hacer con Cloud Functions. Para esto tendríamos mucho trabajo en duplicar los datos entre las bases de datos para no perder la historia e incluso poder hacerlo de a poco.\n+\n+*Cloud Functions*: para los scripts, queues y procesos varios. Hoy están en queues de Laravel, pero si los paso a Cloud Functions, podría escalar automáticamente y no tener problemas de saturación. Utilizo en otro proyecto Lambda en AWS y los costos son muy lógicos y accesibles. Supongo que en este caso será lo mismo.\n+\n+*Cloud CDN*: otra opción que estuve viendo es poder seguir trabajando con los queues de Laravel, pero los resultados subirlos a Cloud CDN y de ahí programar para que los resultados se actualicen en las apps desde ahí. Parece ser más fácil para desarrollar pero no se si será suficientemente rápido, ni los costos que puede traer.\n+\n+*Cloud Storage*: para los datos estáticos web, calendarios y eventos (micro sitios). Hoy generamos archivos html que se guardan en archivos de la instancia. Creo que si estos archivos los genera un CLoud Function y lo sube directamente a Cloud Storage, sería mucho más escalable.\n+\n+*Cloud Run*: más adelante para poder pasar todo a serverless, puede ser una buena opción migrar mi Laravel a este servicio. Pero por el momento no tengo intenciones de hacerlo por el costo que puede traer. ¿Creen que va a ser costoso mantener un Cloud Run con un Laravel? Hoy el costo mensual de mi instancia es muy bajo, menos de 100 dólares y cubre perfectamente lo que necesito.\n\\ No newline at end of file\n"}], "date": 1736692956849, "name": "Commit-0", "content": ""}]}