{"sourceFile": "BRAIN/ROADS/CRONO/SOPORTE Capacitaciones.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1744725544143, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1746713091852, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,148 @@\n+# CAPACITACIONES\n+\n+\n+Chips de otras carreras, ubicación antenas, control de puesto real\n+\n+## CURSOS\n+\n+- Informe de largadas: https://cronometrajeinstantaneo.com/resultados/32-rally-rac-1000/largadas?control=PQAC\n+\n+## TEXTOS VENTAS\n+\n+El sistema cuenta con un widgets que generan información para utilizar en softwares de streaming para TV y redes sociales (OBS Studio, vMix, Wirecast, Streamlabs OSB, etc.):\n+\n+- Meta (últimos participantes en llegada)\n+- Participantes en pista\n+- Participante con Reloj\n+- Sólo Reloj\n+- Sólo Participante\n+- Podio (principal o por categoría)\n+\n+Trabajamos junto con el equipo de diseño para adaptar la identidad de los layouts, ya que podemos adaptar el diseño según la necesidad.\n+\n+Algunos ejemplos de eventos anteriores son:\n+\n+https://www.youtube.com/live/sjADfxyLfP0\n+https://www.youtube.com/live/fmUpiYIEMI0\n+https://fb.watch/nHOdti1bJz/?mibextid=Nif5oz\n+\n+\n+## VIVO EN INSTAGRAM\n+\n+- Hacer listado de preguntas\n+- Probar invitación antes\n+- Usar mic para que se escuche mejor\n+- Sacar foto mientras hago vivo\n+\n+## FEEDBACK DE USHUAIA PARA ORDENAR CRONOMETRAJE CON CHIPS\n+\n+- En acreditaciones se cruzaron chips\n+- No pudimos trabajar viendo la computadora de chips por lo que no sabíamos cuáles pasaban sin leer o con lectura cruzada\n+- No pudimos usar las cámaras, la TV llegó muy tarde, no pudimos estar con el TV y la gente tapaba las cámaras\n+- El backup con app fue muy difícil porque se cruzaba mucha gente\n+- No tuvimos la información de los DNF ordenada, la fuimos subiendo tarde y como pudimos\n+- La app de Griselda se sincronizó durante la entrega de premios, faltaban muchos tiempos y tenía muchos errores\n+- Tendría que haber ido con nube híbrida por la falla de Internet\n+\n+- Tener un plano del parque cerrado, arco y lugar para cronometradores con tiempo y exigir que se cumpla\n+- Parque cerrado sin participantes o sector privado para cronometraje cuidando cables, cámaras, backup con apps, y gazebo\n+- CCTV con TV propia y cuidada\n+- Persona 100m antes avisando a los que no se les ve el número\n+- En las acreditaciones se cruzaron chips. Lo ideal es asignarlos antes y sólo probarlos durante la acreditación. También sería bueno tener un kiosko a parte para auto-chequeo\n+- La computadora con los chips y las cámaras es trabajo de una persona viendo si se escapan\n+- Acordar comunicación de DNFs por un sólo canal y con un sólo respondable\n+\n+- No aceptar armado sin tiempo\n+- No permitir excepciones, es poco profesional y tu laburo sale mal\n+- En eventos medianos y grandes, el responsable no puede estar cronometrando, sino pendiente de todo\n+\n+\n+\n+-------\n+Si, buena pregunta, tendría que haberlo explicado. Cuando pasas un chip ya asignado y hay un número ingresado, el sistema re-asigna el chip\n+\n+\n+---\n+\n+En responsable de cronometraje:\n+- Todo lo que te pasó en Ushuaia\n+- Listado de personas que van a cronometrar\n+- Listado de materiales\n+- Conocer el reglamento completo\n+- BackUps con cámara\n+- Manejo del Modificador de tiempos\n+- Coordinar prueba con los operadores\n+- Coordinar con el responsable de acreditaciones\n+\n+Para tener en cuenta con RFID y eventos grandes:\n+- El Control de chips en largadas permite detectar participantes con cambios de distancias sin informar\n+- Agregar los CONTROLAR XXX en los resultados antes de entregar los podios\n+- Es ideal que los kits con sus números y chips ya estén en las bolsas listas antes de las acreditaciones\n+- El control de chips es obligatorio, idealmente con un kiosko y una persona para los cambios\n+\n+\n+## CURSO CRONO + 1000 RUNNING\n+\n+Me falta escribir:\n+\n+- Acuerdo con el organizador (contrato de cronometraje)\n+- Puestos de control y horario de corte\n+- Mostrar resultados instantaneos\n+- Cooperar con locutor y fotógrafos\n+- Backup manual y/o segunda línea\n+- Backup con vídeo\n+- Cronograma de armado, desarme y descanso\n+- Control de materiales, cables y prueba de chips\n+- Control de cronometraje y armado de podio (chips, repetidos, ignorar)\n+- Estar en el podio\n+- Control de devolución de chips\n+- Puntos, ITRA, UTMB, velocidad promedio, etc.\n+\n+\n+\n+Temas a tener en cuenta para un running de más de 1000 participantes\n+\n+*Presupuesto y profesionalidad*\n+\n+Desde el comienzo es importante planear un servicio profesional en todo sentido y hay que tener en cuenta todo lo necesario dentro del presupuesto.\n+\n+*Inscripciones y asignación de chips*\n+\n+Hay que llegar a un acuerdo con el organizador de cómo se van a registrar las inscripciones, poniendo énfasis en que como máximo 48hs antes del evento ya esté todo migrado al sistema y no se utilicen otros medios de inscripciones o carga de datos por fuera.\n+\n+La asignación y control de chips debe estar resuelto también 48hs antes.\n+\n+Lady se compromete a ver los vídeos del manejo de excel y a probar distintos tipos de importaciones y asignación de chips con el lector USB:\n+\n+- Para la importación y manejo de Excel: https://cronometrajeinstantaneo.com/cursos/importar-exportar-participantes/\n+- Para la asignación manual de chips: https://cronometrajeinstantaneo.com/cursos/cronometraje-con-chips/\n+\n+Además van a publicar en todos los medios posibles (web, redes sociales, qr, mails, etc.) el listado de participantes final para que los mismos participantes puedan comprobar que sus datos estén bien.\n+\n+*Acreditaciones*\n+\n+Hay que tener planeada la acreditación para que no se generen filas y haya tiempo disponible para corregir los que estén mal categorizados. Hay muchas variables para ver, pero hay que pensarlo con tiempo y tener la gente y equipos necesarios para que fluyan como corresponde.\n+\n+Lady se compromete a probar las apps de acreditaciones y a analizar la acreditación del próximo evento.\n+\n+*Diseño de parque cerrado, conexiones, electrcidad*\n+\n+De antemano debe estar acordado un croquis como va a ser la llegada, priorizando que no haya público, ni gente, ni nadie cerca de las antenas, que los operadores de la app para backup manual estén libres y que las cámaras que filman la llegada no sean obstruidas.\n+\n+El control de la señal de internet y de la electrcidad debe estar hecho por lo menos 24hs antes del evento para poder solventar problemas de conexión.\n+\n+Hay que tener un listado de todos los cables necesarios, más cables y equipos de repuesto.\n+\n+Lady se compromete a que estos cables y equipos se deben probar durante la semana previa al evento y una vez que ya está todo armado el día del evento. Hay que reservar unos 30 minutos en el cronograma del armado para poder realizar estas pruebas y cambiar algún cable que pueda fallar.\n+\n+*Responsable de cronometraje libre*\n+\n+Es importante que el responsable de cronometraje, en nuestro caso Lady, esté libre para poder ir verificando que todos los equipos estén funcionando, que los informes estén correctos, detectando errores y con tiempo para corregirlos. También es importante que tenga tiempo para poder atender reclamos de participantes y consultas de la organización.\n+\n+*Backup*\n+\n+Lo ideal es tener un backup para analizar y recuperar información rápida, que casi siempre usamos cronometraje con nuestra app. Además hay que tener un backup en vídeo que demora más el control pero nos puede permitir buscar y ver la llegada completa.\n+\n+*Evento de prueba*\n+\n+Para practicar todo un caso real, este fin de semana van a organizar un evento de prueba. Nosotros nos comprometemos a estar online para poder ir resolviendo todas las dudas que vayan surgiendo.\n\\ No newline at end of file\n"}, {"date": 1746713112019, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -99,10 +99,11 @@\n - Puntos, ITRA, UTMB, velocidad promedio, etc.\n \n \n \n-Temas a tener en cuenta para un running de más de 1000 participantes\n \n+Les paso un resumen a modo de checklist de los temas a tener en cuenta para un running de más de 1000 participantes\n+\n *Presupuesto y profesionalidad*\n \n Desde el comienzo es importante planear un servicio profesional en todo sentido y hay que tener en cuenta todo lo necesario dentro del presupuesto.\n \n"}], "date": 1744725544143, "name": "Commit-0", "content": "# CAPACITACIONES\n\n\nChips de otras carreras, ubicación antenas, control de puesto real\n\n## CURSOS\n\n- Informe de largadas: https://cronometrajeinstantaneo.com/resultados/32-rally-rac-1000/largadas?control=PQAC\n\n## TEXTOS VENTAS\n\nEl sistema cuenta con un widgets que generan información para utilizar en softwares de streaming para TV y redes sociales (OBS Studio, vMix, Wirecast, Streamlabs OSB, etc.):\n\n- <PERSON><PERSON> (últimos participantes en llegada)\n- Participantes en pista\n- Participante con Reloj\n- Sólo Reloj\n- Sólo Participante\n- Podio (principal o por categoría)\n\nTrabajamos junto con el equipo de diseño para adaptar la identidad de los layouts, ya que podemos adaptar el diseño según la necesidad.\n\nAlgunos ejemplos de eventos anteriores son:\n\nhttps://www.youtube.com/live/sjADfxyLfP0\nhttps://www.youtube.com/live/fmUpiYIEMI0\nhttps://fb.watch/nHOdti1bJz/?mibextid=Nif5oz\n\n\n## VIVO EN INSTAGRAM\n\n- Hacer listado de preguntas\n- Probar invitación antes\n- Usar mic para que se escuche mejor\n- Sacar foto mientras hago vivo\n\n## FEEDBACK DE USHUAIA PARA ORDENAR CRONOMETRAJE CON CHIPS\n\n- En acreditaciones se cruzaron chips\n- No pudimos trabajar viendo la computadora de chips por lo que no sabíamos cuáles pasaban sin leer o con lectura cruzada\n- No pudimos usar las cámaras, la TV llegó muy tarde, no pudimos estar con el TV y la gente tapaba las cámaras\n- El backup con app fue muy difícil porque se cruzaba mucha gente\n- No tuvimos la información de los DNF ordenada, la fuimos subiendo tarde y como pudimos\n- La app de Griselda se sincronizó durante la entrega de premios, faltaban muchos tiempos y tenía muchos errores\n- Tendría que haber ido con nube híbrida por la falla de Internet\n\n- Tener un plano del parque cerrado, arco y lugar para cronometradores con tiempo y exigir que se cumpla\n- Parque cerrado sin participantes o sector privado para cronometraje cuidando cables, cámaras, backup con apps, y gazebo\n- CCTV con TV propia y cuidada\n- Persona 100m antes avisando a los que no se les ve el número\n- En las acreditaciones se cruzaron chips. Lo ideal es asignarlos antes y sólo probarlos durante la acreditación. También sería bueno tener un kiosko a parte para auto-chequeo\n- La computadora con los chips y las cámaras es trabajo de una persona viendo si se escapan\n- Acordar comunicación de DNFs por un sólo canal y con un sólo respondable\n\n- No aceptar armado sin tiempo\n- No permitir excepciones, es poco profesional y tu laburo sale mal\n- En eventos medianos y grandes, el responsable no puede estar cronometrando, sino pendiente de todo\n\n\n\n-------\nSi, buena pregunta, tendría que haberlo explicado. Cuando pasas un chip ya asignado y hay un número ingresado, el sistema re-asigna el chip\n\n\n---\n\nEn responsable de cronometraje:\n- Todo lo que te pasó en Ushuaia\n- Listado de personas que van a cronometrar\n- Listado de materiales\n- Conocer el reglamento completo\n- BackUps con cámara\n- Manejo del Modificador de tiempos\n- Coordinar prueba con los operadores\n- Coordinar con el responsable de acreditaciones\n\nPara tener en cuenta con RFID y eventos grandes:\n- El Control de chips en largadas permite detectar participantes con cambios de distancias sin informar\n- Agregar los CONTROLAR XXX en los resultados antes de entregar los podios\n- Es ideal que los kits con sus números y chips ya estén en las bolsas listas antes de las acreditaciones\n- El control de chips es obligatorio, idealmente con un kiosko y una persona para los cambios\n"}]}