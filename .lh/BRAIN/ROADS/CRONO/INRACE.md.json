{"sourceFile": "BRAIN/ROADS/CRONO/INRACE.md", "activeCommit": 0, "commits": [{"activePatchIndex": 173, "patches": [{"date": 1726599044899, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726603769502, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,5 +5,10 @@\n - Primeros 50 inscriptos\n - Individual 1 disciplina $65k\n - Individual 2 disciplinas $95k\n - Individual 3 disciplinas $115k\n-- Individual 1 disciplina corta $35k\n\\ No newline at end of file\n+- Individual 1 disciplina corta $35k\n+\n+## INSCRIPCIONES\n+\n+- Tengo que generar un cuestionario para ver que vas a participar\n+- Ver si me sirve usar tailwind-generator.com y como lo mando a Crono\n"}, {"date": 1726675912131, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,4 +11,10 @@\n ## INSCRIPCIONES\n \n - Tengo que generar un cuestionario para ver que vas a participar\n - Ver si me sirve usar tailwind-generator.com y como lo mando a Crono\n+\n+## IDEAS PARA CONVERSAR\n+\n+- Champagne en el podio\n+- Cinta de meta\n+- Fondo de podio\n\\ No newline at end of file\n"}, {"date": 1726940155060, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,5 +16,12 @@\n ## IDEAS PARA CONVERSAR\n \n - Champagne en el podio\n - Cinta de meta\n-- Fondo de podio\n\\ No newline at end of file\n+- Fondo de podio\n+\n+Ver la web como va a quedar: https://wp.swimrun.ar/\n+Usuario HTTP: \"acuatlon\"\n+Contraseña HTTP: \"Puto WordPress\"\n+\n+Modificar la web como va a quedar: https://wp.swimrun.ar/wp-admin/\n+Usuario: \"acuatlon\"\n\\ No newline at end of file\n"}, {"date": 1727651128632, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,27 +1,47 @@\n # ACUATLON\n \n+## CARRERAS\n+\n+- SwimRun Individual 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Dupla 10K\n+- Acuatlón Posta\n+\n+\n ## PRECIOS ESCALONADOS\n \n - Primeros 50 inscriptos\n - Individual 1 disciplina $65k\n - Individual 2 disciplinas $95k\n - Individual 3 disciplinas $115k\n - Individual 1 disciplina corta $35k\n\\ No newline at end of file\n \n-## INSCRIPCIONES\n \n-- Tengo que generar un cuestionario para ver que vas a participar\n-- Ver si me sirve usar tailwind-generator.com y como lo mando a Crono\n-\n ## IDEAS PARA CONVERSAR\n \n - Champagne en el podio\n - Cinta de meta\n - Fondo de podio\n \n-Ver la web como va a quedar: https://wp.swimrun.ar/\n-Usuario HTTP: \"acuatlon\"\n-Contraseña HTTP: \"Puto WordPress\"\n \n-Modificar la web como va a quedar: https://wp.swimrun.ar/wp-admin/\n-Usuario: \"acuatlon\"\n+## INSCRIPCIONES\n+\n+- Cambiar nombre de las carreras\n+- Subir los reglamentos arreglados y normalizados\n+- Completar sector Información\n+- Publicar sitio web\n+\n+- Generar carreras en el sistema\n+- Generar textos de inscripciones\n+\n+- Generar precios y configurar pago\n"}, {"date": 1727655221438, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,5 +43,11 @@\n \n - Generar carreras en el sistema\n - Generar textos de inscripciones\n \n-- Generar precios y configurar pago\n\\ No newline at end of file\n+- Generar precios y configurar pago\n+\n+\n+Reglamento SwimRun - Acuatlón Fest 2025\n+Reglamento Aguas Abiertas - Acuatlón Fest 2025\n+Reglamento Acuatlón - Acuatlón Fest 2025\n+Reglamento Águila Run - Acuatlón Fest 2025\n"}, {"date": 1727656918581, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,10 +44,10 @@\n - Generar carreras en el sistema\n - Generar textos de inscripciones\n \n - Generar precios y configurar pago\n+- Preparar newsletters\n \n-\n Reglamento SwimRun - Acuatlón Fest 2025\n Reglamento Aguas Abiertas - Acuatlón Fest 2025\n Reglamento Acuatlón - Acuatlón Fest 2025\n Reglamento Águila Run - Acuatlón Fest 2025\n"}, {"date": 1727786229849, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -50,4 +50,87 @@\n Reglamento SwimRun - Acuatlón Fest 2025\n Reglamento Aguas Abiertas - Acuatlón Fest 2025\n Reglamento Acuatlón - Acuatlón Fest 2025\n Reglamento Águila Run - Acuatlón Fest 2025\n+\n+\n+\n+\n+---\n+\n+\n+2193\n+\n+SELECT * FROM carreras WHERE idevento =\n+\n+INSERT INTO carreras (idcarrera, idevento, nombre, largada, observacion, etapas) VALUES\n+(NULL, 35, '21K', '', NULL, 1),\n+\n+\n+- SwimRun Dupla 10K\n+- Acuatlón Posta\n+\n+Aguas Abiertas 1500 + Águila Run 5K\n+\n+ESTRATEGIA\n+- Eligen distancia y se van autocancelando y se le suma el costo de la otra carrera\n+- Una funcion en js genera en un campo a parte los ids de las carreras\n+\n+\n+- SwimRun Individual 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500\n+- SwimRun Individual 10K + Aguas Abiertas 4000\n+\n+- SwimRun Individual 10K + Acuatlón Olímpico\n+- SwimRun Individual 10K + Acuatlón Short\n+\n+- SwimRun Individual 10K + Águila Run 5K\n+- SwimRun Individual 10K + Águila Run 10K\n+- SwimRun Individual 10K + Águila Run 21K\n+\n+- Aguas Abiertas 1500 + Acuatlón Olímpico\n+- Aguas Abiertas 1500 + Acuatlón Short\n+\n+- Aguas Abiertas 1500 + Águila Run 5K\n+- Aguas Abiertas 1500 + Águila Run 10K\n+- Aguas Abiertas 1500 + Águila Run 21K\n+\n+- Aguas Abiertas 4000 + Acuatlón Olímpico\n+- Aguas Abiertas 4000 + Acuatlón Short\n+\n+- Aguas Abiertas 4000 + Águila Run 5K\n+- Aguas Abiertas 4000 + Águila Run 10K\n+- Aguas Abiertas 4000 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n+\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n"}, {"date": 1727819110347, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,17 +33,19 @@\n - Cinta de meta\n - Fondo de podio\n \n \n-## INSCRIPCIONES\n+## CONFIGURAR INSCRIPCIONES\n \n-- Cambiar nombre de las carreras\n-- Subir los reglamentos arreglados y normalizados\n-- Completar sector Información\n-- Publicar sitio web\n+- [x] Cambiar nombre de las carreras\n+- [x] Subir los reglamentos arreglados y normalizados\n+- [x] Completar sector Información\n+- [x] Publicar sitio web\n \n-- Generar carreras en el sistema\n-- Generar textos de inscripciones\n+- [ ] Actualizar dominio\n+- [ ] Desarrollar inscripciones multi-categoria\n+- [ ] Generar carreras en el sistema\n+- [ ] Generar textos de inscripciones\n \n - Generar precios y configurar pago\n - Preparar newsletters\n \n"}, {"date": 1727819388296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -39,25 +39,18 @@\n - [x] Cambiar nombre de las carreras\n - [x] Subir los reglamentos arreglados y normalizados\n - [x] Completar sector Información\n - [x] Publicar sitio web\n+- [ ] Actualizar dominio\n \n-- [ ] Actualizar dominio\n - [ ] Desarrollar inscripciones multi-categoria\n - [ ] Generar carreras en el sistema\n - [ ] Generar textos de inscripciones\n+- [ ] Generar precios y configurar pago\n \n-- Generar precios y configurar pago\n-- Preparar newsletters\n+- [ ] Preparar newsletters\n \n-Reglamento SwimRun - Acuatlón Fest 2025\n-Reglamento Aguas Abiertas - Acuatlón Fest 2025\n-Reglamento Acuatlón - Acuatlón Fest 2025\n-Reglamento Águila Run - Acuatlón Fest 2025\n \n-\n-\n-\n ---\n \n \n 2193\n"}, {"date": 1727827280681, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,34 +51,42 @@\n \n \n ---\n \n+find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/\\/wp.swimrun.ar\\/wp-content/https:\\/\\/acuatlon.ar\\/wp-content/g' {} +\n \n-2193\n+idevento 2193\n \n-SELECT * FROM carreras WHERE idevento =\n+INSERT INTO carreras (idevento, idcarrera, nombre, orden) VALUES\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatl<PERSON> Short', 6),\n+(11107, 2193, 'Acuatlón Postas', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10);\n \n-INSERT INTO carreras (idcarrera, idevento, nombre, largada, observacion, etapas) VALUES\n-(NULL, 35, '21K', '', NULL, 1),\n+INSERT INTO categorias (idcategoria, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+(7412, 1716, 'Caballeros - Juveniles (16-19 años)', 'masculino', '', '2004-02-18', '2008-02-18'),\n \n \n-- SwimRun Dupla 10K\n-- Acuatlón Posta\n \n-Aguas Abiertas 1500 + Águila Run 5K\n \n-ESTRATEGIA\n-- Eligen distancia y se van autocancelando y se le suma el costo de la otra carrera\n-- Una funcion en js genera en un campo a parte los ids de las carreras\n \n \n+\n - SwimRun Individual 10K\n+- SwimRun Dupla 10K\n \n - Aguas Abiertas 1500\n - Aguas Abiertas 4000\n \n - Acuatlón Olímpico\n - Acuatlón Short\n+- Acuatlón Postas\n \n - Águila Run 5K\n - Águila Run 10K\n - Águila Run 21K\n@@ -119,13 +127,4 @@\n - SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n - SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n \n \n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n"}, {"date": 1727830174164, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -51,10 +51,14 @@\n \n \n ---\n \n-find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/\\/wp.swimrun.ar\\/wp-content/https:\\/\\/acuatlon.ar\\/wp-content/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/acuatlon.ar\\/acuatlon.ar\\/acuatlon.ar/g' {} +\n \n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n idevento 2193\n \n INSERT INTO carreras (idevento, idcarrera, nombre, orden) VALUES\n (11101, 2193, 'SwimRun Individual 10K', 1),\n"}, {"date": 1727830333372, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -52,9 +52,9 @@\n \n ---\n \n find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-find /var/www/acuatlon/www -type f -exec sed -i 's/acuatlon.ar\\/acuatlon.ar\\/acuatlon.ar/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n \n rm -r /var/www/acuatlon/www/wp-content/uploads\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n"}, {"date": 1727891591890, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -39,9 +39,9 @@\n - [x] Cambiar nombre de las carreras\n - [x] Subir los reglamentos arreglados y normalizados\n - [x] Completar sector Información\n - [x] Publicar sitio web\n-- [ ] Actualizar dominio\n+- [x] Actualizar dominio\n \n - [ ] Desarrollar inscripciones multi-categoria\n - [ ] Generar carreras en el sistema\n - [ ] Generar textos de inscripciones\n@@ -66,22 +66,55 @@\n (11103, 2193, 'Aguas Abiertas 1500', 3),\n (11104, 2193, 'Aguas Abiertas 4000', 4),\n (11105, 2193, 'Acuatlón Olímpico', 5),\n (11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, '<PERSON>cuatl<PERSON> Post<PERSON>', 7),\n+(11107, 2193, 'Acuatlón Posta', 7),\n (11108, 2193, 'Águila Run 5K', 8),\n (11109, 2193, 'Águila Run 10K', 9),\n (11110, 2193, 'Águila Run 21K', 10);\n \n-INSERT INTO categorias (idcategoria, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n-(7412, 1716, 'Caballeros - Juveniles (16-19 años)', 'masculino', '', '2004-02-18', '2008-02-18'),\n+INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n+(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n \n+(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n \n+(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n \n+(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n \n+(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n \n \n \n+\n+\n+\n - SwimRun Individual 10K\n - SwimRun Dupla 10K\n \n - Aguas Abiertas 1500\n"}, {"date": 1727892437862, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -59,9 +59,12 @@\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n idevento 2193\n \n-INSERT INTO carreras (idevento, idcarrera, nombre, orden) VALUES\n+DELETE FROM carreras WHERE idevento = 2193;\n+DELETE FROM categorias WHERE idevento = 2193;\n+\n+INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n (11101, 2193, 'SwimRun Individual 10K', 1),\n (11102, 2193, 'SwimRun Dupla 10K', 2),\n (11103, 2193, 'Aguas Abiertas 1500', 3),\n (11104, 2193, 'Aguas Abiertas 4000', 4),\n@@ -69,15 +72,18 @@\n (11106, 2193, '<PERSON>cuat<PERSON><PERSON> Short', 6),\n (11107, 2193, 'Acuatlón Posta', 7),\n (11108, 2193, 'Águila Run 5K', 8),\n (11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10);\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n \n INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+\n (NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n (NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n (NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n \n+\n (NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n (NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n (NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n (NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n@@ -109,12 +115,69 @@\n (NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n (NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n \n \n+(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n \n+(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n \n+(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n \n+(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n+(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n+(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n \n+(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n+\n+\n - SwimRun Individual 10K\n - SwimRun Dupla 10K\n \n - Aguas Abiertas 1500\n"}, {"date": 1727894507393, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,241 @@\n+# ACUATLON\n+\n+## CARRERAS\n+\n+- SwimRun Individual 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Dupla 10K\n+- Acuatlón Posta\n+\n+\n+## PRECIOS ESCALONADOS\n+\n+- Primeros 50 inscriptos\n+- Individual 1 disciplina $65k\n+- Individual 2 disciplinas $95k\n+- Individual 3 disciplinas $115k\n+- Individual 1 disciplina corta $35k\n+\n+\n+## IDEAS PARA CONVERSAR\n+\n+- Champagne en el podio\n+- Cinta de meta\n+- Fondo de podio\n+\n+\n+## CONFIGURAR INSCRIPCIONES\n+\n+- [x] Cambiar nombre de las carreras\n+- [x] Subir los reglamentos arreglados y normalizados\n+- [x] Completar sector Información\n+- [x] Publicar sitio web\n+- [x] Actualizar dominio\n+\n+- [ ] Desarrollar inscripciones multi-categoria\n+- [ ] Generar carreras en el sistema\n+- [ ] Generar textos de inscripciones\n+- [ ] Generar precios y configurar pago\n+\n+- [ ] Preparar newsletters\n+\n+\n+---\n+\n+find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+idevento 2193\n+\n+DELETE FROM carreras WHERE idevento = 2193;\n+DELETE FROM categorias WHERE idevento = 2193;\n+\n+INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n+INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+\n+(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n+(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n+\n+\n+(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+\n+(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n+(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n+(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n+\n+(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n+\n+\n+- SwimRun Individual 10K\n+- SwimRun Dupla 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+- Acuatlón Postas\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500\n+- SwimRun Individual 10K + Aguas Abiertas 4000\n+\n+- SwimRun Individual 10K + Acuatlón Olímpico\n+- SwimRun Individual 10K + Acuatlón Short\n+\n+- SwimRun Individual 10K + Águila Run 5K\n+- SwimRun Individual 10K + Águila Run 10K\n+- SwimRun Individual 10K + Águila Run 21K\n+\n+- Aguas Abiertas 1500 + Acuatlón Olímpico\n+- Aguas Abiertas 1500 + Acuatlón Short\n+\n+- Aguas Abiertas 1500 + Águila Run 5K\n+- Aguas Abiertas 1500 + Águila Run 10K\n+- Aguas Abiertas 1500 + Águila Run 21K\n+\n+- Aguas Abiertas 4000 + Acuatlón Olímpico\n+- Aguas Abiertas 4000 + Acuatlón Short\n+\n+- Aguas Abiertas 4000 + Águila Run 5K\n+- Aguas Abiertas 4000 + Águila Run 10K\n+- Aguas Abiertas 4000 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n+\n+\n+En este sitio web en html, con javascript y jquery, tengo un formulario de inscripción para un evento deportivo, en el cual el participante que se inscribe llenando el formulario, elije una carrera, su sexo y la fecha de nacimiento, y se auto-selecciona automáticamente su categoría utilizando la función selectCategoria().\n+\n+Esto funciona correctamente y no hay que modificarlo o no se tiene que ver afectado por los cambios que vamos a realizar.\n+\n+Hay una constante de javascript `multicategoria`. Mientras esa constante sea false, el comportamiento debe ser el mismo que el actual.\n+\n+Necesito que me agregues la funcionalidad de inscripción multi-categoría, que permita a un participante inscribirse en más de una categoría de la misma carrera.\n+\n+Sin darme código, ¿cuál crees que sería la mejor forma de hacerlo?\n+\n+Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n\\ No newline at end of file\n"}, {"date": 1727899326974, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -175,9 +175,31 @@\n (NULL, 117, 11110, 2193, '<PERSON><PERSON>ila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n \n (NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n \n+carreras_juntas = [\n+    \"11103-11104\",\n+    \"11105-11106\",\n+    \"11105-11108\",\n+    \"11105-11109\",\n+    \"11105-11110\",\n+    \"11106-11108\",\n+    \"11106-11109\",\n+    \"11106-11110\",\n+]\n \n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, '<PERSON>wi<PERSON><PERSON><PERSON> Du<PERSON> 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón <PERSON>l<PERSON>mp<PERSON>', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, '<PERSON>guila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n - SwimRun Individual 10K\n - SwimRun Dupla 10K\n \n - Aguas Abiertas 1500\n@@ -237,233 +259,5 @@\n Necesito que me agregues la funcionalidad de inscripción multi-categoría, que permita a un participante inscribirse en más de una categoría de la misma carrera.\n \n Sin darme código, ¿cuál crees que sería la mejor forma de hacerlo?\n \n-Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n-# ACUATLON\n-\n-## CARRERAS\n-\n-- SwimRun Individual 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Dupla 10K\n-- Acuatlón Posta\n-\n-\n-## PRECIOS ESCALONADOS\n-\n-- Primeros 50 inscriptos\n-- Individual 1 disciplina $65k\n-- Individual 2 disciplinas $95k\n-- Individual 3 disciplinas $115k\n-- Individual 1 disciplina corta $35k\n-\n-\n-## IDEAS PARA CONVERSAR\n-\n-- Champagne en el podio\n-- Cinta de meta\n-- Fondo de podio\n-\n-\n-## CONFIGURAR INSCRIPCIONES\n-\n-- [x] Cambiar nombre de las carreras\n-- [x] Subir los reglamentos arreglados y normalizados\n-- [x] Completar sector Información\n-- [x] Publicar sitio web\n-- [x] Actualizar dominio\n-\n-- [ ] Desarrollar inscripciones multi-categoria\n-- [ ] Generar carreras en el sistema\n-- [ ] Generar textos de inscripciones\n-- [ ] Generar precios y configurar pago\n-\n-- [ ] Preparar newsletters\n-\n-\n----\n-\n-find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-idevento 2193\n-\n-DELETE FROM carreras WHERE idevento = 2193;\n-DELETE FROM categorias WHERE idevento = 2193;\n-\n-INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n-\n-(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n-(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n-\n-\n-(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-\n-(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n-(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n-(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n-\n-(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n-\n-\n-- SwimRun Individual 10K\n-- SwimRun Dupla 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-- Acuatlón Postas\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500\n-- SwimRun Individual 10K + Aguas Abiertas 4000\n-\n-- SwimRun Individual 10K + Acuatlón Olímpico\n-- SwimRun Individual 10K + Acuatlón Short\n-\n-- SwimRun Individual 10K + Águila Run 5K\n-- SwimRun Individual 10K + Águila Run 10K\n-- SwimRun Individual 10K + Águila Run 21K\n-\n-- Aguas Abiertas 1500 + Acuatlón Olímpico\n-- Aguas Abiertas 1500 + Acuatlón Short\n-\n-- Aguas Abiertas 1500 + Águila Run 5K\n-- Aguas Abiertas 1500 + Águila Run 10K\n-- Aguas Abiertas 1500 + Águila Run 21K\n-\n-- Aguas Abiertas 4000 + Acuatlón Olímpico\n-- Aguas Abiertas 4000 + Acuatlón Short\n-\n-- Aguas Abiertas 4000 + Águila Run 5K\n-- Aguas Abiertas 4000 + Águila Run 10K\n-- Aguas Abiertas 4000 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n\\ No newline at end of file\n+Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n\\ No newline at end of file\n"}, {"date": 1727899439271, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -184,8 +184,11 @@\n     \"11105-11110\",\n     \"11106-11108\",\n     \"11106-11109\",\n     \"11106-11110\",\n+    \"11108-11109\",\n+    \"11108-11110\",\n+    \"11109-11110\",\n ]\n \n (11101, 2193, 'SwimRun Individual 10K', 1),\n (11102, 2193, 'SwimRun Dupla 10K', 2),\n"}, {"date": 1728046248224, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,12 +42,13 @@\n - [x] Publicar sitio web\n - [x] Actualizar dominio\n \n - [ ] Desarrollar inscripciones multi-categoria\n-- [ ] Generar carreras en el sistema\n+- [x] Generar carreras en el sistema\n - [ ] Generar textos de inscripciones\n - [ ] Generar precios y configurar pago\n \n+- [ ] Aprobar dominios\n - [ ] Preparar newsletters\n \n \n ---\n"}, {"date": 1728046553888, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,9 +43,9 @@\n - [x] Actualizar dominio\n \n - [ ] Desarrollar inscripciones multi-categoria\n - [x] Generar carreras en el sistema\n-- [ ] Generar textos de inscripciones\n+- [ ] Generar textos de inscripciones y diseño\n - [ ] Generar precios y configurar pago\n \n - [ ] Aprobar dominios\n - [ ] Preparar newsletters\n"}, {"date": 1728054770863, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,13 +19,13 @@\n \n \n ## PRECIOS ESCALONADOS\n \n-- Primeros 50 inscriptos\n-- Individual 1 disciplina $65k\n-- Individual 2 disciplinas $95k\n-- Individual 3 disciplinas $115k\n-- Individual 1 disciplina corta $35k\n+- Individual 1 disciplina (distancias cortas) $ 35.000\n+- Individual 1 disciplina $ 65.000\n+- Individual 2 disciplinas $ 95.000 ($ 47.500 c/u)\n+- Individual 3 disciplinas $ 105.000 ($ 35.000 c/u)\n+- Acuatlón Kids GRATIS\n \n \n ## IDEAS PARA CONVERSAR\n \n@@ -41,9 +41,9 @@\n - [x] Completar sector Información\n - [x] Publicar sitio web\n - [x] Actualizar dominio\n \n-- [ ] Desarrollar inscripciones multi-categoria\n+- [x] Desarrollar inscripciones multi-categoria\n - [x] Generar carreras en el sistema\n - [ ] Generar textos de inscripciones y diseño\n - [ ] Generar precios y configurar pago\n \n"}, {"date": 1728076507323, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -19,12 +19,15 @@\n \n \n ## PRECIOS ESCALONADOS\n \n-- Individual 1 disciplina (distancias cortas) $ 35.000\n-- Individual 1 disciplina $ 65.000\n-- Individual 2 disciplinas $ 95.000 ($ 47.500 c/u)\n-- Individual 3 disciplinas $ 105.000 ($ 35.000 c/u)\n+- Individual 1 carrera (distancias cortas) $ 35.000\n+- Individual 1 carrera $ 65.000\n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n - Acuatlón Kids GRATIS\n \n \n ## IDEAS PARA CONVERSAR\n@@ -43,13 +46,13 @@\n - [x] Actualizar dominio\n \n - [x] Desarrollar inscripciones multi-categoria\n - [x] Generar carreras en el sistema\n-- [ ] Generar textos de inscripciones y diseño\n+- [x] Generar textos de inscripciones y diseño\n - [ ] Generar precios y configurar pago\n \n-- [ ] Aprobar dominios\n-- [ ] Preparar newsletters\n+- [x] Aprobar dominios\n+- [x] Preparar newsletters\n \n \n ---\n \n@@ -58,10 +61,47 @@\n \n rm -r /var/www/acuatlon/www/wp-content/uploads\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+\n idevento 2193\n \n+\n+INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n+(2193, 22, 11103),\n+(2193, 22, 11105),\n+(2193, 22, 11108),\n+\n+(2193, 23, 11101),\n+(2193, 23, 11104),\n+(2193, 23, 11105),\n+(2193, 23, 11109),\n+(2193, 23, 11110),\n+\n+(2193, 24, 11102),\n+(2193, 25, 11102),\n+(2193, 26, 11102),\n+\n+(2193, 24, 11107),\n+(2193, 25, 11107),\n+(2193, 26, 11107)\n+\n+\n+- Individual 1 carrera (distancias cortas) $ 35.000\n+- Individual 1 carrera $ 65.000\n+\n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n+\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n+\n+- Acuatlón Kids GRATIS\n+\n+\n DELETE FROM carreras WHERE idevento = 2193;\n DELETE FROM categorias WHERE idevento = 2193;\n \n INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n"}, {"date": 1728078646849, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -61,8 +61,10 @@\n \n rm -r /var/www/acuatlon/www/wp-content/uploads\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n+\n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n \n \n idevento 2193\n"}, {"date": 1728079991626, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -71,9 +71,9 @@\n \n \n INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n (2193, 22, 11103),\n-(2193, 22, 11105),\n+(2193, 22, 11106),\n (2193, 22, 11108),\n \n (2193, 23, 11101),\n (2193, 23, 11104),\n"}, {"date": 1728236775197, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,10 @@\n # ACUATLON\n \n+## MKT\n+\n+- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n"}, {"date": 1728681101139, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,327 @@\n+# ACUATLON\n+\n+## MKT\n+\n+- [ ] Agregar opciones pago efectivo\n+\n+- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n+- [ ] Vídeo de como inscribirse\n+\n+- [ ] Acuatlón kids arriba (cambiar foto)\n+- [ ] En web grupo de whatsapp\n+- [ ] Agregar cronograma\n+- [ ] Acomodar sponsors\n+\n+- [ ] Cargar pagos a Sandoval\n+\n+- [ ] Mandar capturas de dónde pueden ir los sponsors\n+- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+\n+\n+## CARRERAS\n+\n+- SwimRun Individual 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas A<PERSON>rta<PERSON> 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Dupla 10K\n+- Acuatlón Posta\n+\n+\n+## PRECIOS ESCALONADOS\n+\n+- Individual 1 carrera (distancias cortas) $ 35.000\n+- Individual 1 carrera $ 65.000\n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n+- Acuatlón Kids GRATIS\n+\n+\n+## IDEAS PARA CONVERSAR\n+\n+- Champagne en el podio\n+- Cinta de meta\n+- Fondo de podio\n+\n+\n+## CONFIGURAR INSCRIPCIONES\n+\n+- [x] Cambiar nombre de las carreras\n+- [x] Subir los reglamentos arreglados y normalizados\n+- [x] Completar sector Información\n+- [x] Publicar sitio web\n+- [x] Actualizar dominio\n+\n+- [x] Desarrollar inscripciones multi-categoria\n+- [x] Generar carreras en el sistema\n+- [x] Generar textos de inscripciones y diseño\n+- [ ] Generar precios y configurar pago\n+\n+- [x] Aprobar dominios\n+- [x] Preparar newsletters\n+\n+\n+---\n+\n+find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+\n+idevento 2193\n+\n+\n+INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n+(2193, 22, 11103),\n+(2193, 22, 11106),\n+(2193, 22, 11108),\n+\n+(2193, 23, 11101),\n+(2193, 23, 11104),\n+(2193, 23, 11105),\n+(2193, 23, 11109),\n+(2193, 23, 11110),\n+\n+(2193, 24, 11102),\n+(2193, 25, 11102),\n+(2193, 26, 11102),\n+\n+(2193, 24, 11107),\n+(2193, 25, 11107),\n+(2193, 26, 11107)\n+\n+\n+- Individual 1 carrera (distancias cortas) $ 35.000\n+- Individual 1 carrera $ 65.000\n+\n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n+\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n+\n+- Acuatlón Kids GRATIS\n+\n+\n+DELETE FROM carreras WHERE idevento = 2193;\n+DELETE FROM categorias WHERE idevento = 2193;\n+\n+INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n+INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+\n+(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n+(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n+\n+\n+(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+\n+(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n+(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n+(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n+\n+(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n+\n+carreras_juntas = [\n+    \"11103-11104\",\n+    \"11105-11106\",\n+    \"11105-11108\",\n+    \"11105-11109\",\n+    \"11105-11110\",\n+    \"11106-11108\",\n+    \"11106-11109\",\n+    \"11106-11110\",\n+    \"11108-11109\",\n+    \"11108-11110\",\n+    \"11109-11110\",\n+]\n+\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n+- SwimRun Individual 10K\n+- SwimRun Dupla 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+- Acuatlón Postas\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500\n+- SwimRun Individual 10K + Aguas Abiertas 4000\n+\n+- SwimRun Individual 10K + Acuatlón Olímpico\n+- SwimRun Individual 10K + Acuatlón Short\n+\n+- SwimRun Individual 10K + Águila Run 5K\n+- SwimRun Individual 10K + Águila Run 10K\n+- SwimRun Individual 10K + Águila Run 21K\n+\n+- Aguas Abiertas 1500 + Acuatlón Olímpico\n+- Aguas Abiertas 1500 + Acuatlón Short\n+\n+- Aguas Abiertas 1500 + Águila Run 5K\n+- Aguas Abiertas 1500 + Águila Run 10K\n+- Aguas Abiertas 1500 + Águila Run 21K\n+\n+- Aguas Abiertas 4000 + Acuatlón Olímpico\n+- Aguas Abiertas 4000 + Acuatlón Short\n+\n+- Aguas Abiertas 4000 + Águila Run 5K\n+- Aguas Abiertas 4000 + Águila Run 10K\n+- Aguas Abiertas 4000 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n+\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n+- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n+\n+\n+En este sitio web en html, con javascript y jquery, tengo un formulario de inscripción para un evento deportivo, en el cual el participante que se inscribe llenando el formulario, elije una carrera, su sexo y la fecha de nacimiento, y se auto-selecciona automáticamente su categoría utilizando la función selectCategoria().\n+\n+Esto funciona correctamente y no hay que modificarlo o no se tiene que ver afectado por los cambios que vamos a realizar.\n+\n+Hay una constante de javascript `multicategoria`. Mientras esa constante sea false, el comportamiento debe ser el mismo que el actual.\n+\n+Necesito que me agregues la funcionalidad de inscripción multi-categoría, que permita a un participante inscribirse en más de una categoría de la misma carrera.\n+\n+Sin darme código, ¿cuál crees que sería la mejor forma de hacerlo?\n+\n+Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n\\ No newline at end of file\n"}, {"date": 1728682011697, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,7 @@\n # ACUATLON\n \n-## MKT\n+## TODO\n \n - [ ] Agregar opciones pago efectivo\n \n - [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n@@ -37,355 +37,28 @@\n \n \n ## PRECIOS ESCALONADOS\n \n-- Individual 1 carrera (distancias cortas) $ 35.000\n-- Individual 1 carrera $ 65.000\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n-- Acuatlón Kids GRATIS\n+- Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n+- Individual 1 carrera $ 65.000 | 60.000\n \n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u) | 85.000\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u) | 95.000\n \n-## IDEAS PARA CONVERSAR\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u) | 85.000\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u) | 60.000\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n \n-- Champagne en el podio\n-- Cinta de meta\n-- Fondo de podio\n-\n-\n-## CONFIGURAR INSCRIPCIONES\n-\n-- [x] Cambiar nombre de las carreras\n-- [x] Subir los reglamentos arreglados y normalizados\n-- [x] Completar sector Información\n-- [x] Publicar sitio web\n-- [x] Actualizar dominio\n-\n-- [x] Desarrollar inscripciones multi-categoria\n-- [x] Generar carreras en el sistema\n-- [x] Generar textos de inscripciones y diseño\n-- [ ] Generar precios y configurar pago\n-\n-- [x] Aprobar dominios\n-- [x] Preparar newsletters\n-\n-\n----\n-\n-find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n-\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n-\n-\n-idevento 2193\n-\n-\n-INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n-(2193, 22, 11103),\n-(2193, 22, 11106),\n-(2193, 22, 11108),\n-\n-(2193, 23, 11101),\n-(2193, 23, 11104),\n-(2193, 23, 11105),\n-(2193, 23, 11109),\n-(2193, 23, 11110),\n-\n-(2193, 24, 11102),\n-(2193, 25, 11102),\n-(2193, 26, 11102),\n-\n-(2193, 24, 11107),\n-(2193, 25, 11107),\n-(2193, 26, 11107)\n-\n-\n-- Individual 1 carrera (distancias cortas) $ 35.000\n-- Individual 1 carrera $ 65.000\n-\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n-\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n-\n - Acuatlón Kids GRATIS\n \n \n-DELETE FROM carreras WHERE idevento = 2193;\n-DELETE FROM categorias WHERE idevento = 2193;\n-\n-INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n-\n-(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n-(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n-\n-\n-(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-\n-(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n-(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n-(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n-\n-(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n-\n-carreras_juntas = [\n-    \"11103-11104\",\n-    \"11105-11106\",\n-    \"11105-11108\",\n-    \"11105-11109\",\n-    \"11105-11110\",\n-    \"11106-11108\",\n-    \"11106-11109\",\n-    \"11106-11110\",\n-    \"11108-11109\",\n-    \"11108-11110\",\n-    \"11109-11110\",\n-]\n-\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-- SwimRun Individual 10K\n-- SwimRun Dupla 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-- Acuatlón Postas\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500\n-- SwimRun Individual 10K + Aguas Abiertas 4000\n-\n-- SwimRun Individual 10K + Acuatlón Olímpico\n-- SwimRun Individual 10K + Acuatlón Short\n-\n-- SwimRun Individual 10K + Águila Run 5K\n-- SwimRun Individual 10K + Águila Run 10K\n-- SwimRun Individual 10K + Águila Run 21K\n-\n-- Aguas Abiertas 1500 + Acuatlón Olímpico\n-- Aguas Abiertas 1500 + Acuatlón Short\n-\n-- Aguas Abiertas 1500 + Águila Run 5K\n-- Aguas Abiertas 1500 + Águila Run 10K\n-- Aguas Abiertas 1500 + Águila Run 21K\n-\n-- Aguas Abiertas 4000 + Acuatlón Olímpico\n-- Aguas Abiertas 4000 + Acuatlón Short\n-\n-- Aguas Abiertas 4000 + Águila Run 5K\n-- Aguas Abiertas 4000 + Águila Run 10K\n-- Aguas Abiertas 4000 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n-\n-\n-En este sitio web en html, con javascript y jquery, tengo un formulario de inscripción para un evento deportivo, en el cual el participante que se inscribe llenando el formulario, elije una carrera, su sexo y la fecha de nacimiento, y se auto-selecciona automáticamente su categoría utilizando la función selectCategoria().\n-\n-Esto funciona correctamente y no hay que modificarlo o no se tiene que ver afectado por los cambios que vamos a realizar.\n-\n-Hay una constante de javascript `multicategoria`. Mientras esa constante sea false, el comportamiento debe ser el mismo que el actual.\n-\n-Necesito que me agregues la funcionalidad de inscripción multi-categoría, que permita a un participante inscribirse en más de una categoría de la misma carrera.\n-\n-Sin darme código, ¿cuál crees que sería la mejor forma de hacerlo?\n-\n-Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n-# ACUATLON\n-\n-## MKT\n-\n-- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n-\n-## CARRERAS\n-\n-- SwimRun Individual 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Dupla 10K\n-- Acuatlón Posta\n-\n-\n-## PRECIOS ESCALONADOS\n-\n-- Individual 1 carrera (distancias cortas) $ 35.000\n-- Individual 1 carrera $ 65.000\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n-- Acuatlón Kids GRATIS\n-\n-\n ## IDEAS PARA CONVERSAR\n \n - Champagne en el podio\n - Cinta de meta\n - Fondo de podio\n \n \n-## CONFIGURAR INSCRIPCIONES\n-\n-- [x] Cambiar nombre de las carreras\n-- [x] Subir los reglamentos arreglados y normalizados\n-- [x] Completar sector Información\n-- [x] Publicar sitio web\n-- [x] Actualizar dominio\n-\n-- [x] Desarrollar inscripciones multi-categoria\n-- [x] Generar carreras en el sistema\n-- [x] Generar textos de inscripciones y diseño\n-- [ ] Generar precios y configurar pago\n-\n-- [x] Aprobar dominios\n-- [x] Preparar newsletters\n-\n-\n ---\n \n find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n@@ -397,11 +70,12 @@\n \n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n \n \n+---\n+\n idevento 2193\n \n-\n INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n (2193, 22, 11103),\n (2193, 22, 11106),\n (2193, 22, 11108),\n@@ -420,21 +94,8 @@\n (2193, 25, 11107),\n (2193, 26, 11107)\n \n \n-- Individual 1 carrera (distancias cortas) $ 35.000\n-- Individual 1 carrera $ 65.000\n-\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u)\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u)\n-\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u)\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u)\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u)\n-\n-- Acuatlón Kids GRATIS\n-\n-\n DELETE FROM carreras WHERE idevento = 2193;\n DELETE FROM categorias WHERE idevento = 2193;\n \n INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n@@ -574,67 +235,4 @@\n (11108, 2193, 'Águila Run 5K', 8),\n (11109, 2193, 'Águila Run 10K', 9),\n (11110, 2193, 'Águila Run 21K', 10),\n (11111, 2193, 'Acuatlón Kids', 11);\n-\n-- SwimRun Individual 10K\n-- SwimRun Dupla 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-- Acuatlón Postas\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500\n-- SwimRun Individual 10K + Aguas Abiertas 4000\n-\n-- SwimRun Individual 10K + Acuatlón Olímpico\n-- SwimRun Individual 10K + Acuatlón Short\n-\n-- SwimRun Individual 10K + Águila Run 5K\n-- SwimRun Individual 10K + Águila Run 10K\n-- SwimRun Individual 10K + Águila Run 21K\n-\n-- Aguas Abiertas 1500 + Acuatlón Olímpico\n-- Aguas Abiertas 1500 + Acuatlón Short\n-\n-- Aguas Abiertas 1500 + Águila Run 5K\n-- Aguas Abiertas 1500 + Águila Run 10K\n-- Aguas Abiertas 1500 + Águila Run 21K\n-\n-- Aguas Abiertas 4000 + Acuatlón Olímpico\n-- Aguas Abiertas 4000 + Acuatlón Short\n-\n-- Aguas Abiertas 4000 + Águila Run 5K\n-- Aguas Abiertas 4000 + Águila Run 10K\n-- Aguas Abiertas 4000 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 1500 + Águila Run 21K\n-\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Olímpico\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Acuatlón Short\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 5K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 10K\n-- SwimRun Individual 10K + Aguas Abiertas 4000 + Águila Run 21K\n-\n-\n-En este sitio web en html, con javascript y jquery, tengo un formulario de inscripción para un evento deportivo, en el cual el participante que se inscribe llenando el formulario, elije una carrera, su sexo y la fecha de nacimiento, y se auto-selecciona automáticamente su categoría utilizando la función selectCategoria().\n-\n-Esto funciona correctamente y no hay que modificarlo o no se tiene que ver afectado por los cambios que vamos a realizar.\n-\n-Hay una constante de javascript `multicategoria`. Mientras esa constante sea false, el comportamiento debe ser el mismo que el actual.\n-\n-Necesito que me agregues la funcionalidad de inscripción multi-categoría, que permita a un participante inscribirse en más de una categoría de la misma carrera.\n-\n-Sin darme código, ¿cuál crees que sería la mejor forma de hacerlo?\n-\n-Yo tengo pensado poder poner un array con el listado de carreras con sus categorías seleccionadas, que arranque vacío y que el usuario pueda ir agregando. Cada vez que agrega una nueva categoría, se mantiene la selección del sexo y de la fecha de nacimiento, pero se vacía la selección de la carrera para que pueda elegir otra. Al elegir esa otra carrera, se auto-selecciona la categoría correspondiente. Me imagino unos botones de agregar y quitar categorías, y un botón de inscribirse que envíe el formulario con todas las categorías seleccionadas.\n\\ No newline at end of file\n"}, {"date": 1728686069690, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,311 @@\n+# ACUATLON\n+\n+## TODO\n+\n+- [ ] Agregar opciones pago efectivo\n+\n+- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n+- [ ] Vídeo de como inscribirse\n+\n+- [ ] Acuatlón kids arriba (cambiar foto)\n+- [ ] En web grupo de whatsapp\n+- [ ] Agregar cronograma\n+- [ ] Acomodar sponsors\n+\n+- [ ] Cargar pagos a Sandoval\n+\n+- [ ] Mandar capturas de dónde pueden ir los sponsors\n+- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+\n+\n+## CARRERAS\n+\n+- SwimRun Individual 10K\n+\n+- Aguas Abiertas 1500\n+- Aguas Abiertas 4000\n+\n+- Acuatlón Olímpico\n+- Acuatlón Short\n+\n+- Águila Run 5K\n+- Águila Run 10K\n+- Águila Run 21K\n+\n+- SwimRun Dupla 10K\n+- Acuatlón Posta\n+\n+\n+## PRECIOS ESCALONADOS\n+\n+- Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n+- Individual 1 carrera $ 65.000 | 60.000\n+\n+- Individual 2 carreras $ 95.000 ($ 47.500 c/u) | 85.000\n+- Individual 3 carreras $ 105.000 ($ 35.000 c/u) | 95.000\n+\n+- Dupla o Posta $ 95.000 ($ 47.500 c/u) | 85.000\n+- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u) | 60.000\n+- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n+\n+- Acuatlón Kids GRATIS\n+\n+\n+## IDEAS PARA CONVERSAR\n+\n+- Champagne en el podio\n+- Cinta de meta\n+- Fondo de podio\n+\n+\n+---\n+\n+find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+\n+---\n+\n+idevento 2193\n+\n+INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n+(2193, 22, 11103),\n+(2193, 22, 11106),\n+(2193, 22, 11108),\n+\n+(2193, 23, 11101),\n+(2193, 23, 11104),\n+(2193, 23, 11105),\n+(2193, 23, 11109),\n+(2193, 23, 11110),\n+\n+(2193, 24, 11102),\n+(2193, 25, 11102),\n+(2193, 26, 11102),\n+\n+(2193, 24, 11107),\n+(2193, 25, 11107),\n+(2193, 26, 11107)\n+\n+\n+DELETE FROM carreras WHERE idevento = 2193;\n+DELETE FROM categorias WHERE idevento = 2193;\n+\n+INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n+INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n+\n+(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n+(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n+\n+\n+(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+\n+(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n+(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n+(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n+\n+(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n+(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n+\n+(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n+(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n+(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n+(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n+(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n+(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n+(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n+\n+(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n+\n+carreras_juntas = [\n+    \"11103-11104\",\n+    \"11105-11106\",\n+    \"11105-11108\",\n+    \"11105-11109\",\n+    \"11105-11110\",\n+    \"11106-11108\",\n+    \"11106-11109\",\n+    \"11106-11110\",\n+    \"11108-11109\",\n+    \"11108-11110\",\n+    \"11109-11110\",\n+]\n+\n+(11101, 2193, 'SwimRun Individual 10K', 1),\n+(11102, 2193, 'SwimRun Dupla 10K', 2),\n+(11103, 2193, 'Aguas Abiertas 1500', 3),\n+(11104, 2193, 'Aguas Abiertas 4000', 4),\n+(11105, 2193, 'Acuatlón Olímpico', 5),\n+(11106, 2193, 'Acuatlón Short', 6),\n+(11107, 2193, 'Acuatlón Posta', 7),\n+(11108, 2193, 'Águila Run 5K', 8),\n+(11109, 2193, 'Águila Run 10K', 9),\n+(11110, 2193, 'Águila Run 21K', 10),\n+(11111, 2193, 'Acuatlón Kids', 11);\n+\n+\n+\n+INSERT INTO `precios` (`idprecio`, `idevento`, `idpais`, `idplataforma`, `fecha_desde`, `fecha_hasta`, `precio`, `cantidad`, `cuota`, `boton`, `url`) VALUES\n+(27, 2193, 0, 13, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n+(28, 2193, 0, 13, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(29, 2193, 0, 13, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n+(30, 2193, 0, 13, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(31, 2193, 0, 13, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n+\n+(32, 2193, 0, 14, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n+(33, 2193, 0, 14, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(34, 2193, 0, 14, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n+(35, 2193, 0, 14, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(36, 2193, 0, 14, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n+\n+(37, 2193, 0, 15, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n+(38, 2193, 0, 15, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(39, 2193, 0, 15, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n+(40, 2193, 0, 15, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n+(41, 2193, 0, 15, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL);\n+\n+\n+INSERT INTO `preciosxcarreras` (`idprecioxcarrera`, `idevento`, `idprecio`, `idcarrera`) VALUES\n+(NULL, 2193, 27, 11103),\n+(NULL, 2193, 27, 11106),\n+(NULL, 2193, 27, 11108),\n+(NULL, 2193, 32, 11103),\n+(NULL, 2193, 32, 11106),\n+(NULL, 2193, 32, 11108),\n+(NULL, 2193, 37, 11103),\n+(NULL, 2193, 37, 11106),\n+(NULL, 2193, 37, 11108),\n+\n+(NULL, 2193, 28, 11101),\n+(NULL, 2193, 28, 11104),\n+(NULL, 2193, 28, 11105),\n+(NULL, 2193, 28, 11109),\n+(NULL, 2193, 28, 11110),\n+(NULL, 2193, 33, 11101),\n+(NULL, 2193, 33, 11104),\n+(NULL, 2193, 33, 11105),\n+(NULL, 2193, 33, 11109),\n+(NULL, 2193, 33, 11110),\n+(NULL, 2193, 38, 11101),\n+(NULL, 2193, 38, 11104),\n+(NULL, 2193, 38, 11105),\n+(NULL, 2193, 38, 11109),\n+(NULL, 2193, 38, 11110),\n+\n+(NULL, 2193, 29, 11102),\n+(NULL, 2193, 34, 11102),\n+(NULL, 2193, 39, 11102),\n+\n+(NULL, 2193, 30, 11102),\n+(NULL, 2193, 35, 11102),\n+(NULL, 2193, 40, 11102),\n+\n+(NULL, 2193, 31, 11102),\n+(NULL, 2193, 36, 11102),\n+(NULL, 2193, 41, 11102),\n+\n+(NULL, 2193, 29, 11107),\n+(NULL, 2193, 34, 11107),\n+(NULL, 2193, 39, 11107),\n+\n+(NULL, 2193, 30, 11107),\n+(NULL, 2193, 35, 11107),\n+(NULL, 2193, 40, 11107),\n+\n+(NULL, 2193, 31, 11107),\n+(NULL, 2193, 36, 11107),\n+(NULL, 2193, 41, 11107);\n"}, {"date": 1728686935054, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n \n ## TODO\n \n - [ ] Agregar opciones pago efectivo\n+- [ ] Revisar el error de inscripciones en equipos\n \n - [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n - [ ] Vídeo de como inscribirse\n \n@@ -308,242 +309,4 @@\n \n (NULL, 2193, 31, 11107),\n (NULL, 2193, 36, 11107),\n (NULL, 2193, 41, 11107);\n-# ACUATLON\n-\n-## TODO\n-\n-- [ ] Agregar opciones pago efectivo\n-\n-- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n-- [ ] Vídeo de como inscribirse\n-\n-- [ ] Acuatlón kids arriba (cambiar foto)\n-- [ ] En web grupo de whatsapp\n-- [ ] Agregar cronograma\n-- [ ] Acomodar sponsors\n-\n-- [ ] Cargar pagos a Sandoval\n-\n-- [ ] Mandar capturas de dónde pueden ir los sponsors\n-- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n-\n-\n-## CARRERAS\n-\n-- SwimRun Individual 10K\n-\n-- Aguas Abiertas 1500\n-- Aguas Abiertas 4000\n-\n-- Acuatlón Olímpico\n-- Acuatlón Short\n-\n-- Águila Run 5K\n-- Águila Run 10K\n-- Águila Run 21K\n-\n-- SwimRun Dupla 10K\n-- Acuatlón Posta\n-\n-\n-## PRECIOS ESCALONADOS\n-\n-- Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n-- Individual 1 carrera $ 65.000 | 60.000\n-\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u) | 85.000\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u) | 95.000\n-\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u) | 85.000\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u) | 60.000\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n-\n-- Acuatlón Kids GRATIS\n-\n-\n-## IDEAS PARA CONVERSAR\n-\n-- Champagne en el podio\n-- Cinta de meta\n-- Fondo de podio\n-\n-\n----\n-\n-find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n-\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n-\n-\n----\n-\n-idevento 2193\n-\n-INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n-(2193, 22, 11103),\n-(2193, 22, 11106),\n-(2193, 22, 11108),\n-\n-(2193, 23, 11101),\n-(2193, 23, 11104),\n-(2193, 23, 11105),\n-(2193, 23, 11109),\n-(2193, 23, 11110),\n-\n-(2193, 24, 11102),\n-(2193, 25, 11102),\n-(2193, 26, 11102),\n-\n-(2193, 24, 11107),\n-(2193, 25, 11107),\n-(2193, 26, 11107)\n-\n-\n-DELETE FROM carreras WHERE idevento = 2193;\n-DELETE FROM categorias WHERE idevento = 2193;\n-\n-INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n-\n-(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n-(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n-\n-\n-(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-\n-(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n-(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n-(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n-\n-(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n-\n-carreras_juntas = [\n-    \"11103-11104\",\n-    \"11105-11106\",\n-    \"11105-11108\",\n-    \"11105-11109\",\n-    \"11105-11110\",\n-    \"11106-11108\",\n-    \"11106-11109\",\n-    \"11106-11110\",\n-    \"11108-11109\",\n-    \"11108-11110\",\n-    \"11109-11110\",\n-]\n-\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n"}, {"date": 1728741616284, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,12 @@\n ## TODO\n \n - [ ] Agregar opciones pago efectivo\n - [ ] Revisar el error de inscripciones en equipos\n+- [ ] Modificar botón por \"Carreras\" y en otro color\n \n+- [ ] Vídeo de como inscribirse\n - [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n-- [ ] Vídeo de como inscribirse\n \n - [ ] Acuatlón kids arriba (cambiar foto)\n - [ ] En web grupo de whatsapp\n - [ ] Agregar cronograma\n"}, {"date": 1728744290890, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,9 +6,9 @@\n - [ ] Revisar el error de inscripciones en equipos\n - [ ] Modificar botón por \"Carreras\" y en otro color\n \n - [ ] Vídeo de como inscribirse\n-- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria)\n+- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n \n - [ ] Acuatlón kids arriba (cambiar foto)\n - [ ] En web grupo de whatsapp\n - [ ] Agregar cronograma\n"}, {"date": 1728745763308, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,15 +3,15 @@\n ## TODO\n \n - [ ] Agregar opciones pago efectivo\n - [ ] Revisar el error de inscripciones en equipos\n-- [ ] Modificar botón por \"Carreras\" y en otro color\n+- [ ] Ver sponsors en las inscripciones\n \n-- [ ] Vídeo de como inscribirse\n+- [ ] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n - [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n \n-- [ ] Acuatlón kids arriba (cambiar foto)\n-- [ ] En web grupo de whatsapp\n+- [x] Acuatlón kids arriba (cambiar foto)\n+- [x] En web grupo de whatsapp\n - [ ] Agregar cronograma\n - [ ] Acomodar sponsors\n \n - [ ] Cargar pagos a Sandoval\n"}, {"date": 1728748198220, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -61,10 +61,10 @@\n \n \n ---\n \n-find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n \n rm -r /var/www/acuatlon/www/wp-content/uploads\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n"}, {"date": 1728752050934, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,9 +67,9 @@\n \n rm -r /var/www/acuatlon/www/wp-content/uploads\n ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n \n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2ZzFrL251cVlUK200M0U3WlM1TU5YcnJLTndvdEM0T3E5ZlRESG1VRU9CaQ%3D%3D\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n \n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n \n \n"}, {"date": 1728906884578, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,22 +1,21 @@\n # ACUATLON\n \n ## TODO\n \n-- [ ] Agregar opciones pago efectivo\n-- [ ] Revisar el error de inscripciones en equipos\n-- [ ] Ver sponsors en las inscripciones\n+- [x] Revisar el error de inscripciones en equipos\n+- [x] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n+- [x] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n \n-- [ ] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n-- [ ] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n-\n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n+\n+- [ ] Agregar opciones pago efectivo (falta en equipos)\n+- [ ] Ver sponsors en las inscripciones\n - [ ] Agregar cronograma\n - [ ] Acomodar sponsors\n \n - [ ] Cargar pagos a Sandoval\n-\n - [ ] Mandar capturas de dónde pueden ir los sponsors\n - [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n \n \n"}, {"date": 1728906937086, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,16 +4,15 @@\n \n - [x] Revisar el error de inscripciones en equipos\n - [x] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n - [x] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n+- [x] Agregar cronograma\n \n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n \n - [ ] Agregar opciones pago efectivo (falta en equipos)\n-- [ ] Ver sponsors en las inscripciones\n-- [ ] Agregar cronograma\n-- [ ] Acomodar sponsors\n+- [ ] Acomodar sponsors en la web y en el sistema\n \n - [ ] Cargar pagos a Sandoval\n - [ ] Mandar capturas de dónde pueden ir los sponsors\n - [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n"}, {"date": 1728906983950, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,11 +10,11 @@\n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n \n - [ ] Agregar opciones pago efectivo (falta en equipos)\n+- [ ] Cargar pagos a Sandoval\n+\n - [ ] Acomodar sponsors en la web y en el sistema\n-\n-- [ ] Cargar pagos a Sandoval\n - [ ] Mandar capturas de dónde pueden ir los sponsors\n - [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n \n \n"}, {"date": 1729080973792, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,10 +15,12 @@\n \n - [ ] Acomodar sponsors en la web y en el sistema\n - [ ] Mandar capturas de dónde pueden ir los sponsors\n - [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+    - INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n+    - Varios running\n+    - Varios aguas abiertas (Oceanman)\n \n-\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n"}, {"date": 1729438870049, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,18 +9,22 @@\n \n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n \n-- [ ] Agregar opciones pago efectivo (falta en equipos)\n-- [ ] Cargar pagos a Sandoval\n-\n-- [ ] Acomodar sponsors en la web y en el sistema\n-- [ ] Mandar capturas de dónde pueden ir los sponsors\n - [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n     - INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n     - Varios running\n     - Varios aguas abiertas (Oceanman)\n \n+- [ ] Agregar opciones pago efectivo (falta en equipos)\n+- [ ] Cargar pagos a Sandoval (explicar lo de efectivo)\n+- [ ] Seguir con las compras\n+\n+---\n+\n+- [ ] Acomodar sponsors en la web y en el sistema (Cuando haya alguno)\n+- [ ] Mandar capturas de dónde pueden ir los sponsors\n+\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n"}, {"date": 1729443049570, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,16 +9,22 @@\n \n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n \n-- [ ] Newsletter invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+- [ ] Preparar Contactos\n     - INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n     - Varios running\n     - Varios aguas abiertas (Oceanman)\n+    - Tria y duas\n+- [ ] Preparar Newsletters\n+  - [ ] invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+  - [ ] Te enviamos este mail porque alguna vez te inscribiste en Cronometraje Instantáneo. Si no querés recibir más mails, podés desuscribirte acá.\n \n - [ ] Agregar opciones pago efectivo (falta en equipos)\n - [ ] Cargar pagos a Sandoval (explicar lo de efectivo)\n - [ ] Seguir con las compras\n+- [ ] Armar pedido de lonas\n+- [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n \n ---\n \n - [ ] Acomodar sponsors en la web y en el sistema (Cuando haya alguno)\n"}, {"date": 1729460269454, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,17 +9,24 @@\n \n - [x] Acuatlón kids arriba (cambiar foto)\n - [x] En web grupo de whatsapp\n \n-- [ ] Preparar Contactos\n-    - INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n-    - Varios running\n-    - Varios aguas abiertas (Oceanman)\n-    - Tria y duas\n+- [x] Preparar Contactos\n+    - [x] INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n+    - [x] Varios running\n+    - [x] Varios aguas abiertas (Oceanman)\n+    - [x] participantes anteriores\n - [ ] Preparar Newsletters\n-  - [ ] invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n-  - [ ] Te enviamos este mail porque alguna vez te inscribiste en Cronometraje Instantáneo. Si no querés recibir más mails, podés desuscribirte acá.\n+  - [x] invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n+  - [x] Te enviamos este mail porque alguna vez te inscribiste en Cronometraje Instantáneo. Si no querés recibir más mails, podés desuscribirte acá.\n+  - [x] Jornada institucionales\n+  - [x] Acuatlón\n+  - [x] Aguas abiertas\n+  - [ ] Running\n \n+- [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n+  - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n+\n - [ ] Agregar opciones pago efectivo (falta en equipos)\n - [ ] Cargar pagos a Sandoval (explicar lo de efectivo)\n - [ ] Seguir con las compras\n - [ ] Armar pedido de lonas\n"}, {"date": 1729513390997, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,8 +21,9 @@\n   - [x] Jornada institucionales\n   - [x] Acuatlón\n   - [x] Aguas abiertas\n   - [ ] Running\n+  - [ ] Grupo de Whatsapp y contar de Swimrun\n \n - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n"}, {"date": 1729513930977, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,12 +20,13 @@\n   - [x] Te enviamos este mail porque alguna vez te inscribiste en Cronometraje Instantáneo. Si no querés recibir más mails, podés desuscribirte acá.\n   - [x] Jornada institucionales\n   - [x] Acuatlón\n   - [x] Aguas abiertas\n-  - [ ] Running\n-  - [ ] Grupo de Whatsapp y contar de Swimrun\n \n - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n+  - [ ] Running\n+  - [ ] Grupo de Whatsapp\n+  - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n - [ ] Agregar opciones pago efectivo (falta en equipos)\n - [ ] Cargar pagos a Sandoval (explicar lo de efectivo)\n"}, {"date": 1729806317101, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -55,9 +55,17 @@\n \n - SwimRun Dupla 10K\n - Acuatlón Posta\n \n+## MEDALLAS\n \n+300 Finishers (U$D 300)\n+20 x 3 Generales (U$D 240) [3 de SwimRun, 4 de Aguas Abiertas, 7 de Acuatlón, 6 de Running]\n+56 x 3 Categorías (U$D 168) [28 de Aguas Abiertas, 14 de Acuatlón, 14 de Running]\n+Total U$D 708 más U$D 300 envío son U$D 1008\n+\n+De todo hay 7 niveles de edades por ende 7 categorías por sexo por distancia\n+\n ## PRECIOS ESCALONADOS\n \n - Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n - Individual 1 carrera $ 65.000 | 60.000\n"}, {"date": 1729807450316, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -57,15 +57,52 @@\n - Acuatlón Posta\n \n ## MEDALLAS\n \n-300 Finishers (U$D 300)\n+Precios en China:\n+https://thgift.en.alibaba.com/\n+\n+300 Finishers (U$D 300) ($1075 * 300 = $322.500)\n 20 x 3 Generales (U$D 240) [3 de SwimRun, 4 de Aguas Abiertas, 7 de Acuatlón, 6 de Running]\n 56 x 3 Categorías (U$D 168) [28 de Aguas Abiertas, 14 de Acuatlón, 14 de Running]\n-Total U$D 708 más U$D 300 envío son U$D 1008\n \n+Total estimado en China: U$D 708 más U$D 300 envío son U$D 1008\n+\n De todo hay 7 niveles de edades por ende 7 categorías por sexo por distancia\n \n+Precios de simple faz Argentina:\n+50mm $1075\n+60mm $1280\n+70mm $1690\n+80mm $2155\n+90mm $2565\n+100mm $4050\n+\n+Cálculo para las categorías:\n+1280 + 1690 + 2155 = 5125 * 56 = 286.000\n+\n+Precios doble faz Argentina:\n+50mm $1376\n+60mm $1468\n+70mm $1930\n+80mm $2470\n+90mm $3500\n+100mm $4630\n+\n+Cálculo para las generales:\n+1930 + 2470 + 3500 = 7900 * 20 = 158.000\n+\n+Cintas sublimadas 100 a 300 Unidades $550\n+Más de 300 unidades $475\n+\n+Cintas color $260\n+Cinta argentina $275\n+\n+Matrices $35000 * 7 = $245.000\n+\n+Total estimado Argentina: $1.011.000\n+\n+\n ## PRECIOS ESCALONADOS\n \n - Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n - Individual 1 carrera $ 65.000 | 60.000\n"}, {"date": 1730570108482, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -115,9 +115,21 @@\n - Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n \n - Acuatlón Kids GRATIS\n \n+El 1 de diciembre aumenta 10%\n \n+## Plan de MKT\n+\n+- Salida en ArgentinaXtreme\n+- Base de posteos en redes sociales\n+- Armar plan de Newsletters\n+\n+- En Noviembre comunicación a los pre-inscriptos de que va a aumentar\n+- Comunicación personalizada a grupos de running con afiche exclusivo de running\n+- En Diciembre Newsletters y Meta Ads puntuales segmentando por deporte y localidad (pago en efectivo, ¿Te enteraste de Águila Run?)\n+- Todo con medición de ROI al ingresar a la página\n+\n ## IDEAS PARA CONVERSAR\n \n - Champagne en el podio\n - Cinta de meta\n"}, {"date": 1732650372201, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,13 @@\n # ACUATLON\n \n+## ORDENAR\n+\n+- Dividir postas de Swimrun\n+- Ver con Bauti las cuentas y que el sistema funcione bien\n+- Re-activar Newsletters\n+- MKT en redes\n+\n ## TODO\n \n - [x] Revisar el error de inscripciones en equipos\n - [x] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n"}, {"date": 1732707335837, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,52 +1,58 @@\n # ACUATLON\n \n-## ORDENAR\n+## TODO\n \n-- Dividir postas de Swimrun\n-- Ver con Bauti las cuentas y que el sistema funcione bien\n-- Re-activar Newsletters\n-- MKT en redes\n+- [ ] Ver vídeo de PNA https://www.youtube.com/live/yrDo8B_cLX8\n \n-## TODO\n+- [ ] Re-activar Newsletters\n+  - [ ] Analizar herramientas de <PERSON>\n+  - [ ] Ofrecer promociones a pre-inscriptos\n+  - [ ] Segmentar en Neuquén y Bari primero\n+  - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n+  - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n \n-- [x] Revisar el error de inscripciones en equipos\n-- [x] Vídeo de como inscribirse Ponerlo en las Inscripciones también y a redes\n-- [x] Ver con Bauti de pasar pagos (enseñar multi-categoria y dato pagos)\n-- [x] Agregar cronograma\n+- [ ] MKT en redes\n+  - [ ] Ver como publican otros\n+  - [ ] Cargar tarjeta y crédito\n+  - [ ] Escribir ideas de frases\n+  - [ ] Hacer vídeo de Águila Run\n+  - [ ] Configurar y coordinar con las chicas\n+  - [ ] Configurar para medir ROI\n \n-- [x] Acuatlón kids arriba (cambiar foto)\n-- [x] En web grupo de whatsapp\n+- [ ] Cuentas\n+  - [ ] Ver con Bauti las cuentas\n+  - [ ] Que el sistema funcione bien\n+  - [ ] Cargar pagos en efectivo\n+  - [ ] Cargar pago Adalberto\n+  - [ ] Actualizar cuentas\n \n-- [x] Preparar Contactos\n-    - [x] INSCRIPCION 3 JORNADAS DEPORTOLOGICAS VLA (respuestas)\n-    - [x] Varios running\n-    - [x] Varios aguas abiertas (Oceanman)\n-    - [x] participantes anteriores\n-- [ ] Preparar Newsletters\n-  - [x] invitando al programa https://youtu.be/opGf587Hw5s?si=6RvhuKwMbA8UyjwO&t=451\n-  - [x] Te enviamos este mail porque alguna vez te inscribiste en Cronometraje Instantáneo. Si no querés recibir más mails, podés desuscribirte acá.\n-  - [x] Jornada institucionales\n-  - [x] Acuatlón\n-  - [x] Aguas abiertas\n+- [ ] Sistema y web\n+  - [ ] Agregar opciones pago efectivo (falta en equipos)\n+  - [ ] Dividir postas de Swimrun\n+  - [ ] Sponsors en web y sistema\n+  - [ ] Altimetría en web\n+  - [ ] Pedir nacionalidad\n+  - [ ] Agregar información de los campeonatos\n \n+- [ ] Comprar medallas\n+\n+\n - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n   - [ ] Running\n   - [ ] Grupo de Whatsapp\n   - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n-- [ ] Agregar opciones pago efectivo (falta en equipos)\n-- [ ] Cargar pagos a Sandoval (explicar lo de efectivo)\n - [ ] Seguir con las compras\n - [ ] Armar pedido de lonas\n-- [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n \n ---\n \n - [ ] Acomodar sponsors en la web y en el sistema (Cuando haya alguno)\n - [ ] Mandar capturas de dónde pueden ir los sponsors\n \n+\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n@@ -122,10 +128,12 @@\n - Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n \n - Acuatlón Kids GRATIS\n \n-El 1 de diciembre aumenta 10%\n+- El 1 de diciembre aumenta 10%\n+- Ofrecer pago en 2 cuotas\n \n+\n ## Plan de MKT\n \n - Salida en ArgentinaXtreme\n - Base de posteos en redes sociales\n@@ -135,8 +143,9 @@\n - Comunicación personalizada a grupos de running con afiche exclusivo de running\n - En Diciembre Newsletters y Meta Ads puntuales segmentando por deporte y localidad (pago en efectivo, ¿Te enteraste de Águila Run?)\n - Todo con medición de ROI al ingresar a la página\n \n+\n ## IDEAS PARA CONVERSAR\n \n - Champagne en el podio\n - Cinta de meta\n"}, {"date": 1732707376666, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,15 @@\n   - [ ] Ofrecer promociones a pre-inscriptos\n   - [ ] Segmentar en Neuquén y Bari primero\n   - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n   - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n+  - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n+  - [ ] Running\n+  - [ ] Grupo de Whatsapp\n+  - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n+  - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n+\n - [ ] MKT en redes\n   - [ ] Ver como publican otros\n   - [ ] <PERSON>gar tarjeta y crédito\n   - [ ] Escribir ideas de frases\n@@ -29,30 +35,17 @@\n - [ ] Sistema y web\n   - [ ] Agregar opciones pago efectivo (falta en equipos)\n   - [ ] Dividir postas de Swimrun\n   - [ ] Sponsors en web y sistema\n+  - [ ] Mandar capturas de dónde pueden ir los sponsors\n   - [ ] Altimetría en web\n   - [ ] Pedir nacionalidad\n   - [ ] Agregar información de los campeonatos\n \n - [ ] Comprar medallas\n \n \n-- [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n-  - [ ] Running\n-  - [ ] Grupo de Whatsapp\n-  - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n-  - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n-- [ ] Seguir con las compras\n-- [ ] Armar pedido de lonas\n-\n----\n-\n-- [ ] Acomodar sponsors en la web y en el sistema (Cuando haya alguno)\n-- [ ] Mandar capturas de dónde pueden ir los sponsors\n-\n-\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n"}, {"date": 1732715075097, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n \n - [ ] Ver vídeo de PNA https://www.youtube.com/live/yrDo8B_cLX8\n \n - [ ] Re-activar Newsletters\n-  - [ ] Ana<PERSON>zar herram<PERSON> de <PERSON>\n+  - [x] Ana<PERSON>zar herramient<PERSON> de <PERSON>\n   - [ ] Ofrecer promociones a pre-inscriptos\n   - [ ] Segmentar en Neuquén y Bari primero\n   - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n   - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n@@ -17,9 +17,9 @@\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n \n - [ ] MKT en redes\n-  - [ ] Ver como publican otros\n+  - [x] Ver como publican otros\n   - [ ] Cargar tarjeta y crédito\n   - [ ] Escribir ideas de frases\n   - [ ] Hacer vídeo de Águila Run\n   - [ ] Configurar y coordinar con las chicas\n@@ -42,10 +42,14 @@\n   - [ ] Agregar información de los campeonatos\n \n - [ ] Comprar medallas\n \n+## PARA EL EVENTO\n \n+- [ ] Armar llegada agua\n+- [ ] Preparar sonidos de largadas y llegadas\n \n+\n ## CARRERAS\n \n - SwimRun Individual 10K\n \n"}, {"date": 1732722650967, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n # ACUATLON\n \n ## TODO\n \n-- [ ] Ver vídeo de PNA https://www.youtube.com/live/yrDo8B_cLX8\n+- [x] Ver vídeo de PNA https://www.youtube.com/live/yrDo8B_cLX8\n \n - [ ] Re-activar Newsletters\n   - [x] Ana<PERSON>zar herram<PERSON> de <PERSON>\n   - [ ] Ofrecer promociones a pre-inscriptos\n"}, {"date": 1732731411114, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -26,8 +26,9 @@\n   - [ ] Configurar para medir ROI\n \n - [ ] Cuentas\n   - [ ] Ver con Bauti las cuentas\n+  - [ ] Pagar adelanto colocación cartel\n   - [ ] Que el sistema funcione bien\n   - [ ] Cargar pagos en efectivo\n   - [ ] Cargar pago Adalberto\n   - [ ] Actualizar cuentas\n"}, {"date": 1732819364072, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,10 @@\n # ACUATLON\n \n ## TODO\n \n-- [x] Ver vídeo de PNA https://www.youtube.com/live/yrDo8B_cLX8\n+- [ ] Comprar medallas\n+- [ ] <PERSON><PERSON><PERSON> segu<PERSON>\n \n - [ ] Re-activar Newsletters\n   - [x] <PERSON><PERSON><PERSON> her<PERSON><PERSON> de <PERSON>\n   - [ ] Ofrecer promociones a pre-inscriptos\n@@ -11,28 +12,29 @@\n   - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n   - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n   - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n   - [ ] Running\n+  - [ ] Sponsors\n   - [ ] Grupo de Whatsapp\n   - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n \n - [ ] MKT en redes\n   - [x] Ver como publican otros\n-  - [ ] Cargar tarjeta y crédito\n-  - [ ] Escribir ideas de frases\n+  - [x] Cargar tarjeta y crédito\n+  - [x] Escribir ideas de frases\n+  - [x] Configurar y coordinar con las chicas\n   - [ ] Hacer vídeo de Águila Run\n-  - [ ] Configurar y coordinar con las chicas\n   - [ ] Configurar para medir ROI\n \n - [ ] Cuentas\n-  - [ ] Ver con Bauti las cuentas\n-  - [ ] Pagar adelanto colocación cartel\n+  - [x] Pagar adelanto colocación cartel\n+  - [x] Cargar pago Adalberto\n+  - [x] Ver con Bauti las cuentas\n   - [ ] Que el sistema funcione bien\n   - [ ] Cargar pagos en efectivo\n-  - [ ] Cargar pago Adalberto\n-  - [ ] Actualizar cuentas\n+  - [ ] Actualizar presupuesto\n \n - [ ] Sistema y web\n   - [ ] Agregar opciones pago efectivo (falta en equipos)\n   - [ ] Dividir postas de Swimrun\n@@ -41,10 +43,17 @@\n   - [ ] Altimetría en web\n   - [ ] Pedir nacionalidad\n   - [ ] Agregar información de los campeonatos\n \n-- [ ] Comprar medallas\n \n+\n+## FRASES\n+\n+<- ¡Nunca corriste en Piedra del Águila!\n+- El primer running en Piedra del Águila\n+- El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n+- Juntate con tu amigo nadador y corre un Acuatlón en postas\n+>\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1732819446770, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,16 @@\n # ACUATLON\n \n ## TODO\n \n-- [ ] Comprar medallas\n-- [ ] Co<PERSON><PERSON> segu<PERSON>\n+- [ ] Sistema y web\n+  - [ ] Agregar opciones pago efectivo (falta en equipos)\n+  - [ ] Dividir postas de Swimrun\n+  - [ ] Sponsors en web y sistema\n+  - [ ] Mandar capturas de dónde pueden ir los sponsors\n+  - [ ] Altimetría en web\n+  - [ ] Pedir nacionalidad\n+  - [ ] Agregar información de los campeonatos\n \n - [ ] Re-activar Newsletters\n   - [x] Analizar herramientas de Leo\n   - [ ] Ofrecer promociones a pre-inscriptos\n@@ -17,36 +23,31 @@\n   - [ ] Grupo de Whatsapp\n   - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n-\n-- [ ] MKT en redes\n-  - [x] Ver como publican otros\n-  - [x] Cargar tarjeta y crédito\n-  - [x] Escribir ideas de frases\n-  - [x] Configurar y coordinar con las chicas\n-  - [ ] Hacer vídeo de Águila Run\n-  - [ ] Configurar para medir ROI\n-\n - [ ] Cuentas\n   - [x] Pagar adelanto colocación cartel\n   - [x] Cargar pago Adalberto\n   - [x] Ver con Bauti las cuentas\n+  - [ ] Me pidio de revisar un equipo\n   - [ ] Que el sistema funcione bien\n   - [ ] Cargar pagos en efectivo\n   - [ ] Actualizar presupuesto\n \n-- [ ] Sistema y web\n-  - [ ] Agregar opciones pago efectivo (falta en equipos)\n-  - [ ] Dividir postas de Swimrun\n-  - [ ] Sponsors en web y sistema\n-  - [ ] Mandar capturas de dónde pueden ir los sponsors\n-  - [ ] Altimetría en web\n-  - [ ] Pedir nacionalidad\n-  - [ ] Agregar información de los campeonatos\n+- [ ] Comprar medallas\n+- [ ] Cotizar seguros\n \n+---\n \n+- [ ] MKT en redes\n+  - [x] Ver como publican otros\n+  - [x] Cargar tarjeta y crédito\n+  - [x] Escribir ideas de frases\n+  - [x] Configurar y coordinar con las chicas\n+  - [ ] Hacer vídeo de Águila Run\n+  - [ ] Configurar para medir ROI\n \n+\n ## FRASES\n \n <- ¡Nunca corriste en Piedra del Águila!\n - El primer running en Piedra del Águila\n"}, {"date": 1732821141002, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,15 +2,18 @@\n \n ## TODO\n \n - [ ] Sistema y web\n+  - [x] Pedir nacionalidad\n+  - [ ] Me pidio de revisar un equipo\n+  - [ ] Que el sistema funcione bien\n   - [ ] Agregar opciones pago efectivo (falta en equipos)\n   - [ ] Dividir postas de Swimrun\n+\n   - [ ] Sponsors en web y sistema\n+  - [ ] Altimetría en web\n+  - [ ] Agregar información de los campeonatos EN WEB\n   - [ ] Mandar capturas de dónde pueden ir los sponsors\n-  - [ ] Altimetría en web\n-  - [ ] Pedir nacionalidad\n-  - [ ] Agregar información de los campeonatos\n \n - [ ] Re-activar Newsletters\n   - [x] <PERSON><PERSON><PERSON> her<PERSON> de <PERSON>\n   - [ ] Ofrecer promociones a pre-inscriptos\n@@ -23,18 +26,10 @@\n   - [ ] Grupo de Whatsapp\n   - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n   - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n-- [ ] Cuentas\n-  - [x] Pagar adelanto colocación cartel\n-  - [x] Cargar pago Adalberto\n-  - [x] Ver con Bauti las cuentas\n-  - [ ] Me pidio de revisar un equipo\n-  - [ ] Que el sistema funcione bien\n-  - [ ] Cargar pagos en efectivo\n-  - [ ] Actualizar presupuesto\n-\n - [ ] Comprar medallas\n+- [ ] Actualizar presupuesto\n - [ ] Cotizar seguros\n \n ---\n \n@@ -45,9 +40,15 @@\n   - [x] Configurar y coordinar con las chicas\n   - [ ] Hacer vídeo de Águila Run\n   - [ ] Configurar para medir ROI\n \n+- [ ] Cuentas\n+  - [x] Pagar adelanto colocación cartel\n+  - [x] Cargar pago Adalberto\n+  - [x] Ver con Bauti las cuentas\n+  - [ ] Cargar pagos en efectivo\n \n+\n ## FRASES\n \n <- ¡Nunca corriste en Piedra del Águila!\n - El primer running en Piedra del Águila\n"}, {"date": 1732884884488, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,11 +3,11 @@\n ## TODO\n \n - [ ] Sistema y web\n   - [x] Pedir nacionalidad\n-  - [ ] Me pidio de revisar un equipo\n-  - [ ] Que el sistema funcione bien\n-  - [ ] Agregar opciones pago efectivo (falta en equipos)\n+  - [x] Que el sistema funcione bien\n+  - [x] Me pidio de revisar un equipo\n+  - [x] Agregar opciones pago efectivo (falta en equipos)\n   - [ ] Dividir postas de Swimrun\n \n   - [ ] Sponsors en web y sistema\n   - [ ] Altimetría en web\n@@ -172,9 +172,12 @@\n http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n \n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n \n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n \n+\n ---\n \n idevento 2193\n \n"}, {"date": 1732888107462, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,11 @@\n   - [x] Pedir nacionalidad\n   - [x] Que el sistema funcione bien\n   - [x] Me pidio de revisar un equipo\n   - [x] Agregar opciones pago efectivo (falta en equipos)\n-  - [ ] Dividir postas de Swimrun\n+  - [x] Dividir postas de Swimrun\n \n-  - [ ] Sponsors en web y sistema\n+  - [x] Sponsors en web y sistema\n   - [ ] Altimetría en web\n   - [ ] Agregar información de los campeonatos EN WEB\n   - [ ] Mandar capturas de dónde pueden ir los sponsors\n \n"}, {"date": 1732904112839, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,15 +10,18 @@\n   - [x] Dividir postas de Swimrun\n \n   - [x] Sponsors en web y sistema\n   - [ ] Altimetría en web\n+  - [ ] Tema boyas en la web\n   - [ ] Agregar información de los campeonatos EN WEB\n   - [ ] Mandar capturas de dónde pueden ir los sponsors\n \n - [ ] Re-activar Newsletters\n   - [x] Analizar herramientas de Leo\n   - [ ] Ofrecer promociones a pre-inscriptos\n+\n   - [ ] Segmentar en Neuquén y Bari primero\n+\n   - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n   - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n   - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n   - [ ] Running\n"}, {"date": 1732910220415, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -21,9 +21,9 @@\n \n   - [ ] Segmentar en Neuquén y Bari primero\n \n   - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n-  - [ ] Buscar manualmente grupos de entrenamiento de running y natación\n+  - [ ] Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n   - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n   - [ ] Running\n   - [ ] Sponsors\n   - [ ] Grupo de Whatsapp\n"}, {"date": 1733063775574, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n   - [ ] Altimetría en web\n   - [ ] Tema boyas en la web\n   - [ ] Agregar información de los campeonatos EN WEB\n   - [ ] Mandar capturas de dónde pueden ir los sponsors\n+  - [ ] en pagina web cambiar afiche final\n \n - [ ] Re-activar Newsletters\n   - [x] <PERSON><PERSON><PERSON> her<PERSON> de <PERSON>\n   - [ ] Ofrecer promociones a pre-inscriptos\n"}, {"date": 1733076240120, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,40 @@\n # ACUATLON\n \n+*Información sobre el Campeonato Neuquino de Aguas Abiertas*\n+\n+¿Cuales carreras suman puntos?\n+- Todas las carreras competitivas de los eventos sumados al campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n+\n+¿Cómo se calculan los puntos?\n+- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos)\n+- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc)\n+\n+¿Qué se premia?\n+- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato\n+- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato\n+- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito\n+\n+¿Cómo participar?\n+- Al estar inscripto y nadar cualquiera de las carreras competitivas ya estás sumando puntos\n+- Para sumar puntos a tu Club o Equipo, debes especificar cuál es en el formulario de inscripción de cada evento. Si ya te inscribiste podes contactarte con los organizadores para que te asignen el Club que quieras.\n+\n+¿Cuál es el costo/descuento?\n+- Participar del campeonato no tiene ningún costo.\n+- Si te inscribis en los 3 eventos del campeonato, tenés un 15% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n+- Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n+\n+\n+\n ## TODO\n \n - [ ] Sistema y web\n   - [x] Pedir nacionalidad\n   - [x] Que el sistema funcione bien\n   - [x] Me pidio de revisar un equipo\n   - [x] Agregar opciones pago efectivo (falta en equipos)\n   - [x] Dividir postas de Swimrun\n+  - [ ] solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n \n   - [x] Sponsors en web y sistema\n   - [ ] Altimetría en web\n   - [ ] Tema boyas en la web\n"}, {"date": 1733080493841, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,30 +2,36 @@\n \n *Información sobre el Campeonato Neuquino de Aguas Abiertas*\n \n ¿Cuales carreras suman puntos?\n-- Todas las carreras competitivas de los eventos sumados al campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n+- Todas las carreras de los eventos del campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n \n ¿Cómo se calculan los puntos?\n-- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos)\n-- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc)\n+- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos).\n+- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc).\n \n ¿Qué se premia?\n-- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato\n-- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato\n-- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito\n+- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato.\n+- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato.\n+- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito.\n \n ¿Cómo participar?\n-- Al estar inscripto y nadar cualquiera de las carreras competitivas ya estás sumando puntos\n+- Al estar inscripto y nadar cualquiera de las carreras de estos eventos ya estás sumando puntos.\n - Para sumar puntos a tu Club o Equipo, debes especificar cuál es en el formulario de inscripción de cada evento. Si ya te inscribiste podes contactarte con los organizadores para que te asignen el Club que quieras.\n \n ¿Cuál es el costo/descuento?\n - Participar del campeonato no tiene ningún costo.\n-- Si te inscribis en los 3 eventos del campeonato, tenés un 15% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n+- Si te inscribis en los 3 eventos del campeonato, tenés un 10% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n - Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n \n \n+---\n \n+de nqn para la pagina van los dos logos, porque una es de la gestión de gobierno actual y el otro de turismo..........una porqueria los logos pero bueno\n+y en colaboradores Aquiles y meseta wear que mas arriba te pase los logos\n+\n+\n+\n ## TODO\n \n - [ ] Sistema y web\n   - [x] Pedir nacionalidad\n"}, {"date": 1733164908284, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -81,8 +81,11 @@\n   - [x] Pagar adelanto colocación cartel\n   - [x] Cargar pago Adalberto\n   - [x] Ver con Bauti las cuentas\n   - [ ] Cargar pagos en efectivo\n+  - [ ] Avalian pagó 500 lucas que las tiene Sandoval\n+  - [ ] Bauti pagó la instalación\n+  - [ ] Me pasó costos de las remeras\n \n \n ## FRASES\n \n"}, {"date": 1733167709806, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,69 +25,15 @@\n \n \n ---\n \n-de nqn para la pagina van los dos logos, porque una es de la gestión de gobierno actual y el otro de turismo..........una porqueria los logos pero bueno\n-y en colaboradores Aquiles y meseta wear que mas arriba te pase los logos\n \n \n-\n ## TODO\n \n-- [ ] Sistema y web\n-  - [x] Pedir nacionalidad\n-  - [x] Que el sistema funcione bien\n-  - [x] Me pidio de revisar un equipo\n-  - [x] Agregar opciones pago efectivo (falta en equipos)\n-  - [x] Dividir postas de Swimrun\n-  - [ ] solo no coinciden: sr dupla, acua ind y acua short, el resto coincide panel de control con estadisticas\n \n-  - [x] Sponsors en web y sistema\n-  - [ ] Altimetría en web\n-  - [ ] Tema boyas en la web\n-  - [ ] Agregar información de los campeonatos EN WEB\n-  - [ ] Mandar capturas de dónde pueden ir los sponsors\n-  - [ ] en pagina web cambiar afiche final\n \n-- [ ] Re-activar Newsletters\n-  - [x] Analizar herramientas de Leo\n-  - [ ] Ofrecer promociones a pre-inscriptos\n \n-  - [ ] Segmentar en Neuquén y Bari primero\n-\n-  - [ ] Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n-  - [ ] Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n-  - [ ] Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n-  - [ ] Running\n-  - [ ] Sponsors\n-  - [ ] Grupo de Whatsapp\n-  - [ ] Contar de Swimrun y que podes hacer el Acuatlón\n-  - [ ] ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n-\n-- [ ] Comprar medallas\n-- [ ] Actualizar presupuesto\n-- [ ] Cotizar seguros\n-\n----\n-\n-- [ ] MKT en redes\n-  - [x] Ver como publican otros\n-  - [x] Cargar tarjeta y crédito\n-  - [x] Escribir ideas de frases\n-  - [x] Configurar y coordinar con las chicas\n-  - [ ] Hacer vídeo de Águila Run\n-  - [ ] Configurar para medir ROI\n-\n-- [ ] Cuentas\n-  - [x] Pagar adelanto colocación cartel\n-  - [x] Cargar pago Adalberto\n-  - [x] Ver con Bauti las cuentas\n-  - [ ] Cargar pagos en efectivo\n-  - [ ] Avalian pagó 500 lucas que las tiene Sandoval\n-  - [ ] Bauti pagó la instalación\n-  - [ ] Me pasó costos de las remeras\n-\n-\n ## FRASES\n \n <- ¡Nunca corriste en Piedra del Águila!\n - El primer running en Piedra del Águila\n"}, {"date": 1733167800576, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,46 +1,15 @@\n # ACUATLON\n \n-*Información sobre el Campeonato Neuquino de Aguas Abiertas*\n-\n-¿Cuales carreras suman puntos?\n-- Todas las carreras de los eventos del campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n-\n-¿Cómo se calculan los puntos?\n-- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos).\n-- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc).\n-\n-¿Qué se premia?\n-- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato.\n-- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato.\n-- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito.\n-\n-¿Cómo participar?\n-- Al estar inscripto y nadar cualquiera de las carreras de estos eventos ya estás sumando puntos.\n-- Para sumar puntos a tu Club o Equipo, debes especificar cuál es en el formulario de inscripción de cada evento. Si ya te inscribiste podes contactarte con los organizadores para que te asignen el Club que quieras.\n-\n-¿Cuál es el costo/descuento?\n-- Participar del campeonato no tiene ningún costo.\n-- Si te inscribis en los 3 eventos del campeonato, tenés un 10% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n-- Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n-\n-\n----\n-\n-\n-\n-## TODO\n-\n-\n-\n-\n ## FRASES\n \n <- ¡Nunca corriste en Piedra del Águila!\n - El primer running en Piedra del Águila\n - El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n - Juntate con tu amigo nadador y corre un Acuatlón en postas\n >\n+\n+\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n@@ -399,4 +368,30 @@\n \n (NULL, 2193, 31, 11107),\n (NULL, 2193, 36, 11107),\n (NULL, 2193, 41, 11107);\n+\n+\n+## TEXTOS VARIOS\n+\n+*Información sobre el Campeonato Neuquino de Aguas Abiertas*\n+\n+¿Cuales carreras suman puntos?\n+- Todas las carreras de los eventos del campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n+\n+¿Cómo se calculan los puntos?\n+- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos).\n+- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc).\n+\n+¿Qué se premia?\n+- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato.\n+- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato.\n+- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito.\n+\n+¿Cómo participar?\n+- Al estar inscripto y nadar cualquiera de las carreras de estos eventos ya estás sumando puntos.\n+- Para sumar puntos a tu Club o Equipo, debes especificar cuál es en el formulario de inscripción de cada evento. Si ya te inscribiste podes contactarte con los organizadores para que te asignen el Club que quieras.\n+\n+¿Cuál es el costo/descuento?\n+- Participar del campeonato no tiene ningún costo.\n+- Si te inscribis en los 3 eventos del campeonato, tenés un 10% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n+- Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n"}, {"date": 1733167866040, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,8 @@\n # ACUATLON\n \n-## FRASES\n \n-<- ¡Nunca corriste en Piedra del Águila!\n-- El primer running en Piedra del Águila\n-- El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n-- Juntate con tu amigo nadador y corre un Acuatlón en postas\n->\n \n-\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1733176359677, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,23 @@\n+# TRÍA\n+\n+Asunto: Somos Olímpicos!!\n+\n+Este 16 de febrero *te invitamos al *Triatlón de la Confluencia 2025 en el que vas a nadar 1500mts, pedalear 40 km y correr 10k!!\n+\n+En nuestras redes sociales vas a encontrar los links de inscripción individual y postas y ahora vas a poder pagar en cuotas.\n+\n+No podes perderte esta fiesta del triatlón en la que vas a recorrer la Ciudad de Neuquén y en la linea de llegada te van a estar esperando ademas de tu medalla de finisher, kinesiologos, piletas de crioterapia y muchas sorpresas más!!\n+\n+Poné a prueba tus límites y forma parte de la edición 2025 en distancia olímpica del Triatlón de la Confluencia.\n+\n+Visitá nuestras redes sociales para enterarte de más novedades!!\n+\n+IG: @pruebasconbinadasnqn\n+Facebook: Asociación Neuquina de Pruebas Combinadas\n+\n+\n+\n # ACUATLON\n \n \n \n"}, {"date": 1733229108588, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,23 +1,4 @@\n-# TRÍA\n-\n-Asunto: Somos Olímpicos!!\n-\n-Este 16 de febrero *te invitamos al *Triatlón de la Confluencia 2025 en el que vas a nadar 1500mts, pedalear 40 km y correr 10k!!\n-\n-En nuestras redes sociales vas a encontrar los links de inscripción individual y postas y ahora vas a poder pagar en cuotas.\n-\n-No podes perderte esta fiesta del triatlón en la que vas a recorrer la Ciudad de Neuquén y en la linea de llegada te van a estar esperando ademas de tu medalla de finisher, kinesiologos, piletas de crioterapia y muchas sorpresas más!!\n-\n-Poné a prueba tus límites y forma parte de la edición 2025 en distancia olímpica del Triatlón de la Confluencia.\n-\n-Visitá nuestras redes sociales para enterarte de más novedades!!\n-\n-IG: @pruebasconbinadasnqn\n-Facebook: Asociación Neuquina de Pruebas Combinadas\n-\n-\n-\n # ACUATLON\n \n \n \n"}, {"date": 1733776522227, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,22 +10,41 @@\n \n ## CARRERAS\n \n - SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n \n - Aguas Abiertas 1500\n+  - 14 categorias\n - Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n \n - Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n - Acuatlón Short\n+  - 3 categorias\n \n - Águila Run 5K\n+  - Nada\n+\n - Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n - Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n \n-- SwimRun Dupla 10K\n-- Acuatlón Posta\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n \n+\n ## MEDALLAS\n \n Precios en China:\n https://thgift.en.alibaba.com/\n"}, {"date": 1734119680783, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -406,4 +406,10 @@\n ¿Cuál es el costo/descuento?\n - Participar del campeonato no tiene ningún costo.\n - Si te inscribis en los 3 eventos del campeonato, tenés un 10% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n - Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n"}, {"date": 1734128789108, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,30 @@\n # ACUATLON\n \n+Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n \n+NEWSLETTERS AHORA:\n \n+- Sumar un listado de sponsors y contactos. Sumar los logos de los sponsors en el mail. Sumar un botón de Grupo de Whatsapp. Tenes un 10% de descuento por pago en efectivo y otro 10% por participar en todo el campeonato. Pagá el 50% y reserva tu lugar.\n+- Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana? \n+- Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n+- Mail del campeonato a los inscriptos de los eventos de Santi.\n+\n+INVITACIÓN WHATSAPP:\n+\n+- Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n+- Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n+- Segmentar en Neuquén y Bari primero\n+\n+IDEAS DESPUÉS:\n+\n+¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n+\n+\n+Interés de turismo provincial  RAFA\n+•\tInterés del consejo deliberante pda   RAFA\n+\n+\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1734138823483, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,15 @@\n # ACUATLON\n \n-Planear próximos envíos ( https://chatgpt.com/c/6712a7ac-418c-8002-991f-2c8d6ae89bbf )\n-\n NEWSLETTERS AHORA:\n \n-- Sumar un listado de sponsors y contactos. Sumar los logos de los sponsors en el mail. Sumar un botón de Grupo de Whatsapp. Tenes un 10% de descuento por pago en efectivo y otro 10% por participar en todo el campeonato. Pagá el 50% y reserva tu lugar.\n-- Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana? \n-- Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n-- Mail del campeonato a los inscriptos de los eventos de Santi.\n+- [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n \n+- [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n+\n+- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos)\n+- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. (Los que están inscriptos en alguna de las carreras de running)\n+\n INVITACIÓN WHATSAPP:\n \n - Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n - Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n@@ -23,8 +23,23 @@\n Interés de turismo provincial  RAFA\n •\tInterés del consejo deliberante pda   RAFA\n \n \n+\n+\n+¡Nunca corriste en Piedra del Águila!\n+\n+\n+\n+El primer running en Piedra del Águila\n+\n+\n+\n+El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n+Juntate con tu amigo nadador y corre un Acuatlón en postas\n+\n+\n+\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1734140353907, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,17 +5,19 @@\n - [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n \n - [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n \n-- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos)\n-- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. (Los que están inscriptos en alguna de las carreras de running)\n+- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n+- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running)\n \n+\n INVITACIÓN WHATSAPP:\n \n - Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n - Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n - Segmentar en Neuquén y Bari primero\n \n+\n IDEAS DESPUÉS:\n \n ¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n"}, {"date": 1734140382855, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,42 +6,23 @@\n \n - [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n \n - [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n-- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running)\n+- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n+Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n+\n INVITACIÓN WHATSAPP:\n \n - Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n - Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n - Segmentar en Neuquén y Bari primero\n \n \n-IDEAS DESPUÉS:\n \n-¿Porqué en Piedra del Águila? ¿Qué tiene ese lugar? ¿Lo conocés?\n \n \n-Interés de turismo provincial  RAFA\n-•\tInterés del consejo deliberante pda   RAFA\n-\n-\n-\n-\n-¡Nunca corriste en Piedra del Águila!\n-\n-\n-\n-El primer running en Piedra del Águila\n-\n-\n-\n-El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n-Juntate con tu amigo nadador y corre un Acuatlón en postas\n-\n-\n-\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1734186689357, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,8 +6,9 @@\n \n - [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n \n - [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n+\n - [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n@@ -19,10 +20,8 @@\n - Segmentar en Neuquén y Bari primero\n \n \n \n-\n-\n ## PARA EL EVENTO\n \n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n"}, {"date": 1734378383028, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -161,8 +161,11 @@\n Equipo posta:\n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n \n \n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n+\n ---\n \n idevento 2193\n \n"}, {"date": 1734485601142, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,16 @@\n # ACUATLON\n \n+ORDENAR AHORA:\n+\n+- <PERSON><PERSON> guardavidas\n+- Escribir reglamento staff (puede ser por whatsapp)\n+- Planear brollets de frutas\n+- Ver si puedo meter streaming\n+- Llevar TV con netbook\n+\n+\n+\n NEWSLETTERS AHORA:\n \n - [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n \n"}, {"date": 1734640648035, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,16 +1,32 @@\n # ACUATLON\n \n-ORDENAR AHORA:\n+HACER DICIEMBRE:\n \n - Buscar guardavidas\n-- Escribir reglamento staff (puede ser por whatsapp)\n-- Planear brollets de frutas\n-- Ver si puedo meter streaming\n-- Llevar TV con netbook\n \n+CUENTAS:\n \n+- Nic\n \n+WEB DE INFO:\n+\n+- Campeonatos\n+- Cronograma\n+- Altimetría\n+- Recorridos\n+\n+\n+DOCUMENTO STAFF:\n+\n+- Sponsors\n+- Horarios\n+- Responsables\n+- Speach de como tratar a los participantes y a sus acompañantes\n+- Albergue y alimentación Staff\n+- Entrega de premios\n+\n+\n NEWSLETTERS AHORA:\n \n - [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n \n@@ -21,23 +37,29 @@\n - [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n+HACER ENERO:\n \n-INVITACIÓN WHATSAPP:\n+- Ver si puedo meter streaming\n+- Llevar TV con netbook\n+- Planear brollets de frutas\n \n-- Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n-- Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n-- Segmentar en Neuquén y Bari primero\n \n+HACER PRE-EVENTO:\n \n-\n-## PARA EL EVENTO\n-\n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n \n \n+\n+\n+INVITACIÓN WHATSAPP:\n+\n+- Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n+- Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n+- Segmentar en Neuquén y Bari primero\n+\n ## CARRERAS\n \n - SwimRun Individual 10K\n   - 2 generales\n"}, {"date": 1734640903545, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,10 @@\n # ACUATLON\n \n-HACER DICIEMBRE:\n-\n-- <PERSON><PERSON> guardavidas\n-\n CUENTAS:\n \n-- Nic\n+- Nic 25500 * 2\n+-\n \n WEB DE INFO:\n \n - Campeonatos\n"}, {"date": 1734641548019, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,62 +1,14 @@\n # ACUATLON\n \n-CUENTAS:\n \n-- Nic 25500 * 2\n--\n-\n-WEB DE INFO:\n-\n-- Campeonatos\n-- Cronograma\n-- Altimetría\n-- Recorridos\n-\n-\n-DOCUMENTO STAFF:\n-\n-- Sponsors\n-- Horarios\n-- Responsables\n-- Speach de como tratar a los participantes y a sus acompañantes\n-- Albergue y alimentación Staff\n-- Entrega de premios\n-\n-\n NEWSLETTERS AHORA:\n \n - [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n \n - [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n \n-- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n \n-- [ ] Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n-Juntate con tu amigo nadador y corre un Acuatlón en postas\n-\n-\n-HACER ENERO:\n-\n-- Ver si puedo meter streaming\n-- Llevar TV con netbook\n-- Planear brollets de frutas\n-\n-\n-HACER PRE-EVENTO:\n-\n-- [ ] Armar llegada agua\n-- [ ] Preparar sonidos de largadas y llegadas\n-\n-\n-\n-\n-INVITACIÓN WHATSAPP:\n-\n-- Buscar manualmente grupos de entrenamiento de running y natación (tengo los del año pasado de Oceanman)\n-- Armar un listado de piletas y lugares donde mandar el flyer por whatsapp\n-- Segmentar en Neuquén y Bari primero\n-\n ## CARRERAS\n \n - SwimRun Individual 10K\n   - 2 generales\n@@ -166,16 +118,8 @@\n - Comunicación personalizada a grupos de running con afiche exclusivo de running\n - En Diciembre Newsletters y Meta Ads puntuales segmentando por deporte y localidad (pago en efectivo, ¿Te enteraste de Águila Run?)\n - Todo con medición de ROI al ingresar a la página\n \n-\n-## IDEAS PARA CONVERSAR\n-\n-- Champagne en el podio\n-- Cinta de meta\n-- Fondo de podio\n-\n-\n ---\n \n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n@@ -463,4 +407,5 @@\n \n - Unificar el reglamento\n - Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n - Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n"}, {"date": 1734642422949, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,9 @@\n \n - [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n \n \n-## CARRERAS\n+## CARRERAS PREMIOS\n \n - SwimRun Individual 10K\n   - 2 generales\n - SwimRun Dupla 10K\n@@ -43,83 +43,16 @@\n Total generales madera: 20 grupos de 123 de madera\n Total de categorias: 73 grupos de 123 medalla con calco\n \n \n-## MEDALLAS\n+## POST EVENTO PARA EL AÑO QUE VIENE\n \n-Precios en China:\n-https://thgift.en.alibaba.com/\n+- Unificar el reglamento\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n \n-300 Finishers (U$D 300) ($1075 * 300 = $322.500)\n-20 x 3 Generales (U$D 240) [3 de SwimRun, 4 de Aguas Abiertas, 7 de Acuatlón, 6 de Running]\n-56 x 3 Categorías (U$D 168) [28 de Aguas Abiertas, 14 de Acuatlón, 14 de Running]\n \n-Total estimado en China: U$D 708 más U$D 300 envío son U$D 1008\n-\n-De todo hay 7 niveles de edades por ende 7 categorías por sexo por distancia\n-\n-Precios de simple faz Argentina:\n-50mm $1075\n-60mm $1280\n-70mm $1690\n-80mm $2155\n-90mm $2565\n-100mm $4050\n-\n-Cálculo para las categorías:\n-1280 + 1690 + 2155 = 5125 * 56 = 286.000\n-\n-Precios doble faz Argentina:\n-50mm $1376\n-60mm $1468\n-70mm $1930\n-80mm $2470\n-90mm $3500\n-100mm $4630\n-\n-Cálculo para las generales:\n-1930 + 2470 + 3500 = 7900 * 20 = 158.000\n-\n-Cintas sublimadas 100 a 300 Unidades $550\n-Más de 300 unidades $475\n-\n-Cintas color $260\n-Cinta argentina $275\n-\n-Matrices $35000 * 7 = $245.000\n-\n-Total estimado Argentina: $1.011.000\n-\n-\n-## PRECIOS ESCALONADOS\n-\n-- Individual 1 carrera (distancias cortas) $ 35.000 | 30.000\n-- Individual 1 carrera $ 65.000 | 60.000\n-\n-- Individual 2 carreras $ 95.000 ($ 47.500 c/u) | 85.000\n-- Individual 3 carreras $ 105.000 ($ 35.000 c/u) | 95.000\n-\n-- Dupla o Posta $ 95.000 ($ 47.500 c/u) | 85.000\n-- Dupla o Posta con descuento $ 65.000 ($ 32.500 c/u) | 60.000\n-- Dupla o Posta con doble descuento $ 35.000 ($ 17.500 c/u) | 30.000\n-\n-- Acuatlón Kids GRATIS\n-\n-- El 1 de diciembre aumenta 10%\n-- Ofrecer pago en 2 cuotas\n-\n-\n-## Plan de MKT\n-\n-- Salida en ArgentinaXtreme\n-- Base de posteos en redes sociales\n-- Armar plan de Newsletters\n-\n-- En Noviembre comunicación a los pre-inscriptos de que va a aumentar\n-- Comunicación personalizada a grupos de running con afiche exclusivo de running\n-- En Diciembre Newsletters y Meta Ads puntuales segmentando por deporte y localidad (pago en efectivo, ¿Te enteraste de Águila Run?)\n-- Todo con medición de ROI al ingresar a la página\n-\n ---\n \n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n@@ -135,277 +68,4 @@\n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n \n \n sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n-\n-\n----\n-\n-idevento 2193\n-\n-INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES\n-(2193, 22, 11103),\n-(2193, 22, 11106),\n-(2193, 22, 11108),\n-\n-(2193, 23, 11101),\n-(2193, 23, 11104),\n-(2193, 23, 11105),\n-(2193, 23, 11109),\n-(2193, 23, 11110),\n-\n-(2193, 24, 11102),\n-(2193, 25, 11102),\n-(2193, 26, 11102),\n-\n-(2193, 24, 11107),\n-(2193, 25, 11107),\n-(2193, 26, 11107)\n-\n-\n-DELETE FROM carreras WHERE idevento = 2193;\n-DELETE FROM categorias WHERE idevento = 2193;\n-\n-INSERT INTO carreras (idcarrera, idevento, nombre, orden) VALUES\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-INSERT INTO categorias (idcategoria, orden, idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES\n-\n-(NULL, 1, 11101, 2193, 'SwimRun Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 2, 11101, 2193, 'SwimRun Femenino', 'femenino', '', NULL, NULL),\n-(NULL, 3, 11102, 2193, 'SwimRun Dupla', 'mixto', 'dupla', NULL, NULL),\n-\n-\n-(NULL, 11, 11103, 2193, 'Aguas Abiertas 1500 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 12, 11103, 2193, 'Aguas Abiertas 1500 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 13, 11103, 2193, 'Aguas Abiertas 1500 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 14, 11103, 2193, 'Aguas Abiertas 1500 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 15, 11103, 2193, 'Aguas Abiertas 1500 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 16, 11103, 2193, 'Aguas Abiertas 1500 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 17, 11103, 2193, 'Aguas Abiertas 1500 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 21, 11103, 2193, 'Aguas Abiertas 1500 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 22, 11103, 2193, 'Aguas Abiertas 1500 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 23, 11103, 2193, 'Aguas Abiertas 1500 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 24, 11103, 2193, 'Aguas Abiertas 1500 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 25, 11103, 2193, 'Aguas Abiertas 1500 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 26, 11103, 2193, 'Aguas Abiertas 1500 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 27, 11103, 2193, 'Aguas Abiertas 1500 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 31, 11104, 2193, 'Aguas Abiertas 4000 Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 32, 11104, 2193, 'Aguas Abiertas 4000 Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 33, 11104, 2193, 'Aguas Abiertas 4000 Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 34, 11104, 2193, 'Aguas Abiertas 4000 Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 35, 11104, 2193, 'Aguas Abiertas 4000 Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 36, 11104, 2193, 'Aguas Abiertas 4000 Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 37, 11104, 2193, 'Aguas Abiertas 4000 Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 41, 11104, 2193, 'Aguas Abiertas 4000 Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 42, 11104, 2193, 'Aguas Abiertas 4000 Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 43, 11104, 2193, 'Aguas Abiertas 4000 Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 44, 11104, 2193, 'Aguas Abiertas 4000 Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 45, 11104, 2193, 'Aguas Abiertas 4000 Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 46, 11104, 2193, 'Aguas Abiertas 4000 Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 47, 11104, 2193, 'Aguas Abiertas 4000 Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-\n-(NULL, 51, 11105, 2193, 'Acuatlón Olímpico Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 52, 11105, 2193, 'Acuatlón Olímpico Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 53, 11105, 2193, 'Acuatlón Olímpico Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 54, 11105, 2193, 'Acuatlón Olímpico Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 55, 11105, 2193, 'Acuatlón Olímpico Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 56, 11105, 2193, 'Acuatlón Olímpico Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 57, 11105, 2193, 'Acuatlón Olímpico Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 61, 11105, 2193, 'Acuatlón Olímpico Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 62, 11105, 2193, 'Acuatlón Olímpico Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 63, 11105, 2193, 'Acuatlón Olímpico Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 64, 11105, 2193, 'Acuatlón Olímpico Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 65, 11105, 2193, 'Acuatlón Olímpico Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 66, 11105, 2193, 'Acuatlón Olímpico Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 67, 11105, 2193, 'Acuatlón Olímpico Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 71, 11106, 2193, 'Acuatlón Short Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 72, 11106, 2193, 'Acuatlón Short Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 73, 11107, 2193, 'Acuatlón Posta Masculina', 'masculino', 'dupla', NULL, NULL),\n-(NULL, 74, 11107, 2193, 'Acuatlón Posta Femenina', 'femenino', 'dupla', NULL, NULL),\n-(NULL, 75, 11107, 2193, 'Acuatlón Posta Mixta', 'mixto', 'dupla', NULL, NULL),\n-\n-(NULL, 81, 11108, 2193, 'Águila Run 5K Masculino', 'masculino', '', NULL, NULL),\n-(NULL, 82, 11108, 2193, 'Águila Run 5K Femenino', 'femenino', '', NULL, NULL),\n-\n-(NULL, 101, 11109, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11109, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11109, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11109, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11109, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11109, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11109, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11109, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11109, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11109, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11109, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11109, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11109, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11109, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 101, 11110, 2193, 'Águila Run 10K Femenino 16-19', 'femenino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 102, 11110, 2193, 'Águila Run 10K Femenino 20-29', 'femenino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 103, 11110, 2193, 'Águila Run 10K Femenino 30-39', 'femenino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 104, 11110, 2193, 'Águila Run 10K Femenino 40-49', 'femenino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 105, 11110, 2193, 'Águila Run 10K Femenino 50-59', 'femenino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 106, 11110, 2193, 'Águila Run 10K Femenino 60-69', 'femenino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 107, 11110, 2193, 'Águila Run 10K Femenino 70-99', 'femenino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 111, 11110, 2193, 'Águila Run 10K Masculino 16-19', 'masculino', '', '2006-01-01', '2009-12-31'),\n-(NULL, 112, 11110, 2193, 'Águila Run 10K Masculino 20-29', 'masculino', '', '1996-01-01', '2005-12-31'),\n-(NULL, 113, 11110, 2193, 'Águila Run 10K Masculino 30-39', 'masculino', '', '1986-01-01', '1995-12-31'),\n-(NULL, 114, 11110, 2193, 'Águila Run 10K Masculino 40-49', 'masculino', '', '1976-01-01', '1985-12-31'),\n-(NULL, 115, 11110, 2193, 'Águila Run 10K Masculino 50-59', 'masculino', '', '1966-01-01', '1975-12-31'),\n-(NULL, 116, 11110, 2193, 'Águila Run 10K Masculino 60-69', 'masculino', '', '1956-01-01', '1965-12-31'),\n-(NULL, 117, 11110, 2193, 'Águila Run 10K Masculino 70-99', 'masculino', '', '1926-01-01', '1955-12-31'),\n-\n-(NULL, 120, 11111, 2193, 'Acuatlón Kids', 'mixto', '', '2010-01-01', '2022-12-31');\n-\n-carreras_juntas = [\n-    \"11103-11104\",\n-    \"11105-11106\",\n-    \"11105-11108\",\n-    \"11105-11109\",\n-    \"11105-11110\",\n-    \"11106-11108\",\n-    \"11106-11109\",\n-    \"11106-11110\",\n-    \"11108-11109\",\n-    \"11108-11110\",\n-    \"11109-11110\",\n-]\n-\n-(11101, 2193, 'SwimRun Individual 10K', 1),\n-(11102, 2193, 'SwimRun Dupla 10K', 2),\n-(11103, 2193, 'Aguas Abiertas 1500', 3),\n-(11104, 2193, 'Aguas Abiertas 4000', 4),\n-(11105, 2193, 'Acuatlón Olímpico', 5),\n-(11106, 2193, 'Acuatlón Short', 6),\n-(11107, 2193, 'Acuatlón Posta', 7),\n-(11108, 2193, 'Águila Run 5K', 8),\n-(11109, 2193, 'Águila Run 10K', 9),\n-(11110, 2193, 'Águila Run 21K', 10),\n-(11111, 2193, 'Acuatlón Kids', 11);\n-\n-\n-\n-INSERT INTO `precios` (`idprecio`, `idevento`, `idpais`, `idplataforma`, `fecha_desde`, `fecha_hasta`, `precio`, `cantidad`, `cuota`, `boton`, `url`) VALUES\n-(27, 2193, 0, 13, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n-(28, 2193, 0, 13, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(29, 2193, 0, 13, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n-(30, 2193, 0, 13, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(31, 2193, 0, 13, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n-\n-(32, 2193, 0, 14, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n-(33, 2193, 0, 14, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(34, 2193, 0, 14, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n-(35, 2193, 0, 14, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(36, 2193, 0, 14, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n-\n-(37, 2193, 0, 15, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL),\n-(38, 2193, 0, 15, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(39, 2193, 0, 15, NULL, NULL, '85000.00', 0, '100.00', NULL, NULL),\n-(40, 2193, 0, 15, NULL, NULL, '60000.00', 0, '100.00', NULL, NULL),\n-(41, 2193, 0, 15, NULL, NULL, '30000.00', 0, '100.00', NULL, NULL);\n-\n-\n-INSERT INTO `preciosxcarreras` (`idprecioxcarrera`, `idevento`, `idprecio`, `idcarrera`) VALUES\n-(NULL, 2193, 27, 11103),\n-(NULL, 2193, 27, 11106),\n-(NULL, 2193, 27, 11108),\n-(NULL, 2193, 32, 11103),\n-(NULL, 2193, 32, 11106),\n-(NULL, 2193, 32, 11108),\n-(NULL, 2193, 37, 11103),\n-(NULL, 2193, 37, 11106),\n-(NULL, 2193, 37, 11108),\n-\n-(NULL, 2193, 28, 11101),\n-(NULL, 2193, 28, 11104),\n-(NULL, 2193, 28, 11105),\n-(NULL, 2193, 28, 11109),\n-(NULL, 2193, 28, 11110),\n-(NULL, 2193, 33, 11101),\n-(NULL, 2193, 33, 11104),\n-(NULL, 2193, 33, 11105),\n-(NULL, 2193, 33, 11109),\n-(NULL, 2193, 33, 11110),\n-(NULL, 2193, 38, 11101),\n-(NULL, 2193, 38, 11104),\n-(NULL, 2193, 38, 11105),\n-(NULL, 2193, 38, 11109),\n-(NULL, 2193, 38, 11110),\n-\n-(NULL, 2193, 29, 11102),\n-(NULL, 2193, 34, 11102),\n-(NULL, 2193, 39, 11102),\n-\n-(NULL, 2193, 30, 11102),\n-(NULL, 2193, 35, 11102),\n-(NULL, 2193, 40, 11102),\n-\n-(NULL, 2193, 31, 11102),\n-(NULL, 2193, 36, 11102),\n-(NULL, 2193, 41, 11102),\n-\n-(NULL, 2193, 29, 11107),\n-(NULL, 2193, 34, 11107),\n-(NULL, 2193, 39, 11107),\n-\n-(NULL, 2193, 30, 11107),\n-(NULL, 2193, 35, 11107),\n-(NULL, 2193, 40, 11107),\n-\n-(NULL, 2193, 31, 11107),\n-(NULL, 2193, 36, 11107),\n-(NULL, 2193, 41, 11107);\n-\n-\n-## TEXTOS VARIOS\n-\n-*Información sobre el Campeonato Neuquino de Aguas Abiertas*\n-\n-¿Cuales carreras suman puntos?\n-- Todas las carreras de los eventos del campeonato suman puntos en sus clasificaciones: Generales Hombres y Generales Mujeres.\n-\n-¿Cómo se calculan los puntos?\n-- Se suma 1 punto por cada metro de nado para el primero de la general (por ej. para 4000m serían 4000 puntos).\n-- Los que le siguen, tienen 10% menos cada uno (por ej. 3600 puntos el segundo, 3200 puntos el tercero, etc).\n-\n-¿Qué se premia?\n-- El *Campeón Neuquina de Aguas Abiertas* será el hombre con mayor puntaje del campeonato.\n-- La *Campeona Neuquina de Aguas Abiertas* será la mujer con mayor puntaje del campeonato.\n-- El *Equipo Campeón Neuquino de Aguas Abiertas* será el club o equipo que sume más puntos entre todos sus participantes que completaron un circuito.\n-\n-¿Cómo participar?\n-- Al estar inscripto y nadar cualquiera de las carreras de estos eventos ya estás sumando puntos.\n-- Para sumar puntos a tu Club o Equipo, debes especificar cuál es en el formulario de inscripción de cada evento. Si ya te inscribiste podes contactarte con los organizadores para que te asignen el Club que quieras.\n-\n-¿Cuál es el costo/descuento?\n-- Participar del campeonato no tiene ningún costo.\n-- Si te inscribis en los 3 eventos del campeonato, tenés un 10% de descuento en cada inscripción. Si ya pagaste tu inscripción, comunicate con el organizador para que te haga la devolución correspondiente.\n-- Para identificarte debes inscribirte con tu DNI en los 3 eventos.\n-\n-## POST EVENTO PARA EL AÑO QUE VIENE\n-\n-- Unificar el reglamento\n-- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n-- Hacer un posteo en redes sociales con la info del año que viene\n-- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n"}, {"date": 1734642561392, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,14 +1,8 @@\n # ACUATLON\n \n \n-NEWSLETTERS AHORA:\n \n-- [x] Newsletter a los pre-inscriptos contarle de los campeonatos, del gorro y la remera que la pueden ver en el grupo. Contar de Swimrun y que podes hacer el Acuatlón y que vas a poder sumarte a esas pruebas. ¿Te animas a 3 carreras en un fin de semana?\n-\n-- [x] Newsletter a los participantes anteriores que no están pre-inscriptos, contarles de los campeonatos, del gorro y la remera que la pueden ver en el grupo. El Acuatlón este año viene con todo junto para vos.\n-\n-\n ## CARRERAS PREMIOS\n \n - SwimRun Individual 10K\n   - 2 generales\n"}, {"date": 1735866189773, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,69 @@\n # ACUATLON\n \n+## KIT\n \n+- Colo bolsa compostable (calcular costo)\n+- Yerba barrita\n+- Remera y gorra\n \n+## SEGURO\n+\n+- Pedir cotización (Andres + Vivi)\n+- Enviamos por mail\n+\n+## VARIOS\n+\n+- Transito y bomberos es PC (Ambulancia y médicos)\n+- Dibujar parque cerrado con 200m barras\n+- Banderas para marcar (manguear a Javier) y Jose hace estacas\n+- Ver quien limpia los baños y que materiales necesitan\n+- Controlar audio, mic y cableados (miércoles)\n+- Ver electricidad para cronometraje\n+- Ver streaming\n+- IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n+\n+## ACREDITACION CHICAS\n+\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios\n+\n+## POST\n+\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+\n+## GASTOS\n+\n+- Remeras las paga provincia\n+- Availan puso 500\n+- Tac da 8 kits para carreras $200k c//u\n+- Seba locutor consiguió 200 barrijtas y muestras de yerba por $400k\n+- Santi y Seba viajes $300k aprox ambos\n+- Nómade (subir a web) da $500k aprox en premios\n+- Guardavidas más lanchas\n+\n+## NEWSLETTER\n+\n+- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n+- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n+- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+\n+## BOYAS\n+\n+- Plotear la de Andres\n+- 6 boyas grandes rojas y 2 amarillas\n+- Preguntar rompeolas a s/temp.andresmisiak.ar/travesiadeloscerros.com/gVer s de armar el plano de boyas con las coordenadas\n+\n+## HORARIOS\n+\n+- Armar nuevo horario con: hora de charlas técnicas, acreditación desde y hasta\n+- sábado 930 short y 945 olímpico y running\n+\n ## CARRERAS PREMIOS\n \n - SwimRun Individual 10K\n   - 2 generales\n"}, {"date": 1736005593114, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,8 +62,55 @@\n \n - Armar nuevo horario con: hora de charlas técnicas, acreditación desde y hasta\n - sábado 930 short y 945 olímpico y running\n \n+## HORARIOS\n+\n+Cronograma y ubicaciones\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ Ubicaciones\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ Viernes 17 de Enero\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ Sábado 16 de Enero\n+\n+⏰ 8:00 a 8:30 - Acreditaciones en Albergue Municipal (Amancay 46)\n+⏰ 8:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 9:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales\n+\n+🗓️ Domingo 19 de Enero\n+\n+⏰ 9:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 9:45 - Largada Acuatlón Short\n+⏰ 9:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n ## CARRERAS PREMIOS\n \n - SwimRun Individual 10K\n   - 2 generales\n"}, {"date": 1736007831494, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,35 +62,34 @@\n \n - Armar nuevo horario con: hora de charlas técnicas, acreditación desde y hasta\n - sábado 930 short y 945 olímpico y running\n \n-## HORARIOS\n+## CRONOGRAMA Y UBICACIONES\n \n-Cronograma y ubicaciones\n-\n Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n \n-🗺️ Ubicaciones\n+🗺️ *Ubicaciones*\n \n 📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n \n 📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n \n 📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n \n-🗓️ Viernes 17 de Enero\n+🗓️ *Viernes 17 de Enero*\n \n ⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n \n-🗓️ Sábado 16 de Enero\n+🗓️ *Sábado 16 de Enero*\n \n-⏰ 8:00 a 8:30 - Acreditaciones en Albergue Municipal (Amancay 46)\n-⏰ 8:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n-⏰ 9:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n \n ⏰ 12:50 - Charla técnica Acuatlón Kids\n ⏰ 13:00 - Largada Acuatlón Kids\n \n+⏰ 14:00 a 18:00 - Acreditaciones\n ⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n ⏰ 15:00 - Largada Aguas Abiertas 4000m\n ⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n ⏰ 15:30 - Largada Aguas Abiertas  1500m\n@@ -98,13 +97,14 @@\n ⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n \n ⏰ 20:00 - Espectáculos Musicales\n \n-🗓️ Domingo 19 de Enero\n+🗓️ *Domingo 19 de Enero*\n \n-⏰ 9:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n-⏰ 9:45 - Largada Acuatlón Short\n-⏰ 9:50 - Charla técnica para Águila Run\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n ⏰ 10:00 - Largada Acuatlón Olímpico y Águila Run (todas las distancias)\n \n ⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n ⏰ 14:00 - Cierre del evento\n"}, {"date": 1736009753263, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,41 +1,48 @@\n # ACUATLON\n \n-## KIT\n+## TAREAS HOY\n \n-- Colo bolsa compostable (calcular costo)\n-- Yerba barrita\n-- Remera y gorra\n+- [x] Sponsors a web y grupo\n+- [x] Hacer Cronograma\n+- [x] Actualizar circuito 1500m\n+- [x] Armar listado de GPS de Boyas\n+- [ ] Preparar newsletters\n+- [ ] Actualizar presupuesto\n+- [ ] Empezar archivos y listados\n \n-## SEGURO\n \n-- Pedir cotización (Andres + Vivi)\n-- Enviamos por mail\n+## LUNES\n \n-## VARIOS\n+- [ ] Averiguar por rompeolas\n+- [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n+- [ ] Dibujar parque cerrado con 200m barras\n+- [ ] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n+- [ ] Ver quien limpia los baños y que materiales necesitan\n+- [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n+- [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n-- Transito y bomberos es PC (Ambulancia y médicos)\n-- Dibujar parque cerrado con 200m barras\n-- Banderas para marcar (manguear a Javier) y Jose hace estacas\n-- Ver quien limpia los baños y que materiales necesitan\n-- Controlar audio, mic y cableados (miércoles)\n-- Ver electricidad para cronometraje\n-- Ver streaming\n-- IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n \n-## ACREDITACION CHICAS\n+## SI LLEGO\n \n-- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n-- Listado de staff, almuerzos y albergues\n-- Venta de remeras 8 o 2x15\n-- Entrega de premios\n+- [ ] Actualizar imagen de cronograma\n+- [ ] Ver streaming\n \n-## POST\n+## EN PIEDRA\n \n-- Agradecimiento a sponsors\n-  - Posteo\n-  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- [ ] Armar kits\n+- [ ] Controlar audio, mic y cableados\n+- [ ] Ver electricidad para cronometraje\n \n+---\n+\n+## NEWSLETTER\n+\n+- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n+- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n+- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+\n ## GASTOS\n \n - Remeras las paga provincia\n - Availan puso 500\n@@ -44,25 +51,31 @@\n - Santi y Seba viajes $300k aprox ambos\n - Nómade (subir a web) da $500k aprox en premios\n - Guardavidas más lanchas\n \n-## NEWSLETTER\n \n-- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n-- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n-- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n-- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+## ACREDITACION CHICAS\n \n+- Colo bolsa compostable (calcular costo)\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios\n+\n+\n+---\n+\n ## BOYAS\n \n-- Plotear la de Andres\n-- 6 boyas grandes rojas y 2 amarillas\n-- Preguntar rompeolas a s/temp.andresmisiak.ar/travesiadeloscerros.com/gVer s de armar el plano de boyas con las coordenadas\n+Ubicación GPS de las 6 boyas rojas:\n \n-## HORARIOS\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n \n-- Armar nuevo horario con: hora de charlas técnicas, acreditación desde y hasta\n-- sábado 930 short y 945 olímpico y running\n \n ## CRONOGRAMA Y UBICACIONES\n \n Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n@@ -151,8 +164,11 @@\n - Unificar el reglamento\n - Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n - Hacer un posteo en redes sociales con la info del año que viene\n - Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n \n \n ---\n \n"}, {"date": 1736010873936, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,61 +6,78 @@\n - [x] Hacer Cronograma\n - [x] Actualizar circuito 1500m\n - [x] Armar listado de GPS de Boyas\n - [ ] Preparar newsletters\n-- [ ] Actualizar presupuesto\n-- [ ] Empezar archivos y listados\n+- [ ] Actualizar cajas\n+- [ ] Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n \n \n ## LUNES\n \n - [ ] Averiguar por rompeolas\n+- [ ] Averiguar por streaming de la radio y compartir\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Dibujar parque cerrado con 200m barras\n - [ ] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n+## PREPARAR PARA LLEVAR\n+\n+- [ ] Reader y chips de Gaby\n+- [ ] GoPro cargada\n+- [ ] Llevar TV con netbook\n+- [ ] Armar llegada agua\n+- [ ] Preparar sonidos de largadas y llegadas\n+\n+\n ## SI LLEGO\n \n - [ ] Actualizar imagen de cronograma\n - [ ] Ver streaming\n \n+\n ## EN PIEDRA\n \n - [ ] Armar kits\n - [ ] Controlar audio, mic y cableados\n - [ ] Ver electricidad para cronometraje\n \n+\n ---\n \n ## NEWSLETTER\n \n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n - Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n-## GASTOS\n \n+## CAJAS\n+\n - Remeras las paga provincia\n - Availan puso 500\n - Tac da 8 kits para carreras $200k c//u\n - Seba locutor consiguió 200 barrijtas y muestras de yerba por $400k\n - Santi y Seba viajes $300k aprox ambos\n - Nómade (subir a web) da $500k aprox en premios\n - Guardavidas más lanchas\n+- Cargar pagos en efectivo Avalian pagó 500 lucas que las tiene Sandoval Bauti pagó la instalación Me pasó costos de las remeras Transferir Criss gorros y cargar los pagos que te pasó Andres Nic 25500 * 2\n \n \n+\n ## ACREDITACION CHICAS\n \n - Colo bolsa compostable (calcular costo)\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n - Entrega de premios\n+- Brollet de frutas\n \n \n ---\n \n@@ -167,8 +184,11 @@\n - Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n - Agradecimiento a sponsors\n   - Posteo\n   - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Radio en la página\n \n \n ---\n \n"}, {"date": 1736011261615, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,9 +25,9 @@\n \n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n-- [ ] GoPro cargada\n+- [ ] GoPro cargada y probar que grabe varias horas\n - [ ] Llevar TV con netbook\n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n \n@@ -65,11 +65,16 @@\n - Santi y Seba viajes $300k aprox ambos\n - Nómade (subir a web) da $500k aprox en premios\n - Guardavidas más lanchas\n - Cargar pagos en efectivo Avalian pagó 500 lucas que las tiene Sandoval Bauti pagó la instalación Me pasó costos de las remeras Transferir Criss gorros y cargar los pagos que te pasó Andres Nic 25500 * 2\n+- Cargar pagos en efectivo\n+- Avalian pagó 500 lucas que las tiene Sandoval\n+- <PERSON>uti pagó la instalación del cartel\n+- Me pasó costos de las remeras, buscar\n+- Transferir Criss gorros y cargar los pagos que te pasó Andres\n+- Nic 25500 * 2\n \n \n-\n ## ACREDITACION CHICAS\n \n - Colo bolsa compostable (calcular costo)\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -25,9 +25,9 @@\n \n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n-- [ ] GoPro cargada y probar que grabe varias horas\n+- [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n - [ ] Llevar TV con netbook\n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,17 +1,7 @@\n # ACUATLON\n \n-## TAREAS HOY\n \n-- [x] Sponsors a web y grupo\n-- [x] Hacer Cronograma\n-- [x] Actualizar circuito 1500m\n-- [x] Armar listado de GPS de Boyas\n-- [ ] Preparar newsletters\n-- [ ] Actualizar cajas\n-- [ ] Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n-\n-\n ## LUNES\n \n - [ ] Averiguar por rompeolas\n - [ ] Averiguar por streaming de la radio y compartir\n@@ -53,8 +43,9 @@\n - Puel<PERSON>, Owa acá y Marimenuco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n - Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n+- Varias frases para Whatsapp y Redes\n \n \n ## CAJAS\n \n@@ -73,10 +64,11 @@\n - Transferir Criss gorros y cargar los pagos que te pasó Andres\n - Nic 25500 * 2\n \n \n-## ACREDITACION CHICAS\n+## ACREDITACION CHICAS Y LISTAS\n \n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Colo bolsa compostable (calcular costo)\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n"}, {"date": 1736012201907, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,10 +44,12 @@\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n - Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n - Varias frases para Whatsapp y Redes\n+- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n+- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n+- Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n-\n ## CAJAS\n \n - Remeras las paga provincia\n - Availan puso 500\n"}, {"date": 1736012214290, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,8 +48,9 @@\n - Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n - Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n - Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n+\n ## CAJAS\n \n - Remeras las paga provincia\n - Availan puso 500\n"}, {"date": 1736012244429, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,10 +34,8 @@\n - [ ] Controlar audio, mic y cableados\n - [ ] Ver electricidad para cronometraje\n \n \n----\n-\n ## NEWSLETTER\n \n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n - Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n"}, {"date": 1736012279963, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n \n \n ## LUNES\n \n+- [ ] Ver de activar MKT pago en redes\n - [ ] Averiguar por rompeolas\n - [ ] Averiguar por streaming de la radio y compartir\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Dibujar parque cerrado con 200m barras\n"}, {"date": 1736090653797, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,8 +37,9 @@\n \n \n ## NEWSLETTER\n \n+- En todos lados poner los premios de Tac y Nomade\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n - Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n@@ -50,19 +51,10 @@\n \n \n ## CAJAS\n \n-- Remeras las paga provincia\n-- Availan puso 500\n-- Tac da 8 kits para carreras $200k c//u\n-- Seba locutor consiguió 200 barrijtas y muestras de yerba por $400k\n-- Sant<PERSON> y Seba viajes $300k aprox ambos\n-- Nómade (subir a web) da $500k aprox en premios\n - Guardavidas más lanchas\n-- Cargar pagos en efectivo Avalian pagó 500 lucas que las tiene Sandoval Bauti pagó la instalación Me pasó costos de las remeras Transferir Criss gorros y cargar los pagos que te pasó Andres Nic 25500 * 2\n - Cargar pagos en efectivo\n-- Avalian pagó 500 lucas que las tiene Sandoval\n-- Bauti pagó la instalación del cartel\n - Me pasó costos de las remeras, buscar\n - Transferir Criss gorros y cargar los pagos que te pasó Andres\n - Nic 25500 * 2\n \n"}, {"date": 1736090686316, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -55,9 +55,8 @@\n - Guardavidas más lanchas\n - Cargar pagos en efectivo\n - Me pasó costos de las remeras, buscar\n - Transferir Criss gorros y cargar los pagos que te pasó Andres\n-- Nic 25500 * 2\n \n \n ## ACREDITACION CHICAS Y LISTAS\n \n"}, {"date": 1736105966922, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -49,16 +49,8 @@\n - Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n - Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n-## CAJAS\n-\n-- Guardavidas más lanchas\n-- Cargar pagos en efectivo\n-- Me pasó costos de las remeras, buscar\n-- Transferir Criss gorros y cargar los pagos que te pasó <PERSON>\n-\n-\n ## ACREDITACION CHICAS Y LISTAS\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Colo bolsa compostable (calcular costo)\n"}, {"date": 1736105991404, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,11 @@\n # ACUATLON\n \n+## CAJAS\n \n+- Armar consultas para generar las cajas\n+\n+\n ## LUNES\n \n - [ ] Ver de activar MKT pago en redes\n - [ ] Averiguar por rompeolas\n"}, {"date": 1736106047084, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,10 @@\n # ACUATLON\n \n-## CAJAS\n+## SQL TOTALES\n \n - Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n \n \n ## LUNES\n \n"}, {"date": 1736111125051, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,20 @@\n \n - Armar consultas para generar las cajas\n - Armar consultas para ver cuánta gente hay\n \n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas,\n \n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n+\n ## LUNES\n \n - [ ] Ver de activar MKT pago en redes\n - [ ] Averiguar por rompeolas\n"}, {"date": 1736111242391, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -14,8 +14,14 @@\n idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n GROUP BY idinscripcion\n ORDER BY cantidad DESC\n \n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n+ORDER BY idinscripcion\n+\n Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n \n ## LUNES\n \n"}, {"date": 1736111368825, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,9 @@\n \n SELECT\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas\n \n SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n GROUP BY idinscripcion\n@@ -17,9 +17,9 @@\n \n SELECT categorias.nombre AS cat, idinscripcion\n FROM categoriasxparticipantes\n LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n-WHERE idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n+WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n ORDER BY idinscripcion\n \n Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n \n"}, {"date": 1736112532616, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,8 +33,9 @@\n - [ ] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n+- [ ] Si Bauti tiene 2M, que me lo transfiera para pagar Rosita\n \n \n ## PREPARAR PARA LLEVAR\n \n"}, {"date": 1736113027030, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,24 +4,36 @@\n \n - Armar consultas para generar las cajas\n - Armar consultas para ver cuánta gente hay\n \n+*Cantidades de personas*\n SELECT\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas\n \n+*Cantidades de inscripciones en multiples carreras*\n SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n GROUP BY idinscripcion\n ORDER BY cantidad DESC\n \n+*Carreras de inscripciones en multiples carreras*\n SELECT categorias.nombre AS cat, idinscripcion\n FROM categoriasxparticipantes\n LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n ORDER BY idinscripcion\n \n+*Pagos a Bauti*\n+SELECT p.idinscripcion, p.nombre,\n+precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n \n ## LUNES\n \n"}, {"date": 1736113145926, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,10 +24,9 @@\n WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n ORDER BY idinscripcion\n \n *Pagos a Bauti*\n-SELECT p.idinscripcion, p.nombre,\n-precios.precio\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n FROM pagos\n LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n WHERE pagos.idevento = 2193\n"}, {"date": 1736113175851, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,9 @@\n - [ ] Banderas para marcar (manguear a Javier) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n-- [ ] Si Bauti tiene 2M, que me lo transfiera para pagar Rosita\n+- [ ] Si Bauti tiene 2M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita\n \n \n ## PREPARAR PARA LLEVAR\n \n"}, {"date": 1736113269292, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,10 @@\n - [ ] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n-- [ ] Si Bauti tiene 2M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita\n+- [ ] Pasar datos de consultas SQL a Andres y contar pago de Bauti\n+- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n \n \n ## PREPARAR PARA LLEVAR\n \n"}, {"date": 1736113287855, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,9 @@\n - [ ] Banderas para marcar (manguear a Javier) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n-- [ ] Pasar datos de consultas SQL a Andres y contar pago de Bauti\n+- [ ] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n - [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n \n \n ## PREPARAR PARA LLEVAR\n"}, {"date": 1736113460894, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -31,10 +31,12 @@\n LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n WHERE pagos.idevento = 2193\n ORDER BY precios.precio;\n \n+\n Pagos en efectivo según caja, <PERSON><PERSON> con MP, Calcular valor por cantidad de carreras\n \n+\n ## LUNES\n \n - [ ] Ver de activar MKT pago en redes\n - [ ] Averiguar por rompeolas\n"}, {"date": 1736174440723, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -152,9 +152,9 @@\n ⏰ 07:00 a 09:00 - Acreditaciones\n ⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n ⏰ 09:45 - Largada Acuatlón Short\n ⏰ 09:50 - Charla técnica para Águila Run\n-⏰ 10:00 - Largada Acuatlón Olímpico y Águila Run (todas las distancias)\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n \n ⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n ⏰ 14:00 - Cierre del evento\n \n"}, {"date": 1736176335926, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,19 +37,20 @@\n \n \n ## LUNES\n \n-- [ ] Ver de activar MKT pago en redes\n+- [x] Ver de activar MKT pago en redes\n+- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n - [ ] Averiguar por rompeolas\n - [ ] Averiguar por streaming de la radio y compartir\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Dibujar parque cerrado con 200m barras\n - [ ] Banderas para marcar (manguear a Javier) y Jose hace estacas\n - [ ] Ver quien limpia los baños y que materiales necesitan\n-- [ ] IG básico con publicidad anque seaPosteo de los puntajes de los campeonatos y volver a ponerlo en el WhatsappSticers a los premios (y pagar pintura)\n+- [ ] Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n+- [ ] Sticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n - [ ] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n-- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n \n \n ## PREPARAR PARA LLEVAR\n \n"}, {"date": 1736179803635, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -38,28 +38,34 @@\n \n ## LUNES\n \n - [x] Ver de activar MKT pago en redes\n+- [x] Ver quien limpia los baños y que materiales necesitan\n+\n+- [ ] Averiguar por rompeolas con Martín\n+- [ ] Averiguar por streaming de la radio y compartir\n+- [ ] Banderas para marcar (manguear a Javier) y Jose hace estacas\n+\n - [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n-- [ ] Averiguar por rompeolas\n-- [ ] Averiguar por streaming de la radio y compartir\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Dibujar parque cerrado con 200m barras\n-- [ ] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n-- [ ] Ver quien limpia los baños y que materiales necesitan\n - [ ] Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n - [ ] Sticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n - [ ] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n \n+## MARTES\n \n+\n+\n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n - [ ] Llevar TV con netbook\n - [ ] Armar llegada agua\n - [ ] Preparar sonidos de largadas y llegadas\n+- [ ] Retirar las banderas por el gimnasio\n \n \n ## SI LLEGO\n \n@@ -73,8 +79,27 @@\n - [ ] Controlar audio, mic y cableados\n - [ ] Ver electricidad para cronometraje\n \n \n+## POST EVENTO\n+\n+- [ ] Devolver banderines a la municipalidad\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Radio en la página\n+\n+\n ## NEWSLETTER\n \n - En todos lados poner los premios de Tac y Nomade\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n@@ -195,22 +220,8 @@\n Total generales madera: 20 grupos de 123 de madera\n Total de categorias: 73 grupos de 123 medalla con calco\n \n \n-## POST EVENTO PARA EL AÑO QUE VIENE\n-\n-- Unificar el reglamento\n-- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n-- Hacer un posteo en redes sociales con la info del año que viene\n-- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n-- Agradecimiento a sponsors\n-  - Posteo\n-  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n-- Conseguir reloj de llegada\n-- Carteles con distancia y aliento en estacas de madera\n-- Radio en la página\n-\n-\n ---\n \n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n"}, {"date": 1736180300370, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -52,12 +52,14 @@\n - [ ] Sticers a los premios (y pagar pintura)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n - [ ] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n \n-## MARTES\n \n+## MIERCOLES\n \n+- [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n \n+\n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n@@ -101,8 +103,9 @@\n \n ## NEWSLETTER\n \n - En todos lados poner los premios de Tac y Nomade\n+- Streaming en vivo: https://americastreaming.com/munidepiedra/\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n - <PERSON>uel<PERSON>, Owa acá y Mari<PERSON>uco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -56,8 +56,9 @@\n \n ## MIERCOLES\n \n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n+- [ ] Averiguar por vallas\n \n \n ## PREPARAR PARA LLEVAR\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -39,26 +39,24 @@\n ## LUNES\n \n - [x] Ver de activar MKT pago en redes\n - [x] Ver quien limpia los baños y que materiales necesitan\n+- [x] Averiguar por rompeolas con Martín\n+- [x] Averiguar por streaming de la radio y compartir\n+- [x] Banderas para marcar (manguear a Javier) y Jose hace estacas\n+- [x] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n \n-- [ ] Averiguar por rompeolas con Martín\n-- [ ] Averiguar por streaming de la radio y compartir\n-- [ ] Banderas para marcar (manguear a Javier) y Jose hace estacas\n-\n - [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n-- [ ] Dibujar parque cerrado con 200m barras\n-- [ ] Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n-- [ ] Sticers a los premios (y pagar pintura)\n-- [ ] Ver de retirar lona para arco y todo para el fondo de podio\n-- [ ] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n \n \n ## MIERCOLES\n \n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n - [ ] Averiguar por vallas\n+- [ ] Pagar pintura premios\n+- [ ] Dibujar parque cerrado con vallas\n+- [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n ## PREPARAR PARA LLEVAR\n \n@@ -106,8 +104,9 @@\n \n - En todos lados poner los premios de Tac y Nomade\n - Streaming en vivo: https://americastreaming.com/munidepiedra/\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n+- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n - Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n - Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n"}, {"date": 1736184515642, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,18 +44,17 @@\n - [x] Averiguar por streaming de la radio y compartir\n - [x] Banderas para marcar (manguear a <PERSON>) y Jose hace estacas\n - [x] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n \n-- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n-- [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n \n-\n ## MIERCOLES\n \n+- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n+- [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n-- [ ] Averiguar por vallas\n+- [ ] Dibujar parque cerrado con vallas\n+- [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n - [ ] Pagar pintura premios\n-- [ ] Dibujar parque cerrado con vallas\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n ## PREPARAR PARA LLEVAR\n"}, {"date": 1736284378232, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,8 +43,9 @@\n - [x] <PERSON><PERSON><PERSON>r por rompeolas con Martín\n - [x] Averiguar por streaming de la radio y compartir\n - [x] Banderas para marcar (manguear a Javier) y Jose hace estacas\n - [x] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n+- [x] Pagar pintura premios\n \n \n ## MIERCOLES\n \n@@ -52,9 +53,8 @@\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n - [ ] Dibujar parque cerrado con vallas\n - [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n-- [ ] Pagar pintura premios\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n ## PREPARAR PARA LLEVAR\n@@ -100,8 +100,9 @@\n \n \n ## NEWSLETTER\n \n+- Pre inscripto para que no se duerman\n - En todos lados poner los premios de Tac y Nomade\n - Streaming en vivo: https://americastreaming.com/munidepiedra/\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n - Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n"}, {"date": 1736285045915, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -156,9 +156,9 @@\n 🗓️ *Viernes 17 de Enero*\n \n ⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n \n-🗓️ *Sábado 16 de Enero*\n+🗓️ *Sábado 18 de Enero*\n \n ⏰ 08:00 a 12:00 - Acreditaciones\n ⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n ⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n@@ -173,9 +173,9 @@\n ⏰ 15:30 - Largada Aguas Abiertas  1500m\n \n ⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n \n-⏰ 20:00 - Espectáculos Musicales\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n \n 🗓️ *Domingo 19 de Enero*\n \n ⏰ 07:00 a 09:00 - Acreditaciones\n"}, {"date": 1736285069711, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -52,9 +52,8 @@\n - [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n - [ ] Dibujar parque cerrado con vallas\n-- [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n - [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n ## PREPARAR PARA LLEVAR\n@@ -70,8 +69,9 @@\n ## SI LLEGO\n \n - [ ] Actualizar imagen de cronograma\n - [ ] Ver streaming\n+- [ ] <PERSON>rig<PERSON>r por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n \n \n ## EN PIEDRA\n \n"}, {"date": 1736286807839, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,17 +48,16 @@\n \n \n ## MIERCOLES\n \n-- [ ] Si Bauti tiene 2.5M (me da que recaudó 3430000 menos 10%), que me lo transfiera para pagar Rosita y ajustar algo de las cajas\n - [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n - [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n - [ ] Dibujar parque cerrado con vallas\n-- [ ] Ver de retirar lona para arco y todo para el fondo de podio\n \n \n ## PREPARAR PARA LLEVAR\n \n+- [ ] Bauti retira efectivo\n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n - [ ] Llevar TV con netbook\n - [ ] Armar llegada agua\n@@ -82,8 +81,9 @@\n \n ## POST EVENTO\n \n - [ ] Devolver banderines a la municipalidad\n+- [ ] Equiparar gastos/ganancias\n \n \n ## POST EVENTO PARA EL AÑO QUE VIENE\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,225 @@\n+# ACUATLON\n+\n+## SQL TOTALES\n+\n+- Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n+\n+*Cantidades de personas*\n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas\n+\n+*Cantidades de inscripciones en multiples carreras*\n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+*Carreras de inscripciones en multiples carreras*\n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n+ORDER BY idinscripcion\n+\n+*Pagos a Bauti*\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n+\n+Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n+\n+\n+## PREPARAR PARA LLEVAR\n+\n+- [ ] Bauti retira efectivo\n+- [ ] Reader y chips de Gaby\n+- [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n+- [ ] Llevar TV con netbook\n+- [ ] Armar llegada agua\n+- [ ] Preparar sonidos de largadas y llegadas\n+- [ ] Retirar las banderas por el gimnasio\n+\n+\n+## SI LLEGO\n+\n+- [ ] Actualizar imagen de cronograma\n+- [ ] Ver streaming\n+- [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n+\n+\n+## EN PIEDRA\n+\n+- [ ] Armar kits\n+- [ ] Controlar audio, mic y cableados\n+- [ ] Ver electricidad para cronometraje\n+\n+\n+## POST EVENTO\n+\n+- [ ] Devolver banderines a la municipalidad\n+- [ ] Equiparar gastos/ganancias\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Radio en la página\n+\n+\n+## NEWSLETTER\n+\n+- Pre inscripto para que no se duerman\n+- En todos lados poner los premios de Tac y Nomade\n+- Streaming en vivo: https://americastreaming.com/munidepiedra/\n+- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n+- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n+- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n+- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n+- Varias frases para Whatsapp y Redes\n+- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n+- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n+- Juntate con tu amigo nadador y corre un Acuatlón en postas\n+\n+\n+## ACREDITACION CHICAS Y LISTAS\n+\n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n+- Colo bolsa compostable (calcular costo)\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios\n+- Brollet de frutas\n+\n+\n+---\n+\n+## BOYAS\n+\n+Ubicación GPS de las 6 boyas rojas:\n+\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n+\n+\n+## CRONOGRAMA Y UBICACIONES\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ *Ubicaciones*\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ *Viernes 17 de Enero*\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ *Sábado 18 de Enero*\n+\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:00 a 18:00 - Acreditaciones\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n+\n+🗓️ *Domingo 19 de Enero*\n+\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n+## CARRERAS PREMIOS\n+\n+- SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n+\n+- Aguas Abiertas 1500\n+  - 14 categorias\n+- Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n+\n+- Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n+- Acuatlón Short\n+  - 3 categorias\n+\n+- Águila Run 5K\n+  - Nada\n+\n+- Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n+- Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n+\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n+\n+\n+---\n+\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n+\n+\n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n"}, {"date": 1736363353975, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -82,266 +82,27 @@\n \n \n ## NEWSLETTER\n \n-- Pre inscripto para que no se duerman\n-- En todos lados poner los premios de Tac y Nomade\n-- Streaming en vivo: https://americastreaming.com/munidepiedra/\n+- [x] Pre inscripto para que no se duerman\n - Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n-- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n - Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n - Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n-- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n-- Varias frases para Whatsapp y Redes\n - Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n-- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n-- Juntate con tu amigo nadador y corre un Acuatlón en postas\n+- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n-## ACREDITACION CHICAS Y LISTAS\n+## WHATSAPP\n \n-- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n-- Colo bolsa compostable (calcular costo)\n-- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n-- Listado de staff, almuerzos y albergues\n-- Venta de remeras 8 o 2x15\n-- Entrega de premios\n-- Brollet de frutas\n+- En todos lados poner los premios de Tac y Nomade\n+- Streaming en vivo: https://americastreaming.com/munidepiedra/\n+- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n \n \n----\n \n-## BOYAS\n \n-Ubicación GPS de las 6 boyas rojas:\n \n-- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n-- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n-- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n-- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n-- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n-- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n-\n-\n-## CRONOGRAMA Y UBICACIONES\n-\n-Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n-\n-🗺️ *Ubicaciones*\n-\n-📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n-\n-📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n-\n-📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n-\n-🗓️ *Viernes 17 de Enero*\n-\n-⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n-\n-🗓️ *Sábado 18 de Enero*\n-\n-⏰ 08:00 a 12:00 - Acreditaciones\n-⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n-⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n-\n-⏰ 12:50 - Charla técnica Acuatlón Kids\n-⏰ 13:00 - Largada Acuatlón Kids\n-\n-⏰ 14:00 a 18:00 - Acreditaciones\n-⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n-⏰ 15:00 - Largada Aguas Abiertas 4000m\n-⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n-⏰ 15:30 - Largada Aguas Abiertas  1500m\n-\n-⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n-\n-⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n-\n-🗓️ *Domingo 19 de Enero*\n-\n-⏰ 07:00 a 09:00 - Acreditaciones\n-⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n-⏰ 09:45 - Largada Acuatlón Short\n-⏰ 09:50 - Charla técnica para Águila Run\n-⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n-\n-⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n-⏰ 14:00 - Cierre del evento\n-\n-\n-## CARRERAS PREMIOS\n-\n-- SwimRun Individual 10K\n-  - 2 generales\n-- SwimRun Dupla 10K\n-  - 3 generales\n-\n-- Aguas Abiertas 1500\n-  - 14 categorias\n-- Aguas Abiertas 4000\n-  - 3 generales\n-  - 14 categorias\n-\n-- Acuatlón Olímpico\n-  - 3 generales\n-  - 14 categorias\n-- Acuatlón Posta\n-  - 3 generales\n-- Acuatlón Short\n-  - 3 categorias\n-\n-- Águila Run 5K\n-  - Nada\n-\n-- Águila Run 10K\n-  - 3 generales\n-  - 14 categorias\n-\n-- Águila Run 21K\n-  - 3 generales\n-  - 14 categorias\n-\n-Total generales madera: 20 grupos de 123 de madera\n-Total de categorias: 73 grupos de 123 medalla con calco\n-\n-\n----\n-\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n-\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n-\n-Equipo posta:\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n-\n-\n-sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n-# ACUATLON\n-\n-## SQL TOTALES\n-\n-- Armar consultas para generar las cajas\n-- Armar consultas para ver cuánta gente hay\n-\n-*Cantidades de personas*\n-SELECT\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas\n-\n-*Cantidades de inscripciones en multiples carreras*\n-SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n-idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n-GROUP BY idinscripcion\n-ORDER BY cantidad DESC\n-\n-*Carreras de inscripciones en multiples carreras*\n-SELECT categorias.nombre AS cat, idinscripcion\n-FROM categoriasxparticipantes\n-LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n-WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n-ORDER BY idinscripcion\n-\n-*Pagos a Bauti*\n-SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n-FROM pagos\n-LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n-LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n-WHERE pagos.idevento = 2193\n-ORDER BY precios.precio;\n-\n-\n-Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n-\n-\n-## LUNES\n-\n-- [x] Ver de activar MKT pago en redes\n-- [x] Ver quien limpia los baños y que materiales necesitan\n-- [x] Averiguar por rompeolas con Martín\n-- [x] Averiguar por streaming de la radio y compartir\n-- [x] Banderas para marcar (manguear a Javier) y Jose hace estacas\n-- [x] Pasar datos de consultas SQL y cajas a Andres y contar pago de Bauti\n-- [x] Pagar pintura premios\n-\n-\n-## MIERCOLES\n-\n-- [ ] Pedir cotización (Andres + Vivi) Enviamos por mail\n-- [ ] Calcos para los premios de 5cm x 6cm (o 4x6)\n-- [ ] Dibujar parque cerrado con vallas\n-\n-\n-## PREPARAR PARA LLEVAR\n-\n-- [ ] Bauti retira efectivo\n-- [ ] Reader y chips de Gaby\n-- [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n-- [ ] Llevar TV con netbook\n-- [ ] Armar llegada agua\n-- [ ] Preparar sonidos de largadas y llegadas\n-- [ ] Retirar las banderas por el gimnasio\n-\n-\n-## SI LLEGO\n-\n-- [ ] Actualizar imagen de cronograma\n-- [ ] Ver streaming\n-- [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n-\n-\n-## EN PIEDRA\n-\n-- [ ] Armar kits\n-- [ ] Controlar audio, mic y cableados\n-- [ ] Ver electricidad para cronometraje\n-\n-\n-## POST EVENTO\n-\n-- [ ] Devolver banderines a la municipalidad\n-- [ ] Equiparar gastos/ganancias\n-\n-\n-## POST EVENTO PARA EL AÑO QUE VIENE\n-\n-- Unificar el reglamento\n-- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n-- Hacer un posteo en redes sociales con la info del año que viene\n-- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n-- Agradecimiento a sponsors\n-  - Posteo\n-  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n-- Conseguir reloj de llegada\n-- Carteles con distancia y aliento en estacas de madera\n-- Radio en la página\n-\n-\n-## NEWSLETTER\n-\n-- Pre inscripto para que no se duerman\n-- En todos lados poner los premios de Tac y Nomade\n-- Streaming en vivo: https://americastreaming.com/munidepiedra/\n-- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n-- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n-- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n-- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n-- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n-- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos) Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n-- Varias frases para Whatsapp y Redes\n-- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n-- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público)\n-- Juntate con tu amigo nadador y corre un Acuatlón en postas\n-\n-\n ## ACREDITACION CHICAS Y LISTAS\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Colo bolsa compostable (calcular costo)\n"}, {"date": 1736382211127, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -83,13 +83,14 @@\n \n ## NEWSLETTER\n \n - [x] Pre inscripto para que no se duerman\n-- Corredores Lolog + zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n-- Puelo, Owa acá y Marimenuco (primer evento de aguas abiertas)\n-- Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n-- Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n-- Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n+- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- [x] Corredores Lolog\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n+- [ ] Corredores zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n+- [ ] Puelo, Owa acá\n+- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n - Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n ## WHATSAPP\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,15 +37,14 @@\n \n \n ## PREPARAR PARA LLEVAR\n \n-- [ ] Bauti retira efectivo\n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n - [ ] Llevar TV con netbook\n-- [ ] Armar llegada agua\n+- [ ] Armar llegada agua (llevar sogas, baldes y bidones)\n - [ ] Preparar sonidos de largadas y llegadas\n-- [ ] Retirar las banderas por el gimnasio\n+- [ ] Configurar puntos de los campeonatos\n \n \n ## SI LLEGO\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -106,8 +106,9 @@\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Colo bolsa compostable (calcular costo)\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de pagos y cajas\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n - Entrega de premios\n - Brollet de frutas\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,18 @@\n \n \n Pagos en efectivo según caja, Pa<PERSON> con MP, Calcular valor por cantidad de carreras\n \n+## LISTAS PARA MAILS\n \n+> Nadadores Aguas Abiertas de Neuquén y alrededores\n+\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%'\n+GROUP BY mail;\n+\n+\n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -43,9 +43,18 @@\n WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%'\n GROUP BY mail;\n \n+> Nadadores Aguas Abiertas de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN ( AND user_id IN (3, 45, 88, 107)) AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n \n+> Corredores de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' GROUP BY mail;\n+\n+> Corredores de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n+\n+\n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -53,8 +53,11 @@\n > Corredores de Bariloche y alrededores\n SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n \n \n+\n+\n+\n ## PREPARAR PARA LLEVAR\n \n - [ ] Reader y chips de Gaby\n - [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n@@ -102,15 +105,21 @@\n \n - [x] Pre inscripto para que no se duerman\n - [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - [x] Corredores Lolog\n-- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que tanto te gusta\n-- [ ] Corredores zapala o alguna carrera ahora cerca (te quedaste con ganas de correr, se viene la próxima)\n-- [ ] Puelo, Owa acá\n-- [ ] Mail del campeonato a los inscriptos de los eventos de Santi. (Los que están inscriptos en alguno de los 3 eventos que no pierdan los puntos de los 3 eventos)\n-- Mail a runners de Bariloche, Neuquén y Cipolletti. Ya llega el primer running de Piedra del Águila, muy cerca de tu casa. (Los que están inscriptos en alguna de las carreras de running). ¡Nunca corriste en Piedra del Águila! El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n+- [ ] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [ ] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n \n+- [ ] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [ ] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- ¿Qué carreras hay? Se viene el Acuatlón Fest 2025 en Piedra del Águila donde vamos a tener todas estas carreras\n+- ¿Qué premios hay? Remera y gorra de natación de la mejor calidad, medallas, trofeos, un kit super completo, trajes y elementos de swimran y natación para los ganadores y para sortear, carreras gratis para tus hijos, carritos de comida, lago, sombra y el jardín hermoso para disfrutar toda la tarde, fiesta con bandas en vivo y mucho más.\n+- El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n+\n+\n ## WHATSAPP\n \n - En todos lados poner los premios de Tac y Nomade\n - Streaming en vivo: https://americastreaming.com/munidepiedra/\n"}, {"date": 1736607599842, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -114,9 +114,13 @@\n - [ ] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n - [ ] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n \n - ¿Qué carreras hay? Se viene el Acuatlón Fest 2025 en Piedra del Águila donde vamos a tener todas estas carreras\n+- ¿Cuándo es? botón a la página\n+- ¿Cuánto sale? bot whatsapp\n - ¿Qué premios hay? Remera y gorra de natación de la mejor calidad, medallas, trofeos, un kit super completo, trajes y elementos de swimran y natación para los ganadores y para sortear, carreras gratis para tus hijos, carritos de comida, lago, sombra y el jardín hermoso para disfrutar toda la tarde, fiesta con bandas en vivo y mucho más.\n+- ¿Querés saber más? botón al grupo\n+\n - El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n \n ## WHATSAPP\n"}, {"date": 1736633676811, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,9 +44,11 @@\n AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%'\n GROUP BY mail;\n \n > Nadadores Aguas Abiertas de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN ( AND user_id IN (3, 45, 88, 107)) AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n \n > Corredores de Neuquén y alrededores\n SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' GROUP BY mail;\n \n"}, {"date": 1736688029150, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -37,24 +37,30 @@\n \n ## LISTAS PARA MAILS\n \n > Nadadores Aguas Abiertas de Neuquén y alrededores\n-\n SELECT nombre, mail, localidad FROM `participantes`\n WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%'\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n GROUP BY mail;\n \n > Nadadores Aguas Abiertas de Bariloche y alrededores\n SELECT nombre, mail, localidad FROM `participantes`\n WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n+GROUP BY mail;\n \n > Corredores de Neuquén y alrededores\n-SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' GROUP BY mail;\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n+GROUP BY mail;\n \n > Corredores de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes` WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5) AND localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' GROUP BY mail;\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n+GROUP BY mail;\n \n \n \n \n@@ -108,24 +114,17 @@\n - [x] Pre inscripto para que no se duerman\n - [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n - [x] Corredores Lolog\n \n-- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n-- [ ] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n-- [ ] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n+- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n \n-- [ ] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n-- [ ] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n \n-- ¿Qué carreras hay? Se viene el Acuatlón Fest 2025 en Piedra del Águila donde vamos a tener todas estas carreras\n-- ¿Cuándo es? botón a la página\n-- ¿Cuánto sale? bot whatsapp\n-- ¿Qué premios hay? Remera y gorra de natación de la mejor calidad, medallas, trofeos, un kit super completo, trajes y elementos de swimran y natación para los ganadores y para sortear, carreras gratis para tus hijos, carritos de comida, lago, sombra y el jardín hermoso para disfrutar toda la tarde, fiesta con bandas en vivo y mucho más.\n-- ¿Querés saber más? botón al grupo\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n \n-- El nuevo running muy cerca de tu casa en el Valle / La Cordillera (sólo si pueden segmentar por localidad, que no creo por va a ser poco público) Juntate con tu amigo nadador y corre un Acuatlón en postas\n \n-\n ## WHATSAPP\n \n - En todos lados poner los premios de Tac y Nomade\n - Streaming en vivo: https://americastreaming.com/munidepiedra/\n"}, {"date": 1736880896266, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -72,8 +72,9 @@\n - [ ] Llevar TV con netbook\n - [ ] Armar llegada agua (llevar sogas, baldes y bidones)\n - [ ] Preparar sonidos de largadas y llegadas\n - [ ] Configurar puntos de los campeonatos\n+- [ ] Handies cargados\n \n \n ## SI LLEGO\n \n"}, {"date": 1736890735663, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -77,10 +77,8 @@\n \n \n ## SI LLEGO\n \n-- [ ] Actualizar imagen de cronograma\n-- [ ] Ver streaming\n - [ ] Averiguar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n \n \n ## EN PIEDRA\n@@ -106,9 +104,11 @@\n   - Posteo\n   - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n - Conseguir reloj de llegada\n - Carteles con distancia y aliento en estacas de madera\n-- Radio en la página\n+- Poner la Radio y el Streaming en la página\n+- Hacer streaming\n+- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n \n \n ## NEWSLETTER\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,37 +62,14 @@\n GROUP BY mail;\n \n \n \n-\n-\n-## PREPARAR PARA LLEVAR\n-\n-- [ ] Reader y chips de Gaby\n-- [ ] GoPro cargada y probar que grabe varias horas con el powerbank\n-- [ ] Llevar TV con netbook\n-- [ ] Armar llegada agua (llevar sogas, baldes y bidones)\n-- [ ] Preparar sonidos de largadas y llegadas\n-- [ ] Configurar puntos de los campeonatos\n-- [ ] Handies cargados\n-\n-\n-## SI LLEGO\n-\n-- [ ] Ave<PERSON>uar por vallas (dibujar parque cerrado y ver cuánto sería lo mínimo, después buscar precios)\n-\n-\n-## EN PIEDRA\n-\n-- [ ] Armar kits\n-- [ ] Controlar audio, mic y cableados\n-- [ ] Ver electricidad para cronometraje\n-\n-\n ## POST EVENTO\n \n+- [ ] Pagar seguro\n - [ ] Devolver banderines a la municipalidad\n - [ ] Equiparar gastos/ganancias\n+- [ ] Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n \n \n ## POST EVENTO PARA EL AÑO QUE VIENE\n \n@@ -124,24 +101,13 @@\n \n - [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n \n \n-## WHATSAPP\n-\n-- En todos lados poner los premios de Tac y Nomade\n-- Streaming en vivo: https://americastreaming.com/munidepiedra/\n-- Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n-\n-\n-\n-\n-\n ## ACREDITACION CHICAS Y LISTAS\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n-- Colo bolsa compostable (calcular costo)\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n-- Listado de pagos y cajas\n+-\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n - Entrega de premios\n - Brollet de frutas\n"}, {"date": 1736898556915, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -105,12 +105,11 @@\n ## ACREDITACION CHICAS Y LISTAS\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n--\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n-- Entrega de premios\n+- Entrega de premios (ordenar y poner cuántos premios se entregan)\n - Brollet de frutas\n \n \n ---\n"}, {"date": 1736978751788, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -101,18 +101,17 @@\n \n - [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n \n \n-## ACREDITACION CHICAS Y LISTAS\n+## DOCUMENTO PARA STAFF\n \n - Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n - Remeras staff recuperarlas (prioridad los que salen en las fotos)\n - Listado de staff, almuerzos y albergues\n - Venta de remeras 8 o 2x15\n - Entrega de premios (ordenar y poner cuántos premios se entregan)\n-- Brollet de frutas\n+- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n \n-\n ---\n \n ## BOYAS\n \n"}, {"date": 1737063879570, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -67,9 +67,9 @@\n \n - [ ] Pagar seguro\n - [ ] Devolver banderines a la municipalidad\n - [ ] Equiparar gastos/ganancias\n-- [ ] Posteo de los puntajes de los campeonatos y volver a ponerlo en el Whatsapp\n+- [ ] Posteo de los puntajes de los campeonatos, cargarlo en la carrera de Santi (compartir a Chile) y volver a ponerlo en el Whatsapp\n \n \n ## POST EVENTO PARA EL AÑO QUE VIENE\n \n"}, {"date": 1737063894024, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -73,8 +73,9 @@\n \n ## POST EVENTO PARA EL AÑO QUE VIENE\n \n - Unificar el reglamento\n+- Escribir manual de procesos completo\n - Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n - Hacer un posteo en redes sociales con la info del año que viene\n - Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n - Agradecimiento a sponsors\n"}, {"date": 1737381695883, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,16 +62,8 @@\n GROUP BY mail;\n \n \n \n-## POST EVENTO\n-\n-- [ ] Pagar seguro\n-- [ ] Devolver banderines a la municipalidad\n-- [ ] Equiparar gastos/ganancias\n-- [ ] Posteo de los puntajes de los campeonatos, cargarlo en la carrera de Santi (compartir a Chile) y volver a ponerlo en el Whatsapp\n-\n-\n ## POST EVENTO PARA EL AÑO QUE VIENE\n \n - Unificar el reglamento\n - Escribir manual de procesos completo\n"}, {"date": 1737729777132, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -217,4 +217,38 @@\n https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n \n \n sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n+\n+## FEEDBACK E IDEAS PARA 2026\n+\n+### OBJETIVO\n+\n+- El primer objetivo fue divertirnos, creo que ambos 2 lo logramos, aunque no tanto Juli y la Colo\n+- El segundo objetivo fue probar el concepto y aprender, creo que fue totalmente logrado\n+- El tercer objetivo fue salir hecho, lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n+\n+### FEEDBACK\n+\n+Los problemas que veo son:\n+\n+- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n+- No había suficiente alojamiento y mucha gente vino sólo por el día.\n+- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n+- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó complicaciones.\n+\n+Mi propuesta para el año que viene:\n+\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana)\n+- Modificar los circuitos, incluyendo SwimRun con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada (la de Acuatlón de este año) y una misma llegada\n+- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n+- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n+- El viernes reunión con todo el personal por grupos de responsabilidades\n+- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n+- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs\n+- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo)\n+- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n+- Con 100m de vallas alcanza\n+- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n+- Las chicas con más personal de ayuda y mejor asesoradas\n+- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n"}, {"date": 1737729866827, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -251,4 +251,5 @@\n - Con 100m de vallas alcanza\n - Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n - Las chicas con más personal de ayuda y mejor asesoradas\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n+- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n"}, {"date": 1737730021534, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -252,4 +252,5 @@\n - Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n - Las chicas con más personal de ayuda y mejor asesoradas\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n - Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n+- Carteles con indicación de distancia y bifurcaciones (con aliento)\n"}, {"date": 1737730072393, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -253,4 +253,5 @@\n - Las chicas con más personal de ayuda y mejor asesoradas\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n - Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n - Carteles con indicación de distancia y bifurcaciones (con aliento)\n+- No ofrecer 21K, sólo 10K y 5K\n"}, {"date": 1737730570575, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -235,8 +235,9 @@\n - Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n - No había suficiente alojamiento y mucha gente vino sólo por el día.\n - Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n - Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó complicaciones.\n+- No se si la fecha fue la correcta. Si se que el lugar lo fue para el experimento que queríamos hacer.\n \n Mi propuesta para el año que viene:\n \n - Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana)\n"}, {"date": 1737732983356, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -239,10 +239,10 @@\n - No se si la fecha fue la correcta. Si se que el lugar lo fue para el experimento que queríamos hacer.\n \n Mi propuesta para el año que viene:\n \n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana)\n-- Modificar los circuitos, incluyendo SwimRun con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada (la de Acuatlón de este año) y una misma llegada\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n+- Modificar los circuitos, incluyendo SwimRun con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n - Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n - Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n - El viernes reunión con todo el personal por grupos de responsabilidades\n - El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n"}, {"date": 1737732994957, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -240,9 +240,9 @@\n \n Mi propuesta para el año que viene:\n \n - Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n-- Modificar los circuitos, incluyendo SwimRun con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n+- Modificar los circuitos, incluyendo SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n - Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n - Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n - El viernes reunión con todo el personal por grupos de responsabilidades\n - El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n"}, {"date": 1737733066251, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -254,5 +254,5 @@\n - Las chicas con más personal de ayuda y mejor asesoradas\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n - Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n - Carteles con indicación de distancia y bifurcaciones (con aliento)\n-- No ofrecer 21K, sólo 10K y 5K\n+- No ofrecer running de 21K, sólo 10K y 5K\n"}, {"date": 1737734145372, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,283 @@\n+# ACUATLON\n+\n+## SQL TOTALES\n+\n+- Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n+\n+*Acreditados sin tiempos*\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'acreditado'\n+AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+*Inscriptos con tiempos*\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'inscripto'\n+AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+\n+*Cantidades de personas*\n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n+\n+*Cantidades de inscripciones en multiples carreras*\n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+*Carreras de inscripciones en multiples carreras*\n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n+ORDER BY idinscripcion\n+\n+*Pagos a Bauti*\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n+\n+Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n+\n+## LISTAS PARA MAILS\n+\n+> Nadadores Aguas Abiertas de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n+GROUP BY mail;\n+\n+> Nadadores Aguas Abiertas de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n+GROUP BY mail;\n+\n+> Corredores de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n+GROUP BY mail;\n+\n+> Corredores de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n+GROUP BY mail;\n+\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Escribir manual de procesos completo\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Poner la Radio y el Streaming en la página\n+- Hacer streaming\n+- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n+\n+\n+## NEWSLETTER\n+\n+- [x] Pre inscripto para que no se duerman\n+- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- [x] Corredores Lolog\n+\n+- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n+\n+\n+## DOCUMENTO PARA STAFF\n+\n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios (ordenar y poner cuántos premios se entregan)\n+- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n+\n+---\n+\n+## BOYAS\n+\n+Ubicación GPS de las 6 boyas rojas:\n+\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n+\n+\n+## CRONOGRAMA Y UBICACIONES\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ *Ubicaciones*\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ *Viernes 17 de Enero*\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ *Sábado 18 de Enero*\n+\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:00 a 18:00 - Acreditaciones\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n+\n+🗓️ *Domingo 19 de Enero*\n+\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n+## CARRERAS PREMIOS\n+\n+- SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n+\n+- Aguas Abiertas 1500\n+  - 14 categorias\n+- Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n+\n+- Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n+- Acuatlón Short\n+  - 3 categorias\n+\n+- Águila Run 5K\n+  - Nada\n+\n+- Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n+- Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n+\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n+\n+\n+---\n+\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n+\n+\n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n+\n+## FEEDBACK 2025 E IDEAS PARA 2026\n+\n+### CANTIDADES\n+\n+*Cantidades de personas*\n+Participantes Individuales: 113\n+Participantes Equipos: 18\n+Personas Diferentes: 149\n+\n+*Cantidades de inscripciones en multiples carreras*\n+En 3 carreras: 1 (sólo una persona)\n+En 2 carreras: 9 (sólo 6% de los inscriptos)\n+\n+### OBJETIVOS\n+\n+- El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n+- El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n+- El tercer objetivo fue *no perder dinero*. Lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n+\n+### FEEDBACK\n+\n+Los problemas que veo son:\n+\n+- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n+- No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.\n+- Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.\n+- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.\n+- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n+- No sé si la fecha fue la correcta. Si sé que el lugar lo fue, para la prueba de concepto que queríamos hacer.\n+\n+Mi propuesta para el año que viene:\n+\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n+- Modificar los circuitos, incluyendo SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n+- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n+- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n+- El viernes reunión con todo el personal por grupos de responsabilidades\n+- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n+- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs\n+- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo)\n+- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n+- Con 100m de vallas alcanza\n+- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n+- Las chicas con más personal de ayuda y mejor asesoradas\n+- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n+- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n+- Carteles con indicación de distancia y bifurcaciones (con aliento)\n+- No ofrecer running de 21K, sólo 10K y 5K\n"}, {"date": 1737735340291, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,9 +33,9 @@\n *Carreras de inscripciones en multiples carreras*\n SELECT categorias.nombre AS cat, idinscripcion\n FROM categoriasxparticipantes\n LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n-WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n+WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n ORDER BY idinscripcion\n \n *Pagos a Bauti*\n SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n@@ -234,19 +234,47 @@\n \n \n ## FEEDBACK 2025 E IDEAS PARA 2026\n \n-### CANTIDADES\n+### ESTADÍSTICAS\n \n *Cantidades de personas*\n-Participantes Individuales: 113\n-Participantes Equipos: 18\n-Personas Diferentes: 149\n \n-*Cantidades de inscripciones en multiples carreras*\n-En 3 carreras: 1 (sólo una persona)\n-En 2 carreras: 9 (sólo 6% de los inscriptos)\n+- Personas Diferentes: 149\n+- Acreditados Individuales: 113\n+- Acreditados en Equipos: 18\n+- Acreditados en Acuatlón Olímpico: 23\n+- Acreditados en Acuatlón Posta: 15\n+- Acreditados en Acuatlón Short: 14\n+- Acreditados en Aguas Abiertas 1500: 27\n+- Acreditados en Aguas Abiertas 4000: 29\n+- Acreditados en SwimRun Dupla 10K: 3\n+- Acreditados en SwimRun Individual 10K: 10\n+- Acreditados en Águila Run 10K: 12\n+- Acreditados en Águila Run 5K: 9\n+- Inscriptos en Águila Run 21K: 3 (los pasamos a 10K)\n+- Acreditados en 3 carreras: 1 (sólo una persona)\n+- Acreditados en 2 carreras: 9 (sólo 6% de los inscriptos)\n+- Inscriptos sin acreditarse (faltaron): 9 (6% del total)\n+- Preinscriptos (no pagaron): 51 (34% de los acreditados)\n \n+*Conclusiones*\n+\n+- Tuvimos casi 150 personas\n+- Todos los que combinaron carreras hicieron Acuatlón\n+- 3 combinaron junto con SwimRun\n+- 5 combinaron junto con Aguas Abiertas 4000m\n+- 1 combinaron junto con Aguas Abiertas 1500m\n+- Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar\n+- Ninguno combinó Running, son públicos diferentes\n+- Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar\n+- Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo\n+- Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta\n+- SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n+- Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n+- Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n+\n+\n ### OBJETIVOS\n \n - El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n - El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n@@ -260,282 +288,26 @@\n - No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.\n - Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.\n - Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.\n - Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n-- No sé si la fecha fue la correcta. Si sé que el lugar lo fue, para la prueba de concepto que queríamos hacer.\n+- No sé si la fecha fue la correcta, hay que analizar otras opciones.\n+- Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.\n \n Mi propuesta para el año que viene:\n \n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n - Modificar los circuitos, incluyendo SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n - Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n-- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n-- El viernes reunión con todo el personal por grupos de responsabilidades\n-- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n-- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs\n-- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo)\n+- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.\n+- El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n+- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n+- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón\n+- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n+- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n - El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n-- Con 100m de vallas alcanza\n-- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n-- Las chicas con más personal de ayuda y mejor asesoradas\n-- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n-- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n-- Carteles con indicación de distancia y bifurcaciones (con aliento)\n-- No ofrecer running de 21K, sólo 10K y 5K\n-# ACUATLON\n-\n-## SQL TOTALES\n-\n-- Armar consultas para generar las cajas\n-- Armar consultas para ver cuánta gente hay\n-\n-*Cantidades de personas*\n-SELECT\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '') AS individuales,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = 'dupla') AS equipos,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'inscripto' AND equipo IN ('', 'participante')) AS personas\n-\n-*Cantidades de inscripciones en multiples carreras*\n-SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n-idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'inscripto' AND equipo = '')\n-GROUP BY idinscripcion\n-ORDER BY cantidad DESC\n-\n-*Carreras de inscripciones en multiples carreras*\n-SELECT categorias.nombre AS cat, idinscripcion\n-FROM categoriasxparticipantes\n-LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n-WHERE idinscripcion IN (942722, 971931, 990652, 1022249, 961954, 1023832, 957888, 1026299)\n-ORDER BY idinscripcion\n-\n-*Pagos a Bauti*\n-SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n-FROM pagos\n-LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n-LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n-WHERE pagos.idevento = 2193\n-ORDER BY precios.precio;\n-\n-\n-Pagos en efectivo según caja, Pagos con MP, Calcular valor por cantidad de carreras\n-\n-## LISTAS PARA MAILS\n-\n-> Nadadores Aguas Abiertas de Neuquén y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n-GROUP BY mail;\n-\n-> Nadadores Aguas Abiertas de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n-GROUP BY mail;\n-\n-> Corredores de Neuquén y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n-AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n-GROUP BY mail;\n-\n-> Corredores de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n-AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n-GROUP BY mail;\n-\n-\n-\n-## POST EVENTO PARA EL AÑO QUE VIENE\n-\n-- Unificar el reglamento\n-- Escribir manual de procesos completo\n-- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n-- Hacer un posteo en redes sociales con la info del año que viene\n-- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n-- Agradecimiento a sponsors\n-  - Posteo\n-  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n-- Conseguir reloj de llegada\n-- Carteles con distancia y aliento en estacas de madera\n-- Poner la Radio y el Streaming en la página\n-- Hacer streaming\n-- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n-\n-\n-## NEWSLETTER\n-\n-- [x] Pre inscripto para que no se duerman\n-- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n-- [x] Corredores Lolog\n-\n-- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n-- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n-\n-- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n-- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n-\n-- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n-\n-\n-## DOCUMENTO PARA STAFF\n-\n-- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n-- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n-- Listado de staff, almuerzos y albergues\n-- Venta de remeras 8 o 2x15\n-- Entrega de premios (ordenar y poner cuántos premios se entregan)\n-- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n-\n----\n-\n-## BOYAS\n-\n-Ubicación GPS de las 6 boyas rojas:\n-\n-- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n-- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n-- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n-- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n-- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n-- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n-\n-\n-## CRONOGRAMA Y UBICACIONES\n-\n-Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n-\n-🗺️ *Ubicaciones*\n-\n-📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n-\n-📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n-\n-📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n-\n-🗓️ *Viernes 17 de Enero*\n-\n-⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n-\n-🗓️ *Sábado 18 de Enero*\n-\n-⏰ 08:00 a 12:00 - Acreditaciones\n-⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n-⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n-\n-⏰ 12:50 - Charla técnica Acuatlón Kids\n-⏰ 13:00 - Largada Acuatlón Kids\n-\n-⏰ 14:00 a 18:00 - Acreditaciones\n-⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n-⏰ 15:00 - Largada Aguas Abiertas 4000m\n-⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n-⏰ 15:30 - Largada Aguas Abiertas  1500m\n-\n-⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n-\n-⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n-\n-🗓️ *Domingo 19 de Enero*\n-\n-⏰ 07:00 a 09:00 - Acreditaciones\n-⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n-⏰ 09:45 - Largada Acuatlón Short\n-⏰ 09:50 - Charla técnica para Águila Run\n-⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n-\n-⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n-⏰ 14:00 - Cierre del evento\n-\n-\n-## CARRERAS PREMIOS\n-\n-- SwimRun Individual 10K\n-  - 2 generales\n-- SwimRun Dupla 10K\n-  - 3 generales\n-\n-- Aguas Abiertas 1500\n-  - 14 categorias\n-- Aguas Abiertas 4000\n-  - 3 generales\n-  - 14 categorias\n-\n-- Acuatlón Olímpico\n-  - 3 generales\n-  - 14 categorias\n-- Acuatlón Posta\n-  - 3 generales\n-- Acuatlón Short\n-  - 3 categorias\n-\n-- Águila Run 5K\n-  - Nada\n-\n-- Águila Run 10K\n-  - 3 generales\n-  - 14 categorias\n-\n-- Águila Run 21K\n-  - 3 generales\n-  - 14 categorias\n-\n-Total generales madera: 20 grupos de 123 de madera\n-Total de categorias: 73 grupos de 123 medalla con calco\n-\n-\n----\n-\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n-\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n-\n-Equipo posta:\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n-\n-\n-sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n-\n-\n-## FEEDBACK E IDEAS PARA 2026\n-\n-### OBJETIVO\n-\n-- El primer objetivo fue divertirnos, creo que ambos 2 lo logramos, aunque no tanto Juli y la Colo\n-- El segundo objetivo fue probar el concepto y aprender, creo que fue totalmente logrado\n-- El tercer objetivo fue salir hecho, lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n-\n-### FEEDBACK\n-\n-Los problemas que veo son:\n-\n-- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n-- No había suficiente alojamiento y mucha gente vino sólo por el día.\n-- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n-- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó complicaciones.\n-- No se si la fecha fue la correcta. Si se que el lugar lo fue para el experimento que queríamos hacer.\n-\n-Mi propuesta para el año que viene:\n-\n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n-- Modificar los circuitos, incluyendo SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n-- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n-- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche y viajamos de vuelta tranquilos el domingo.\n-- El viernes reunión con todo el personal por grupos de responsabilidades\n-- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 hacemos todo el evento de un tirón\n-- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs\n-- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo)\n-- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n-- Con 100m de vallas alcanza\n-- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector\n-- Las chicas con más personal de ayuda y mejor asesoradas\n-- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes\n-- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8\n-- Carteles con indicación de distancia y bifurcaciones (con aliento)\n-- No ofrecer running de 21K, sólo 10K y 5K\n+- Con 100m de vallas alcanza.\n+- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n+- Las chicas con más personal de ayuda y mejor asesoradas.\n+- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n+- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n+- Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n+- No ofrecer running de 21K, sólo 10K y 5K.\n"}, {"date": 1737735896411, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -272,9 +272,18 @@\n - SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n - Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n - Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n \n+*Cajas*\n \n+- MercadoPago: 140 (hubo algunos pagos de 50%)\n+- Oceano Virgen: 17\n+- Aquiles: 5\n+- Meseta Wear: 1\n+- Andy: 12\n+- Andre<PERSON>: 3\n+\n+\n ### OBJETIVOS\n \n - El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n - El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n@@ -293,16 +302,21 @@\n - Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.\n \n Mi propuesta para el año que viene:\n \n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde)\n-- Modificar los circuitos, incluyendo SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada\n-- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n+*Para el cronograma*\n+\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde).\n+- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n+- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón.\n+- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n - Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.\n - El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n-- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n-- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón\n-- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n+\n+*Para los circuitos*\n+\n+- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n+- Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.\n - No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n - El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n - Con 100m de vallas alcanza.\n - Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n@@ -310,4 +324,5 @@\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n - Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n - Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n - No ofrecer running de 21K, sólo 10K y 5K.\n+- Los circuitos de natación fueron correctos, los dejaría igual.\n"}, {"date": 1737735928545, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -274,9 +274,9 @@\n - Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n \n *Cajas*\n \n-- MercadoPago: 140 (hubo algunos pagos de 50%)\n+- MercadoPago: 120 (hubo algunos pagos de 50%)\n - Oceano Virgen: 17\n - <PERSON>quiles: 5\n - Meseta Wear: 1\n - <PERSON>: 12\n"}, {"date": 1737735955399, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -44,10 +44,16 @@\n LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n WHERE pagos.idevento = 2193\n ORDER BY precios.precio;\n \n+*Pagos a cada caja*\n+SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n+FROM participantes\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n+WHERE participantes.idevento = 2193\n+AND estado IN ('inscripto', 'acreditado')\n+GROUP BY datosxparticipantes.dato\n \n-Pagos en efectivo según caja, <PERSON><PERSON> con MP, Calcular valor por cantidad de carreras\n \n ## LISTAS PARA MAILS\n \n > Nadadores Aguas Abiertas de Neuquén y alrededores\n"}, {"date": 1737736256787, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -280,14 +280,14 @@\n - <PERSON>gu<PERSON> Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n \n *Cajas*\n \n-- MercadoPago: 120 (hubo algunos pagos de 50%)\n-- Oceano Virgen: 17\n-- A<PERSON><PERSON>: 5\n-- Meseta Wear: 1\n-- Andy: 12\n-- <PERSON><PERSON>: 3\n+- <PERSON><PERSON>: 90 \t\n+- <PERSON>: 12 \t\n+- Oceano Virgen: 14 \t\n+- <PERSON><PERSON><PERSON>: 3 \t\n+- <PERSON><PERSON>: 1 \t\n+- <PERSON><PERSON><PERSON> Wear: 1 \t\n \n \n ### OBJETIVOS\n \n"}, {"date": 1737736848009, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -271,25 +271,32 @@\n - 5 combinaron junto con Aguas Abiertas 4000m\n - 1 combinaron junto con Aguas Abiertas 1500m\n - Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar\n - Ninguno combinó Running, son públicos diferentes\n+- Ningún equipo hizo más de una carrera, no es necesario que se puedan combinar\n - Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar\n - Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo\n - Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta\n - SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n - Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n - Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n \n-*Cajas*\n+*Pagos según Cajas*\n \n-- Bauti: 90 \t\n-- Andy: 12 \t\n-- Oceano Virgen: 14 \t\n-- Aquiles: 3 \t\n-- Andres: 1 \t\n-- Meseta Wear: 1 \t\n+- MercadoPago: 90\n+- Oceano Virgen: 14\n+- Aquiles: 3\n+- Meseta Wear: 1\n+- Andy: 12\n+- Andres: 1\n \n+*Conclusiones*\n \n+- MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n+- Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n+- Falta un punto de cobro en efectivo en Piedra del Águila\n+\n+\n ### OBJETIVOS\n \n - El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n - El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n@@ -319,16 +326,21 @@\n - El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n \n *Para los circuitos*\n \n-- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente, ni bollas, ni nada\n+- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente\n+- Las boyas tienen que servir para SwimRun sin moverlas\n - Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.\n+- Los circuitos de natación fueron correctos, los dejaría igual.\n+- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n+\n+*Otros cambios*\n+\n - No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n - El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n-- Con 100m de vallas alcanza.\n+- Con 100m de vallas alcanza y hay forma de ubicarlas para que no haya que moverlas.\n - Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n - Las chicas con más personal de ayuda y mejor asesoradas.\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n-- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n - Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n - No ofrecer running de 21K, sólo 10K y 5K.\n-- Los circuitos de natación fueron correctos, los dejaría igual.\n+\n"}, {"date": 1737736916467, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -282,19 +282,21 @@\n \n *Pagos según <PERSON>*\n \n - MercadoPago: 90\n-- Oceano Virgen: 14\n-- Aquiles: 3\n-- Meseta Wear: 1\n-- Andy: 12\n-- Andres: 1\n+- Efectivo en Oceano Virgen: 14\n+- Efectivo en Aquiles: 3\n+- Efectivo en Meseta Wear: 1\n+- MercadoPago Andy primera semana: 12\n+- En efectivo a Andres: 1\n+- En Acreditación: 10\n \n *Conclusiones*\n \n - MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n - Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n - Falta un punto de cobro en efectivo en Piedra del Águila\n+- El cobro en las acreditaciones hay que seguir manteniéndolo\n \n \n ### OBJETIVOS\n \n"}, {"date": 1737736944397, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -279,8 +279,10 @@\n - SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n - Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n - Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n \n+### ECONOMÍA\n+\n *Pagos según Cajas*\n \n - MercadoPago: 90\n - Efectivo en Oceano Virgen: 14\n"}, {"date": 1737736989948, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,21 +5,24 @@\n - Armar consultas para generar las cajas\n - Armar consultas para ver cuánta gente hay\n \n *Acreditados sin tiempos*\n+\n SELECT * FROM `participantes`\n WHERE idevento = 2193\n AND estado = 'acreditado'\n AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n \n *Inscriptos con tiempos*\n+\n SELECT * FROM `participantes`\n WHERE idevento = 2193\n AND estado = 'inscripto'\n AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n \n \n *Cantidades de personas*\n+\n SELECT\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n (SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n@@ -30,31 +33,45 @@\n GROUP BY idinscripcion\n ORDER BY cantidad DESC\n \n *Carreras de inscripciones en multiples carreras*\n+\n SELECT categorias.nombre AS cat, idinscripcion\n FROM categoriasxparticipantes\n LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n ORDER BY idinscripcion\n \n *Pagos a Bauti*\n+\n SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n FROM pagos\n LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n WHERE pagos.idevento = 2193\n ORDER BY precios.precio;\n \n *Pagos a cada caja*\n+\n SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n FROM participantes\n LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n WHERE participantes.idevento = 2193\n AND estado IN ('inscripto', 'acreditado')\n GROUP BY datosxparticipantes.dato\n \n+*Listado de pagos*\n \n+SELECT idparticipante, nombre, mail, estado, equipo, datosxparticipantes.dato AS caja,\n+(SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras\n+FROM participantes p\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n+WHERE P.idevento = 2193\n+AND estado IN ('inscripto', 'acreditado')\n+AND equipo != 'participante'\n+ORDER BY caja\n+\n+\n ## LISTAS PARA MAILS\n \n > Nadadores Aguas Abiertas de Neuquén y alrededores\n SELECT nombre, mail, localidad FROM `participantes`\n"}, {"date": 1737750218757, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -60,13 +60,13 @@\n GROUP BY datosxparticipantes.dato\n \n *Listado de pagos*\n \n-SELECT idparticipante, nombre, mail, estado, equipo, datosxparticipantes.dato AS caja,\n+SELECT idparticipante, nombre, mail, estado, equipo, datosxparticipantes.dato AS caja, fechapago,\n (SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras\n FROM participantes p\n LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n-WHERE P.idevento = 2193\n+WHERE p.idevento = 2193\n AND estado IN ('inscripto', 'acreditado')\n AND equipo != 'participante'\n ORDER BY caja\n \n"}, {"date": 1737751044501, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,370 @@\n+# ACUATLON\n+\n+## SQL TOTALES\n+\n+- Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n+\n+*Acreditados sin tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'acreditado'\n+AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+*Inscriptos con tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'inscripto'\n+AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+\n+*Cantidades de personas*\n+\n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n+\n+*Cantidades de inscripciones en multiples carreras*\n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+*Carreras de inscripciones en multiples carreras*\n+\n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n+ORDER BY idinscripcion\n+\n+*Pagos a Bauti*\n+\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n+*Pagos a cada caja*\n+\n+SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n+FROM participantes\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n+WHERE participantes.idevento = 2193\n+AND estado IN ('inscripto', 'acreditado')\n+GROUP BY datosxparticipantes.dato\n+\n+*Listado de pagos*\n+\n+SELECT idparticipante, nombre, mail, p.estado, equipo, fechapago,\n+(SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras,\n+datosxparticipantes.dato AS caja, precios.precio\n+FROM participantes p\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE p.idevento = 2193\n+AND p.estado IN ('inscripto', 'acreditado')\n+AND p.equipo != 'participante'\n+ORDER BY caja\n+\n+\n+## LISTAS PARA MAILS\n+\n+> Nadadores Aguas Abiertas de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n+GROUP BY mail;\n+\n+> Nadadores Aguas Abiertas de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n+GROUP BY mail;\n+\n+> Corredores de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n+GROUP BY mail;\n+\n+> Corredores de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n+GROUP BY mail;\n+\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Escribir manual de procesos completo\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Poner la Radio y el Streaming en la página\n+- Hacer streaming\n+- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n+\n+\n+## NEWSLETTER\n+\n+- [x] Pre inscripto para que no se duerman\n+- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- [x] Corredores Lolog\n+\n+- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n+\n+\n+## DOCUMENTO PARA STAFF\n+\n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios (ordenar y poner cuántos premios se entregan)\n+- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n+\n+---\n+\n+## BOYAS\n+\n+Ubicación GPS de las 6 boyas rojas:\n+\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n+\n+\n+## CRONOGRAMA Y UBICACIONES\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ *Ubicaciones*\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ *Viernes 17 de Enero*\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ *Sábado 18 de Enero*\n+\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:00 a 18:00 - Acreditaciones\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n+\n+🗓️ *Domingo 19 de Enero*\n+\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n+## CARRERAS PREMIOS\n+\n+- SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n+\n+- Aguas Abiertas 1500\n+  - 14 categorias\n+- Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n+\n+- Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n+- Acuatlón Short\n+  - 3 categorias\n+\n+- Águila Run 5K\n+  - Nada\n+\n+- Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n+- Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n+\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n+\n+\n+---\n+\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n+\n+\n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n+\n+## FEEDBACK 2025 E IDEAS PARA 2026\n+\n+### ESTADÍSTICAS\n+\n+*Cantidades de personas*\n+\n+- Personas Diferentes: 149\n+- Acreditados Individuales: 113\n+- Acreditados en Equipos: 18\n+- Acreditados en Acuatlón Olímpico: 23\n+- Acreditados en Acuatlón Posta: 15\n+- Acreditados en Acuatlón Short: 14\n+- Acreditados en Aguas Abiertas 1500: 27\n+- Acreditados en Aguas Abiertas 4000: 29\n+- Acreditados en SwimRun Dupla 10K: 3\n+- Acreditados en SwimRun Individual 10K: 10\n+- Acreditados en Águila Run 10K: 12\n+- Acreditados en Águila Run 5K: 9\n+- Inscriptos en Águila Run 21K: 3 (los pasamos a 10K)\n+- Acreditados en 3 carreras: 1 (sólo una persona)\n+- Acreditados en 2 carreras: 9 (sólo 6% de los inscriptos)\n+- Inscriptos sin acreditarse (faltaron): 9 (6% del total)\n+- Preinscriptos (no pagaron): 51 (34% de los acreditados)\n+\n+*Conclusiones*\n+\n+- Tuvimos casi 150 personas\n+- Todos los que combinaron carreras hicieron Acuatlón\n+- 3 combinaron junto con SwimRun\n+- 5 combinaron junto con Aguas Abiertas 4000m\n+- 1 combinaron junto con Aguas Abiertas 1500m\n+- Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar\n+- Ninguno combinó Running, son públicos diferentes\n+- Ningún equipo hizo más de una carrera, no es necesario que se puedan combinar\n+- Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar\n+- Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo\n+- Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta\n+- SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n+- Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n+- Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n+\n+### ECONOMÍA\n+\n+*Pagos según Cajas*\n+\n+- MercadoPago: 90\n+- Efectivo en Oceano Virgen: 14\n+- Efectivo en Aquiles: 3\n+- Efectivo en Meseta Wear: 1\n+- MercadoPago Andy primera semana: 12\n+- En efectivo a Andres: 1\n+- En Acreditación: 10\n+\n+*Conclusiones*\n+\n+- MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n+- Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n+- Falta un punto de cobro en efectivo en Piedra del Águila\n+- El cobro en las acreditaciones hay que seguir manteniéndolo\n+\n+\n+### OBJETIVOS\n+\n+- El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n+- El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n+- El tercer objetivo fue *no perder dinero*. Lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n+\n+### FEEDBACK\n+\n+Los problemas que veo son:\n+\n+- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n+- No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.\n+- Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.\n+- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.\n+- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n+- No sé si la fecha fue la correcta, hay que analizar otras opciones.\n+- Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.\n+\n+Mi propuesta para el año que viene:\n+\n+*Para el cronograma*\n+\n+- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde).\n+- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n+- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón.\n+- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n+- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.\n+- El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n+\n+*Para los circuitos*\n+\n+- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente\n+- Las boyas tienen que servir para SwimRun sin moverlas\n+- Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.\n+- Los circuitos de natación fueron correctos, los dejaría igual.\n+- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n+\n+*Otros cambios*\n+\n+- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n+- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n+- Con 100m de vallas alcanza y hay forma de ubicarlas para que no haya que moverlas.\n+- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n+- Las chicas con más personal de ayuda y mejor asesoradas.\n+- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n+- Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n+- No ofrecer running de 21K, sólo 10K y 5K.\n+\n"}, {"date": 1737752965695, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -62,12 +62,12 @@\n *Listado de pagos*\n \n SELECT idparticipante, nombre, mail, p.estado, equipo, fechapago,\n (SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras,\n-datosxparticipantes.dato AS caja, precios.precio\n+datosxparticipantes.dato AS caja, precios.precio, p.observacion\n FROM participantes p\n LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n-LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN pagos ON p.idinscripcion = pagos.idinscripcion\n LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n WHERE p.idevento = 2193\n AND p.estado IN ('inscripto', 'acreditado')\n AND p.equipo != 'participante'\n@@ -311,8 +311,21 @@\n - MercadoPago Andy primera semana: 12\n - En efectivo a Andres: 1\n - En Acreditación: 10\n \n+*Ingresos y Egresos*\n+\n+Kits: $ -5.218.033,00 \n+Premios: $ -2.677.734,00 \n+Prensa: $ -1.100.000,00 \n+Promoción: $ -1.403.000,00 \n+Recursos: $ -2.969.754,17 \n+Seguridad: $ -600.000,00 \n+Sponsors: $ 6.008.593,00 \n+Venta: $ 8.191.700,00 \n+\n+\n+\n *Conclusiones*\n \n - MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n - Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n"}, {"date": 1737754347732, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -311,23 +311,35 @@\n - MercadoPago Andy primera semana: 12\n - En efectivo a Andres: 1\n - En Acreditación: 10\n \n-*Ingresos y Egresos*\n+*Ingresos*\n \n-Kits: $ -5.218.033,00 \n-Premios: $ -2.677.734,00 \n-Prensa: $ -1.100.000,00 \n-Promoción: $ -1.403.000,00 \n-Recursos: $ -2.969.754,17 \n-Seguridad: $ -600.000,00 \n-Sponsors: $ 6.008.593,00 \n-Venta: $ 8.191.700,00 \n+Venta: $7M\n+Sponsors: $6M\n+Total: $13M\n \n+*Egresos*\n \n+Arco: $1.5M\n+Kits: $5M\n+Premios: $2.7M\n+Prensa: $1.4M\n+Promoción: $1.4M\n+Recursos: $1.6M\n+Seguridad: $0.6M\n+Comidas: $0.6M\n+Comisiones: $0.8M\n+Cronometraje: $0.4M\n+Total: $16M\n \n *Conclusiones*\n \n+- Perdimos $3M aprox. con 150 personas, cuando deberíamos estar en cero con menos participantes, sino no hay ganancias posibles\n+- El arco ($1.5M) que ya no hay que gastarlo\n+- Los $2M de los gorros nos movieron mucho la caja, pero sobraron varios que podemos re-utilizar\n+- Gastamos bastante en prensa, por ser la primera está bien, pero no creo que sea necesario hacerlo el próximo año\n+- Si ahorramos en premios, prensa y arco, estamos en cero y necesitamos 200 personas el año que viene recién para recuperar la pérdida de este año y 300 participantes para tener ganancias que valgan la pena el esfuerzo\n - MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n - Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n - Falta un punto de cobro en efectivo en Piedra del Águila\n - El cobro en las acreditaciones hay que seguir manteniéndolo\n@@ -380,371 +392,4 @@\n - Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n - Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n - No ofrecer running de 21K, sólo 10K y 5K.\n \n-# ACUATLON\n-\n-## SQL TOTALES\n-\n-- Armar consultas para generar las cajas\n-- Armar consultas para ver cuánta gente hay\n-\n-*Acreditados sin tiempos*\n-\n-SELECT * FROM `participantes`\n-WHERE idevento = 2193\n-AND estado = 'acreditado'\n-AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n-\n-*Inscriptos con tiempos*\n-\n-SELECT * FROM `participantes`\n-WHERE idevento = 2193\n-AND estado = 'inscripto'\n-AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n-\n-\n-*Cantidades de personas*\n-\n-SELECT\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n-(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n-\n-*Cantidades de inscripciones en multiples carreras*\n-SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n-idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '')\n-GROUP BY idinscripcion\n-ORDER BY cantidad DESC\n-\n-*Carreras de inscripciones en multiples carreras*\n-\n-SELECT categorias.nombre AS cat, idinscripcion\n-FROM categoriasxparticipantes\n-LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n-WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n-ORDER BY idinscripcion\n-\n-*Pagos a Bauti*\n-\n-SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n-FROM pagos\n-LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n-LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n-WHERE pagos.idevento = 2193\n-ORDER BY precios.precio;\n-\n-*Pagos a cada caja*\n-\n-SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n-FROM participantes\n-LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n-WHERE participantes.idevento = 2193\n-AND estado IN ('inscripto', 'acreditado')\n-GROUP BY datosxparticipantes.dato\n-\n-*Listado de pagos*\n-\n-SELECT idparticipante, nombre, mail, estado, equipo, datosxparticipantes.dato AS caja, fechapago,\n-(SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras\n-FROM participantes p\n-LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n-WHERE p.idevento = 2193\n-AND estado IN ('inscripto', 'acreditado')\n-AND equipo != 'participante'\n-ORDER BY caja\n-\n-\n-## LISTAS PARA MAILS\n-\n-> Nadadores Aguas Abiertas de Neuquén y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n-GROUP BY mail;\n-\n-> Nadadores Aguas Abiertas de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n-AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n-GROUP BY mail;\n-\n-> Corredores de Neuquén y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n-AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n-GROUP BY mail;\n-\n-> Corredores de Bariloche y alrededores\n-SELECT nombre, mail, localidad FROM `participantes`\n-WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n-AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n-GROUP BY mail;\n-\n-\n-\n-## POST EVENTO PARA EL AÑO QUE VIENE\n-\n-- Unificar el reglamento\n-- Escribir manual de procesos completo\n-- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n-- Hacer un posteo en redes sociales con la info del año que viene\n-- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n-- Agradecimiento a sponsors\n-  - Posteo\n-  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n-- Conseguir reloj de llegada\n-- Carteles con distancia y aliento en estacas de madera\n-- Poner la Radio y el Streaming en la página\n-- Hacer streaming\n-- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n-\n-\n-## NEWSLETTER\n-\n-- [x] Pre inscripto para que no se duerman\n-- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n-- [x] Corredores Lolog\n-\n-- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n-- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n-\n-- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n-- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n-\n-- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n-\n-\n-## DOCUMENTO PARA STAFF\n-\n-- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n-- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n-- Listado de staff, almuerzos y albergues\n-- Venta de remeras 8 o 2x15\n-- Entrega de premios (ordenar y poner cuántos premios se entregan)\n-- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n-\n----\n-\n-## BOYAS\n-\n-Ubicación GPS de las 6 boyas rojas:\n-\n-- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n-- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n-- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n-- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n-- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n-- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n-\n-\n-## CRONOGRAMA Y UBICACIONES\n-\n-Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n-\n-🗺️ *Ubicaciones*\n-\n-📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n-\n-📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n-\n-📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n-\n-🗓️ *Viernes 17 de Enero*\n-\n-⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n-\n-🗓️ *Sábado 18 de Enero*\n-\n-⏰ 08:00 a 12:00 - Acreditaciones\n-⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n-⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n-\n-⏰ 12:50 - Charla técnica Acuatlón Kids\n-⏰ 13:00 - Largada Acuatlón Kids\n-\n-⏰ 14:00 a 18:00 - Acreditaciones\n-⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n-⏰ 15:00 - Largada Aguas Abiertas 4000m\n-⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n-⏰ 15:30 - Largada Aguas Abiertas  1500m\n-\n-⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n-\n-⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n-\n-🗓️ *Domingo 19 de Enero*\n-\n-⏰ 07:00 a 09:00 - Acreditaciones\n-⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n-⏰ 09:45 - Largada Acuatlón Short\n-⏰ 09:50 - Charla técnica para Águila Run\n-⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n-\n-⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n-⏰ 14:00 - Cierre del evento\n-\n-\n-## CARRERAS PREMIOS\n-\n-- SwimRun Individual 10K\n-  - 2 generales\n-- SwimRun Dupla 10K\n-  - 3 generales\n-\n-- Aguas Abiertas 1500\n-  - 14 categorias\n-- Aguas Abiertas 4000\n-  - 3 generales\n-  - 14 categorias\n-\n-- Acuatlón Olímpico\n-  - 3 generales\n-  - 14 categorias\n-- Acuatlón Posta\n-  - 3 generales\n-- Acuatlón Short\n-  - 3 categorias\n-\n-- Águila Run 5K\n-  - Nada\n-\n-- Águila Run 10K\n-  - 3 generales\n-  - 14 categorias\n-\n-- Águila Run 21K\n-  - 3 generales\n-  - 14 categorias\n-\n-Total generales madera: 20 grupos de 123 de madera\n-Total de categorias: 73 grupos de 123 medalla con calco\n-\n-\n----\n-\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n-sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n-\n-rm -r /var/www/acuatlon/www/wp-content/uploads\n-ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n-\n-http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n-\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n-\n-Equipo posta:\n-https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n-\n-\n-sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n-\n-\n-## FEEDBACK 2025 E IDEAS PARA 2026\n-\n-### ESTADÍSTICAS\n-\n-*Cantidades de personas*\n-\n-- Personas Diferentes: 149\n-- Acreditados Individuales: 113\n-- Acreditados en Equipos: 18\n-- Acreditados en Acuatlón Olímpico: 23\n-- Acreditados en Acuatlón Posta: 15\n-- Acreditados en Acuatlón Short: 14\n-- Acreditados en Aguas Abiertas 1500: 27\n-- Acreditados en Aguas Abiertas 4000: 29\n-- Acreditados en SwimRun Dupla 10K: 3\n-- Acreditados en SwimRun Individual 10K: 10\n-- Acreditados en Águila Run 10K: 12\n-- Acreditados en Águila Run 5K: 9\n-- Inscriptos en Águila Run 21K: 3 (los pasamos a 10K)\n-- Acreditados en 3 carreras: 1 (sólo una persona)\n-- Acreditados en 2 carreras: 9 (sólo 6% de los inscriptos)\n-- Inscriptos sin acreditarse (faltaron): 9 (6% del total)\n-- Preinscriptos (no pagaron): 51 (34% de los acreditados)\n-\n-*Conclusiones*\n-\n-- Tuvimos casi 150 personas\n-- Todos los que combinaron carreras hicieron Acuatlón\n-- 3 combinaron junto con SwimRun\n-- 5 combinaron junto con Aguas Abiertas 4000m\n-- 1 combinaron junto con Aguas Abiertas 1500m\n-- Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar\n-- Ninguno combinó Running, son públicos diferentes\n-- Ningún equipo hizo más de una carrera, no es necesario que se puedan combinar\n-- Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar\n-- Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo\n-- Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta\n-- SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n-- Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n-- Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n-\n-### ECONOMÍA\n-\n-*Pagos según Cajas*\n-\n-- MercadoPago: 90\n-- Efectivo en Oceano Virgen: 14\n-- Efectivo en Aquiles: 3\n-- Efectivo en Meseta Wear: 1\n-- MercadoPago Andy primera semana: 12\n-- En efectivo a Andres: 1\n-- En Acreditación: 10\n-\n-*Conclusiones*\n-\n-- MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n-- Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n-- Falta un punto de cobro en efectivo en Piedra del Águila\n-- El cobro en las acreditaciones hay que seguir manteniéndolo\n-\n-\n-### OBJETIVOS\n-\n-- El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n-- El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n-- El tercer objetivo fue *no perder dinero*. Lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n-\n-### FEEDBACK\n-\n-Los problemas que veo son:\n-\n-- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n-- No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.\n-- Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.\n-- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.\n-- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n-- No sé si la fecha fue la correcta, hay que analizar otras opciones.\n-- Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.\n-\n-Mi propuesta para el año que viene:\n-\n-*Para el cronograma*\n-\n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde).\n-- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n-- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón.\n-- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n-- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.\n-- El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n-\n-*Para los circuitos*\n-\n-- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente\n-- Las boyas tienen que servir para SwimRun sin moverlas\n-- Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.\n-- Los circuitos de natación fueron correctos, los dejaría igual.\n-- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n-\n-*Otros cambios*\n-\n-- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n-- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n-- Con 100m de vallas alcanza y hay forma de ubicarlas para que no haya que moverlas.\n-- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n-- Las chicas con más personal de ayuda y mejor asesoradas.\n-- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n-- Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n-- No ofrecer running de 21K, sólo 10K y 5K.\n-\n"}, {"date": 1737759283446, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -257,139 +257,4 @@\n \n \n sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n \n-\n-## FEEDBACK 2025 E IDEAS PARA 2026\n-\n-### ESTADÍSTICAS\n-\n-*Cantidades de personas*\n-\n-- Personas Diferentes: 149\n-- Acreditados Individuales: 113\n-- Acreditados en Equipos: 18\n-- Acreditados en Acuatlón Olímpico: 23\n-- Acreditados en Acuatlón Posta: 15\n-- Acreditados en Acuatlón Short: 14\n-- Acreditados en Aguas Abiertas 1500: 27\n-- Acreditados en Aguas Abiertas 4000: 29\n-- Acreditados en SwimRun Dupla 10K: 3\n-- Acreditados en SwimRun Individual 10K: 10\n-- Acreditados en Águila Run 10K: 12\n-- Acreditados en Águila Run 5K: 9\n-- Inscriptos en Águila Run 21K: 3 (los pasamos a 10K)\n-- Acreditados en 3 carreras: 1 (sólo una persona)\n-- Acreditados en 2 carreras: 9 (sólo 6% de los inscriptos)\n-- Inscriptos sin acreditarse (faltaron): 9 (6% del total)\n-- Preinscriptos (no pagaron): 51 (34% de los acreditados)\n-\n-*Conclusiones*\n-\n-- Tuvimos casi 150 personas\n-- Todos los que combinaron carreras hicieron Acuatlón\n-- 3 combinaron junto con SwimRun\n-- 5 combinaron junto con Aguas Abiertas 4000m\n-- 1 combinaron junto con Aguas Abiertas 1500m\n-- Ninguno combinó SwimRun y Aguas Abiertas, no es necesario que se puedan combiar\n-- Ninguno combinó Running, son públicos diferentes\n-- Ningún equipo hizo más de una carrera, no es necesario que se puedan combinar\n-- Sólo hubo 3 inscriptos en 21 K, no hay interés por esta distancia en este lugar\n-- Sumando a los preinscriptos interesados, suman 200 que era nuestro número objetivo\n-- Acuatlón llevó 52 personas, un tercio del total y bien distribuido entre las distancias y posta\n-- SwimRun llevó 13 personas, un poco menos de lo esperado y de las versiones anteriores, hay que seguir insistiendo pero con menos dedicación\n-- Aguas Abiertas llevó 56 personas, es poco considerando que enviamos mucha publicidad y tenemos muchos contactos\n-- Águila Run llevó 21 personas, casi nada considerando la cantidad de runners de la zona, por lo que es una carrera puramente para Piedra del Águila y relleno\n-\n-### ECONOMÍA\n-\n-*Pagos según Cajas*\n-\n-- MercadoPago: 90\n-- Efectivo en Oceano Virgen: 14\n-- Efectivo en Aquiles: 3\n-- Efectivo en Meseta Wear: 1\n-- MercadoPago Andy primera semana: 12\n-- En efectivo a Andres: 1\n-- En Acreditación: 10\n-\n-*Ingresos*\n-\n-Venta: $7M\n-Sponsors: $6M\n-Total: $13M\n-\n-*Egresos*\n-\n-Arco: $1.5M\n-Kits: $5M\n-Premios: $2.7M\n-Prensa: $1.4M\n-Promoción: $1.4M\n-Recursos: $1.6M\n-Seguridad: $0.6M\n-Comidas: $0.6M\n-Comisiones: $0.8M\n-Cronometraje: $0.4M\n-Total: $16M\n-\n-*Conclusiones*\n-\n-- Perdimos $3M aprox. con 150 personas, cuando deberíamos estar en cero con menos participantes, sino no hay ganancias posibles\n-- El arco ($1.5M) que ya no hay que gastarlo\n-- Los $2M de los gorros nos movieron mucho la caja, pero sobraron varios que podemos re-utilizar\n-- Gastamos bastante en prensa, por ser la primera está bien, pero no creo que sea necesario hacerlo el próximo año\n-- Si ahorramos en premios, prensa y arco, estamos en cero y necesitamos 200 personas el año que viene recién para recuperar la pérdida de este año y 300 participantes para tener ganancias que valgan la pena el esfuerzo\n-- MercadoPago fue el método de pago más usado, y funcionó perfectamente con Bauti\n-- Oceano Virgen muy bien, Aquiles casi nadie pero es bueno, Meseta Wear nunca más\n-- Falta un punto de cobro en efectivo en Piedra del Águila\n-- El cobro en las acreditaciones hay que seguir manteniéndolo\n-\n-\n-### OBJETIVOS\n-\n-- El primer objetivo fue *divertirnos*. Creo que lo logramos, aunque no tanto Juli y la Colo.\n-- El segundo objetivo fue *probar el concepto y aprender*. Creo que fue totalmente logrado.\n-- El tercer objetivo fue *no perder dinero*. Lamentablemente no lo logramos, pero veremos si podemos hacerlo en el futuro\n-\n-### FEEDBACK\n-\n-Los problemas que veo son:\n-\n-- Mucho trabajo de armado y desarmado, lo que generó un desgaste importante.\n-- No había suficiente alojamiento, se llenó la localidad por lo que no podríamos llevar el doble de gente.\n-- Mucha gente vino sólo por el día, más allá del alojamiento, quicieron hacerlo así.\n-- Muy poca gente hizo más de una actividad y nos dejó muy poco dinero extra, pero si generó varias complicaciones.\n-- Algo de falta de coordinación, especialmente en la parte de las chicas y de algunos sectores municipales.\n-- No sé si la fecha fue la correcta, hay que analizar otras opciones.\n-- Si sé que el lugar fue el correcto, especialmente para la prueba de concepto que queríamos hacer.\n-\n-Mi propuesta para el año que viene:\n-\n-*Para el cronograma*\n-\n-- Hacerlo todo en un mismo día, desde las 10:00 hasta las 18:00 y ahí la entrega de todos los premios (Acuatlón + Run a la mañana, SwimRun y Aguas Abiertas a la tarde).\n-- El viernes reunión con todo el personal por grupos de responsabilidades, todos saben que hacer y no hay explicaciones el sábado.\n-- El sábado a las 8 ya está todo armado y el personal ahí, a las 9 todos en sus posiciones, a las 10 arrancamos y hacemos todo el evento de un tirón.\n-- La acreditación se hace el viernes a la tarde desde las 18:00 a las 20:00 y el sábado desde las 7:00 hasta las 15:00 hs.\n-- Todo el recurso tiene que estar listo el jueves, marcamos circuitos el jueves, armamos todo el circo el viernes y un cuidador se queda para cuidar todo. Desarmamos el mismo sábado a la noche.\n-- El domingo disfrutamos de correrla nosotros y viajamos de vuelta tranquilos.\n-\n-*Para los circuitos*\n-\n-- Los circuitos tienen que pasar por los mismos PCs, así no movemos ni gente\n-- Las boyas tienen que servir para SwimRun sin moverlas\n-- Modificar el circuito de SwimRun (con los 5K del Run y rulo de 10K) para que todo sea desde una misma largada y una misma llegada.\n-- Los circuitos de natación fueron correctos, los dejaría igual.\n-- Estirar Run 10K (después de PC 5) para evitar PCs: 1, 2, 7 y 8.\n-\n-*Otros cambios*\n-\n-- No hay premios por categorías, sólo 5 puestos de cada general (hombre, mujer y equipo).\n-- El almuerzo es sólo el del sábado con choripan y paty, con sólo una hora de descanso, y sólo damos cenas al staff núcleo principal\n-- Con 100m de vallas alcanza y hay forma de ubicarlas para que no haya que moverlas.\n-- Repetimos el mismo equipo de gente, pero con coordinadores responsables por sector.\n-- Las chicas con más personal de ayuda y mejor asesoradas.\n-- Charlas técnicas y circuitos pre-grabados y subidos a las redes una semana antes.\n-- Carteles con indicación de distancia y bifurcaciones (con aliento) y reforzar la marcación.\n-- No ofrecer running de 21K, sólo 10K y 5K.\n-\n"}, {"date": 1738283333297, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,21 @@\n # ACUATLON\n \n+## CERRANDO CUENTAS\n+\n+Te paso unas actualizaciones de las cajas:\n+\n+- Bauti me dice que le ingresaron $5.066.055 (lo cual parece bien y obvio confiamos)\n+- O sea que le quedan $506.605 de Comisión para Bauti\n+- Tenemos registrados $3.628.310 de Gastos que hizo\n+- Por lo cual quedan $931.139 que me lo tendría que pasar en efectivo a mí en algún momento\n+- El me dice que en MP tiene $1.108.497, me da miedo que le haya comido mucho MP (eso se configura desde 0% a 6%) pero le quedarían en el peor de los casos $177.358 y en el mejor $506.605\n+- Se va a fijar si le falta pasarme algún pago que hizo y ahí sería más para él.\n+- Con estos números perdimos $1.398.739 entre los dos (o sea que perdimos $699.369 cada uno)\n+- También me dijo que vos tenés lo de Oceano Virgen que según el sistema fue $ 1.013.000,00\n+- O sea que para equiparar cajas, tendrías que pasarme en efectivo $1.405.635\n+\n+\n ## SQL TOTALES\n \n - Armar consultas para generar las cajas\n - Armar consultas para ver cuánta gente hay\n"}, {"date": 1739134621391, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,16 @@\n+# TRIATLÓN\n+\n+\n+Hidratación Running\n+\n+- Se pasa a los 2.00, 4.25 y 7.75 y 10.30 km\n+- 1° puesto en la llegada y 2° puesto en Gatica al fondo\n+- Mesa y gazebo\n+- En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas\n+\n+\n+\n # ACUATLON\n \n ## CERRANDO CUENTAS\n \n"}, {"date": 1739134886253, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,12 @@\n - Se pasa a los 2.00, 4.25 y 7.75 y 10.30 km\n - 1° puesto en la llegada y 2° puesto en Gatica al fondo\n - Mesa y gazebo\n - En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas\n+- Charla para los PCs, contactos o grupo whatsapp para coordinar\n+- Armado y desarmado de puestos de hidratación, ubicar a los PCs\n \n \n-\n # ACUATLON\n \n ## CERRANDO CUENTAS\n \n"}, {"date": 1739134944544, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,16 @@\n # TRIATLÓN\n \n+Personal:\n \n-Hidratación Running\n+<PERSON><PERSON><PERSON><PERSON><PERSON> (cortes): 8\n+PCs (personas con bandera): 11\n+Control retome (personal de cronometraje): 1\n+Marcación con conos, cinta o vayas: 2\n+Hidratación (persona con puesto): 2\n \n+Hidratación Running:\n+\n - Se pasa a los 2.00, 4.25 y 7.75 y 10.30 km\n - 1° puesto en la llegada y 2° puesto en Gatica al fondo\n - Mesa y gazebo\n - En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas\n"}, {"date": 1739136066695, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,9 @@\n \n - Se pasa a los 2.00, 4.25 y 7.75 y 10.30 km\n - 1° puesto en la llegada y 2° puesto en Gatica al fondo\n - Mesa y gazebo\n-- En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas\n+- En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas, basurero grande\n - Charla para los PCs, contactos o grupo whatsapp para coordinar\n - Armado y desarmado de puestos de hidratación, ubicar a los PCs\n \n \n"}, {"date": 1739193990090, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,9 +16,24 @@\n - En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas, basurero grande\n - Charla para los PCs, contactos o grupo whatsapp para coordinar\n - Armado y desarmado de puestos de hidratación, ubicar a los PCs\n \n+Cronometraje:\n \n+- Permiso para circular\n+- Electricidad y ubicación en Control Bici\n+- Electricidad en en llegada\n+\n+\n+Preparar:\n+\n+- TV para mostrar resultados\n+- Macsha probado antes\n+- GoPro para Prisci\n+- Celu Crono con trípode en parque cerrado\n+- Celu Agente con trípode para llegada\n+\n+\n # ACUATLON\n \n ## CERRANDO CUENTAS\n \n"}, {"date": 1739223233635, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,17 +23,8 @@\n - Electricidad y ubicación en Control Bici\n - Electricidad en en llegada\n \n \n-Preparar:\n-\n-- TV para mostrar resultados\n-- Macsha probado antes\n-- GoPro para Prisci\n-- Celu Crono con trípode en parque cerrado\n-- Celu Agente con trípode para llegada\n-\n-\n # ACUATLON\n \n ## CERRANDO CUENTAS\n \n"}, {"date": 1739226231781, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,15 +16,9 @@\n - En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas, basurero grande\n - Charla para los PCs, contactos o grupo whatsapp para coordinar\n - Armado y desarmado de puestos de hidratación, ubicar a los PCs\n \n-Cronometraje:\n \n-- Permiso para circular\n-- Electricidad y ubicación en Control Bici\n-- Electricidad en en llegada\n-\n-\n # ACUATLON\n \n ## CERRANDO CUENTAS\n \n"}, {"date": 1739239188360, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,8 +16,17 @@\n - En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas, basurero grande\n - Charla para los PCs, contactos o grupo whatsapp para coordinar\n - Armado y desarmado de puestos de hidratación, ubicar a los PCs\n \n+Cortes de Tránsito para Running:\n+- Transito 1: Linares antes de la rotonda de entrada a la isla\n+- Transito 2: Av. Ara San Juan antes de la rotonda de salida a la isla\n+- Transito 3: Río Negro antes de la rotonda del puente\n+- Transito 4: Av. Olascuaga al fondo\n+- Transito 5: La Pampa al fondo\n+- Transito 6: Los Cipreses al fondo\n+- Transito 7: Leguizamón y Río Senguer\n+- Transito 8: Gatica al fondo en la rotonda con Ignacio Rivas\n \n # ACUATLON\n \n ## CERRANDO CUENTAS\n"}, {"date": 1739478208166, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,275 @@\n+# ACUATLON\n+\n+## CERRANDO CUENTAS\n+\n+Te paso unas actualizaciones de las cajas:\n+\n+- Bauti me dice que le ingresaron $5.066.055 (lo cual parece bien y obvio confiamos)\n+- O sea que le quedan $506.605 de Comisión para Bauti\n+- Tenemos registrados $3.628.310 de Gastos que hizo\n+- Por lo cual quedan $931.139 que me lo tendría que pasar en efectivo a mí en algún momento\n+- El me dice que en MP tiene $1.108.497, me da miedo que le haya comido mucho MP (eso se configura desde 0% a 6%) pero le quedarían en el peor de los casos $177.358 y en el mejor $506.605\n+- Se va a fijar si le falta pasarme algún pago que hizo y ahí sería más para él.\n+- Con estos números perdimos $1.398.739 entre los dos (o sea que perdimos $699.369 cada uno)\n+- También me dijo que vos tenés lo de Oceano Virgen que según el sistema fue $ 1.013.000,00\n+- O sea que para equiparar cajas, tendrías que pasarme en efectivo $1.405.635\n+\n+\n+## SQL TOTALES\n+\n+- Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n+\n+*Acreditados sin tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'acreditado'\n+AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+*Inscriptos con tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'inscripto'\n+AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+\n+*Cantidades de personas*\n+\n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n+\n+*Cantidades de inscripciones en multiples carreras*\n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+*Carreras de inscripciones en multiples carreras*\n+\n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n+ORDER BY idinscripcion\n+\n+*Pagos a Bauti*\n+\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n+*Pagos a cada caja*\n+\n+SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n+FROM participantes\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n+WHERE participantes.idevento = 2193\n+AND estado IN ('inscripto', 'acreditado')\n+GROUP BY datosxparticipantes.dato\n+\n+*Listado de pagos*\n+\n+SELECT idparticipante, nombre, mail, p.estado, equipo, fechapago,\n+(SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras,\n+datosxparticipantes.dato AS caja, precios.precio, p.observacion\n+FROM participantes p\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n+LEFT JOIN pagos ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE p.idevento = 2193\n+AND p.estado IN ('inscripto', 'acreditado')\n+AND p.equipo != 'participante'\n+ORDER BY caja\n+\n+\n+## LISTAS PARA MAILS\n+\n+> Nadadores Aguas Abiertas de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n+GROUP BY mail;\n+\n+> Nadadores Aguas Abiertas de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n+GROUP BY mail;\n+\n+> Corredores de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n+GROUP BY mail;\n+\n+> Corredores de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n+GROUP BY mail;\n+\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Escribir manual de procesos completo\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Poner la Radio y el Streaming en la página\n+- Hacer streaming\n+- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n+\n+\n+## NEWSLETTER\n+\n+- [x] Pre inscripto para que no se duerman\n+- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- [x] Corredores Lolog\n+\n+- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n+\n+\n+## DOCUMENTO PARA STAFF\n+\n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios (ordenar y poner cuántos premios se entregan)\n+- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n+\n+---\n+\n+## BOYAS\n+\n+Ubicación GPS de las 6 boyas rojas:\n+\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n+\n+\n+## CRONOGRAMA Y UBICACIONES\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ *Ubicaciones*\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ *Viernes 17 de Enero*\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ *Sábado 18 de Enero*\n+\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:00 a 18:00 - Acreditaciones\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n+\n+🗓️ *Domingo 19 de Enero*\n+\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n+## CARRERAS PREMIOS\n+\n+- SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n+\n+- Aguas Abiertas 1500\n+  - 14 categorias\n+- Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n+\n+- Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n+- Acuatlón Short\n+  - 3 categorias\n+\n+- Águila Run 5K\n+  - Nada\n+\n+- Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n+- Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n+\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n+\n+\n+---\n+\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n+\n+\n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n"}, {"date": 1740090807365, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,424 @@\n+# TRIA\n+\n+## FEEDBACK TRIA\n+\n+Cambiar categorías a anual en tria\n+\n+# TRIATLÓN\n+\n+*LARGADAS*\n+\n+Amarillo ruta\n+Naranja mbt\n+Blanco posta\n+\n+\n+*CATEGORÍAS UNIFICADAS*\n+\n+- Caballeros Master F (+65 años) se unió en Caballeros Master E (60-64 años) como Caballeros Master E (+60 años)\n+- <PERSON><PERSON> (16-19 años) se unió con Damas Mayores (20-29 años) como Damas Juveniles (16-29 años)\n+- <PERSON><PERSON> Master D (55-59 años) y Damas Master C (50-54 años) se unieron con Damas Master B (45-49 años) como Damas Master B (+45 años)\n+- MTB Caballeros Master D (55-59 años) se unió con MTB Caballeros Master C (50-54 años) como MTB Caballeros Master C (+50 años)\n+- MTB Damas Master C (50-54 años) se unió con MTB Damas Master A (40-44 años) como MTB Damas Master A (+40 años)\n+\n+\n+*ORDEN PREMIACIÓN*\n+\n+Sorteo de 5 premios mientras se junta la gente\n+\n+Agradecimiento a los sponsors\n+\n+Generales Damas Ruta\n+Generales Caballeros Ruta\n+\n+Categorías Ruta (suben juntos damas y caballeros):\n+\n+Damas - Juveniles (16-29 años)\n+Caballeros - Mayores (20-29 años)\n+Damas - Pre-master (30-34 años)\n+Caballeros - Pre-master (30-34 años)\n+Damas - Master (35-39 años)\n+Caballeros - Master (35-39 años)\n+Damas - Master A (40-44 años)\n+Caballeros - Master A (40-44 años)\n+Damas - Master B (+45 años)\n+Caballeros - Master B (45-49 años)\n+Caballeros - Master C (50-54 años)\n+Caballeros - Master D (55-59 años)\n+Caballeros - Master E (+60 años)\n+\n+Sorteo de 5 premios\n+\n+Generales Damas MTB\n+Generales Caballeros MTB\n+\n+Categorías MTB (suben juntos damas y caballeros):\n+\n+MTB - Damas - Mayores (20-29 años)\n+MTB - Caballeros - Mayores (20-29 años)\n+MTB - Damas - Pre-master (30-34 años)\n+MTB - Caballeros - Pre-master (30-34 años)\n+MTB - Damas - Master (35-39 años)\n+MTB - Caballeros - Master (35-39 años)\n+MTB - Damas - Master A (+40 años)\n+MTB - Caballeros - Master A (40-44 años)\n+MTB - Caballeros - Master B (45-49 años)\n+MTB - Caballeros - Master C (+50 años)\n+\n+Sorteo de 3 premios\n+\n+Generales Postas Mixtas\n+Generales Postas Femeninas\n+Generales Postas Masculinas\n+\n+Sorteo Bici\n+\n+Agradecimiento Final\n+Cierre del evento\n+\n+\n+*PERSONAL*\n+\n+*NATACIÓN*\n+\n+GUARDAVIDAS: 20\n+VOLUNTARIOS: 2\n+- 2 en boyas de llegada\n+\n+---\n+\n+*CICLISMO*\n+\n+CORTE DE RUTA 22: 80 efectivos policiales para la ruta\n+CORTE DE CALLE SATURNINO: 15 VOLUNTARIOS para ayudar al personal de tránsito municipal\n+VOLUNTARIOS MARCACIÓN: 12\n+- 1 en Rotonda Tronador\n+- 2 en Rotonda Saturnino\n+- 2 en Baden de Chocón (bandera silbato)\n+- 2 en Badén casi Ricchieri (bandera silbato)\n+- 2 en Empalme Ruta22 (bandera silbato)\n+- 1 en Sobre ruta 22 para señalar bajar por Saturnino para iniciar 2da Vuelta\n+- 1 en Retome en Paimún (sumar conos)\n+- 1 en Retome en El Cholar (sumar conos)\n+CONTROL RETOME: 1 personal de cronometraje\n+\n+---\n+\n+*RUNNING*\n+\n+CORTES DE RUTA: 8 personal de Tránsito\n+- Transito 1: Linares antes de la rotonda de entrada a la isla\n+- Transito 2: Av. Ara San Juan antes de la rotonda de salida a la isla\n+- Transito 3: Río Negro antes de la rotonda del puente\n+- Transito 4: Av. Olascuaga al fondo\n+- Transito 5: La Pampa al fondo\n+- Transito 6: Los Cipreses al fondo\n+- Transito 7: Leguizamón y Río Senguer\n+- Transito 8: Gatica al fondo en la rotonda con Ignacio Rivas\n+VOLUNTARIOS PCS: 11 distribuidos según mapa\n+CONTROL RETOME: 1 personal de cronometraje\n+HIDRATACIÓN: 2 en puesto Gatica\n+\n+---\n+\n+*PARQUE CERRADO*\n+\n+VOLUNTARIOS: 3 para ayudar a los 3 de la organización\n+CONTROL CRONO: 2 personal de cronometraje\n+\n+---\n+\n+*LLEGADA*\n+\n+VOLUNTARIOS: 10 sobrantes para tareas varias\n+CONTROL CRONO: 2 personal de cronometraje\n+\n+TOTAL VOLUNTARIOS: 53\n+\n+\n+Hidratación Running:\n+\n+- Se pasa a los 2.00, 4.25 y 7.75 y 10.30 km\n+- 1° puesto en la llegada y 2° puesto en Gatica al fondo\n+- Mesa y gazebo\n+- En cada puesto: cajones de frutas, 6 bidones de 20L, elementos para cortar y servir fruta, vasos y/o botellas, basurero grande\n+- Charla para los PCs, contactos o grupo whatsapp para coordinar\n+- Armado y desarmado de puestos de hidratación, ubicar a los PCs\n+\n+\n+\n+# ACUATLON\n+\n+## CERRANDO CUENTAS\n+\n+Te paso unas actualizaciones de las cajas:\n+\n+- Bauti me dice que le ingresaron $5.066.055 (lo cual parece bien y obvio confiamos)\n+- O sea que le quedan $506.605 de Comisión para Bauti\n+- Tenemos registrados $3.628.310 de Gastos que hizo\n+- Por lo cual quedan $931.139 que me lo tendría que pasar en efectivo a mí en algún momento\n+- El me dice que en MP tiene $1.108.497, me da miedo que le haya comido mucho MP (eso se configura desde 0% a 6%) pero le quedarían en el peor de los casos $177.358 y en el mejor $506.605\n+- Se va a fijar si le falta pasarme algún pago que hizo y ahí sería más para él.\n+- Con estos números perdimos $1.398.739 entre los dos (o sea que perdimos $699.369 cada uno)\n+- También me dijo que vos tenés lo de Oceano Virgen que según el sistema fue $ 1.013.000,00\n+- O sea que para equiparar cajas, tendrías que pasarme en efectivo $1.405.635\n+\n+\n+## SQL TOTALES\n+\n+- Armar consultas para generar las cajas\n+- Armar consultas para ver cuánta gente hay\n+\n+*Acreditados sin tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'acreditado'\n+AND NOT EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+*Inscriptos con tiempos*\n+\n+SELECT * FROM `participantes`\n+WHERE idevento = 2193\n+AND estado = 'inscripto'\n+AND EXISTS (SELECT * FROM lecturas WHERE idevento = 2193 AND participantes.idparticipante = lecturas.idparticipante)\n+\n+\n+*Cantidades de personas*\n+\n+SELECT\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '') AS individuales,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = 'dupla') AS equipos,\n+(SELECT COUNT(*) FROM `participantes` WHERE idevento = 2193 AND estado = 'acreditado' AND equipo IN ('', 'participante')) AS personas\n+\n+*Cantidades de inscripciones en multiples carreras*\n+SELECT COUNT(*) AS cantidad, idinscripcion FROM categoriasxparticipantes WHERE\n+idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento = 2193 AND estado = 'acreditado' AND equipo = '')\n+GROUP BY idinscripcion\n+ORDER BY cantidad DESC\n+\n+*Carreras de inscripciones en multiples carreras*\n+\n+SELECT categorias.nombre AS cat, idinscripcion\n+FROM categoriasxparticipantes\n+LEFT JOIN categorias ON categorias.idcategoria = categoriasxparticipantes.idcategoria\n+WHERE idinscripcion IN (942722, 990652, 1022249, 971931, 1023832, 1024288, 957888, 1026299, 961954)\n+ORDER BY idinscripcion\n+\n+*Pagos a Bauti*\n+\n+SELECT p.idinscripcion, p.nombre, p.mail, precios.precio\n+FROM pagos\n+LEFT JOIN participantes p ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE pagos.idevento = 2193\n+ORDER BY precios.precio;\n+\n+*Pagos a cada caja*\n+\n+SELECT COUNT(*) AS cantidad, datosxparticipantes.dato AS caja\n+FROM participantes\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = participantes.idinscripcion AND iddato = 'caja'\n+WHERE participantes.idevento = 2193\n+AND estado IN ('inscripto', 'acreditado')\n+GROUP BY datosxparticipantes.dato\n+\n+*Listado de pagos*\n+\n+SELECT idparticipante, nombre, mail, p.estado, equipo, fechapago,\n+(SELECT COUNT(*) FROM categoriasxparticipantes WHERE p.idinscripcion = idinscripcion) AS carreras,\n+datosxparticipantes.dato AS caja, precios.precio, p.observacion\n+FROM participantes p\n+LEFT JOIN datosxparticipantes ON datosxparticipantes.idinscripcion = p.idinscripcion AND iddato = 'caja'\n+LEFT JOIN pagos ON p.idinscripcion = pagos.idinscripcion\n+LEFT JOIN precios ON precios.idprecio = pagos.idprecio\n+WHERE p.idevento = 2193\n+AND p.estado IN ('inscripto', 'acreditado')\n+AND p.equipo != 'participante'\n+ORDER BY caja\n+\n+\n+## LISTAS PARA MAILS\n+\n+> Nadadores Aguas Abiertas de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%')\n+GROUP BY mail;\n+\n+> Nadadores Aguas Abiertas de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 1 AND user_id IN (3, 45, 88, 107))\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%')\n+GROUP BY mail;\n+\n+> Corredores de Neuquén y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE 'neuqu%' OR localidad LIKE 'cipolleti%' OR localidad LIKE 'plottier' OR localidad LIKE 'zapala%' OR localidad LIKE 'cutral%' OR localidad LIKE 'centenario%' OR localidad LIKE '%huincul%' OR localidad LIKE '%roca%')\n+GROUP BY mail;\n+\n+> Corredores de Bariloche y alrededores\n+SELECT nombre, mail, localidad FROM `participantes`\n+WHERE idevento IN (SELECT idevento FROM eventos WHERE fecha > '2024-01-01' AND iddisciplina = 5)\n+AND (localidad LIKE '%bariloche%' OR localidad LIKE '%bolson%' OR localidad LIKE '%angostura%' OR localidad LIKE 'trevelin%' OR localidad LIKE 'san mart%' OR localidad LIKE 'esquel%' OR localidad LIKE '%junin%')\n+GROUP BY mail;\n+\n+\n+\n+## POST EVENTO PARA EL AÑO QUE VIENE\n+\n+- Unificar el reglamento\n+- Escribir manual de procesos completo\n+- Actualizar el sitio web con toda la info (sería genial pasarlo a MicroSitio de Crono)\n+- Hacer un posteo en redes sociales con la info del año que viene\n+- Actualizar documento para la búsqueda de sponsors con capturas y fotos de sus apariciones\n+- Agradecimiento a sponsors\n+  - Posteo\n+  - PDF con estadísticas, enlace a vídeos y fotos, agradecimiento e invitación al año que viene\n+- Conseguir reloj de llegada\n+- Carteles con distancia y aliento en estacas de madera\n+- Poner la Radio y el Streaming en la página\n+- Hacer streaming\n+- Mejorar el protocolo de seguridad: que esté más prolijo, que incluya los horarios de cortes o tiempos máximos de cada etapa, normalizar los diseños de circuitos\n+\n+\n+## NEWSLETTER\n+\n+- [x] Pre inscripto para que no se duerman\n+- [x] Swimrun (los de Chile + viejos): Único evento de Arg y campeonato\n+- [x] Corredores Lolog\n+\n+- [x] Corredores cerca de Neuquén: ¿Corremos cerca de Neuquén el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Neuquén: ¿Nadamos cerca de Neuquén el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [x] Corredores cerca de Bariloche: ¿Corremos cerca de Bariloche el finde que viene? A que nunca corriste en Piedra del Águila\n+- [x] Nadadores cerca de Bariloche: ¿Nadamos cerca de Bariloche el finde que viene? A que nunca nadaste en Piedra del Águila\n+\n+- [ ] Acuatlón (historial) queda poco para la nueva versión del acuatlón que te va a encantar\n+\n+\n+## DOCUMENTO PARA STAFF\n+\n+- Empezar archivos y listados ( https://docs.google.com/document/d/1N0EehRtFJNiIiHad-KgsQf7E26hnR22cxaHhgsynsdU/edit?tab=t.0 )\n+- Remeras staff recuperarlas (prioridad los que salen en las fotos)\n+- Listado de staff, almuerzos y albergues\n+- Venta de remeras 8 o 2x15\n+- Entrega de premios (ordenar y poner cuántos premios se entregan)\n+- chicos en el albergue no hay toalla ni ropa de cama, traigan lo suyo porfa.\n+\n+---\n+\n+## BOYAS\n+\n+Ubicación GPS de las 6 boyas rojas:\n+\n+- Boya A: 40° 3'3.36\"S / 70° 1'24.42\"O\n+- Boya B: 40° 3'3.38\"S / 70° 1'45.87\"O\n+- Boya C: 40° 3'6.25\"S / 70° 1'46.08\"O\n+- Boya D: 40° 3'11.53\"S / 70° 1'13.77\"O\n+- Boya E: 40° 3'10.60\"S / 70° 0'32.97\"O\n+- Boya F: 40° 3'3.45\"S / 70° 1'16.57\"O\n+\n+\n+## CRONOGRAMA Y UBICACIONES\n+\n+Todo es en el Centro Recreativo Kumelkayen ubicado en el Perilago ( excepto lo aclarado )\n+\n+🗺️ *Ubicaciones*\n+\n+📌 Albergue Municipal: Amancay 46 ( https://maps.app.goo.gl/WDGsjNygPhqbeKAV9 )\n+\n+📌 Las 4 Papas se ubica en el ingreso por Lanin 215 ( https://maps.app.goo.gl/vVNu4z8ECJwucsRHA )\n+\n+📌 Centro recreativo Kumelkayen en el Balneario Municipal ( https://maps.app.goo.gl/u8BZgh2K9jeCsJt86 )\n+\n+🗓️ *Viernes 17 de Enero*\n+\n+⏰ 18:00 a 20:00 - Acreditaciones en Albergue Municipal (Amancay 46)\n+\n+🗓️ *Sábado 18 de Enero*\n+\n+⏰ 08:00 a 12:00 - Acreditaciones\n+⏰ 08:45 - Charla técnica para SwimRun en 4 Papas (Lanin 215)\n+⏰ 09:00 - Largada SwimRun en 4 Papas (Lanin 215)\n+\n+⏰ 12:50 - Charla técnica Acuatlón Kids\n+⏰ 13:00 - Largada Acuatlón Kids\n+\n+⏰ 14:00 a 18:00 - Acreditaciones\n+⏰ 14:45 - Charla técnica Aguas Abiertas 4000m\n+⏰ 15:00 - Largada Aguas Abiertas 4000m\n+⏰ 15:15 - Charla técnica Aguas Abiertas 1500m\n+⏰ 15:30 - Largada Aguas Abiertas  1500m\n+\n+⏰ 18:00 - Entrega de Premios SwimRun y Aguas Abiertas\n+\n+⏰ 20:00 - Espectáculos Musicales y Carritos de Comida en Plaza San Martín\n+\n+🗓️ *Domingo 19 de Enero*\n+\n+⏰ 07:00 a 09:00 - Acreditaciones\n+⏰ 09:30 - Charla técnica para Acuatlón Short y Acuatlón Olímpico\n+⏰ 09:45 - Largada Acuatlón Short\n+⏰ 09:50 - Charla técnica para Águila Run\n+⏰ 10:00 - Largada Acuatlón Olímpico, Acuatlón Posta y Águila Run (todas las distancias)\n+\n+⏰ 13:00 - Entrega de Premios Acuatlón y Águila Run\n+⏰ 14:00 - Cierre del evento\n+\n+\n+## CARRERAS PREMIOS\n+\n+- SwimRun Individual 10K\n+  - 2 generales\n+- SwimRun Dupla 10K\n+  - 3 generales\n+\n+- Aguas Abiertas 1500\n+  - 14 categorias\n+- Aguas Abiertas 4000\n+  - 3 generales\n+  - 14 categorias\n+\n+- Acuatlón Olímpico\n+  - 3 generales\n+  - 14 categorias\n+- Acuatlón Posta\n+  - 3 generales\n+- Acuatlón Short\n+  - 3 categorias\n+\n+- Águila Run 5K\n+  - Nada\n+\n+- Águila Run 10K\n+  - 3 generales\n+  - 14 categorias\n+\n+- Águila Run 21K\n+  - 3 generales\n+  - 14 categorias\n+\n+Total generales madera: 20 grupos de 123 de madera\n+Total de categorias: 73 grupos de 123 medalla con calco\n+\n+\n+---\n+\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/wp.swimrun.ar/acuatlon.ar/g' {} +\n+sudo find /var/www/acuatlon/www -type f -exec sed -i 's/https:\\/acuatlon.ar/https:\\/\\/acuatlon.ar/g' {} +\n+\n+rm -r /var/www/acuatlon/www/wp-content/uploads\n+ln -s /var/www/acuatlon/wp/wp-content/uploads /var/www/acuatlon/www/wp-content/uploads\n+\n+http://cronometrajeinstantaneo.lan/inscripciones/acuatlon-fest-2025/OWo2V2VyTlFNSkx2UEFISDQxaXVncFZVQmV0cFBBSzdqS2prTHhORzZOTnl2VUI4NkFKL0JOSmlIak8yMFZzTQ%3D%3D\n+\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/TVY0VmllcFk0WXVLaHhvWWxWK2E2aThNYjdjSVJDM0ozTmRweTVMRzg1ekJZQjQyREhRWHorRGVJOThxdTh0UA%3D%3D\n+\n+Equipo posta:\n+https://cronometrajeinstantaneo.com/inscripciones/acuatlon-fest-2025/dupla/Yk9FNlRIQVVQbytMT1dSY0FYcTgrZmhmL2lXTWRGaDc3ZmliUCtjM3J4SXZwemRaUHdUZm5MS2lyV2lYYWRpdg%3D%3D\n+\n+\n+sudo find /var/www/travesiadeloscerros/public -type f -exec sed -i 's/temp.andresmisiak.ar/travesiadeloscerros.com/g' {} +\n+\n"}, {"date": 1747428031548, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,14 @@\n # TRIA\n \n ## FEEDBACK TRIA\n \n-Cambiar categorías a anual en tria\n+- [ ] Cambiar categorías a anual en tria\n+- [ ] Ver Streaming\n+- [ ] Sitio web\n+- [ ] Sacar ganadores generales de las categorías\n \n+\n # TRIATLÓN\n \n *LARGADAS*\n \n"}], "date": 1726599044899, "name": "Commit-0", "content": "# ACUATLON\n\n## PRECIOS ESCALONADOS\n\n- Primeros 50 inscriptos\n- Individual 1 disciplina $65k\n- Individual 2 disciplinas $95k\n- Individual 3 disciplinas $115k\n- Individual 1 disciplina corta $35k"}]}