{"sourceFile": "BRAIN/ROADS/CRONO/SOPORTE.md", "activeCommit": 0, "commits": [{"activePatchIndex": 139, "patches": [{"date": 1724943644081, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1724944167065, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n \n - Tengo un pago de Claudio ya facturado sin informar\n - <PERSON><PERSON><PERSON> tiene a favor un evento\n \n+\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n@@ -12,8 +13,9 @@\n \n -20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n -30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n \n+\n ## PRECIOS DE KARTING\n \n Abono Sistema Karting x 1 mes: U$D 50\n https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n"}, {"date": 1725108961055, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,10 +1,19 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## AHORA\n+\n+- Llamada Nestor Caballos\n+- Presupuesto Joaco Necochea\n+- Preparar pedido <PERSON>\n+- Preparar pedido <PERSON>\n+- <PERSON><PERSON><PERSON><PERSON>\n+\n - Tengo un pago de Claudio ya facturado sin informar\n - Rodolfo usuahia tiene a favor un evento\n \n+-\n \n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n"}, {"date": 1725109363260, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,14 +2,18 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n+- Chips Chile\n - Llamada Nestor Caballos\n+- <PERSON><PERSON><PERSON><PERSON>\n+\n - Presupuesto Joaco Necochea\n - Preparar pedido <PERSON>\n - Preparar pedido Claudio\n-- <PERSON><PERSON><PERSON><PERSON>\n \n+- Cargar pagos Ecuador\n+\n - Tengo un pago de Claudio ya facturado sin informar\n - <PERSON><PERSON><PERSON> usuahia tiene a favor un evento\n \n -\n"}, {"date": 1725109917451, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,24 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-## AHORA\n \n-- Chips Chile\n-- Llamada Nestor C<PERSON>llo<PERSON>\n-- <PERSON><PERSON><PERSON><PERSON>\n \n-- Presupuesto Joaco Necochea\n-- Preparar pedido <PERSON>\n-- Preparar pedido Claudio\n-\n-- Cargar pagos Ecuador\n-\n-- Tengo un pago de Claudio ya facturado sin informar\n-- <PERSON><PERSON><PERSON> usua<PERSON> tiene a favor un evento\n-\n--\n-\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n"}, {"date": 1725240389235, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -49,9 +49,9 @@\n Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n-UPDATE `tags` SET idorganizacion = 430, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n"}, {"date": 1725243785226, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,13 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## AHORA\n \n+- [ ] Tengo un pago de Claudio ya facturado sin informar, de<PERSON><PERSON> a favor\n+- [ ] <PERSON><PERSON><PERSON> us<PERSON> tiene a favor un evento\n \n+\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n"}, {"date": 1725301069842, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,90 @@\n+# 🥇 ROADS > CRONO > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+## AHORA\n+\n+- [ ] Tengo un pago de Claudio ya facturado sin informar, dejar<PERSON> a favor\n+- [ ] <PERSON><PERSON><PERSON> usua<PERSON> tiene a favor un evento\n+\n+\n+## BOTONES POR PAÍS\n+\n+PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n+COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n+CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n+\n+-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n+-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n+\n+\n+## PRECIOS DE KARTING\n+\n+Abono Sistema Karting x 1 mes: U$D 50\n+https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n+\n+Abono Sistema Karting x 6 meses: U$D 250\n+https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n+\n+Abono Sistema Karting x 12 meses: U$D 400\n+https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n+\n+Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n+\n+\n+\n+## MERCADOPAGO PARA RIDE\n+\n+UPDATE precios SET idplataforma = 6, url = '<a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>' WHERE idprecio = 12;\n+UPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;\n+\n+EX BOTÓN DE LA KAVA: <a href=\"https://mpago.la/1R2pnUB\" target=\"_blank\">Pagar</a>\n+EX BOTÓN DE RIDE: <a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>\n+\n+RIDE:\n+https://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D\n+\n+\n+\n+## CHIPS MOVIDOS\n+\n+Son de ID 4 los míos (Hasta CI0200)\n+UPDATE `tags` SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n+\n+Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n+UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n+\n+Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n+UPDATE `tags` SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+\n+Son de ID 41 de Ecuador-Colombia\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n+\n+Son de ID 84 de Esquel\n+UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n+UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n+\n+\n+ES0000 a <EMAIL> (374)\n+UPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\n+DT000 a <EMAIL> (379)\n+UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n+\n+\n+## CHIPS EN ALTO CENTER KARTING KARTODROMO\n+\n+SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n+\n+<EMAIL>\n+San Juan F1\n+\n+## RESPUESTA YOUTUBE\n+\n+Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n+\n+Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n+\n+https://web.whatsapp.com/send?phone=5492944551009\n+\n"}, {"date": 1725580025035, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -58,8 +58,9 @@\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8323;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n@@ -87,93 +88,4 @@\n Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n \n https://web.whatsapp.com/send?phone=5492944551009\n \n-# 🥇 ROADS > CRONO > SOPORTE\n--------------------------------------------------------------------------------\n-\n-## AHORA\n-\n-- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n-- [ ] Rodolfo usuahia tiene a favor un evento\n-\n-\n-## BOTONES POR PAÍS\n-\n-PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n-COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n-CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n-\n--20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n--30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n-\n-\n-## PRECIOS DE KARTING\n-\n-Abono Sistema Karting x 1 mes: U$D 50\n-https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n-\n-Abono Sistema Karting x 6 meses: U$D 250\n-https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n-\n-Abono Sistema Karting x 12 meses: U$D 400\n-https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n-\n-Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n-\n-\n-\n-## MERCADOPAGO PARA RIDE\n-\n-UPDATE precios SET idplataforma = 6, url = '<a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>' WHERE idprecio = 12;\n-UPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;\n-\n-EX BOTÓN DE LA KAVA: <a href=\"https://mpago.la/1R2pnUB\" target=\"_blank\">Pagar</a>\n-EX BOTÓN DE RIDE: <a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>\n-\n-RIDE:\n-https://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D\n-\n-\n-\n-## CHIPS MOVIDOS\n-\n-Son de ID 4 los míos (Hasta CI0200)\n-UPDATE `tags` SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n-\n-Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n-UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n-\n-Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n-UPDATE `tags` SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n-\n-Son de ID 41 de Ecuador-Colombia\n-UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n-UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n-\n-Son de ID 84 de Esquel\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422);\n-\n-\n-ES0000 a <EMAIL> (374)\n-UPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\n-DT000 a <EMAIL> (379)\n-UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n-\n-\n-## CHIPS EN ALTO CENTER KARTING KARTODROMO\n-\n-SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n-https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n-https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n-\n-<EMAIL>\n-San Juan F1\n-\n-## RESPUESTA YOUTUBE\n-\n-Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n-\n-Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n-\n-https://web.whatsapp.com/send?phone=5492944551009\n-\n"}, {"date": 1725580697080, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -58,9 +58,9 @@\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n-UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8323;\n+UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n"}, {"date": 1725977716406, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -70,9 +70,11 @@\n UPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\n DT000 a <EMAIL> (379)\n UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n \n+Son el ID 482 de <EMAIL>\n \n+\n ## CHIPS EN ALTO CENTER KARTING KARTODROMO\n \n SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n"}, {"date": 1726003172899, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -71,10 +71,10 @@\n DT000 a <EMAIL> (379)\n UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n \n Son el ID 482 de <EMAIL>\n+000000000000000000000001\n \n-\n ## CHIPS EN ALTO CENTER KARTING KARTODROMO\n \n SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n"}, {"date": 1726087392279, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,23 @@\n \n - [ ] Tengo un pago de Claudio ya facturado sin informar, de<PERSON><PERSON> a favor\n - [ ] <PERSON><PERSON><PERSON> usuahia tiene a favor un evento\n \n+## ACTUALIZO APELLIDOS\n \n+UPDATE participantes AS p SET apellido =\n+    (SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\n+WHERE idevento = 2176;\n+\n+UPDATE datosxparticipantes\n+SET dato = DATE_FORMAT(STR_TO_DATE(dato, '%d/%m/%Y'), '%Y-%m-%d')\n+WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n+\n+UPDATE datosxparticipantes\n+SET dato = TRUNCATE(dato, 0)\n+WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n+\n+\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n"}, {"date": 1726087413606, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,9 @@\n WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n \n UPDATE datosxparticipantes\n SET dato = TRUNCATE(dato, 0)\n-WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n+WHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;\n \n \n ## BOTONES POR PAÍS\n \n"}, {"date": 1726152460920, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,11 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n+dale la factura al cuit de mi empresa 30-71745220-4 Bellas sur sas\n+Factura por $60000\n+\n - [ ] Tengo un pago de Claudio ya facturado sin informar, dejar<PERSON> a favor\n - [ ] <PERSON><PERSON><PERSON> us<PERSON> tiene a favor un evento\n \n ## ACTUALIZO APELLIDOS\n"}, {"date": 1726178733432, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,17 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n+.sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {\n+    display: block;\n+}\n+\n+.medalla {\n+    display: none;\n+}\n+\n+\n dale la factura al cuit de mi empresa 30-71745220-4 Bellas sur sas\n Factura por $60000\n \n - [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n"}, {"date": 1726240783480, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,17 @@\n -------------------------------------------------------------------------------\n \n ## AHORA\n \n+Medio de pago Mercadopago\n+Precio 50.000 (pre inscripción)\n+Carreras en las que se aplica (Open Shimano Enduro Las Vegas) O (campeonato Argentino Enduro\n+Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones) no hay limite cantidad.\n+Fecha y hora desde que se habilita este precio desde hoy\n+<PERSON>cha y hora hasta que se deshabilita este precio hasta el día 30/9 23:30hs\n+\n+---\n+\n .sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {\n     display: block;\n }\n \n"}, {"date": 1726240831702, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,10 @@\n Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones) no hay limite cantidad.\n Fecha y hora desde que se habilita este precio desde hoy\n Fecha y hora hasta que se deshabilita este precio hasta el día 30/9 23:30hs\n \n+Y si se puede configurar que ese pago tenga una especie de ID mejor para despues filtrar y hacer las cuentas\n+\n ---\n \n .sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {\n     display: block;\n"}, {"date": 1726252151581, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -27,8 +27,9 @@\n Factura por $60000\n \n - [ ] Tengo un pago de Claudio ya facturado sin informar, de<PERSON><PERSON> a favor\n - [ ] <PERSON><PERSON><PERSON> us<PERSON> tiene a favor un evento\n+- [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n \n ## ACTUALIZO APELLIDOS\n \n UPDATE participantes AS p SET apellido =\n"}, {"date": 1726269450396, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,21 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## VARIOS\n+\n+- [ ] Feedback G<PERSON> Género\n+- [ ] SARR: Logos + Cat\n+- [ ] Sacar texto Mendoza\n+- [ ] Responder Lona a Claudio\n+- [ ] Vídeo explicativo Orozco\n+\n+\n+## ORDENAR\n+\n+- [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n+\n+\n ## AHORA\n \n Medio de pago Mercadopago\n Precio 50.000 (pre inscripción)\n"}, {"date": 1726269515078, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,13 +7,16 @@\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n - [ ] Vídeo explicativo Orozco\n+- [ ] Desactivar enlace Panamá\n \n \n ## ORDENAR\n \n - [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n+- [ ] Buscar pileta\n+- [ ] Cortar árbol mañana\n \n \n ## AHORA\n \n"}, {"date": 1726269528134, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,8 +2,9 @@\n -------------------------------------------------------------------------------\n \n ## VARIOS\n \n+- [ ] Habilitar Beta Multimoneda a Javi\n - [ ] Feedback G<PERSON> G<PERSON>ero\n - [ ] SARR: Logos + Cat\n - [ ] <PERSON><PERSON> texto Mendoza\n - [ ] Responder <PERSON>na a <PERSON>\n"}, {"date": 1726269629204, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,13 +4,15 @@\n ## VARIOS\n \n - [ ] Habilitar Beta Multimoneda a Javi\n - [ ] Feedback Gaby Género\n+- [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n - [ ] Vídeo explicativo Orozco\n - [ ] Desactivar enlace Panamá\n+- [ ] Configurar botones Javi Ushuaia y ver presupuesto\n \n \n ## ORDENAR\n \n"}, {"date": 1726271493459, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,9 +3,14 @@\n \n ## VARIOS\n \n - [ ] Habilitar Beta Multimoneda a Javi\n+- [ ] Recuperar tu tools\n+\n - [ ] Feedback Gaby Género\n+- [ ] Pedido Colombia\n+- [ ] Reunion España rosfit\n+- [ ] Factura 60 a Gabriela\n - [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n"}, {"date": 1726273560251, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,12 +2,13 @@\n -------------------------------------------------------------------------------\n \n ## VARIOS\n \n-- [ ] Habilitar Beta Multimoneda a Javi\n-- [ ] Recuperar tu tools\n+- [x] Habilitar Beta Multimoneda a Javi\n+- [x] Recuperar tu tools\n \n - [ ] Feedback <PERSON><PERSON>\n+- [ ] Aprobar pago Venezuela y comprar BTC\n - [ ] Pedido Colombia\n - [ ] Reunion España rosfit\n - [ ] Factura 60 a Gabriela\n - [ ] Configurar botones de pago Ride\n"}, {"date": 1726275293234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,16 +7,17 @@\n - [x] Recuperar tu tools\n \n - [ ] Feedback <PERSON><PERSON>\n - [ ] Aprobar pago Venezuela y comprar BTC\n-- [ ] Pedido Colombia\n-- [ ] Reunion España rosfit\n-- [ ] Factura 60 a Gabriela\n - [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n+\n - [ ] Vídeo explicativo Orozco\n+- [ ] Pedido Colombia\n+- [ ] Reunion España rosfit\n+- [ ] Factura 60 a Gabriela\n - [ ] Desactivar enlace Panamá\n - [ ] Configurar botones Javi Ushuaia y ver presupuesto\n \n \n@@ -137,17 +138,18 @@\n \n Son el ID 482 de <EMAIL>\n 000000000000000000000001\n \n-## CHIPS EN ALTO CENTER KARTING KARTODROMO\n+## CHIPS EN KARTING KARTODROMO\n \n SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n \n <EMAIL>\n San Juan F1\n \n+\n ## RESPUESTA YOUTUBE\n \n Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n \n"}, {"date": 1726275387477, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,9 @@\n \n - [x] Habilitar Beta Multimoneda a Javi\n - [x] Recuperar tu tools\n \n+- [ ] Arreglar chips Kart Chile\n - [ ] Feedback Gaby Género\n - [ ] Aprobar pago Venezuela y comprar BTC\n - [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n"}, {"date": 1726275410097, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,9 +5,9 @@\n \n - [x] Habilitar Beta Multimoneda a Javi\n - [x] Recuperar tu tools\n \n-- [ ] Arreglar chips Kart Chile\n+- [ ] Arreglar chips Kart Chile (anotar que debe septiembre)\n - [ ] Feedback Gaby Género\n - [ ] Aprobar pago Venezuela y comprar BTC\n - [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n"}, {"date": 1726278539208, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,21 +5,21 @@\n \n - [x] Habilitar Beta Multimoneda a Javi\n - [x] Recuperar tu tools\n \n-- [ ] Arreglar chips Kart Chile (anotar que debe septiembre)\n+- [x] Arreglar chips Kart Chile (anotar que debe septiembre)\n+- [x] Aprobar pago Venezuela y comprar BTC\n+- [ ] Configurar botones de pago Ride\n - [ ] Feedback Gaby Género\n-- [ ] Aprobar pago Venezuela y comprar BTC\n-- [ ] Configurar botones de pago Ride\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n \n - [ ] Vídeo explicativo Orozco\n - [ ] Pedido Colombia\n - [ ] Reunion España rosfit\n - [ ] Factura 60 a Gabriela\n-- [ ] Desactivar enlace Panamá\n+- [x] Desactivar enlace Panamá\n - [ ] Configurar botones Javi Ushuaia y ver presupuesto\n \n \n ## ORDENAR\n@@ -148,9 +148,12 @@\n \n <EMAIL>\n San Juan F1\n \n+CHILE:\n \n+idorganizacion = 472;\n+\n ## RESPUESTA YOUTUBE\n \n Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n \n"}, {"date": 1726315898639, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,10 +7,12 @@\n - [x] Recuperar tu tools\n \n - [x] Arreglar chips Kart Chile (anotar que debe septiembre)\n - [x] Aprobar pago Venezuela y comprar BTC\n+\n+- [ ] Feedback <PERSON><PERSON>\n - [ ] Configurar botones de pago Ride\n-- [ ] Feedback <PERSON><PERSON>\n+\n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder <PERSON>na a <PERSON>\n \n"}, {"date": 1726317581328, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -28,8 +28,9 @@\n \n - [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n - [ ] Buscar pileta\n - [ ] Cortar árbol mañana\n+- [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n \n \n ## AHORA\n \n"}, {"date": 1726318101236, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,21 +7,24 @@\n - [x] Recuperar tu tools\n \n - [x] Arreglar chips Kart Chile (anotar que debe septiembre)\n - [x] Aprobar pago Venezuela y comprar BTC\n+- [x] Desactivar enlace Panamá\n \n - [ ] Feedback Gaby Género\n+- [ ] Ver que terminas haciendo con Nequi\n - [ ] Configurar botones de pago Ride\n \n - [ ] SARR: Logos + Cat\n - [ ] Sacar texto Mendoza\n - [ ] Responder Lona a Claudio\n-\n - [ ] Vídeo explicativo Orozco\n - [ ] Pedido Colombia\n+\n - [ ] Reunion España rosfit\n - [ ] Factura 60 a Gabriela\n-- [x] Desactivar enlace Panamá\n+- [ ] Ordenar Todoist\n+\n - [ ] Configurar botones Javi Ushuaia y ver presupuesto\n \n \n ## ORDENAR\n"}, {"date": 1726318995334, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,18 +8,20 @@\n \n - [x] Arreglar chips Kart Chile (anotar que debe septiembre)\n - [x] Aprobar pago Venezuela y comprar BTC\n - [x] Desactivar enlace Panamá\n+- [x] Sacar texto Mendoza\n \n - [ ] Feedback Gaby Género\n+\n+- [ ] Responder Lona a Claudio\n+\n - [ ] Ver que terminas haciendo con Nequi\n - [ ] Configurar botones de pago Ride\n \n - [ ] SARR: Logos + Cat\n-- [ ] Sacar texto Mendoza\n-- [ ] Responder Lona a Claudio\n+- [ ] Pedido Colombia\n - [ ] Vídeo explicativo Orozco\n-- [ ] Pedido Colombia\n \n - [ ] Reunion España rosfit\n - [ ] Factura 60 a Gabriela\n - [ ] Ordenar Todoist\n"}, {"date": 1726320365877, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,12 +10,11 @@\n - [x] Aprobar pago Venezuela y comprar BTC\n - [x] Desactivar enlace Panamá\n - [x] Sacar texto Mendoza\n \n-- [ ] Feedback <PERSON><PERSON>\n+- [x] Feedback <PERSON><PERSON>\n+- [x] Responder <PERSON><PERSON> a Claudio\n \n-- [ ] Responder Lona a Claudio\n-\n - [ ] Ver que terminas haciendo con Nequi\n - [ ] Configurar botones de pago Ride\n \n - [ ] SARR: Logos + Cat\n"}, {"date": 1726323536234, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,10 @@\n \n - [x] Feedback <PERSON><PERSON>\n - [x] Responder Lona a Claudio\n \n+- [ ] Procesar tus audios\n+\n - [ ] Ver que terminas haciendo con Nequi\n - [ ] Configurar botones de pago Ride\n \n - [ ] SARR: Logos + Cat\n"}, {"date": 1726331613689, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -23,9 +23,9 @@\n - [ ] Pedido Colombia\n - [ ] Vídeo explicativo Orozco\n \n - [ ] Reunion España rosfit\n-- [ ] Factura 60 a Gabriela\n+- [x] Factura 60 a Gabriela\n - [ ] Ordenar Todoist\n \n - [ ] Configurar botones Javi Ushuaia y ver presupuesto\n \n"}, {"date": 1726405816897, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,23 +12,23 @@\n - [x] <PERSON>car texto <PERSON>\n \n - [x] Feedback <PERSON><PERSON>\n - [x] Responder Lona a <PERSON>\n+- [x] Factura 60 a Gabriela\n \n-- [ ] Procesar tus audios\n-\n-- [ ] Ver que terminas haciendo con Nequi\n+- [x] Reunion España rosfit\n+- [ ] Configurar botones Javi Ushuaia y ver presupuesto\n - [ ] Configurar botones de pago Ride\n-\n - [ ] SARR: Logos + Cat\n - [ ] Pedido Colombia\n+- [ ] Ver que terminas haciendo con Nequi\n - [ ] Vídeo explicativo Orozco\n \n-- [ ] Reunion España rosfit\n-- [x] Factura 60 a Gabriela\n+- [ ] Procesar tus audios\n - [ ] Ordenar Todoist\n \n-- [ ] Configurar botones Javi Ushuaia y ver presupuesto\n+- [ ] Soporte SaaS\n+- [ ] Acuatlón\n \n \n ## ORDENAR\n \n"}, {"date": 1726418838661, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,55 +15,34 @@\n - [x] Responder Lona a Claudio\n - [x] Factura 60 a Gabriela\n \n - [x] Reunion España rosfit\n-- [ ] Configurar botones Javi Ushuaia y ver presupuesto\n-- [ ] Configurar botones de pago Ride\n-- [ ] SARR: Logos + Cat\n-- [ ] Pedido Colombia\n-- [ ] Ver que terminas haciendo con Nequi\n-- [ ] Vídeo explicativo Orozco\n+- [x] Configurar botones de pago Ride\n+- [x] Pedido Colombia\n+- [x] SARR: Logos + Cat\n+- [x] Configurar botones Javi Ushuaia\n \n+- [ ] Buscar pileta\n+- [ ] Cortar árbol mañana\n - [ ] Procesar tus audios\n+- [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n - [ ] Ordenar Todoist\n+- [ ] MailZero\n \n+- [ ] Ver que terminas haciendo con Nequi\n+- [ ] Vídeo explicativo Orozco\n+\n - [ ] Soporte SaaS\n - [ ] Acuatlón\n \n \n ## ORDENAR\n \n-- [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n-- [ ] Buscar pileta\n-- [ ] Cortar árbol mañana\n - [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n \n \n ## AHORA\n \n-Medio de pago Mercadopago\n-Precio 50.000 (pre inscripción)\n-Carreras en las que se aplica (Open Shimano Enduro Las Vegas) O (campeonato Argentino Enduro\n-Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones) no hay limite cantidad.\n-Fecha y hora desde que se habilita este precio desde hoy\n-Fecha y hora hasta que se deshabilita este precio hasta el día 30/9 23:30hs\n-\n-Y si se puede configurar que ese pago tenga una especie de ID mejor para despues filtrar y hacer las cuentas\n-\n----\n-\n-.sin_medalla, .nacionalidad_nombre, #informe_generales .sexo, #informe_categorias .sexo, #informe_etapas .sexo {\n-    display: block;\n-}\n-\n-.medalla {\n-    display: none;\n-}\n-\n-\n-dale la factura al cuit de mi empresa 30-71745220-4 Bellas sur sas\n-Factura por $60000\n-\n - [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n - [ ] Rodolfo usuahia tiene a favor un evento\n - [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n \n"}, {"date": 1726418982259, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,10 +20,8 @@\n - [x] Pedido Colombia\n - [x] SARR: Logos + Cat\n - [x] Configurar botones Javi Ushuaia\n \n-- [ ] Buscar pileta\n-- [ ] Cortar árbol mañana\n - [ ] Procesar tus audios\n - [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n - [ ] Ordenar Todoist\n - [ ] MailZero\n"}, {"date": 1726420837993, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,16 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## PRE SOPORTE\n+\n+- Tus prioridades reales son\n+    URGENTE: Servidor caído o Error que genere inconsistencia\n+    IMPORTANTE: Próxima prioridad de DEV y después Próxima prioridad de GROWTH\n+    CLIENTES: Procesar Soporte a cliente y Procesar Ventas\n+\n+\n+\n ## VARIOS\n \n - [x] Habilitar Beta Multimoneda a Javi\n - [x] Recuperar tu tools\n"}, {"date": 1726424961160, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,8 +36,9 @@\n - [ ] MailZero\n \n - [ ] Ver que terminas haciendo con Nequi\n - [ ] Vídeo explicativo Orozco\n+- [ ] Mide 5 de ancho por 3.40 de alto\n \n - [ ] Soporte SaaS\n - [ ] Acuatlón\n \n"}, {"date": 1726495651904, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,15 @@\n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n     IMPORTANTE: Próxima prioridad de DEV y después Próxima prioridad de GROWTH\n     CLIENTES: Procesar Soporte a cliente y Procesar Ventas\n+- Siempre responder con buena onda, agradecer el contacto y delegar si se puede.\n+- Si no se puede la respuesta es que estás con mucho trabajo, te lo vas a anotar y lo vas a responder en cuanto puedas.\n+- Si es desarrollo directamente decir que este año ya no entra en el roadmap y con las prioridades actuales.\n+- Reformular que en DEV = contruyendo productos, en GROWTH = construyendo marca, en SOPORTE = construyendo clientes, en CURSOS = construyendo equipo. El soporte no debe tender a cero, sino balancearse en una cantidad de tiempo y tender a que lo que te llegue sea cada vez más avanzado, delegando lo otro al equipo.\n+- No necesitas responder todo, NUNCA DAR FECHA, y si te apuran mensaje de \"Te aviso en cuanto tenga alguna novedad\".\n \n \n-\n ## VARIOS\n \n - [x] Habilitar Beta Multimoneda a Javi\n - [x] Recuperar tu tools\n"}, {"date": 1726514399883, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,52 +13,15 @@\n - Reformular que en DEV = contruyendo productos, en GROWTH = construyendo marca, en SOPORTE = construyendo clientes, en CURSOS = construyendo equipo. El soporte no debe tender a cero, sino balancearse en una cantidad de tiempo y tender a que lo que te llegue sea cada vez más avanzado, delegando lo otro al equipo.\n - No necesitas responder todo, NUNCA DAR FECHA, y si te apuran mensaje de \"Te aviso en cuanto tenga alguna novedad\".\n \n \n-## VARIOS\n+## ADMIN\n \n-- [x] Habilitar Beta Multimoneda a Javi\n-- [x] Recuperar tu tools\n-\n-- [x] Arreglar chips Kart Chile (anotar que debe septiembre)\n-- [x] Aprobar pago Venezuela y comprar BTC\n-- [x] Desactivar enlace Panamá\n-- [x] Sacar texto <PERSON>\n-\n-- [x] Feedback <PERSON><PERSON>\n-- [x] Responder Lona a Claudio\n-- [x] Factura 60 a Gabriela\n-\n-- [x] Reunion España rosfit\n-- [x] Configurar botones de pago Ride\n-- [x] Pedido Colombia\n-- [x] SARR: Logos + Cat\n-- [x] Configurar botones Javi Ushuaia\n-\n-- [ ] Procesar tus audios\n-- [ ] Frameworks Ayuda: Curso y Notas (Crono) / FAQ Público (SaaS)\n-- [ ] Ordenar Todoist\n-- [ ] MailZero\n-\n-- [ ] Ver que terminas haciendo con Nequi\n-- [ ] Vídeo explicativo Orozco\n-- [ ] Mide 5 de ancho por 3.40 de alto\n-\n-- [ ] Soporte SaaS\n-- [ ] Acuatlón\n-\n-\n-## ORDENAR\n-\n-- [ ] Idea para unificar todos los informes de resultados en uno solo (pensar en ordenar por puntos y cuando tengamos tiempos ya cargados)\n-\n-\n-## AHORA\n-\n - [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n - [ ] Rodolfo usuahia tiene a favor un evento\n - [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n \n+\n ## ACTUALIZO APELLIDOS\n \n UPDATE participantes AS p SET apellido =\n     (SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\n"}, {"date": 1726605719849, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,20 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## COBRAR RIDE\n+\n+Estos son los eventos (los que pusiste vos):\n+\n+2024-10-12 \tCampeonato Argentino de Enduro 2024   \n+2024-10-12 \tOSE #4 - Mendoza 2024   \n+2024-09-15 \tCasi Enduro   \n+2024-08-31 \tOSE #3 - San Javier 2024   \n+2024-05-25 \tOSE #2 - Las Pircas 2024   \n+\n+Hoy está $120k - 20% = $96k * 5 = $480k pero como son varios les hago más descuento y podemos dejar los 5 en $400k.\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Ser<PERSON><PERSON> caído o Error que genere inconsistencia\n"}, {"date": 1726605799702, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,15 +4,15 @@\n ## COBRAR RIDE\n \n Estos son los eventos (los que pusiste vos):\n \n-2024-10-12 \tCampeonato Argentino de Enduro 2024   \n-2024-10-12 \tOSE #4 - Mendoza 2024   \n-2024-09-15 \tCasi Enduro   \n-2024-08-31 \tOSE #3 - San Javier 2024   \n-2024-05-25 \tOSE #2 - Las Pircas 2024   \n+2024-10-12 \tCampeonato Argentino de Enduro 2024\n+2024-10-12 \tOSE #4 - Mendoza 2024\n+2024-09-15 \tCasi Enduro\n+2024-08-31 \tOSE #3 - San Javier 2024\n+2024-05-25 \tOSE #2 - Las Pircas 2024\n \n-Hoy está $120k - 20% = $96k * 5 = $480k pero como son varios les hago más descuento y podemos dejar los 5 en $400k.\n+Pero el Argentino va junto con el OSE #4, o sea que son 4 eventos. Hoy está $120k - 20% = $96k * 4 = $384k\n \n \n ## PRE SOPORTE\n \n"}, {"date": 1726662814281, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,20 +1,7 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-## COBRAR RIDE\n-\n-Estos son los eventos (los que pusiste vos):\n-\n-2024-10-12 \tCampeonato Argentino de Enduro 2024\n-2024-10-12 \tOSE #4 - Mendoza 2024\n-2024-09-15 \tCasi Enduro\n-2024-08-31 \tOSE #3 - San Javier 2024\n-2024-05-25 \tOSE #2 - Las Pircas 2024\n-\n-Pero el Argentino va junto con el OSE #4, o sea que son 4 eventos. Hoy está $120k - 20% = $96k * 4 = $384k\n-\n-\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1726663093174, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -60,21 +60,8 @@\n Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n \n \n \n-## MERCADOPAGO PARA RIDE\n-\n-UPDATE precios SET idplataforma = 6, url = '<a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>' WHERE idprecio = 12;\n-UPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;\n-\n-EX BOTÓN DE LA KAVA: <a href=\"https://mpago.la/1R2pnUB\" target=\"_blank\">Pagar</a>\n-EX BOTÓN DE RIDE: <a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>\n-\n-RIDE:\n-https://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D\n-\n-\n-\n ## CHIPS MOVIDOS\n \n Son de ID 4 los míos (Hasta CI0200)\n UPDATE `tags` SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n@@ -82,8 +69,9 @@\n Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n+UPDATE `tags` SET idorganizacion = 457, idevento = 1992, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n UPDATE `tags` SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n"}, {"date": 1727723869611, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -78,10 +78,10 @@\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n-UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 358, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n \n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\n"}, {"date": 1727959544947, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,24 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## VARIOS AHORA\n+\n+- [x] <PERSON>gar <PERSON>\n+- [x] <PERSON>dar a Lisa el pedido de Chile\n+- [x] Links de Federados a Rally BsAs\n+- [x] Subir logo RVM y uno más\n+- [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n+- [ ] Mover chips Claudio (los míos y los de él)\n+- [ ] Agregar el último precio a Ride\n+\n+## PASAR A TODOIST\n+\n+- <PERSON><PERSON><PERSON> va a tener mi fotocélulas cuando las reciba de Maribel\n+- Claudio:\n+  - Le entregué todos mis chips con straps\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1727959698680, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n - [x] Subir logo RVM y uno más\n - [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n - [ ] Mover chips Claudio (los míos y los de él)\n - [ ] Agregar el último precio a Ride\n+- [ ] Ver si cargaste gasto impresora\n \n ## PASAR A TODOIST\n \n - Cristian va a tener mi fotocélulas cuando las reciba de Maribel\n"}, {"date": 1728044855296, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,9 +10,11 @@\n - [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n - [ ] Mover chips Claudio (los míos y los de él)\n - [ ] Agregar el último precio a Ride\n - [ ] Ver si cargaste gasto impresora\n+- [ ] Cargar pago Colombia\n \n+\n ## PASAR A TODOIST\n \n - Cristian va a tener mi fotocélulas cuando las reciba de Maribel\n - Claudio:\n"}, {"date": 1728044918646, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,8 +11,9 @@\n - [ ] Mover chips <PERSON> (los míos y los de él)\n - [ ] Agregar el último precio a Ride\n - [ ] Ver si cargaste gasto impresora\n - [ ] Cargar pago Colombia\n+- [ ] Ver si está la carrera de Enduro Serrano\n \n \n ## PASAR A TODOIST\n \n"}, {"date": 1728045287859, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -8,8 +8,9 @@\n - [x] Links de Federados a Rally BsAs\n - [x] Subir logo RVM y uno más\n - [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n - [ ] Mover chips Claudio (los míos y los de él)\n+- [ ] Codigos con parche para pumptrack\n - [ ] Agregar el último precio a Ride\n - [ ] Ver si cargaste gasto impresora\n - [ ] Cargar pago Colombia\n - [ ] Ver si está la carrera de Enduro Serrano\n"}, {"date": 1728062801994, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -12,9 +12,11 @@\n - [ ] Codigos con parche para pumptrack\n - [ ] Agregar el último precio a Ride\n - [ ] Ver si cargaste gasto impresora\n - [ ] Cargar pago Colombia\n+\n - [ ] Ver si está la carrera de Enduro Serrano\n+- [ ] Poner Nombre y Apellido al listado de participantes\n \n \n ## PASAR A TODOIST\n \n"}, {"date": 1728136130891, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,11 +6,12 @@\n - [x] <PERSON><PERSON>\n - [x] Mandar a Lisa el pedido de Chile\n - [x] Links de Federados a Rally BsAs\n - [x] Subir logo RVM y uno más\n+- [ ] Codigos con parche para pumptrack\n - [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n - [ ] Mover chips Claudio (los míos y los de él)\n-- [ ] Codigos con parche para pumptrack\n+\n - [ ] Agregar el último precio a Ride\n - [ ] Ver si cargaste gasto impresora\n - [ ] Cargar pago Colombia\n \n@@ -87,16 +88,16 @@\n \n ## CHIPS MOVIDOS\n \n Son de ID 4 los míos (Hasta CI0200)\n-UPDATE `tags` SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n-UPDATE `tags` SET idorganizacion = 457, idevento = 1992, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 445, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n"}, {"date": 1728137073401, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,15 +6,15 @@\n - [x] <PERSON><PERSON>\n - [x] Mandar a Lisa el pedido de Chile\n - [x] Links de Federados a Rally BsAs\n - [x] Subir logo RVM y uno más\n-- [ ] Codigos con parche para pumptrack\n-- [ ] Ocurlar botón de generales en los resultados por filtros de Chile\n-- [ ] Mover chips Claudio (los míos y los de él)\n+- [x] Codigos con parche para pumptrack\n+- [x] Mover chips Claudio (los míos y los de él)\n+- [x] Ocurlar botón de generales en los resultados por filtros de Chile\n \n - [ ] Agregar el último precio a Ride\n+- [ ] Cargar pago Colombia\n - [ ] Ver si cargaste gasto impresora\n-- [ ] Cargar pago Colombia\n \n - [ ] Ver si está la carrera de Enduro <PERSON>no\n - [ ] Poner Nombre y Apellido al listado de participantes\n \n"}, {"date": 1728234846822, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n \n - [ ] Agregar el último precio a Ride\n - [ ] Cargar pago Colombia\n - [ ] Ver si cargaste gasto impresora\n+- [ ] Importar participantes Torneo Itinerante\n \n - [ ] Ver si está la carrera de Enduro Serrano\n - [ ] Poner Nombre y Apellido al listado de participantes\n \n"}, {"date": 1728236204249, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,9 +18,11 @@\n \n - [ ] Ver si está la carrera de Enduro Serrano\n - [ ] Poner Nombre y Apellido al listado de participantes\n \n+- [ ] Debo chips a Joaco\n \n+\n ## PASAR A TODOIST\n \n - Cristian va a tener mi fotocélulas cuando las reciba de Maribel\n - Claudio:\n"}, {"date": 1728237468364, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -18,11 +18,9 @@\n \n - [ ] Ver si está la carrera de Enduro Serrano\n - [ ] Poner Nombre y Apellido al listado de participantes\n \n-- [ ] Debo chips a Joaco\n \n-\n ## PASAR A TODOIST\n \n - Cristian va a tener mi fotocélulas cuando las reciba de Maribel\n - Claudio:\n"}, {"date": 1728325010745, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -10,15 +10,14 @@\n - [x] Codigos con parche para pumptrack\n - [x] Mover chips <PERSON> (los míos y los de él)\n - [x] Ocurlar botón de generales en los resultados por filtros de Chile\n \n-- [ ] Agregar el último precio a Ride\n+- [x] Agregar el último precio a Ride\n+- [x] Ver si cargaste gasto impresora\n - [ ] Cargar pago Colombia\n-- [ ] Ver si cargaste gasto impresora\n - [ ] Importar participantes Torneo Itinerante\n \n - [ ] Ver si está la carrera de Enduro Serrano\n-- [ ] Poner Nombre y Apellido al listado de participantes\n \n \n ## PASAR A TODOIST\n \n"}, {"date": 1728347921331, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,32 +1,11 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-## VARIOS AHORA\n \n-- [x] Pagar <PERSON>\n-- [x] <PERSON>dar a Lisa el pedido de Chile\n-- [x] Links de Federados a Rally BsAs\n-- [x] Subir logo RVM y uno más\n-- [x] Codigos con parche para pumptrack\n-- [x] Mover chips Claudio (los míos y los de él)\n-- [x] Ocurlar botón de generales en los resultados por filtros de Chile\n+- [ ] Cargar pago Colombia: PROINSALUD - CARRERA ATLETICA 10K (ID 2124)\n \n-- [x] Agregar el último precio a Ride\n-- [x] Ver si cargaste gasto impresora\n-- [ ] Cargar pago Colombia\n-- [ ] Importar participantes Torneo Itinerante\n \n-- [ ] Ver si está la carrera de Enduro Serrano\n-\n-\n-## PASAR A TODOIST\n-\n-- Cristian va a tener mi fotocélulas cuando las reciba de Maribel\n-- Claudio:\n-  - Le entregué todos mis chips con straps\n-\n-\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1728390768712, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n \n-- [ ] Cargar pago Colombia: PROINSALUD - CARRERA ATLETICA 10K (ID 2124)\n \n-\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1728492276641, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,24 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## EVENTOS PENDIENTES DE PAGO\n \n+DAMIAN:\n+2024-08-18 \tDUATLON LA FALDA\n+2024-09-14 \tYABOTY ULTRAMARATON 2024\n \n+RIDE:\n+2024-08-31 \tOSE #3 - San Javier 2024\n+2024-09-15 \tC<PERSON> Enduro\n+\n+CLAUDIO:\n+2024-09-22 \tPrueba huinganco\n+\n+JUAN CERDA:\n+2024-08-29 \tNinwist<PERSON>_bike_tour\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Ser<PERSON>or caído o <PERSON>rror que genere inconsistencia\n"}, {"date": 1728525172966, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -52,8 +52,13 @@\n SET dato = TRUNCATE(dato, 0)\n WHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;\n \n \n+## EVENTOS PENDIENTES ECUADOR\n+\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND pago = 0 ORDER BY fecha;\n+\n+\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n"}, {"date": 1728526285044, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -92,9 +92,10 @@\n UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n"}, {"date": 1728563436520, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,8 +15,9 @@\n 2024-09-22 \tPrueba huinganco\n \n JUAN CERDA:\n 2024-08-29 \tNinwistler_bike_tour\n+Karting\n \n \n ## PRE SOPORTE\n \n"}, {"date": 1728576393005, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -107,9 +107,9 @@\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n \n \n ES0000 a <EMAIL> (374)\n-UPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\n+UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n DT000 a <EMAIL> (379)\n UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n \n Son el ID 482 de <EMAIL>\n"}, {"date": 1728595694547, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,9 +17,11 @@\n JUAN CERDA:\n 2024-08-29 \tNi<PERSON><PERSON><PERSON><PERSON>_bike_tour\n Karting\n \n+Se resetió el evento SILOE DOWNHILL & HARD ENDURO (ID 1172) de la organización MTB Carreras (<EMAIL>) con fecha 2022-12-29.\n \n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1728600679172, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,11 +17,11 @@\n JUAN CERDA:\n 2024-08-29 \tNi<PERSON><PERSON><PERSON><PERSON>_bike_tour\n Karting\n \n-Se resetió el evento SILOE DOWNHILL & HARD ENDURO (ID 1172) de la organización MTB Carreras (<EMAIL>) con fecha 2022-12-29.\n+QUATRO VIENTOS:\n+2 eventos\n \n-\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1728660281940, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -11,14 +11,17 @@\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n \n CLAUDIO:\n-2024-09-22 \tPrueba huinganco\n+2024-09-22 \tPrueba huinganco (VACÍO)\n \n JUAN CERDA:\n-2024-08-29 \tNinwistler_bike_tour\n+2024-08-29 \tNinwistler_bike_tour (VACÍO)\n Karting\n \n+ECUADOR:\n+2024-09-08 \tDESAFIO ROCKGARDEn (VACÍO)\n+\n QUATRO VIENTOS:\n 2 eventos\n \n ## PRE SOPORTE\n"}, {"date": 1728660536415, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,8 +9,9 @@\n \n RIDE:\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n+2024-10-12 \tCampeonato Argentino de Enduro 2024 (VACÍO)\n \n CLAUDIO:\n 2024-09-22 \tPrueba huinganco (VACÍO)\n \n"}, {"date": 1728680957506, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,11 @@\n \n QUATRO VIENTOS:\n 2 eventos\n \n+BORRAR: 2234\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1729112123110, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -98,9 +98,10 @@\n Son de ID 4 los míos (Hasta CI0200)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n-UPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n+UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n+UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n Son de ID 445 de Cronometraje Instantáneo <PERSON> (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n"}, {"date": 1729199370013, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -9,9 +9,8 @@\n \n RIDE:\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n-2024-10-12 \tCampeonato Argentino de Enduro 2024 (VACÍO)\n \n CLAUDIO:\n 2024-09-22 \tPrueba huinganco (VACÍO)\n \n@@ -24,9 +23,9 @@\n \n QUATRO VIENTOS:\n 2 eventos\n \n-BORRAR: 2234\n+BORRAR: 2234, 2256, 2177\n \n \n ## PRE SOPORTE\n \n"}, {"date": 1729293430144, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -15,19 +15,28 @@\n 2024-09-22 \t<PERSON><PERSON><PERSON> (VACÍO)\n \n JUAN CERDA:\n 2024-08-29 \tNinwi<PERSON>ler_bike_tour (VACÍO)\n+2024-04-14 (<PERSON><PERSON>)\n+Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n+\n+2024-05-19 (<PERSON><PERSON>)\n+COIHUERACE ANIVERSARIO 2024 \tcoihuerace-aniversario-2024\n+\n+2024-08-29 (Este evento no fue usado y por ende no hay que pagarlo, ¿Quieres que lo elimine?)\n+Ninwistler_bike_tour\n+\n+2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n+4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n Karting\n \n ECUADOR:\n 2024-09-08 \tDESAFIO ROCKGARDEn (VACÍO)\n \n QUATRO VIENTOS:\n 2 eventos\n \n-BORRAR: 2234, 2256, 2177\n \n-\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1729548101625, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,9 +34,16 @@\n \n QUATRO VIENTOS:\n 2 eventos\n \n+## CHIPS ESQUEL\n \n+Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n+Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n+Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n+\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1729548466042, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -114,9 +114,8 @@\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n-UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n@@ -128,11 +127,11 @@\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n-\n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n DT000 a <EMAIL> (379)\n UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n"}, {"date": 1729718272256, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,19 +2,22 @@\n -------------------------------------------------------------------------------\n \n ## EVENTOS PENDIENTES DE PAGO\n \n+Datos de facturación: club del progreso SCyD\n+CUIT 30-56299016-6\n+Dirección Mitre 987\n+General Roca RN\n+\n+\n DAMIAN:\n 2024-08-18 \tDUATLON LA FALDA\n 2024-09-14 \tYABOTY ULTRAMARATON 2024\n \n RIDE:\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n \n-CLAUDIO:\n-2024-09-22 \tP<PERSON><PERSON> huinganco (VACÍO)\n-\n JUAN CERDA:\n 2024-08-29 \tNinwistler_bike_tour (VACÍO)\n 2024-04-14 (<PERSON><PERSON>)\n Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n"}, {"date": 1729720130731, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,14 +2,9 @@\n -------------------------------------------------------------------------------\n \n ## EVENTOS PENDIENTES DE PAGO\n \n-Datos de facturación: club del progreso SCyD\n-CUIT 30-56299016-6\n-<PERSON><PERSON>cción Mitre 987\n-General Roca RN\n \n-\n DAMIAN:\n 2024-08-18 \tDUATLON LA FALDA\n 2024-09-14 \tYABOTY ULTRAMARATON 2024\n \n"}, {"date": 1729774557589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -29,11 +29,14 @@\n \n ECUADOR:\n 2024-09-08 \tDESAFIO ROCKGARDEn (VACÍO)\n \n-QUATRO VIENTOS:\n-2 eventos\n+Cambio en evento\n \n+Organización: La Kava Cronometra (<EMAIL> | ID 465)\n+Nombre anterior: Enduro La Kava Test (ID 2059)\n+Nombre nuevo: Desafio Enduro Race Paraná 2024\n+\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n"}, {"date": 1729870593589, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,8 @@\n -------------------------------------------------------------------------------\n \n ## EVENTOS PENDIENTES DE PAGO\n \n-\n DAMIAN:\n 2024-08-18 \tDUATLON LA FALDA\n 2024-09-14 \tYABOTY ULTRAMARATON 2024\n \n"}, {"date": 1729885809109, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -80,8 +80,9 @@\n \n ## EVENTOS PENDIENTES ECUADOR\n \n SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND pago = 0 ORDER BY fecha;\n+https://cronometrajeinstantaneo.com/scripts/aprobarpago.php?check=jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw&idevento=2148&forma=paypal&aprobar=1\n \n \n ## BOTONES POR PAÍS\n \n"}, {"date": 1729955901085, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -118,9 +118,9 @@\n UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 477, idevento = 2092, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n"}, {"date": 1730037696224, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -41,9 +41,15 @@\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n \n \n+## CHIPS CLAUDIO CI\n \n+- 1041 está como CI0221\n+- <PERSON><PERSON><PERSON> grabados hasta el 220\n+- 152 => 406\n+- 72 está\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1730043038516, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -34,8 +34,25 @@\n Organización: La Kava Cronometra (<EMAIL> | ID 465)\n Nombre anterior: Enduro La Kava Test (ID 2059)\n Nombre nuevo: Desafio Enduro Race Paraná 2024\n \n+## CUBA\n+\n+- 1 x Notebook Lenovo T470\n+- 1 x Notebook Lenovo Ideapad L340\n+- 1 x Notebook Asus X515EA\n+- 1 x Netbook Noblex SF20GM7\n+- 2 x Reader/Writer USB Desktop Invelion YR9011\n+- 2 x Reader Invelión YR8900 (8 ports)\n+- 6 x Antennas Invelion YR9028 (9 dbi)\n+- 4 x Antennas Invelion YR2013 (12dbi)\n+- 2 x Antennas Yagis 12dbi\n+4 Antenna Cables (6m, 5m, 4m, 3m)\n+500 RFID Disposable Tags (Encoded with the same EPC)\n+\n+\n+\n+\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n"}, {"date": 1730045068014, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -45,14 +45,14 @@\n - 2 x Reader Invelión YR8900 (8 ports)\n - 6 x Antennas Invelion YR9028 (9 dbi)\n - 4 x Antennas Invelion YR2013 (12dbi)\n - 2 x Antennas Yagis 12dbi\n-4 Antenna Cables (6m, 5m, 4m, 3m)\n-500 RFID Disposable Tags (Encoded with the same EPC)\n+- 14 x Cables RF TNC-N (100 metros)\n+- 2000 RFID Disposable Tags (Encoded with the same EPC)\n+- 3 x Multitomas eléctricas con alargadores\n \n \n \n-\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n"}, {"date": 1730048069252, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,11 +48,17 @@\n - 2 x Antennas Yagis 12dbi\n - 14 x Cables RF TNC-N (100 metros)\n - 2000 RFID Disposable Tags (Encoded with the same EPC)\n - 3 x Multitomas eléctricas con alargadores\n+- 1 x GoPro 12\n+- 1 x iPhone 14\n+- 1 x iPhone 15\n+- 2 x Readmi Note 11\n+- 2 x Samsung Antiguos\n+- 3 x Mouse\n+- 3 x Auriculares\n \n \n-\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n"}, {"date": 1730154827688, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -36,29 +36,33 @@\n Nombre nuevo: Desafio Enduro Race Paraná 2024\n \n ## CUBA\n \n-- 1 x Notebook Lenovo T470\n-- 1 x Notebook Lenovo Ideapad L340\n-- 1 x Notebook Asus X515EA\n-- 1 x Netbook Noblex SF20GM7\n-- 2 x Reader/Writer USB Desktop Invelion YR9011\n-- 2 x Reader Invelión YR8900 (8 ports)\n-- 6 x Antennas Invelion YR9028 (9 dbi)\n-- 4 x Antennas Invelion YR2013 (12dbi)\n-- 2 x Antennas Yagis 12dbi\n-- 14 x Cables RF TNC-N (100 metros)\n-- 2000 RFID Disposable Tags (Encoded with the same EPC)\n-- 3 x Multitomas eléctricas con alargadores\n-- 1 x GoPro 12\n-- 1 x iPhone 14\n-- 1 x iPhone 15\n-- 2 x Readmi Note 11\n-- 2 x Samsung Antiguos\n-- 3 x Mouse\n-- 3 x Auriculares\n+Valores aproximados:\n \n+1 x Notebook Lenovo T470 | USADO | Valor actual: U$D 600\n+1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n+1 x Notebook Asus X515EA | USADO | Valor actual: U$D 600\n+1 x Netbook Noblex SF20GM7 | USADO | Valor actual: U$D 200\n+2 x Reader/Writer USB Desktop Invelion YR9011 | USADO | Valor actual unitario: U$D 70\n+2 x Reader Invelion YR8900 (8 ports) | USADO | Valor actual unitario: U$D 500\n+1 x Reader Invelion YR8700 (4 ports) | USADO | Valor actual unitario: U$D 400\n+6 x Antennas Invelion YR9028 (9 dbi) | USADO | Valor actual unitario: U$D 40\n+4 x Antennas Invelion YR2013 (12dbi) | USADO | Valor actual unitario: U$D 80\n+2 x Antennas Yagis 12dbi | USADO | Valor actual unitario: U$D 130\n+14 x Cables RF TNC-N (100 metros) | USADO | Valor actual unitario: U$D 20\n+2000 RFID Disposable Tags (Encoded with the same EPC) | NUEVO | Valor actual unitario: U$D 0.20\n+3 x Multitomas eléctricas con alargadores | USADO | Valor actual unitario: U$D 20\n+1 x GoPro 12 | USADO | Valor actual: U$D 400\n+1 x iPhone 14 | USADO | Valor actual: U$D 700\n+1 x iPhone 15 | USADO | Valor actual: U$D 900\n+2 x Readmi Note 11 | USADO | Valor actual unitario: U$D 200\n+2 x Samsung Antiguos | USADO | Valor actual unitario: U$D 100\n+3 x Mouse Inalámbricos | USADO | Valor actual unitario: U$D 10\n+3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n+Total: U$D 7490\n \n+\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n"}, {"date": 1730156512855, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,12 +2,8 @@\n -------------------------------------------------------------------------------\n \n ## EVENTOS PENDIENTES DE PAGO\n \n-DAMIAN:\n-2024-08-18 \tDUATLON LA FALDA\n-2024-09-14 \tYABOTY ULTRAMARATON 2024\n-\n RIDE:\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n \n@@ -151,9 +147,9 @@\n UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n \n Son de ID 445 de Cronometraje Instantáneo <PERSON> (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 477, idevento = 2092, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 475, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n"}, {"date": 1730217637275, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -147,9 +147,9 @@\n UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n \n Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 475, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 497, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n \n Son de ID 41 de Ecuador-Colombia\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n@@ -157,9 +157,9 @@\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 475, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n"}, {"date": 1730251585556, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+\n ## EVENTOS PENDIENTES DE PAGO\n \n RIDE:\n 2024-08-31 \tOSE #3 - San <PERSON> 2024\n"}, {"date": 1730323069038, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,19 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+## CHIPS BEAGLE\n \n+03401 al 03600\n+03601 al 03800\n+03001 al 03200\n+03801 al 4000\n+03201  al 3400\n+18201 al 18400\n+17801 al 18000\n+18001 al 18100\n+\n+\n ## EVENTOS PENDIENTES DE PAGO\n \n RIDE:\n 2024-08-31 \tOSE #3 - San <PERSON> 2024\n"}, {"date": 1730400268961, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,19 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-## CHIPS BEAGLE\n \n-03401 al 03600\n-03601 al 03800\n-03001 al 03200\n-03801 al 4000\n-03201  al 3400\n-18201 al 18400\n-17801 al 18000\n-18001 al 18100\n \n-\n ## EVENTOS PENDIENTES DE PAGO\n \n RIDE:\n 2024-08-31 \tOSE #3 - San <PERSON> 2024\n"}, {"date": 1730407632097, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,13 +4,18 @@\n \n \n ## EVENTOS PENDIENTES DE PAGO\n \n-RIDE:\n+*RIDE*\n+\n 2024-08-31 \tOSE #3 - San Javier 2024\n 2024-09-15 \tCasi Enduro\n+2024-11-03 \tCronometrada picada del siete\n+2024-08-31 \tOSE #3 - San Javier 2024\n \n-JUAN CERDA:\n+\n+*JUAN CERDA*\n+\n 2024-08-29 \tNinwist<PERSON>_bike_tour (VACÍO)\n 2024-04-14 (<PERSON><PERSON>)\n Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n \n@@ -23,17 +28,9 @@\n 2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n 4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n Karting\n \n-ECUADOR:\n-2024-09-08 \tDESAFIO ROCKGARDEn (VACÍO)\n \n-Cambio en evento\n-\n-Organización: La Kava Cronometra (<EMAIL> | ID 465)\n-Nombre anterior: Enduro La Kava Test (ID 2059)\n-Nombre nuevo: Desafio Enduro Race Paraná 2024\n-\n ## CUBA\n \n Valores aproximados:\n \n"}, {"date": 1730407678773, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,36 +1,7 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-\n-\n-## EVENTOS PENDIENTES DE PAGO\n-\n-*RIDE*\n-\n-2024-08-31 \tOSE #3 - San Javier 2024\n-2024-09-15 \tCasi Enduro\n-2024-11-03 \tCronometrada picada del siete\n-2024-08-31 \tOSE #3 - San Javier 2024\n-\n-\n-*JUAN CERDA*\n-\n-2024-08-29 \tNinwistler_bike_tour (VACÍO)\n-2024-04-14 (<PERSON><PERSON>)\n-Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n-\n-2024-05-19 (<PERSON><PERSON>)\n-COIHUERACE ANIVERSARIO 2024 \tcoihuerace-aniversario-2024\n-\n-2024-08-29 (Este evento no fue usado y por ende no hay que pagarlo, ¿Quieres que lo elimine?)\n-Ninwistler_bike_tour\n-\n-2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n-4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n-Karting\n-\n-\n ## CUBA\n \n Valores aproximados:\n \n@@ -56,8 +27,11 @@\n 3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n Total: U$D 7490\n \n \n+\n+\n+\n ## CHIPS ESQUEL\n \n Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n@@ -70,8 +44,42 @@\n - Están grabados hasta el 220\n - 152 => 406\n - 72 está\n \n+\n+\n+## EVENTOS PENDIENTES DE PAGO\n+\n+*OWA*\n+\n+Armar paquete con descuento\n+\n+\n+*RIDE*\n+\n+2024-08-31 \tOSE #3 - San Javier 2024\n+2024-09-15 \tCasi Enduro\n+2024-11-03 \tCronometrada picada del siete\n+2024-08-31 \tOSE #3 - San Javier 2024\n+\n+\n+*JUAN CERDA*\n+\n+2024-08-29 \tNinwistler_bike_tour (VACÍO)\n+2024-04-14 (Muy atrasado)\n+Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n+\n+2024-05-19 (Muy atrasado)\n+COIHUERACE ANIVERSARIO 2024 \tcoihuerace-aniversario-2024\n+\n+2024-08-29 (Este evento no fue usado y por ende no hay que pagarlo, ¿Quieres que lo elimine?)\n+Ninwistler_bike_tour\n+\n+2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n+4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n+Karting\n+\n+\n ## PRE SOPORTE\n \n - Tus prioridades reales son\n     URGENTE: Servidor caído o Error que genere inconsistencia\n"}, {"date": 1730509907211, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -4,9 +4,9 @@\n ## CUBA\n \n Valores aproximados:\n \n-1 x Notebook Lenovo T470 | USADO | Valor actual: U$D 600\n+1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n 1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n 1 x Notebook Asus X515EA | USADO | Valor actual: U$D 600\n 1 x Netbook Noblex SF20GM7 | USADO | Valor actual: U$D 200\n 2 x Reader/Writer USB Desktop Invelion YR9011 | USADO | Valor actual unitario: U$D 70\n"}, {"date": 1731520647707, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -164,10 +164,10 @@\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 475, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n-UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n+UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n DT000 a <EMAIL> (379)\n"}, {"date": 1731963800606, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,23 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+ESTA SEMANA\n+\n+- Ver si puede cronometrar el de Pasto\n+\n+\n+PRÓXIMA SEMANA\n+\n+- Mandar pistola a Tatu y configurarlas todas\n+\n+\n+CUENTAS\n+\n+- Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n+\n+\n+\n ## CUBA\n \n Valores aproximados:\n \n"}, {"date": 1731964854263, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,9 +7,9 @@\n \n \n PRÓXIMA SEMANA\n \n-- Mandar pistola a Tatu y configurarlas todas\n+- Mandar pistola a Tatu y configurarlas todas (Probar Starlink)\n \n \n CUENTAS\n \n"}, {"date": 1731967331888, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,11 @@\n -------------------------------------------------------------------------------\n \n ESTA SEMANA\n \n-- Ver si puede cronometrar el de Pasto\n+- Ver si puede cronometrar el de Pasto (Cargar pagos)\n+- Ayudar por Reader Ecuador\n+- Acomodar cajas\n \n \n PRÓXIMA SEMANA\n \n"}, {"date": 1731968362980, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,19 +7,21 @@\n - Ayudar por Reader Ecuador\n - Acomodar cajas\n \n \n+BORRAR CRONO: 2315\n+\n PRÓXIMA SEMANA\n \n - Mandar pistola a Tatu y configurarlas todas (Probar Starlink)\n \n \n CUENTAS\n \n - Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n+- Cobrar 3 eventos a Organización: Tecnología Puconet EIRL (<EMAIL> | ID 188)\n \n \n-\n ## CUBA\n \n Valores aproximados:\n \n"}, {"date": 1731969120765, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,11 +5,12 @@\n \n - Ver si puede cronometrar el de Pasto (Cargar pagos)\n - Ayudar por Reader Ecuador\n - Acomodar cajas\n+  - You sent €155.00 EUR\n \n \n-BORRAR CRONO: 2315\n+BORRAR CRONO: 2315, 2262\n \n PRÓXIMA SEMANA\n \n - Mandar pistola a Tatu y configurarlas todas (Probar Starlink)\n@@ -18,8 +19,9 @@\n CUENTAS\n \n - Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n - Cobrar 3 eventos a Organización: Tecnología Puconet EIRL (<EMAIL> | ID 188)\n+- https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n \n \n ## CUBA\n \n"}, {"date": 1731969337896, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -6,10 +6,11 @@\n - Ver si puede cronometrar el de Pasto (Cargar pagos)\n - Ayudar por Reader Ecuador\n - Acomodar cajas\n   - You sent €155.00 EUR\n+- Minors:\n+  - Organización: OROZCO (<EMAIL> | ID 427) País: Etiopía\n \n-\n BORRAR CRONO: 2315, 2262\n \n PRÓXIMA SEMANA\n \n"}, {"date": 1732027317536, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -187,9 +187,9 @@\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n"}, {"date": 1732111201691, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,12 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+\n+\n ESTA SEMANA\n \n - Ver si puede cronometrar el de Pasto (Cargar pagos)\n-- Ayudar por Reader Ecuador\n - Acomodar cajas\n   - You sent €155.00 EUR\n - Minors:\n   - Organización: OROZCO (<EMAIL> | ID 427) País: Etiopía\n"}, {"date": 1732116527502, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+6.9.12\n \n-\n ESTA SEMANA\n \n - Ver si puede cronometrar el de Pasto (Cargar pagos)\n - Acomodar cajas\n"}, {"date": 1732128529430, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,213 @@\n+# 🥇 ROADS > CRONO > SOPORTE\n+-------------------------------------------------------------------------------\n+\n+\n+- Minors:\n+  - Organización: OROZCO (<EMAIL> | ID 427) País: Etiopía\n+\n+\n+\n+CUENTAS\n+\n+- You sent €155.00 EUR\n+- Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n+- Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n+- https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n+\n+\n+## CUBA\n+\n+Valores aproximados:\n+\n+1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n+1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n+1 x Notebook Asus X515EA | USADO | Valor actual: U$D 600\n+1 x Netbook Noblex SF20GM7 | USADO | Valor actual: U$D 200\n+2 x Reader/Writer USB Desktop Invelion YR9011 | USADO | Valor actual unitario: U$D 70\n+2 x Reader Invelion YR8900 (8 ports) | USADO | Valor actual unitario: U$D 500\n+1 x Reader Invelion YR8700 (4 ports) | USADO | Valor actual unitario: U$D 400\n+6 x Antennas Invelion YR9028 (9 dbi) | USADO | Valor actual unitario: U$D 40\n+4 x Antennas Invelion YR2013 (12dbi) | USADO | Valor actual unitario: U$D 80\n+2 x Antennas Yagis 12dbi | USADO | Valor actual unitario: U$D 130\n+14 x Cables RF TNC-N (100 metros) | USADO | Valor actual unitario: U$D 20\n+2000 RFID Disposable Tags (Encoded with the same EPC) | NUEVO | Valor actual unitario: U$D 0.20\n+3 x Multitomas eléctricas con alargadores | USADO | Valor actual unitario: U$D 20\n+1 x GoPro 12 | USADO | Valor actual: U$D 400\n+1 x iPhone 14 | USADO | Valor actual: U$D 700\n+1 x iPhone 15 | USADO | Valor actual: U$D 900\n+2 x Readmi Note 11 | USADO | Valor actual unitario: U$D 200\n+2 x Samsung Antiguos | USADO | Valor actual unitario: U$D 100\n+3 x Mouse Inalámbricos | USADO | Valor actual unitario: U$D 10\n+3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n+Total: U$D 7490\n+\n+\n+\n+\n+\n+## CHIPS ESQUEL\n+\n+Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n+Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n+Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n+\n+\n+## CHIPS CLAUDIO CI\n+\n+- 1041 está como CI0221\n+- Están grabados hasta el 220\n+- 152 => 406\n+- 72 está\n+\n+\n+\n+## EVENTOS PENDIENTES DE PAGO\n+\n+*OWA*\n+\n+Armar paquete con descuento\n+\n+\n+*RIDE*\n+\n+2024-08-31 \tOSE #3 - San Javier 2024\n+2024-09-15 \tCasi Enduro\n+2024-11-03 \tCronometrada picada del siete\n+2024-08-31 \tOSE #3 - San Javier 2024\n+\n+\n+*JUAN CERDA*\n+\n+2024-08-29 \tNinwistler_bike_tour (VACÍO)\n+2024-04-14 (Muy atrasado)\n+Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n+\n+2024-05-19 (Muy atrasado)\n+COIHUERACE ANIVERSARIO 2024 \tcoihuerace-aniversario-2024\n+\n+2024-08-29 (Este evento no fue usado y por ende no hay que pagarlo, ¿Quieres que lo elimine?)\n+Ninwistler_bike_tour\n+\n+2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n+4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n+Karting\n+\n+\n+## PRE SOPORTE\n+\n+- Tus prioridades reales son\n+    URGENTE: Servidor caído o Error que genere inconsistencia\n+    IMPORTANTE: Próxima prioridad de DEV y después Próxima prioridad de GROWTH\n+    CLIENTES: Procesar Soporte a cliente y Procesar Ventas\n+- Siempre responder con buena onda, agradecer el contacto y delegar si se puede.\n+- Si no se puede la respuesta es que estás con mucho trabajo, te lo vas a anotar y lo vas a responder en cuanto puedas.\n+- Si es desarrollo directamente decir que este año ya no entra en el roadmap y con las prioridades actuales.\n+- Reformular que en DEV = contruyendo productos, en GROWTH = construyendo marca, en SOPORTE = construyendo clientes, en CURSOS = construyendo equipo. El soporte no debe tender a cero, sino balancearse en una cantidad de tiempo y tender a que lo que te llegue sea cada vez más avanzado, delegando lo otro al equipo.\n+- No necesitas responder todo, NUNCA DAR FECHA, y si te apuran mensaje de \"Te aviso en cuanto tenga alguna novedad\".\n+\n+\n+## ADMIN\n+\n+- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n+- [ ] Rodolfo usuahia tiene a favor un evento\n+- [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n+\n+\n+## ACTUALIZO APELLIDOS\n+\n+UPDATE participantes AS p SET apellido =\n+    (SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\n+WHERE idevento = 2176;\n+\n+UPDATE datosxparticipantes\n+SET dato = DATE_FORMAT(STR_TO_DATE(dato, '%d/%m/%Y'), '%Y-%m-%d')\n+WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n+\n+UPDATE datosxparticipantes\n+SET dato = TRUNCATE(dato, 0)\n+WHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;\n+\n+\n+## EVENTOS PENDIENTES ECUADOR\n+\n+SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND pago = 0 ORDER BY fecha;\n+https://cronometrajeinstantaneo.com/scripts/aprobarpago.php?check=jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw&idevento=2148&forma=paypal&aprobar=1\n+\n+\n+## BOTONES POR PAÍS\n+\n+PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n+COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n+CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n+\n+-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n+-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n+\n+\n+## PRECIOS DE KARTING\n+\n+Abono Sistema Karting x 1 mes: U$D 50\n+https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n+\n+Abono Sistema Karting x 6 meses: U$D 250\n+https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n+\n+Abono Sistema Karting x 12 meses: U$D 400\n+https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n+\n+Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n+\n+\n+\n+## CHIPS MOVIDOS\n+\n+Son de ID 4 los míos (Hasta CI0200)\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n+\n+Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n+UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n+\n+Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n+UPDATE `tags` SET idorganizacion = 497, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n+\n+Son de ID 41 de Ecuador-Colombia\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n+UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n+\n+Son de ID 84 de Esquel\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n+UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n+\n+ES0000 a <EMAIL> (374)\n+UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n+DT000 a <EMAIL> (379)\n+UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n+\n+Son el ID 482 de <EMAIL>\n+000000000000000000000001\n+\n+## CHIPS EN KARTING KARTODROMO\n+\n+SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n+\n+<EMAIL>\n+San Juan F1\n+\n+CHILE:\n+\n+idorganizacion = 472;\n+\n+## RESPUESTA YOUTUBE\n+\n+Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n+\n+Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n+\n+https://web.whatsapp.com/send?phone=5492944551009\n+\n"}, {"date": 1732128601249, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,13 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n \n-- Minors:\n-  - Organización: OROZCO (<EMAIL> | ID 427) País: Etiopía\n \n-\n-\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n@@ -210,227 +206,4 @@\n Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n \n https://web.whatsapp.com/send?phone=5492944551009\n \n-# 🥇 ROADS > CRONO > SOPORTE\n--------------------------------------------------------------------------------\n-\n-6.9.12\n-\n-ESTA SEMANA\n-\n-- Ver si puede cronometrar el de Pasto (Cargar pagos)\n-- Acomodar cajas\n-  - You sent €155.00 EUR\n-- Minors:\n-  - Organización: OROZCO (<EMAIL> | ID 427) País: Etiopía\n-\n-BORRAR CRONO: 2315, 2262\n-\n-PRÓXIMA SEMANA\n-\n-- Mandar pistola a Tatu y configurarlas todas (Probar Starlink)\n-\n-\n-CUENTAS\n-\n-- Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n-- Cobrar 3 eventos a Organización: Tecnología Puconet EIRL (<EMAIL> | ID 188)\n-- https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n-\n-\n-## CUBA\n-\n-Valores aproximados:\n-\n-1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n-1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n-1 x Notebook Asus X515EA | USADO | Valor actual: U$D 600\n-1 x Netbook Noblex SF20GM7 | USADO | Valor actual: U$D 200\n-2 x Reader/Writer USB Desktop Invelion YR9011 | USADO | Valor actual unitario: U$D 70\n-2 x Reader Invelion YR8900 (8 ports) | USADO | Valor actual unitario: U$D 500\n-1 x Reader Invelion YR8700 (4 ports) | USADO | Valor actual unitario: U$D 400\n-6 x Antennas Invelion YR9028 (9 dbi) | USADO | Valor actual unitario: U$D 40\n-4 x Antennas Invelion YR2013 (12dbi) | USADO | Valor actual unitario: U$D 80\n-2 x Antennas Yagis 12dbi | USADO | Valor actual unitario: U$D 130\n-14 x Cables RF TNC-N (100 metros) | USADO | Valor actual unitario: U$D 20\n-2000 RFID Disposable Tags (Encoded with the same EPC) | NUEVO | Valor actual unitario: U$D 0.20\n-3 x Multitomas eléctricas con alargadores | USADO | Valor actual unitario: U$D 20\n-1 x GoPro 12 | USADO | Valor actual: U$D 400\n-1 x iPhone 14 | USADO | Valor actual: U$D 700\n-1 x iPhone 15 | USADO | Valor actual: U$D 900\n-2 x Readmi Note 11 | USADO | Valor actual unitario: U$D 200\n-2 x Samsung Antiguos | USADO | Valor actual unitario: U$D 100\n-3 x Mouse Inalámbricos | USADO | Valor actual unitario: U$D 10\n-3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n-Total: U$D 7490\n-\n-\n-\n-\n-\n-## CHIPS ESQUEL\n-\n-Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n-Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n-Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n-\n-\n-## CHIPS CLAUDIO CI\n-\n-- 1041 está como CI0221\n-- Están grabados hasta el 220\n-- 152 => 406\n-- 72 está\n-\n-\n-\n-## EVENTOS PENDIENTES DE PAGO\n-\n-*OWA*\n-\n-Armar paquete con descuento\n-\n-\n-*RIDE*\n-\n-2024-08-31 \tOSE #3 - San Javier 2024\n-2024-09-15 \tCasi Enduro\n-2024-11-03 \tCronometrada picada del siete\n-2024-08-31 \tOSE #3 - San Javier 2024\n-\n-\n-*JUAN CERDA*\n-\n-2024-08-29 \tNinwistler_bike_tour (VACÍO)\n-2024-04-14 (Muy atrasado)\n-Carrera -El Mirador- Alto Palermo Quillón 2024 \tcarrera-el-mirador-alto-palermo-quillon-2024\n-\n-2024-05-19 (Muy atrasado)\n-COIHUERACE ANIVERSARIO 2024 \tcoihuerace-aniversario-2024\n-\n-2024-08-29 (Este evento no fue usado y por ende no hay que pagarlo, ¿Quieres que lo elimine?)\n-Ninwistler_bike_tour\n-\n-2024-11-17 (Es un evento futuro, lo puedes pagar hasta el día del evento)\n-4o COPA DE DOWNHILL ANIVERSARIO PORTEZUELO 2024\n-Karting\n-\n-\n-## PRE SOPORTE\n-\n-- Tus prioridades reales son\n-    URGENTE: Servidor caído o Error que genere inconsistencia\n-    IMPORTANTE: Próxima prioridad de DEV y después Próxima prioridad de GROWTH\n-    CLIENTES: Procesar Soporte a cliente y Procesar Ventas\n-- Siempre responder con buena onda, agradecer el contacto y delegar si se puede.\n-- Si no se puede la respuesta es que estás con mucho trabajo, te lo vas a anotar y lo vas a responder en cuanto puedas.\n-- Si es desarrollo directamente decir que este año ya no entra en el roadmap y con las prioridades actuales.\n-- Reformular que en DEV = contruyendo productos, en GROWTH = construyendo marca, en SOPORTE = construyendo clientes, en CURSOS = construyendo equipo. El soporte no debe tender a cero, sino balancearse en una cantidad de tiempo y tender a que lo que te llegue sea cada vez más avanzado, delegando lo otro al equipo.\n-- No necesitas responder todo, NUNCA DAR FECHA, y si te apuran mensaje de \"Te aviso en cuanto tenga alguna novedad\".\n-\n-\n-## ADMIN\n-\n-- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n-- [ ] Rodolfo usuahia tiene a favor un evento\n-- [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n-\n-\n-## ACTUALIZO APELLIDOS\n-\n-UPDATE participantes AS p SET apellido =\n-    (SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\n-WHERE idevento = 2176;\n-\n-UPDATE datosxparticipantes\n-SET dato = DATE_FORMAT(STR_TO_DATE(dato, '%d/%m/%Y'), '%Y-%m-%d')\n-WHERE idevento = 2176 AND iddato = 'nacimiento' AND idinscripcion = 900072;\n-\n-UPDATE datosxparticipantes\n-SET dato = TRUNCATE(dato, 0)\n-WHERE idevento = 2176 AND iddato = 'edad' AND idinscripcion = 900072;\n-\n-\n-## EVENTOS PENDIENTES ECUADOR\n-\n-SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND pago = 0 ORDER BY fecha;\n-https://cronometrajeinstantaneo.com/scripts/aprobarpago.php?check=jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw&idevento=2148&forma=paypal&aprobar=1\n-\n-\n-## BOTONES POR PAÍS\n-\n-PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n-COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n-CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n-\n--20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n--30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n-\n-\n-## PRECIOS DE KARTING\n-\n-Abono Sistema Karting x 1 mes: U$D 50\n-https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n-\n-Abono Sistema Karting x 6 meses: U$D 250\n-https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n-\n-Abono Sistema Karting x 12 meses: U$D 400\n-https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n-\n-Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n-\n-\n-\n-## CHIPS MOVIDOS\n-\n-Son de ID 4 los míos (Hasta CI0200)\n-UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n-\n-Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n-UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n-\n-Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n-UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n-UPDATE `tags` SET idorganizacion = 497, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n-UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n-\n-Son de ID 41 de Ecuador-Colombia\n-UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n-UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n-UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n-\n-Son de ID 84 de Esquel\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n-UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n-\n-ES0000 a <EMAIL> (374)\n-UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n-DT000 a <EMAIL> (379)\n-UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n-\n-Son el ID 482 de <EMAIL>\n-000000000000000000000001\n-\n-## CHIPS EN KARTING KARTODROMO\n-\n-SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n-https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n-https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n-\n-<EMAIL>\n-San Juan F1\n-\n-CHILE:\n-\n-idorganizacion = 472;\n-\n-## RESPUESTA YOUTUBE\n-\n-Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n-\n-Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n-\n-https://web.whatsapp.com/send?phone=5492944551009\n-\n"}, {"date": 1732149896751, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,12 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+HYBRID\n \n+-> El 387 le dimos chips 187\n \n+\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": 1732149910906, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,12 +1,15 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-HYBRID\n+HYBRID VIERNES\n \n -> El 387 le dimos chips 187\n \n \n+HYBRID SÁBADO\n+\n+\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": 1732235022359, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,10 @@\n \n HYBRID VIERNES\n \n -> El 387 le dimos chips 187\n+-> Tenemos que grabar un nuevo chip para nosotros para el 152\n+-> Nuestros chips se pasan a Joaquín\n \n \n HYBRID SÁBADO\n \n@@ -53,32 +55,16 @@\n Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n \n \n-## CHIPS CLAUDIO CI\n \n-- 1041 está como CI0221\n-- Están grabados hasta el 220\n-- 152 => 406\n-- 72 está\n-\n-\n-\n ## EVENTOS PENDIENTES DE PAGO\n \n *OWA*\n \n Armar paquete con descuento\n \n \n-*RIDE*\n-\n-2024-08-31 \tOSE #3 - San Javier 2024\n-2024-09-15 \tCasi Enduro\n-2024-11-03 \tCronometrada picada del siete\n-2024-08-31 \tOSE #3 - San Javier 2024\n-\n-\n *JUAN CERDA*\n \n 2024-08-29 \tNinwistler_bike_tour (VACÍO)\n 2024-04-14 (Muy atrasado)\n"}, {"date": 1732241129226, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -7,8 +7,32 @@\n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n \n \n+*Con Chips*\n+\n+https://cronometrajeinstantaneo.com/resultados/argentina-throwdown-vueltas\n+\n+Largada: *HVLA* (Duplicado)\n+Vueltas: *HVVU* (Compu Andy)\n+Llegada: *HVFI* (Compu Gaby)\n+Test Salida: *HVSA* (Sin usar)\n+\n+*Resultados*\n+\n+https://cronometrajeinstantaneo.com/resultados/argentina-throwdown-vueltas\n+\n+Largada: *HVLA*\n+Estación 1: *HVE1*\n+Estación 2: *HVE2*\n+Estación 3: *HVE3*\n+Estación 4: *HVE4*\n+Estación 5: *HVE5*\n+Estación 6: *HVE6*\n+Estación 7: *HVE7*\n+Estación 8: *HVE8*\n+Llegada: *HVLE*\n+\n HYBRID SÁBADO\n \n \n CUENTAS\n"}, {"date": 1732322577070, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -40,8 +40,9 @@\n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n - Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n - https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n+- HYBRID: 1.5M - 360K (sueldos) - 570k (Gaby)\n \n \n ## CUBA\n \n"}, {"date": 1732357174305, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,39 +1,49 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+TODO\n+\n+- Configurar controles\n+- Configurar QR\n+- Ver diseño para sábado\n+\n+- Probar velocidad\n+\n+- Facturar\n+- <PERSON><PERSON>\n+\n+\n HYBRID VIERNES\n \n--> El 387 le dimos chips 187\n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n \n \n *Con Chips*\n \n-https://cronometrajeinstantaneo.com/resultados/argentina-throwdown-vueltas\n+https://cronometrajeinstantaneo.com/resultados/the-hybrid-race-2024-vueltas/generales\n+https://cronometrajeinstantaneo.com/resultados/the-hybrid-race-2024-vueltas/categorias\n \n-Largada: *HVLA* (Duplicado)\n-Vueltas: *HVVU* (Compu Andy)\n-Llegada: *HVFI* (Compu Gaby)\n-Test Salida: *HVSA* (Sin usar)\n+Largada: *HSLA* (Duplicado)\n+Vueltas: *HSVU* (Compu Andy)\n+Llegada: *HSFI* (Compu Gaby)\n \n *Resultados*\n \n https://cronometrajeinstantaneo.com/resultados/argentina-throwdown-vueltas\n \n-Largada: *HVLA*\n-Estación 1: *HVE1*\n-Estación 2: *HVE2*\n-Estación 3: *HVE3*\n-Estación 4: *HVE4*\n-Estación 5: *HVE5*\n-Estación 6: *HVE6*\n-Estación 7: *HVE7*\n-Estación 8: *HVE8*\n-Llegada: *HVLE*\n+Largada: *HSLA*\n+Estación 1: *HSE1*\n+Estación 2: *HSE2*\n+Estación 3: *HSE3*\n+Estación 4: *HSE4*\n+Estación 5: *HSE5*\n+Estación 6: *HSE6*\n+Estación 7: *HSE7*\n+Estación 8: *HSE8*\n+Llegada: *HSLE*\n \n-HYBRID SÁBADO\n \n \n CUENTAS\n \n"}, {"date": 1732357656488, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,8 @@\n -------------------------------------------------------------------------------\n \n TODO\n \n-- Configurar controles\n - Configurar QR\n - Ver diseño para sábado\n \n - Probar velocidad\n"}, {"date": 1732585919943, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,59 +1,28 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-TODO\n+BORRAR: 2362\n \n-- Configurar QR\n-- Ver diseño para sábado\n-\n-- Probar velocidad\n-\n-- Facturar\n-- <PERSON><PERSON>ggl\n-\n-\n HYBRID VIERNES\n \n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n \n \n-*Con Chips*\n-\n-https://cronometrajeinstantaneo.com/resultados/the-hybrid-race-2024-vueltas/generales\n-https://cronometrajeinstantaneo.com/resultados/the-hybrid-race-2024-vueltas/categorias\n-\n-Largada: *HSLA* (Duplicado)\n-Vueltas: *HSVU* (Compu Andy)\n-Llegada: *HSFI* (Compu Gaby)\n-\n-*Resultados*\n-\n-https://cronometrajeinstantaneo.com/resultados/argentina-throwdown-vueltas\n-\n-Largada: *HSLA*\n-Estación 1: *HSE1*\n-Estación 2: *HSE2*\n-Estación 3: *HSE3*\n-Estación 4: *HSE4*\n-Estación 5: *HSE5*\n-Estación 6: *HSE6*\n-Estación 7: *HSE7*\n-Estación 8: *HSE8*\n-Llegada: *HSLE*\n-\n-\n-\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n - Perú debe 2023-09-30 \tCampeonato Nacional de Ruta Elite 2023   PENDIENTE DE PAGO\n - https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n - HYBRID: 1.5M - 360K (sueldos) - 570k (Gaby)\n+- Dividendos SaaS 1.5M\n+- Ariana y Christian pagaron 2 de 80\n \n+- Cobrar MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n+\n ## CUBA\n \n Valores aproximados:\n \n"}, {"date": 1732711291078, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -169,9 +169,10 @@\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'G%';\n UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n"}, {"date": 1732711318668, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -17,8 +17,9 @@\n - https://alfa.saasargentina.com/clientes.php?a=ver&id=389 debe 2 eventos y no se quien es\n - HYBRID: 1.5M - 360K (sueldos) - 570k (Gaby)\n - Dividendos SaaS 1.5M\n - Ariana y <PERSON> pagaron 2 de 80\n+- Compré 1250 de chips (200 recuperables y 5k descartables)\n \n - <PERSON><PERSON> MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n \n"}, {"date": 1732711715980, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,9 +1,7 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-BORRAR: 2362\n-\n HYBRID VIERNES\n \n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n"}, {"date": 1732720609479, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -16,8 +16,9 @@\n - HYBRID: 1.5M - 360K (sueldos) - 570k (Gaby)\n - Dividendos SaaS 1.5M\n - Ariana y Christian pagaron 2 de 80\n - Compré 1250 de chips (200 recuperables y 5k descartables)\n+- Pasar pago de evento solidario Necochea\n \n - Cobrar MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n \n"}, {"date": 1733175562674, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,11 +168,12 @@\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n+SELECT codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE codigo LIKE 'ES0%' OR codigo LIKE 'EQT%' OR codigo LIKE 'DT%' ORDER BY codigo;\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n-UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'G%';\n+UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EQT%';\n UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n"}, {"date": 1733176187200, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -168,9 +168,9 @@\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n-SELECT codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE codigo LIKE 'ES0%' OR codigo LIKE 'EQT%' OR codigo LIKE 'DT%' ORDER BY codigo;\n+SELECT tags.estado, tags.codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE tags.codigo LIKE 'ES0%' OR tags.codigo LIKE 'EQT%' OR tags.codigo LIKE 'DT%' ORDER BY tags.codigo;\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EQT%';\n UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n"}, {"date": 1733176485210, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -49,18 +49,8 @@\n Total: U$D 7490\n \n \n \n-\n-\n-## CHIPS ESQUEL\n-\n-Para el 3/11 necesito los ES00000 en <NAME_EMAIL> (esto ya lo dejas hecho el 28/10)\n-Para el 10/11 necesito los DT000 en <EMAIL> que también podes pasarlos desde el 28/10\n-Y para el 17/11 nos va a quedar pasar los DT000 a <EMAIL> (este es el que nos queda dando vueltas para resolver)\n-\n-\n-\n ## EVENTOS PENDIENTES DE PAGO\n \n *OWA*\n \n"}, {"date": 1733239849344, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,45 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-HYBRID VIERNES\n+PAGOS ECUADOR\n \n--> Tenemos que grabar un nuevo chip para nosotros para el 152\n--> Nuestros chips se pasan a Joaquín\n+1/4 MARATON NOCTURNO ÀBREGO 2024\n+ECOFEST OCTUBRE\n+Downhill Copa Pichincha 2024 Gran Final\n+Downhill Ciudad Blanca 2024\n+Salcedo Corre 7k\n+Galope Campero Banco Visionfund (1) Y DOS\n+Jipijapa - 5ta Válida Campeonato Provincial de Enduro 2024\n+RETO FOX DH 3ra VÁLIDA\n+Copa Pangua 2024 Downhill Urbano\n+Circuito Tradicional Piñas El Oro 2024\n+OPEN MORRO DH #4 2024\n+Trepada de Montaña 2024\n+Baja Montecristi\n+COPA DOWNHILL 2024 PERU\n+BORJA XTREMO - DOWNHILL PANECILLO\n+Enduro Echeandia\n+Machachi 7k\n+DH Chorde Urbano\n+La Ruta del Sol\n+Downhill del Cerro\n+Olmedo - 6ta Válida Campeonato Provincial de Enduro 2024\n+Campeonato Departamental Downhill Guaitarilla 2024 COLO\n+Trepada de Montaña Ciudad de Riobamba\n+Galope Campero Banco Visionfund - Gran Final Salinas de Ibarra (1) Y 2\n+Race Camp VI - 2024\n+RED BULL CARROS LOCOS PERU 2 eventos\n+Ambato 5k\n+Yanaconas Trail\n+SAN JUAN CERRO ABAJO DH 2024\n+DH Limón\n+Enduro Junín 2024\n+Downhill Cahuasquí Viuda Negra\n+Copa Nacional de Enduro 2024 Válida 4\n+Enduro del Sur - Final\n+POP DOWNHILL FEST - POPAYÁN 2024\n+Campeonato Provincial de Enduro Manabí - Última Válida Manta\n \n \n CUENTAS\n \n@@ -48,9 +82,14 @@\n 3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n Total: U$D 7490\n \n \n+HYBRID VIERNES\n \n+-> Tenemos que grabar un nuevo chip para nosotros para el 152\n+-> Nuestros chips se pasan a Joaquín\n+\n+\n ## EVENTOS PENDIENTES DE PAGO\n \n *OWA*\n \n"}, {"date": 1733240242866, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,28 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+\n+Name: <PERSON>\n+Email: <EMAIL>\n+Phone: +57 3202477702\n+TAX ID: 5824133\n+Address: Carrera 8 # 65 -24  casa # 24\n+City: Ibague\n+Postal Code: \n+State: Tolima\n+Country: Colombia\n+Pay by: Credit Card in AliExpress\n+\n+Items:\n+1 Reader Invelion YR8900 (8 ports)\n+1 Reader/Writer USB Desktop\n+4 Antennas Invelion YR9028 (9 dbi)\n+4 Antenna Cables (10m, 8m, 6m, 4m)\n+500 RFID Disposable Tags (Encoded with the same EPC)\n+\n+\n+\n PAGOS ECUADOR\n \n 1/4 MARATON NOCTURNO ÀBREGO 2024\n ECOFEST OCTUBRE\n"}, {"date": 1733253103037, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,66 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-\n-Name: <PERSON>\n-Email: <EMAIL>\n-Phone: +57 3202477702\n-TAX ID: 5824133\n-Address: Carrera 8 # 65 -24  casa # 24\n-City: Ibague\n-Postal Code: \n-State: Tolima\n-Country: Colombia\n-Pay by: Credit Card in AliExpress\n-\n-Items:\n-1 Reader Invelion YR8900 (8 ports)\n-1 Reader/Writer USB Desktop\n-4 Antennas Invelion YR9028 (9 dbi)\n-4 Antenna Cables (10m, 8m, 6m, 4m)\n-500 RFID Disposable Tags (Encoded with the same EPC)\n-\n-\n-\n PAGOS ECUADOR\n \n-1/4 MARATON NOCTURNO ÀBREGO 2024\n-ECOFEST OCTUBRE\n-Downhill Copa Pichincha 2024 Gran Final\n-Downhill Ciudad Blanca 2024\n-Salcedo Corre 7k\n-Galope Campero Banco Visionfund (1) Y DOS\n-Jipijapa - 5ta Válida Campeonato Provincial de Enduro 2024\n-RETO FOX DH 3ra VÁLIDA\n-Copa Pangua 2024 Downhill Urbano\n-Circuito Tradicional Piñas El Oro 2024\n-OPEN MORRO DH #4 2024\n-Trepada de Montaña 2024\n-Baja Montecristi\n-COPA DOWNHILL 2024 PERU\n-BORJA XTREMO - DOWNHILL PANECILLO\n-Enduro Echeandia\n-Machachi 7k\n-DH Chorde Urbano\n-La Ruta del Sol\n-Downhill del Cerro\n-Olmedo - 6ta Válida Campeonato Provincial de Enduro 2024\n-Campeonato Departamental Downhill Guaitarilla 2024 COLO\n-Trepada de Montaña Ciudad de Riobamba\n-Galope Campero Banco Visionfund - Gran Final Salinas de Ibarra (1) Y 2\n-Race Camp VI - 2024\n-RED BULL CARROS LOCOS PERU 2 eventos\n-Ambato 5k\n-Yanaconas Trail\n-SAN JUAN CERRO ABAJO DH 2024\n-DH Limón\n-Enduro Junín 2024\n-Downhill Cahuasquí Viuda Negra\n-Copa Nacional de Enduro 2024 Válida 4\n-Enduro del Sur - Final\n-POP DOWNHILL FEST - POPAYÁN 2024\n-Campeonato Provincial de Enduro Manabí - Última Válida Manta\n \n \n CUENTAS\n \n@@ -171,9 +114,9 @@\n \n ## EVENTOS PENDIENTES ECUADOR\n \n SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (41, 151)) AND pago = 0 ORDER BY fecha;\n-https://cronometrajeinstantaneo.com/scripts/aprobarpago.php?check=jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw&idevento=2148&forma=paypal&aprobar=1\n+https://cronometrajeinstantaneo.com/scripts/aprobarpago.php?check=jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw&forma=paypal&aprobar=1&idevento=2148\n \n \n ## BOTONES POR PAÍS\n \n"}, {"date": 1733258949907, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,9 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-PAGOS ECUADOR\n \n \n-\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -13,8 +13,9 @@\n - Dividendos SaaS 1.5M\n - Ariana y Christian pagaron 2 de 80\n - Compré 1250 de chips (200 recuperables y 5k descartables)\n - Pasar pago de evento solidario Necochea\n+- $140k de tarjeta SD y Powerbank\n \n - Cobrar MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,8 +20,11 @@\n \n \n ## CUBA\n \n+2283\n+\n+\n Valores aproximados:\n \n 1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n 1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -20,10 +20,20 @@\n \n \n ## CUBA\n \n-2283\n+idevento 2283\n \n+864\n+849\n+816\n+202\n+Desde 532 2024-12-08 16:19:44.572\n+Hasta 871 2024-12-08 16:23:03.831\n+217\n+613\n+612\n+611\n \n Valores aproximados:\n \n 1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,7 +1,8 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+- [ ] Cargar pago a DH Mauie\n \n \n CUENTAS\n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -3,8 +3,9 @@\n \n - [ ] Cargar pago a DH Mauie\n \n \n+\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n@@ -15,8 +16,9 @@\n - Ariana y Christian pagaron 2 de 80\n - Compré 1250 de chips (200 recuperables y 5k descartables)\n - Pasar pago de evento solidario Necochea\n - $140k de tarjeta SD y Powerbank\n++ 5000 Cuba\n \n - Cobrar MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n \n"}, {"date": *************, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,11 +1,8 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-- [ ] Cargar pago a DH Mauie\n \n-\n-\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n@@ -21,48 +18,8 @@\n \n - Cobrar MTB Carreras (<EMAIL> | ID 117) | Nuevo evento: COPA NACIONAL DE DOWNHILL 3 VALIDA CALARCA (ID 2368)\n \n \n-## CUBA\n-\n-idevento 2283\n-\n-864\n-849\n-816\n-202\n-<PERSON><PERSON> 532 2024-12-08 16:19:44.572\n-<PERSON><PERSON> 871 2024-12-08 16:23:03.831\n-217\n-613\n-612\n-611\n-\n-<PERSON><PERSON> aproximados:\n-\n-1 x Notebook Lenovo T470 | USADO |  Valor actual: U$D 600\n-1 x Notebook Lenovo Ideapad L340 | USADO | Valor actual: U$D 300\n-1 x Notebook Asus X515EA | USADO | Valor actual: U$D 600\n-1 x Netbook Noblex SF20GM7 | USADO | Valor actual: U$D 200\n-2 x Reader/Writer USB Desktop Invelion YR9011 | USADO | Valor actual unitario: U$D 70\n-2 x Reader Invelion YR8900 (8 ports) | USADO | Valor actual unitario: U$D 500\n-1 x Reader Invelion YR8700 (4 ports) | USADO | Valor actual unitario: U$D 400\n-6 x Antennas Invelion YR9028 (9 dbi) | USADO | Valor actual unitario: U$D 40\n-4 x Antennas Invelion YR2013 (12dbi) | USADO | Valor actual unitario: U$D 80\n-2 x Antennas Yagis 12dbi | USADO | Valor actual unitario: U$D 130\n-14 x Cables RF TNC-N (100 metros) | USADO | Valor actual unitario: U$D 20\n-2000 RFID Disposable Tags (Encoded with the same EPC) | NUEVO | Valor actual unitario: U$D 0.20\n-3 x Multitomas eléctricas con alargadores | USADO | Valor actual unitario: U$D 20\n-1 x GoPro 12 | USADO | Valor actual: U$D 400\n-1 x iPhone 14 | USADO | Valor actual: U$D 700\n-1 x iPhone 15 | USADO | Valor actual: U$D 900\n-2 x Readmi Note 11 | USADO | Valor actual unitario: U$D 200\n-2 x Samsung Antiguos | USADO | Valor actual unitario: U$D 100\n-3 x Mouse Inalámbricos | USADO | Valor actual unitario: U$D 10\n-3 x Auriculares Inalámbricos | USADO | Valor actual unitario: U$D 20\n-Total: U$D 7490\n-\n-\n HYBRID VIERNES\n \n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n"}, {"date": 1734013077774, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -24,8 +24,15 @@\n -> Tenemos que grabar un nuevo chip para nosotros para el 152\n -> Nuestros chips se pasan a Joaquín\n \n \n+## ADMIN\n+\n+- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n+- [ ] <PERSON><PERSON>fo usuahia tiene a favor un evento\n+- [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n+\n+\n ## EVENTOS PENDIENTES DE PAGO\n \n *OWA*\n \n@@ -61,15 +68,8 @@\n - Reformular que en DEV = contruyendo productos, en GROWTH = construyendo marca, en SOPORTE = construyendo clientes, en CURSOS = construyendo equipo. El soporte no debe tender a cero, sino balancearse en una cantidad de tiempo y tender a que lo que te llegue sea cada vez más avanzado, delegando lo otro al equipo.\n - No necesitas responder todo, NUNCA DAR FECHA, y si te apuran mensaje de \"Te aviso en cuanto tenga alguna novedad\".\n \n \n-## ADMIN\n-\n-- [ ] Tengo un pago de Claudio ya facturado sin informar, dejarla a favor\n-- [ ] Rodolfo usuahia tiene a favor un evento\n-- [ ] Ordenar donde poner experiencia de usuario: https://ayuda.negocios.nequi.co/hc/es-419/articles/17904426969101--Qu%C3%A9-es-UX-o-experiencia-de-usuario\n-\n-\n ## ACTUALIZO APELLIDOS\n \n UPDATE participantes AS p SET apellido =\n     (SELECT dato FROM datosxparticipantes AS dxp WHERE idevento = 2176 AND iddato = 'club' AND dxp.idinscripcion = p.idinscripcion)\n@@ -135,11 +135,11 @@\n \n Son de ID 84 de Esquel\n SELECT tags.estado, tags.codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE tags.codigo LIKE 'ES0%' OR tags.codigo LIKE 'EQT%' OR tags.codigo LIKE 'DT%' ORDER BY tags.codigo;\n UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n-UPDATE `tags` SET idorganizacion = 435, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EQT%';\n-UPDATE `tags` SET idorganizacion = 478, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EQT%';\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n ES0000 a <EMAIL> (374)\n UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n DT000 a <EMAIL> (379)\n"}, {"date": 1734177741464, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -133,18 +133,12 @@\n UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n \n Son de ID 84 de Esquel\n-SELECT tags.estado, tags.codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE tags.codigo LIKE 'ES0%' OR tags.codigo LIKE 'EQT%' OR tags.codigo LIKE 'DT%' ORDER BY tags.codigo;\n-UPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422,358);\n-UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES0%';\n-UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EQT%';\n+SELECT tags.estado, tags.codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE tags.codigo LIKE 'ES%' OR tags.codigo LIKE 'DT%' ORDER BY tags.codigo;\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n \n-ES0000 a <EMAIL> (374)\n-UPDATE `tags` SET idorganizacion = 355, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n-DT000 a <EMAIL> (379)\n-UPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n \n Son el ID 482 de <EMAIL>\n 000000000000000000000001\n \n"}, {"date": 1734630500223, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,8 +1,12 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n+¡Qué lindo suena esto!\n+https://www.youtube.com/watch?v=HTMKCWCak8E\n \n+Estaba con YT de fondo y terminó un vídeo y me reprodujo automáticamente esto y sin saber que era me encantó, varios minutos después pude ver en la compu que era y estabas vos ahí\n+\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": 1734632535863, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -5,8 +5,11 @@\n https://www.youtube.com/watch?v=HTMKCWCak8E\n \n Estaba con YT de fondo y terminó un vídeo y me reprodujo automáticamente esto y sin saber que era me encantó, varios minutos después pude ver en la compu que era y estabas vos ahí\n \n+\n+\n+\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": 1734644191754, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,15 +1,11 @@\n # 🥇 ROADS > CRONO > SOPORTE\n -------------------------------------------------------------------------------\n \n-¡Qué lindo suena esto!\n-https://www.youtube.com/watch?v=HTMKCWCak8E\n \n-Estaba con YT de fondo y terminó un vídeo y me reprodujo automáticamente esto y sin saber que era me encantó, varios minutos después pude ver en la compu que era y estabas vos ahí\n \n \n \n-\n CUENTAS\n \n - You sent €155.00 EUR\n - Evento car Catamarca de Carlos de Catamarca pago los 60 restantes\n"}, {"date": 1734649975231, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -102,5 +102,73 @@\n ## BOTONES POR PAÍS\n \n PERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\n COLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\n-CHILE: https://che\n\\ No newline at end of file\n+CHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n+\n+-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n+-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n+\n+\n+## PRECIOS DE KARTING\n+\n+Abono Sistema Karting x 1 mes: U$D 50\n+https://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n+\n+Abono Sistema Karting x 6 meses: U$D 250\n+https://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n+\n+Abono Sistema Karting x 12 meses: U$D 400\n+https://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n+\n+Para pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n+\n+\n+\n+## CHIPS MOVIDOS\n+\n+Son de ID 4 los míos (Hasta CI0200)\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n+\n+Son de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\n+UPDATE `tags` SET idorganizacion = 45, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'SKB%';\n+\n+Son de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\n+UPDATE `tags` SET idorganizacion = 469, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 445; // Trail del Bosque\n+UPDATE `tags` SET idorganizacion = 497, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n+UPDATE `tags` SET idorganizacion = 242, idevento = 1807, idinscripcion = 0 WHERE codigo LIKE 'CI0%';\n+\n+Son de ID 41 de Ecuador-Colombia\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\n+UPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n+UPDATE `tags` SET idorganizacion = 443, idevento = 2157, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7872 AND codigo <= 8350;\n+\n+Son de ID 84 de Esquel\n+SELECT tags.estado, tags.codigo, tagID, organizaciones.nombre FROM `tags` LEFT JOIN organizaciones ON tags.idorganizacion = organizaciones.idorganizacion WHERE tags.codigo LIKE 'ES%' OR tags.codigo LIKE 'DT%' ORDER BY tags.codigo;\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'ES%';\n+UPDATE `tags` SET idorganizacion = 444, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'DT%';\n+\n+\n+Son el ID 482 de <EMAIL>\n+000000000000000000000001\n+\n+## CHIPS EN KARTING KARTODROMO\n+\n+SELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\n+https://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n+\n+<EMAIL>\n+San Juan F1\n+\n+CHILE:\n+\n+idorganizacion = 472;\n+\n+## RESPUESTA YOUTUBE\n+\n+Hola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n+\n+Te puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n+\n+https://web.whatsapp.com/send?phone=5492944551009\n+\n"}, {"date": 1734982581021, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -2,9 +2,9 @@\n -------------------------------------------------------------------------------\n \n Cuando puedas podrás agregar estos campos datos extras?\n \n-Teléfono del Copiloto\n+Teléfono del Copiloto 222\n N° de Matrícula\n \n \n CUENTAS\n"}], "date": 1724943644081, "name": "Commit-0", "content": "# 🥇 ROADS > CRONO > SOPORTE\n-------------------------------------------------------------------------------\n\n- Tengo un pago de Claudio ya facturado sin informar\n- <PERSON><PERSON><PERSON> us<PERSON> tiene a favor un evento\n\n## BOTONES POR PAÍS\n\nPERU: https://checkout.dlocalgo.com/validate/recurring/gr4y16w4RlWC7TZwLwC70nTvUTJtCdhF\nCOLOMBIA: https://checkout.dlocalgo.com/validate/recurring/dtzn5YzzgviAM3mE5TUoZWGrsxKzVOGg\nCHILE: https://checkout.dlocalgo.com/validate/recurring/XrEQJzWZ1fZEEKyzkp51r7Ht2lj4TQMd\n\n-20%: https://checkout.dlocalgo.com/validate/recurring/jiVGs0BQkh3TEtVrtxi2FhZgQ3DIDURO\n-30%: https://checkout.dlocalgo.com/validate/recurring/fa2zyokMZfIIB7BEP3CnSTg8K1iiLIrG\n\n## PRECIOS DE KARTING\n\nAbono Sistema Karting x 1 mes: U$D 50\nhttps://checkout.dlocalgo.com/validate/recurring/TwN0rFGHWvsM5rwwyBilyTvGCwCWSxne\n\nAbono Sistema Karting x 6 meses: U$D 250\nhttps://checkout.dlocalgo.com/validate/recurring/uGCmmEXPOTwrnAlKpA8qGrowAIZ9E9vX\n\nAbono Sistema Karting x 12 meses: U$D 400\nhttps://checkout.dlocalgo.com/validate/recurring/bQQuchQq9PZVHd7fmJKvhUKEa3ucpodY\n\nPara pagos con Paypal cualquier valor: https://paypal.me/cronoinstantaneo (luego informar por Whatsapp)\n\n\n\n## MERCADOPAGO PARA RIDE\n\nUPDATE precios SET idplataforma = 6, url = '<a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>' WHERE idprecio = 12;\nUPDATE precios SET idplataforma = 7, url = '' WHERE idprecio = 12;\n\nEX BOTÓN DE LA KAVA: <a href=\"https://mpago.la/1R2pnUB\" target=\"_blank\">Pagar</a>\nEX BOTÓN DE RIDE: <a class=\"button\" href=\"https://mpago.la/2y7DW77\" target=\"_blank\">Pagar</a>\n\nRIDE:\nhttps://cronometrajeinstantaneo.com/inscripciones/ose-3-san-javier-2024/czMxNmhWTUxKVER2MEQyUUM0V0dRSkRvLzB2V25nTTZrQS9xaEZqY0tBczBoVmVXcWVmKzEvK0xmQVZGKytrUg%3D%3D\n\n\n\n## CHIPS MOVIDOS\n\nSon de ID 4 los míos (Hasta CI0200)\nUPDATE `tags` SET idorganizacion = 4, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n\nSon de ID 13 de Gaby (Desde SKB1001 hasta SKB1356)\nUPDATE `tags` SET idorganizacion = 436, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 13;\n\nSon de ID 445 de Cronometraje Instantáneo Zapala (Hasta EV00300)\nUPDATE `tags` SET idorganizacion = 430, idevento = 0, idinscripcion = 0 WHERE codigo LIKE 'EV0%';\n\nSon de ID 41 de Ecuador-Colombia\nUPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo >= 7019 AND codigo < 7400;\nUPDATE `tags` SET idorganizacion = 446, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 41 AND codigo > 7500 AND codigo < 7821;\n\nSon de ID 84 de Esquel\nUPDATE `tags` SET idorganizacion = 84, idevento = 0, idinscripcion = 0 WHERE idorganizacion IN (374,379,84,357,422);\n\n\nES0000 a <EMAIL> (374)\nUPDATE `tags` SET idorganizacion = 374, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'ES%';\nDT000 a <EMAIL> (379)\nUPDATE `tags` SET idorganizacion = 379, idevento = 0, idinscripcion = 0 WHERE idorganizacion = 367 AND codigo LIKE 'DT%';\n\n\n## CHIPS EN ALTO CENTER KARTING KARTODROMO\n\nSELECT * FROM `tags` WHERE idtag >= 36206 AND idtag <= 36235 ORDER BY idtag;\nhttps://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/tickets?idinscripcion=690970\nhttps://cronometrajeinstantaneo.com/resultados/alto-center-kartodromo/generales?idcarrera=5950\n\n<EMAIL>\nSan Juan F1\n\n## RESPUESTA YOUTUBE\n\nHola, te cuento que vendemos la aplicación, dentro de un servicio que incluye toda nuestra plataforma, nuestra capacitación y soporte.\n\nTe puedes comunicar a nuestro Whatsapp +54 9 (************* para que te asesoremos puntualmente sobre la necesidad de tus eventos y el costo para ellos.\n\nhttps://web.whatsapp.com/send?phone=5492944551009\n\n"}]}