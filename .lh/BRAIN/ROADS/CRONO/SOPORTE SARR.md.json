{"sourceFile": "BRAIN/ROADS/CRONO/SOPORTE SARR.md", "activeCommit": 0, "commits": [{"activePatchIndex": 9, "patches": [{"date": 1735821689501, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1740319261078, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -39,175 +39,114 @@\n *******************************************************************************\n   CONFIGURACIONES VARIAS\n *******************************************************************************\n \n-INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n+### CARRERAS\n \n-(13340, 1630, 'SA70', 11576, 'Largada', 70, 'largada'),\n-(13340, 1630, 'SA70', 11579, 'Largada', 70, 'largada'),\n-(13340, 1630, 'SA70', 11582, 'Largada', 70, 'largada'),\n-(13340, 1630, 'SA70', 11585, 'Largada', 70, 'largada'),\n+11986 \t2338 \tMOTOS \t1\n+11987 \t2338 \tAUTOS \t4\n+11990 \t2338 \tQUADS \t2\n+11991 \t2338 \tUTV \t3\n \n-(13341, 1630, 'SA71', 11576, 'H.WP 5', 71, 'parcial'),\n-(13341, 1630, 'SA71', 11579, 'H.WP 5', 71, 'parcial'),\n-(13341, 1630, 'SA71', 11582, 'H.WP 5', 71, 'parcial'),\n-(13341, 1630, 'SA71', 11585, 'H.WP 5', 71, 'parcial'),\n+### ETAPAS\n \n-(13342, 1630, 'SA72', 11576, 'H.WP 10', 72, 'parcial'),\n-(13342, 1630, 'SA72', 11579, 'H.WP 10', 72, 'parcial'),\n-(13342, 1630, 'SA72', 11582, 'H.WP 10', 72, 'parcial'),\n-(13342, 1630, 'SA72', 11585, 'H.WP 10', 72, 'parcial'),\n+96213 \t2338 \t11986 \tETAPA 1\n+96237 \t2338 \t11991 \tETAPA 1\n+96219 \t2338 \t11987 \tETAPA 1\n+96231 \t2338 \t11990 \tETAPA 1\n \n-(13343, 1630, 'SA73', 11576, 'H.WP 15', 73, 'parcial'),\n-(13343, 1630, 'SA73', 11579, 'H.WP 15', 73, 'parcial'),\n-(13343, 1630, 'SA73', 11582, 'H.WP 15', 73, 'parcial'),\n-(13343, 1630, 'SA73', 11585, 'H.WP 15', 73, 'parcial'),\n+INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n \n-(13344, 1630, 'SA74', 11576, 'H.WP 20', 74, 'parcial'),\n-(13344, 1630, 'SA74', 11579, 'H.WP 20', 74, 'parcial'),\n-(13344, 1630, 'SA74', 11582, 'H.WP 20', 74, 'parcial'),\n-(13344, 1630, 'SA74', 11585, 'H.WP 20', 74, 'parcial'),\n \n-(13345, 1630, 'SA75', 11576, 'H.WP 25 ASS1', 75, 'parcial'),\n-(13345, 1630, 'SA75', 11579, 'H.WP 25 ASS1', 75, 'parcial'),\n-(13345, 1630, 'SA75', 11582, 'H.WP 25 ASS1', 75, 'parcial'),\n-(13345, 1630, 'SA75', 11585, 'H.WP 25 ASS1', 75, 'parcial'),\n+(18130, 2338, 'SA11', 96213, 'H.WP 14', 11, 'parcial'),\n+(18130, 2338, 'SA11', 96237, 'H.WP 14', 11, 'parcial'),\n+(18130, 2338, 'SA11', 96219, 'H.WP 14', 11, 'parcial'),\n+(18130, 2338, 'SA11', 96231, 'H.WP 14', 11, 'parcial'),\n \n-(13346, 1630, 'SA76', 11576, 'H.WP 28', 76, 'parcial'),\n-(13346, 1630, 'SA76', 11579, 'H.WP 28', 76, 'parcial'),\n-(13346, 1630, 'SA76', 11582, 'H.WP 28', 76, 'parcial'),\n-(13346, 1630, 'SA76', 11585, 'H.WP 28', 76, 'parcial'),\n+(18131, 2338, 'SA12', 96213, 'H.WP 30', 12, 'parcial'),\n+(18131, 2338, 'SA12', 96237, 'H.WP 30', 12, 'parcial'),\n+(18131, 2338, 'SA12', 96219, 'H.WP 30', 12, 'parcial'),\n+(18131, 2338, 'SA12', 96231, 'H.WP 30', 12, 'parcial'),\n \n-(13347, 1630, 'SA77', 11576, 'H.WP 34', 77, 'parcial'),\n-(13347, 1630, 'SA77', 11579, 'H.WP 34', 77, 'parcial'),\n-(13347, 1630, 'SA77', 11582, 'H.WP 34', 77, 'parcial'),\n-(13347, 1630, 'SA77', 11585, 'H.WP 34', 77, 'parcial'),\n+(18132, 2338, 'SA13', 96213, 'H.ASS1', 13, 'parcial'),\n+(18132, 2338, 'SA13', 96237, 'H.ASS1', 13, 'parcial'),\n+(18132, 2338, 'SA13', 96219, 'H.ASS1', 13, 'parcial'),\n+(18132, 2338, 'SA13', 96231, 'H.ASS1', 13, 'parcial'),\n \n-(13348, 1630, 'SA78', 11576, 'H.WP 41', 78, 'parcial'),\n-(13348, 1630, 'SA78', 11579, 'H.WP 41', 78, 'parcial'),\n-(13348, 1630, 'SA78', 11582, 'H.WP 41', 78, 'parcial'),\n-(13348, 1630, 'SA78', 11585, 'H.WP 41', 78, 'parcial'),\n+(18133, 2338, 'SA14', 96213, 'H.WP 41', 14, 'parcial'),\n+(18133, 2338, 'SA14', 96237, 'H.WP 41', 14, 'parcial'),\n+(18133, 2338, 'SA14', 96219, 'H.WP 41', 14, 'parcial'),\n+(18133, 2338, 'SA14', 96231, 'H.WP 41', 14, 'parcial'),\n \n-(13349, 1630, 'SA79', 11576, 'H.WP 44 ASS2', 79, 'parcial'),\n-(13349, 1630, 'SA79', 11579, 'H.WP 44 ASS2', 79, 'parcial'),\n-(13349, 1630, 'SA79', 11582, 'H.WP 44 ASS2', 79, 'parcial'),\n-(13349, 1630, 'SA79', 11585, 'H.WP 44 ASS2', 79, 'parcial'),\n+(18134, 2338, 'SA15', 96213, 'H.ASS2', 15, 'parcial'),\n+(18134, 2338, 'SA15', 96237, 'H.ASS2', 15, 'parcial'),\n+(18134, 2338, 'SA15', 96219, 'H.ASS2', 15, 'parcial'),\n+(18134, 2338, 'SA15', 96231, 'H.ASS2', 15, 'parcial'),\n \n-(13350, 1630, 'SA7A', 11576, 'H.WP 54', 80, 'parcial'),\n-(13350, 1630, 'SA7A', 11579, 'H.WP 54', 80, 'parcial'),\n-(13350, 1630, 'SA7A', 11582, 'H.WP 54', 80, 'parcial'),\n-(13350, 1630, 'SA7A', 11585, 'H.WP 54', 80, 'parcial'),\n+(18135, 2338, 'SA16', 96213, 'H.WP 55', 16, 'parcial'),\n+(18135, 2338, 'SA16', 96237, 'H.WP 55', 16, 'parcial'),\n+(18135, 2338, 'SA16', 96219, 'H.WP 55', 16, 'parcial'),\n+(18135, 2338, 'SA16', 96231, 'H.WP 55', 16, 'parcial'),\n \n-(13351, 1630, 'SA7B', 11576, 'H.WP 60', 81, 'parcial'),\n-(13351, 1630, 'SA7B', 11579, 'H.WP 60', 81, 'parcial'),\n-(13351, 1630, 'SA7B', 11582, 'H.WP 60', 81, 'parcial'),\n-(13351, 1630, 'SA7B', 11585, 'H.WP 60', 81, 'parcial'),\n+(18136, 2338, 'SA17', 96213, 'H.WP 73', 17, 'parcial'),\n+(18136, 2338, 'SA17', 96237, 'H.WP 73', 17, 'parcial'),\n+(18136, 2338, 'SA17', 96219, 'H.WP 73', 17, 'parcial'),\n+(18136, 2338, 'SA17', 96231, 'H.WP 73', 17, 'parcial'),\n \n-(13352, 1630, 'SA7C', 11576, 'H.WP 67', 82, 'parcial'),\n-(13352, 1630, 'SA7C', 11579, 'H.WP 67', 82, 'parcial'),\n-(13352, 1630, 'SA7C', 11582, 'H.WP 67', 82, 'parcial'),\n-(13352, 1630, 'SA7C', 11585, 'H.WP 67', 82, 'parcial'),\n+(18137, 2338, 'SA18', 96213, 'H.WP 79', 18, 'parcial'),\n+(18137, 2338, 'SA18', 96237, 'H.WP 79', 18, 'parcial'),\n+(18137, 2338, 'SA18', 96219, 'H.WP 79', 18, 'parcial'),\n+(18137, 2338, 'SA18', 96231, 'H.WP 79', 18, 'parcial')\n \n-(13353, 1630, 'SA7D', 11576, 'H.WP 70 ASS 3', 83, 'parcial'),\n-(13353, 1630, 'SA7D', 11579, 'H.WP 70 ASS 3', 83, 'parcial'),\n-(13353, 1630, 'SA7D', 11582, 'H.WP 70 ASS 3', 83, 'parcial'),\n-(13353, 1630, 'SA7D', 11585, 'H.WP 70 ASS 3', 83, 'parcial'),\n \n-(13354, 1630, 'SA7F', 11576, 'ASS', 84, 'final'),\n-(13354, 1630, 'SA7F', 11579, 'ASS', 84, 'final'),\n-(13354, 1630, 'SA7F', 11582, 'ASS', 84, 'final'),\n-(13354, 1630, 'SA7F', 11585, 'ASS', 84, 'final')\n \n+https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n+http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n \n+idevento: 2338\n+ID=7088\n+Token=1fCjTmxrjD\n \n+ETAPA 1\n \n-http://rest.anube.es/rallyrest/default/api/waypoint_times/5704/1.xml?token=RQlQysdXQ2\n-https://html5.anube.es/?rally=rally5704&port=auto&token=RQlQysdXQ2\n-idevento: 1630\n-ID=5704\n-Token=RQlQysdXQ2\n+# 18130 H. WP 14\n+# 392439|14-M|14240|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18130&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=14-M&waypoint_id=392439&cache=sarr-2025\"\n \n+# 18131 H. WP 30\n+# 392455|30-FZ|81490|FZ|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18131&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=30-FZ&waypoint_id=392455&cache=sarr-2025\"\n \n-ETAPA 8\n+# 18132 H. ASS1 37\n+# 392462|ASS1|118280|INEU-ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18132&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=ASS1&waypoint_id=392462&cache=sarr-2025\"\n \n-285839|ASS1|134200|ASS|1\n-285851|ASS2|266100|ASS|1\n-285865|ASS3|297700|ASS|1\n+# 18133 H. WP 41\n+# 392466|41-M|121890|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18133&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=41-M&waypoint_id=392466&cache=sarr-2025\"\n \n-13370 \t1630 \tSA80 \t11577 \tLargada \t80 \tlargada\n-13371 \t1630 \tSA81 \t11577 \tH.ASS1 \t81 \tparcial\n-13372 \t1630 \tSA82 \t11577 \tH.ASS2 \t82 \tparcial\n-13373 \t1630 \tSA83 \t11577 \tH.ASS3 \t83 \tparcial\n-13374 \t1630 \tSA8F \t11577 \tASS8 \t84 \tfinal\n+# 18134 H. ASS2 51\n+# 392476|ASS2|159290|INEU-ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18134&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=ASS2&waypoint_id=392476&cache=sarr-2025\"\n \n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13371&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS1&waypoint_id=285839&cache=sarr-2024-sertoes-series\"\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13372&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS2&waypoint_id=285851&cache=sarr-2024-sertoes-series\"\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13373&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS3&waypoint_id=285865&cache=sarr-2024-sertoes-series\"\n+# 18135 H. WP 55\n+# 392480|55-M|164350|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18135&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=55-M&waypoint_id=392480&cache=sarr-2025\"\n \n-ETAPA 7\n+# 18136 H. WP 73\n+# 392498|73-N|207960|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18136&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=73-N&waypoint_id=392498&cache=sarr-2025\"\n \n-(13340, 1630, 'SA70', 11576, 'Largada', 70, 'largada'),\n+# 18137 H. WP 79\n+# 392504|79-M|226410|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18137&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=79-M&waypoint_id=392504&cache=sarr-2025\"\n \n+\n # P1 WP5\n # (13341, 1630, 'SA71', 11576, 'H.WP 5', 71, 'parcial'),\n */5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13341&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n \n-# P2 WP10\n-# (13342, 1630, 'SA72', 11576, 'H.WP 10', 72, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13342&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n \n-# P3 WP15\n-# (13343, 1630, 'SA73', 11576, 'H.WP 15', 73, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13343&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P4 WP20\n-# (13344, 1630, 'SA74', 11576, 'H.WP 20', 74, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13344&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P5 WP25 ass1\n-# (13345, 1630, 'SA75', 11576, 'H.WP 25 ASS1', 75, 'parcial'),\n-285756\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13345&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=ASS1&waypoint_id=285756&cache=sarr-2024-sertoes-series\"\n-\n-# P6 WP28\n-# (13346, 1630, 'SA76', 11576, 'H.WP 28', 76, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13346&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P7 WP34\n-# (13347, 1630, 'SA77', 11576, 'H.WP 34', 77, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13347&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P8 WP41\n-# (13348, 1630, 'SA78', 11576, 'H.WP 41', 78, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13348&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P9 WP44 ass2\n-# (13349, 1630, 'SA79', 11576, 'H.WP 44 ASS2', 79, 'parcial'),\n-# 285775|ASS2|162630|ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13349&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285775&cache=sarr-2024-sertoes-series\"\n-\n-# P10 WP54\n-# (13350, 1630, 'SA7A', 11576, 'H.WP 54', 80, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13350&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P11 WP60\n-# (13351, 1630, 'SA7B', 11576, 'H.WP 60', 81, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13351&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P12 WP67\n-# (13352, 1630, 'SA7C', 11576, 'H.WP 67', 82, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13352&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n-\n-# P13 WP70 ass3\n-# (13353, 1630, 'SA7D', 11576, 'H.WP 70 ASS 3', 83, 'parcial'),\n-285801|ASS3|314800|ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13353&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285801&cache=sarr-2024-sertoes-series\"\n-\n-FINAL XAPP\n-(13354, 1630, 'SA7F', 11576, 'ASS', 84, 'final'),\n-\n-\n-\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}, {"date": 1740402291441, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,105 +48,193 @@\n 11991 \t2338 \tUTV \t3\n \n ### ETAPAS\n \n-96213 \t2338 \t11986 \tETAPA 1\n-96237 \t2338 \t11991 \tETAPA 1\n-96219 \t2338 \t11987 \tETAPA 1\n-96231 \t2338 \t11990 \tETAPA 1\n+96214 \t2338 \t11986 \tETAPA 2\n+96238 \t2338 \t11991 \tETAPA 2\n+96232 \t2338 \t11990 \tETAPA 2\n+96220 \t2338 \t11987 \tETAPA 2\n \n INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n \n+# WP 5 DSS1 PUESTO DE CONTROL \n+# WP 26 DSS2 PUESTO DE CONTROL \n+# WP 58 DSS3 PUESTO DE CONTROL \n+# WP 71 ASS3  PUESTO DE CONTROL \n \n-(18130, 2338, 'SA11', 96213, 'H.WP 14', 11, 'parcial'),\n-(18130, 2338, 'SA11', 96237, 'H.WP 14', 11, 'parcial'),\n-(18130, 2338, 'SA11', 96219, 'H.WP 14', 11, 'parcial'),\n-(18130, 2338, 'SA11', 96231, 'H.WP 14', 11, 'parcial'),\n+(18138, 2338, 'SA2A', 96214, 'PC.WP 5', 11, 'control'),\n+(18138, 2338, 'SA2A', 96238, 'PC.WP 5', 11, 'control'),\n+(18138, 2338, 'SA2A', 96232, 'PC.WP 5', 11, 'control'),\n+(18138, 2338, 'SA2A', 96220, 'PC.WP 5', 11, 'control'),\n \n-(18131, 2338, 'SA12', 96213, 'H.WP 30', 12, 'parcial'),\n-(18131, 2338, 'SA12', 96237, 'H.WP 30', 12, 'parcial'),\n-(18131, 2338, 'SA12', 96219, 'H.WP 30', 12, 'parcial'),\n-(18131, 2338, 'SA12', 96231, 'H.WP 30', 12, 'parcial'),\n+(18139, 2338, 'SA2B', 96214, 'PC.WP 26', 12, 'control'),\n+(18139, 2338, 'SA2B', 96238, 'PC.WP 26', 12, 'control'),\n+(18139, 2338, 'SA2B', 96232, 'PC.WP 26', 12, 'control'),\n+(18139, 2338, 'SA2B', 96220, 'PC.WP 26', 12, 'control'),\n \n-(18132, 2338, 'SA13', 96213, 'H.ASS1', 13, 'parcial'),\n-(18132, 2338, 'SA13', 96237, 'H.ASS1', 13, 'parcial'),\n-(18132, 2338, 'SA13', 96219, 'H.ASS1', 13, 'parcial'),\n-(18132, 2338, 'SA13', 96231, 'H.ASS1', 13, 'parcial'),\n+(18140, 2338, 'SA2C', 96214, 'PC.WP 58', 13, 'control'),\n+(18140, 2338, 'SA2C', 96238, 'PC.WP 58', 13, 'control'),\n+(18140, 2338, 'SA2C', 96232, 'PC.WP 58', 13, 'control'),\n+(18140, 2338, 'SA2C', 96220, 'PC.WP 58', 13, 'control'),\n \n-(18133, 2338, 'SA14', 96213, 'H.WP 41', 14, 'parcial'),\n-(18133, 2338, 'SA14', 96237, 'H.WP 41', 14, 'parcial'),\n-(18133, 2338, 'SA14', 96219, 'H.WP 41', 14, 'parcial'),\n-(18133, 2338, 'SA14', 96231, 'H.WP 41', 14, 'parcial'),\n+(18141, 2338, 'SA2D', 96214, 'PC.WP 71', 14, 'control'),\n+(18141, 2338, 'SA2D', 96238, 'PC.WP 71', 14, 'control'),\n+(18141, 2338, 'SA2D', 96232, 'PC.WP 71', 14, 'control'),\n+(18141, 2338, 'SA2D', 96220, 'PC.WP 71', 14, 'control'),\n \n-(18134, 2338, 'SA15', 96213, 'H.ASS2', 15, 'parcial'),\n-(18134, 2338, 'SA15', 96237, 'H.ASS2', 15, 'parcial'),\n-(18134, 2338, 'SA15', 96219, 'H.ASS2', 15, 'parcial'),\n-(18134, 2338, 'SA15', 96231, 'H.ASS2', 15, 'parcial'),\n+# HST WP 8\n+(18142, 2338, 'SA21', 96214, 'H.WP 8', 21, 'parcial'),\n+(18142, 2338, 'SA21', 96238, 'H.WP 8', 21, 'parcial'),\n+(18142, 2338, 'SA21', 96232, 'H.WP 8', 21, 'parcial'),\n+(18142, 2338, 'SA21', 96220, 'H.WP 8', 21, 'parcial'),\n \n-(18135, 2338, 'SA16', 96213, 'H.WP 55', 16, 'parcial'),\n-(18135, 2338, 'SA16', 96237, 'H.WP 55', 16, 'parcial'),\n-(18135, 2338, 'SA16', 96219, 'H.WP 55', 16, 'parcial'),\n-(18135, 2338, 'SA16', 96231, 'H.WP 55', 16, 'parcial'),\n+# HST WP 11 ASS1\n+(18143, 2338, 'SA22', 96214, 'H.WP ASS1', 22, 'parcial'),\n+(18143, 2338, 'SA22', 96238, 'H.WP ASS1', 22, 'parcial'),\n+(18143, 2338, 'SA22', 96232, 'H.WP ASS1', 22, 'parcial'),\n+(18143, 2338, 'SA22', 96220, 'H.WP ASS1', 22, 'parcial'),\n \n-(18136, 2338, 'SA17', 96213, 'H.WP 73', 17, 'parcial'),\n-(18136, 2338, 'SA17', 96237, 'H.WP 73', 17, 'parcial'),\n-(18136, 2338, 'SA17', 96219, 'H.WP 73', 17, 'parcial'),\n-(18136, 2338, 'SA17', 96231, 'H.WP 73', 17, 'parcial'),\n+# HST WP 28\n+(18144, 2338, 'SA23', 96214, 'H.WP 24', 23, 'parcial'),\n+(18144, 2338, 'SA23', 96238, 'H.WP 24', 23, 'parcial'),\n+(18144, 2338, 'SA23', 96232, 'H.WP 24', 23, 'parcial'),\n+(18144, 2338, 'SA23', 96220, 'H.WP 24', 23, 'parcial'),\n \n-(18137, 2338, 'SA18', 96213, 'H.WP 79', 18, 'parcial'),\n-(18137, 2338, 'SA18', 96237, 'H.WP 79', 18, 'parcial'),\n-(18137, 2338, 'SA18', 96219, 'H.WP 79', 18, 'parcial'),\n-(18137, 2338, 'SA18', 96231, 'H.WP 79', 18, 'parcial')\n+# HST WP 34\n+(18145, 2338, 'SA24', 96214, 'H.WP 34', 24, 'parcial'),\n+(18145, 2338, 'SA24', 96238, 'H.WP 34', 24, 'parcial'),\n+(18145, 2338, 'SA24', 96232, 'H.WP 34', 24, 'parcial'),\n+(18145, 2338, 'SA24', 96220, 'H.WP 34', 24, 'parcial'),\n \n+# HST WP 41 \n+(18146, 2338, 'SA25', 96214, 'H.WP 41', 25, 'parcial'),\n+(18146, 2338, 'SA25', 96238, 'H.WP 41', 25, 'parcial'),\n+(18146, 2338, 'SA25', 96232, 'H.WP 41', 25, 'parcial'),\n+(18146, 2338, 'SA25', 96220, 'H.WP 41', 25, 'parcial'),\n \n+# HST WP 44\n+(18147, 2338, 'SA26', 96214, 'H.WP 44', 26, 'parcial'),\n+(18147, 2338, 'SA26', 96238, 'H.WP 44', 26, 'parcial'),\n+(18147, 2338, 'SA26', 96232, 'H.WP 44', 26, 'parcial'),\n+(18147, 2338, 'SA26', 96220, 'H.WP 44', 26, 'parcial'),\n \n+# HST WP 48 ASS2\n+(18148, 2338, 'SA27', 96214, 'H.WP ASS2', 27, 'parcial'),\n+(18148, 2338, 'SA27', 96238, 'H.WP ASS2', 27, 'parcial'),\n+(18148, 2338, 'SA27', 96232, 'H.WP ASS2', 27, 'parcial'),\n+(18148, 2338, 'SA27', 96220, 'H.WP ASS2', 27, 'parcial'),\n+\n+# HST WP 60\n+(18149, 2338, 'SA28', 96214, 'H.WP 60', 28, 'parcial'),\n+(18149, 2338, 'SA28', 96238, 'H.WP 60', 28, 'parcial'),\n+(18149, 2338, 'SA28', 96232, 'H.WP 60', 28, 'parcial'),\n+(18149, 2338, 'SA28', 96220, 'H.WP 60', 28, 'parcial'),\n+\n+# HST WP 62\n+(18150, 2338, 'SA29', 96214, 'H.WP 62', 29, 'parcial'),\n+(18150, 2338, 'SA29', 96238, 'H.WP 62', 29, 'parcial'),\n+(18150, 2338, 'SA29', 96232, 'H.WP 62', 29, 'parcial'),\n+(18150, 2338, 'SA29', 96220, 'H.WP 62', 29, 'parcial'),\n+\n+# HST WP 66\n+(18151, 2338, 'SA3E', 96214, 'H.WP 66', 30, 'parcial'),\n+(18151, 2338, 'SA3E', 96238, 'H.WP 66', 30, 'parcial'),\n+(18151, 2338, 'SA3E', 96232, 'H.WP 66', 30, 'parcial'),\n+(18151, 2338, 'SA3E', 96220, 'H.WP 66', 30, 'parcial'),\n+\n+# HST WP 69\n+(18152, 2338, 'SA2F', 96214, 'H.WP 69', 31, 'parcial'),\n+(18152, 2338, 'SA2F', 96238, 'H.WP 69', 31, 'parcial'),\n+(18152, 2338, 'SA2F', 96232, 'H.WP 69', 31, 'parcial'),\n+(18152, 2338, 'SA2F', 96220, 'H.WP 69', 31, 'parcial'),\n+\n+\n https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n \n idevento: 2338\n ID=7088\n Token=1fCjTmxrjD\n \n-ETAPA 1\n+ETAPA 2\n \n-# 18130 H. WP 14\n-# 392439|14-M|14240|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18130&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=14-M&waypoint_id=392439&cache=sarr-2025\"\n+# WP 5 DSS1 PUESTO DE CONTROL \n+# 392752|DSS1||DSS|1\n+# (18138, 2338, 'SA2A', 96214, 'PC.WP 5', 11, 'control'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18138&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS1&waypoint_id=392752&cache=sarr-2025\"\n \n-# 18131 H. WP 30\n-# 392455|30-FZ|81490|FZ|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18131&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=30-FZ&waypoint_id=392455&cache=sarr-2025\"\n+# WP 26 DSS2 PUESTO DE CONTROL \n+# 392773|DSS2|430810|FNEU-DSS|1\n+# (18139, 2338, 'SA2B', 96214, 'PC.WP 26', 12, 'control'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18139&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS2&waypoint_id=392773&cache=sarr-2025\"\n \n-# 18132 H. ASS1 37\n-# 392462|ASS1|118280|INEU-ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18132&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=ASS1&waypoint_id=392462&cache=sarr-2025\"\n+# WP 58 DSS3 PUESTO DE CONTROL \n+# 392805|DSS3|635630|DSS|1\n+# (18140, 2338, 'SA2C', 96214, 'PC.WP 58', 13, 'control'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18140&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS3&waypoint_id=392805&cache=sarr-2025\"\n \n-# 18133 H. WP 41\n-# 392466|41-M|121890|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18133&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=41-M&waypoint_id=392466&cache=sarr-2025\"\n+# WP 71 ASS3  PUESTO DE CONTROL \n+# 392818|ASS3|644830|ASS|1\n+# (18141, 2338, 'SA2D', 96214, 'PC.WP 71', 14, 'control'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18141&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS3&waypoint_id=392818&cache=sarr-2025\"\n \n-# 18134 H. ASS2 51\n-# 392476|ASS2|159290|INEU-ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18134&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=ASS2&waypoint_id=392476&cache=sarr-2025\"\n+# HST WP 8\n+# 392755|8-M|28980|TP|1\n+# (18142, 2338, 'SA21', 96214, 'H.WP 8', 21, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18142&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=8-M&waypoint_id=392755&cache=sarr-2025\"\n \n-# 18135 H. WP 55\n-# 392480|55-M|164350|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18135&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=55-M&waypoint_id=392480&cache=sarr-2025\"\n+# HST WP 11 ASS1\n+# 392758|ASS1|48900|INEU-ASS|1\n+# (18143, 2338, 'SA22', 96214, 'H.WP ASS1', 22, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18143&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS1&waypoint_id=392758&cache=sarr-2025\"\n \n-# 18136 H. WP 73\n-# 392498|73-N|207960|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18136&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=73-N&waypoint_id=392498&cache=sarr-2025\"\n+# HST WP 28\n+# 392775|28-M|442430|TP|1\n+# (18144, 2338, 'SA23', 96214, 'H.WP 24', 23, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18144&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=28-M&waypoint_id=392775&cache=sarr-2025\"\n \n-# 18137 H. WP 79\n-# 392504|79-M|226410|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18137&idRace=7088&token=1fCjTmxrjD&n_etapa=1&waypoint_code=79-M&waypoint_id=392504&cache=sarr-2025\"\n+# HST WP 34\n+# 392781|34-N|466810|TP|1\n+# (18145, 2338, 'SA24', 96214, 'H.WP 34', 24, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18145&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=34-N&waypoint_id=392781&cache=sarr-2025\"\n \n+# HST WP 41 \n+# 392788|41-M|495910|TP|1\n+# (18146, 2338, 'SA25', 96214, 'H.WP 41', 25, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18146&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=41-M&waypoint_id=392788&cache=sarr-2025\"\n \n-# P1 WP5\n-# (13341, 1630, 'SA71', 11576, 'H.WP 5', 71, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13341&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n+# HST WP 44\n+# 392791|44-M|514970|TP|1\n+# (18147, 2338, 'SA26', 96214, 'H.WP 44', 26, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18147&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=44-M&waypoint_id=392791&cache=sarr-2025\"\n \n+# HST WP 48 ASS2\n+# 392795|ASS2|545970|ASS|1\n+# (18148, 2338, 'SA27', 96214, 'H.WP ASS2', 27, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18148&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS2&waypoint_id=392795&cache=sarr-2025\"\n \n+# HST WP 60\n+# 392807|60-N|636300|TP|1\n+# (18149, 2338, 'SA28', 96214, 'H.WP 60', 28, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18149&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=60-N&waypoint_id=392807&cache=sarr-2025\"\n+\n+# HST WP 62\n+# 392809|62-N|637130|TP|1\n+# (18150, 2338, 'SA29', 96214, 'H.WP 62', 29, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18150&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=62-N&waypoint_id=392809&cache=sarr-2025\"\n+\n+# HST WP 66\n+# 392813|66-N|638460|TP|1\n+# (18151, 2338, 'SA3E', 96214, 'H.WP 66', 30, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18151&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=66-N&waypoint_id=392813&cache=sarr-2025\"\n+\n+# HST WP 69\n+# 392816|69-N|641220|TP|1\n+# (18152, 2338, 'SA2F', 96214, 'H.WP 69', 31, 'parcial'),\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18152&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=69-N&waypoint_id=392816&cache=sarr-2025\"\n+\n+\n+\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}, {"date": 1740488858539, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -48,193 +48,146 @@\n 11991 \t2338 \tUTV \t3\n \n ### ETAPAS\n \n-96214 \t2338 \t11986 \tETAPA 2\n-96238 \t2338 \t11991 \tETAPA 2\n-96232 \t2338 \t11990 \tETAPA 2\n-96220 \t2338 \t11987 \tETAPA 2\n+SELECT idetapa, nombre FROM etapas WHERE idevento = 2338 ORDER BY nombre;\n \n-INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n+96233 \tETAPA 3\n+96215 \tETAPA 3\n+96239 \tETAPA 3\n+96221 \tETAPA 3\n \n-# WP 5 DSS1 PUESTO DE CONTROL \n-# WP 26 DSS2 PUESTO DE CONTROL \n-# WP 58 DSS3 PUESTO DE CONTROL \n-# WP 71 ASS3  PUESTO DE CONTROL \n+SELECT * FROM `controles` ORDER BY `controles`.`idcontrol` DESC LIMIT 1;\n \n-(18138, 2338, 'SA2A', 96214, 'PC.WP 5', 11, 'control'),\n-(18138, 2338, 'SA2A', 96238, 'PC.WP 5', 11, 'control'),\n-(18138, 2338, 'SA2A', 96232, 'PC.WP 5', 11, 'control'),\n-(18138, 2338, 'SA2A', 96220, 'PC.WP 5', 11, 'control'),\n+18159\n \n-(18139, 2338, 'SA2B', 96214, 'PC.WP 26', 12, 'control'),\n-(18139, 2338, 'SA2B', 96238, 'PC.WP 26', 12, 'control'),\n-(18139, 2338, 'SA2B', 96232, 'PC.WP 26', 12, 'control'),\n-(18139, 2338, 'SA2B', 96220, 'PC.WP 26', 12, 'control'),\n+INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n \n-(18140, 2338, 'SA2C', 96214, 'PC.WP 58', 13, 'control'),\n-(18140, 2338, 'SA2C', 96238, 'PC.WP 58', 13, 'control'),\n-(18140, 2338, 'SA2C', 96232, 'PC.WP 58', 13, 'control'),\n-(18140, 2338, 'SA2C', 96220, 'PC.WP 58', 13, 'control'),\n+# H.WP 7\n+(18161, 2338, 'SA31', 96233, 'H.WP 7', 31, 'parcial'),\n+(18161, 2338, 'SA31', 96215, 'H.WP 7', 31, 'parcial'),\n+(18161, 2338, 'SA31', 96239, 'H.WP 7', 31, 'parcial'),\n+(18161, 2338, 'SA31', 96221, 'H.WP 7', 31, 'parcial'),\n \n-(18141, 2338, 'SA2D', 96214, 'PC.WP 71', 14, 'control'),\n-(18141, 2338, 'SA2D', 96238, 'PC.WP 71', 14, 'control'),\n-(18141, 2338, 'SA2D', 96232, 'PC.WP 71', 14, 'control'),\n-(18141, 2338, 'SA2D', 96220, 'PC.WP 71', 14, 'control'),\n+# H.WP 13\n+(18162, 2338, 'SA32', 96233, 'H.WP 13', 32, 'parcial'),\n+(18162, 2338, 'SA32', 96215, 'H.WP 13', 32, 'parcial'),\n+(18162, 2338, 'SA32', 96239, 'H.WP 13', 32, 'parcial'),\n+(18162, 2338, 'SA32', 96221, 'H.WP 13', 32, 'parcial'),\n \n-# HST WP 8\n-(18142, 2338, 'SA21', 96214, 'H.WP 8', 21, 'parcial'),\n-(18142, 2338, 'SA21', 96238, 'H.WP 8', 21, 'parcial'),\n-(18142, 2338, 'SA21', 96232, 'H.WP 8', 21, 'parcial'),\n-(18142, 2338, 'SA21', 96220, 'H.WP 8', 21, 'parcial'),\n+# H.WP 22\n+(18163, 2338, 'SA33', 96233, 'H.WP 22', 33, 'parcial'),\n+(18163, 2338, 'SA33', 96215, 'H.WP 22', 33, 'parcial'),\n+(18163, 2338, 'SA33', 96239, 'H.WP 22', 33, 'parcial'),\n+(18163, 2338, 'SA33', 96221, 'H.WP 22', 33, 'parcial'),\n \n-# HST WP 11 ASS1\n-(18143, 2338, 'SA22', 96214, 'H.WP ASS1', 22, 'parcial'),\n-(18143, 2338, 'SA22', 96238, 'H.WP ASS1', 22, 'parcial'),\n-(18143, 2338, 'SA22', 96232, 'H.WP ASS1', 22, 'parcial'),\n-(18143, 2338, 'SA22', 96220, 'H.WP ASS1', 22, 'parcial'),\n+# H.WP 27\n+(18164, 2338, 'SA34', 96233, 'H.WP 27', 34, 'parcial'),\n+(18164, 2338, 'SA34', 96215, 'H.WP 27', 34, 'parcial'),\n+(18164, 2338, 'SA34', 96239, 'H.WP 27', 34, 'parcial'),\n+(18164, 2338, 'SA34', 96221, 'H.WP 27', 34, 'parcial'),\n \n-# HST WP 28\n-(18144, 2338, 'SA23', 96214, 'H.WP 24', 23, 'parcial'),\n-(18144, 2338, 'SA23', 96238, 'H.WP 24', 23, 'parcial'),\n-(18144, 2338, 'SA23', 96232, 'H.WP 24', 23, 'parcial'),\n-(18144, 2338, 'SA23', 96220, 'H.WP 24', 23, 'parcial'),\n+# H.WP 36\n+(18165, 2338, 'SA35', 96233, 'H.WP 36', 35, 'parcial'),\n+(18165, 2338, 'SA35', 96215, 'H.WP 36', 35, 'parcial'),\n+(18165, 2338, 'SA35', 96239, 'H.WP 36', 35, 'parcial'),\n+(18165, 2338, 'SA35', 96221, 'H.WP 36', 35, 'parcial'),\n \n-# HST WP 34\n-(18145, 2338, 'SA24', 96214, 'H.WP 34', 24, 'parcial'),\n-(18145, 2338, 'SA24', 96238, 'H.WP 34', 24, 'parcial'),\n-(18145, 2338, 'SA24', 96232, 'H.WP 34', 24, 'parcial'),\n-(18145, 2338, 'SA24', 96220, 'H.WP 34', 24, 'parcial'),\n+# ASS1.WP 45\n+(18166, 2338, 'SA36', 96233, 'ASS1.WP 45', 36, 'parcial'),\n+(18166, 2338, 'SA36', 96215, 'ASS1.WP 45', 36, 'parcial'),\n+(18166, 2338, 'SA36', 96239, 'ASS1.WP 45', 36, 'parcial'),\n+(18166, 2338, 'SA36', 96221, 'ASS1.WP 45', 36, 'parcial'),\n \n-# HST WP 41 \n-(18146, 2338, 'SA25', 96214, 'H.WP 41', 25, 'parcial'),\n-(18146, 2338, 'SA25', 96238, 'H.WP 41', 25, 'parcial'),\n-(18146, 2338, 'SA25', 96232, 'H.WP 41', 25, 'parcial'),\n-(18146, 2338, 'SA25', 96220, 'H.WP 41', 25, 'parcial'),\n+# H.WP 52\n+(18167, 2338, 'SA37', 96233, 'H.WP 52', 37, 'parcial'),\n+(18167, 2338, 'SA37', 96215, 'H.WP 52', 37, 'parcial'),\n+(18167, 2338, 'SA37', 96239, 'H.WP 52', 37, 'parcial'),\n+(18167, 2338, 'SA37', 96221, 'H.WP 52', 37, 'parcial'),\n \n-# HST WP 44\n-(18147, 2338, 'SA26', 96214, 'H.WP 44', 26, 'parcial'),\n-(18147, 2338, 'SA26', 96238, 'H.WP 44', 26, 'parcial'),\n-(18147, 2338, 'SA26', 96232, 'H.WP 44', 26, 'parcial'),\n-(18147, 2338, 'SA26', 96220, 'H.WP 44', 26, 'parcial'),\n+# H.WP 63\n+(18168, 2338, 'SA38', 96233, 'H.WP 63', 38, 'parcial'),\n+(18168, 2338, 'SA38', 96215, 'H.WP 63', 38, 'parcial'),\n+(18168, 2338, 'SA38', 96239, 'H.WP 63', 38, 'parcial'),\n+(18168, 2338, 'SA38', 96221, 'H.WP 63', 38, 'parcial'),\n \n-# HST WP 48 ASS2\n-(18148, 2338, 'SA27', 96214, 'H.WP ASS2', 27, 'parcial'),\n-(18148, 2338, 'SA27', 96238, 'H.WP ASS2', 27, 'parcial'),\n-(18148, 2338, 'SA27', 96232, 'H.WP ASS2', 27, 'parcial'),\n-(18148, 2338, 'SA27', 96220, 'H.WP ASS2', 27, 'parcial'),\n+# H.WP 71\n+(18169, 2338, 'SA39', 96233, 'H.WP 71', 39, 'parcial'),\n+(18169, 2338, 'SA39', 96215, 'H.WP 71', 39, 'parcial'),\n+(18169, 2338, 'SA39', 96239, 'H.WP 71', 39, 'parcial'),\n+(18169, 2338, 'SA39', 96221, 'H.WP 71', 39, 'parcial'),\n \n-# HST WP 60\n-(18149, 2338, 'SA28', 96214, 'H.WP 60', 28, 'parcial'),\n-(18149, 2338, 'SA28', 96238, 'H.WP 60', 28, 'parcial'),\n-(18149, 2338, 'SA28', 96232, 'H.WP 60', 28, 'parcial'),\n-(18149, 2338, 'SA28', 96220, 'H.WP 60', 28, 'parcial'),\n+# H.WP 81\n+(18170, 2338, 'SA3A', 96233, 'H.WP 81', 40, 'parcial'),\n+(18170, 2338, 'SA3A', 96215, 'H.WP 81', 40, 'parcial'),\n+(18170, 2338, 'SA3A', 96239, 'H.WP 81', 40, 'parcial'),\n+(18170, 2338, 'SA3A', 96221, 'H.WP 81', 40, 'parcial')\n \n-# HST WP 62\n-(18150, 2338, 'SA29', 96214, 'H.WP 62', 29, 'parcial'),\n-(18150, 2338, 'SA29', 96238, 'H.WP 62', 29, 'parcial'),\n-(18150, 2338, 'SA29', 96232, 'H.WP 62', 29, 'parcial'),\n-(18150, 2338, 'SA29', 96220, 'H.WP 62', 29, 'parcial'),\n \n-# HST WP 66\n-(18151, 2338, 'SA3E', 96214, 'H.WP 66', 30, 'parcial'),\n-(18151, 2338, 'SA3E', 96238, 'H.WP 66', 30, 'parcial'),\n-(18151, 2338, 'SA3E', 96232, 'H.WP 66', 30, 'parcial'),\n-(18151, 2338, 'SA3E', 96220, 'H.WP 66', 30, 'parcial'),\n \n-# HST WP 69\n-(18152, 2338, 'SA2F', 96214, 'H.WP 69', 31, 'parcial'),\n-(18152, 2338, 'SA2F', 96238, 'H.WP 69', 31, 'parcial'),\n-(18152, 2338, 'SA2F', 96232, 'H.WP 69', 31, 'parcial'),\n-(18152, 2338, 'SA2F', 96220, 'H.WP 69', 31, 'parcial'),\n \n-\n https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n \n idevento: 2338\n ID=7088\n Token=1fCjTmxrjD\n \n-ETAPA 2\n+ETAPA 3\n \n-# WP 5 DSS1 PUESTO DE CONTROL \n-# 392752|DSS1||DSS|1\n-# (18138, 2338, 'SA2A', 96214, 'PC.WP 5', 11, 'control'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18138&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS1&waypoint_id=392752&cache=sarr-2025\"\n+# H.WP 7\n+# (18161, 2338, 'SA31', 96233, 'H.WP 7', 31, 'parcial'),\n+# 392518|7-M|2800|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18161&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=7-N&waypoint_id=392518&cache=sarr-2025\"\n \n-# WP 26 DSS2 PUESTO DE CONTROL \n-# 392773|DSS2|430810|FNEU-DSS|1\n-# (18139, 2338, 'SA2B', 96214, 'PC.WP 26', 12, 'control'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18139&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS2&waypoint_id=392773&cache=sarr-2025\"\n+# H.WP 13\n+# (18162, 2338, 'SA32', 96233, 'H.WP 13', 32, 'parcial'),\n+# 392524|13-M|31040|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18162&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=13-M&waypoint_id=392524&cache=sarr-2025\"\n \n-# WP 58 DSS3 PUESTO DE CONTROL \n-# 392805|DSS3|635630|DSS|1\n-# (18140, 2338, 'SA2C', 96214, 'PC.WP 58', 13, 'control'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18140&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=DSS3&waypoint_id=392805&cache=sarr-2025\"\n+# H.WP 22\n+# (18163, 2338, 'SA33', 96233, 'H.WP 22', 33, 'parcial'),\n+# 392533|22-N|70570|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18163&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=22-N&waypoint_id=392533&cache=sarr-2025\"\n \n-# WP 71 ASS3  PUESTO DE CONTROL \n-# 392818|ASS3|644830|ASS|1\n-# (18141, 2338, 'SA2D', 96214, 'PC.WP 71', 14, 'control'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18141&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS3&waypoint_id=392818&cache=sarr-2025\"\n+# H.WP 27\n+# (18164, 2338, 'SA34', 96233, 'H.WP 27', 34, 'parcial'),\n+# 392538|27-M|93190|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18164&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=27-M&waypoint_id=392538&cache=sarr-2025\"\n \n-# HST WP 8\n-# 392755|8-M|28980|TP|1\n-# (18142, 2338, 'SA21', 96214, 'H.WP 8', 21, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18142&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=8-M&waypoint_id=392755&cache=sarr-2025\"\n+# H.WP 36\n+# (18165, 2338, 'SA35', 96233, 'H.WP 36', 35, 'parcial'),\n+# 392547|36-N|121090|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18165&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=36-N&waypoint_id=392547&cache=sarr-2025\"\n \n-# HST WP 11 ASS1\n-# 392758|ASS1|48900|INEU-ASS|1\n-# (18143, 2338, 'SA22', 96214, 'H.WP ASS1', 22, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18143&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS1&waypoint_id=392758&cache=sarr-2025\"\n+# ASS1.WP 45\n+# (18166, 2338, 'SA36', 96233, 'ASS1.WP 45', 36, 'parcial'),\n+# 392556|ASS1|145100|ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18166&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=ASS1&waypoint_id=392556&cache=sarr-2025\"\n \n-# HST WP 28\n-# 392775|28-M|442430|TP|1\n-# (18144, 2338, 'SA23', 96214, 'H.WP 24', 23, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18144&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=28-M&waypoint_id=392775&cache=sarr-2025\"\n+# H.WP 52\n+# (18167, 2338, 'SA37', 96233, 'H.WP 52', 37, 'parcial'),\n+# 392563|52-M|169420|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18167&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=52-M&waypoint_id=392563&cache=sarr-2025\"\n \n-# HST WP 34\n-# 392781|34-N|466810|TP|1\n-# (18145, 2338, 'SA24', 96214, 'H.WP 34', 24, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18145&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=34-N&waypoint_id=392781&cache=sarr-2025\"\n+# H.WP 63\n+# (18168, 2338, 'SA38', 96233, 'H.WP 63', 38, 'parcial'),\n+# 392574|63-M|203670|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18168&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=63-M&waypoint_id=392574&cache=sarr-2025\"\n \n-# HST WP 41 \n-# 392788|41-M|495910|TP|1\n-# (18146, 2338, 'SA25', 96214, 'H.WP 41', 25, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18146&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=41-M&waypoint_id=392788&cache=sarr-2025\"\n+# H.WP 71\n+# (18169, 2338, 'SA39', 96233, 'H.WP 71', 39, 'parcial'),\n+# 392582|71-M|234260|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18169&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=71-M&waypoint_id=392582&cache=sarr-2025\"\n \n-# HST WP 44\n-# 392791|44-M|514970|TP|1\n-# (18147, 2338, 'SA26', 96214, 'H.WP 44', 26, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18147&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=44-M&waypoint_id=392791&cache=sarr-2025\"\n+# H.WP 81\n+# (18170, 2338, 'SA3A', 96233, 'H.WP 81', 40, 'parcial'),\n+# 392592|81-M|297570|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18170&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=81-M&waypoint_id=392592&cache=sarr-2025\"\n \n-# HST WP 48 ASS2\n-# 392795|ASS2|545970|ASS|1\n-# (18148, 2338, 'SA27', 96214, 'H.WP ASS2', 27, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18148&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=ASS2&waypoint_id=392795&cache=sarr-2025\"\n \n-# HST WP 60\n-# 392807|60-N|636300|TP|1\n-# (18149, 2338, 'SA28', 96214, 'H.WP 60', 28, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18149&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=60-N&waypoint_id=392807&cache=sarr-2025\"\n \n-# HST WP 62\n-# 392809|62-N|637130|TP|1\n-# (18150, 2338, 'SA29', 96214, 'H.WP 62', 29, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18150&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=62-N&waypoint_id=392809&cache=sarr-2025\"\n \n-# HST WP 66\n-# 392813|66-N|638460|TP|1\n-# (18151, 2338, 'SA3E', 96214, 'H.WP 66', 30, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18151&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=66-N&waypoint_id=392813&cache=sarr-2025\"\n-\n-# HST WP 69\n-# 392816|69-N|641220|TP|1\n-# (18152, 2338, 'SA2F', 96214, 'H.WP 69', 31, 'parcial'),\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18152&idRace=7088&token=1fCjTmxrjD&n_etapa=2&waypoint_code=69-N&waypoint_id=392816&cache=sarr-2025\"\n-\n-\n-\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}, {"date": 1740519961887, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -42,152 +42,276 @@\n \n ### CARRERAS\n \n 11986 \t2338 \tMOTOS \t1\n-11987 \t2338 \tAUTOS \t4\n 11990 \t2338 \tQUADS \t2\n 11991 \t2338 \tUTV \t3\n+11987 \t2338 \tAUTOS \t4\n \n ### ETAPAS\n \n SELECT idetapa, nombre FROM etapas WHERE idevento = 2338 ORDER BY nombre;\n \n-96233 \tETAPA 3\n-96215 \tETAPA 3\n-96239 \tETAPA 3\n-96221 \tETAPA 3\n-\n SELECT * FROM `controles` ORDER BY `controles`.`idcontrol` DESC LIMIT 1;\n \n-18159\n+18201\n \n INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n \n # H.WP 7\n-(18161, 2338, 'SA31', 96233, 'H.WP 7', 31, 'parcial'),\n-(18161, 2338, 'SA31', 96215, 'H.WP 7', 31, 'parcial'),\n-(18161, 2338, 'SA31', 96239, 'H.WP 7', 31, 'parcial'),\n-(18161, 2338, 'SA31', 96221, 'H.WP 7', 31, 'parcial'),\n+(18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96240, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96216, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96234, 'H.WP 7', 41, 'parcial'),\n \n # H.WP 13\n-(18162, 2338, 'SA32', 96233, 'H.WP 13', 32, 'parcial'),\n-(18162, 2338, 'SA32', 96215, 'H.WP 13', 32, 'parcial'),\n-(18162, 2338, 'SA32', 96239, 'H.WP 13', 32, 'parcial'),\n-(18162, 2338, 'SA32', 96221, 'H.WP 13', 32, 'parcial'),\n+(18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96240, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96216, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96234, 'H.WP 13', 42, 'parcial'),\n \n-# H.WP 22\n-(18163, 2338, 'SA33', 96233, 'H.WP 22', 33, 'parcial'),\n-(18163, 2338, 'SA33', 96215, 'H.WP 22', 33, 'parcial'),\n-(18163, 2338, 'SA33', 96239, 'H.WP 22', 33, 'parcial'),\n-(18163, 2338, 'SA33', 96221, 'H.WP 22', 33, 'parcial'),\n+# H.WP 21\n+(18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96240, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96216, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96234, 'H.WP 21', 43, 'parcial'),\n \n-# H.WP 27\n-(18164, 2338, 'SA34', 96233, 'H.WP 27', 34, 'parcial'),\n-(18164, 2338, 'SA34', 96215, 'H.WP 27', 34, 'parcial'),\n-(18164, 2338, 'SA34', 96239, 'H.WP 27', 34, 'parcial'),\n-(18164, 2338, 'SA34', 96221, 'H.WP 27', 34, 'parcial'),\n+# H.WP 32\n+(18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96240, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96216, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96234, 'H.WP 32', 44, 'parcial'),\n \n-# H.WP 36\n-(18165, 2338, 'SA35', 96233, 'H.WP 36', 35, 'parcial'),\n-(18165, 2338, 'SA35', 96215, 'H.WP 36', 35, 'parcial'),\n-(18165, 2338, 'SA35', 96239, 'H.WP 36', 35, 'parcial'),\n-(18165, 2338, 'SA35', 96221, 'H.WP 36', 35, 'parcial'),\n+# H.ASS1 39\n+(18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96240, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96216, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96234, 'H.ASS1 39', 45, 'parcial'),\n \n-# ASS1.WP 45\n-(18166, 2338, 'SA36', 96233, 'ASS1.WP 45', 36, 'parcial'),\n-(18166, 2338, 'SA36', 96215, 'ASS1.WP 45', 36, 'parcial'),\n-(18166, 2338, 'SA36', 96239, 'ASS1.WP 45', 36, 'parcial'),\n-(18166, 2338, 'SA36', 96221, 'ASS1.WP 45', 36, 'parcial'),\n+# H.WP 45\n+(18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96240, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96216, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96234, 'H.WP 45', 46, 'parcial'),\n \n-# H.WP 52\n-(18167, 2338, 'SA37', 96233, 'H.WP 52', 37, 'parcial'),\n-(18167, 2338, 'SA37', 96215, 'H.WP 52', 37, 'parcial'),\n-(18167, 2338, 'SA37', 96239, 'H.WP 52', 37, 'parcial'),\n-(18167, 2338, 'SA37', 96221, 'H.WP 52', 37, 'parcial'),\n+# H.ASS2 52\n+(18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96240, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96216, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96234, 'H.ASS2 52', 47, 'parcial'),\n \n-# H.WP 63\n-(18168, 2338, 'SA38', 96233, 'H.WP 63', 38, 'parcial'),\n-(18168, 2338, 'SA38', 96215, 'H.WP 63', 38, 'parcial'),\n-(18168, 2338, 'SA38', 96239, 'H.WP 63', 38, 'parcial'),\n-(18168, 2338, 'SA38', 96221, 'H.WP 63', 38, 'parcial'),\n+# H.WP 58\n+(18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96240, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96216, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96234, 'H.WP 58', 48, 'parcial'),\n \n+# H.WP 65\n+(18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96240, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96216, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96234, 'H.WP 65', 49, 'parcial'),\n+\n # H.WP 71\n-(18169, 2338, 'SA39', 96233, 'H.WP 71', 39, 'parcial'),\n-(18169, 2338, 'SA39', 96215, 'H.WP 71', 39, 'parcial'),\n-(18169, 2338, 'SA39', 96239, 'H.WP 71', 39, 'parcial'),\n-(18169, 2338, 'SA39', 96221, 'H.WP 71', 39, 'parcial'),\n+(18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96240, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96216, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96234, 'H.WP 71', 50, 'parcial'),\n \n-# H.WP 81\n-(18170, 2338, 'SA3A', 96233, 'H.WP 81', 40, 'parcial'),\n-(18170, 2338, 'SA3A', 96215, 'H.WP 81', 40, 'parcial'),\n-(18170, 2338, 'SA3A', 96239, 'H.WP 81', 40, 'parcial'),\n-(18170, 2338, 'SA3A', 96221, 'H.WP 81', 40, 'parcial')\n \n+# H.WP 10\n+(18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96223, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96217, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96241, 'H.WP 10', 51, 'parcial'),\n \n+# H.WP 19\n+(18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96223, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96217, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96241, 'H.WP 19', 52, 'parcial'),\n \n+# H.WP 31\n+(18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96223, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96217, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96241, 'H.WP 31', 53, 'parcial'),\n \n+# H.ASS1 34\n+(18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96223, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96217, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96241, 'H.ASS1 34', 54, 'parcial'),\n+\n+# H.WP 38\n+(18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96223, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96217, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96241, 'H.WP 38', 55, 'parcial'),\n+\n+# H.WP 42\n+(18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96223, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96217, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96241, 'H.WP 42', 56, 'parcial'),\n+\n+# H.WP 49\n+(18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96223, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96217, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96241, 'H.WP 49', 57, 'parcial'),\n+\n+# H.ASS2 52\n+(18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96223, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96217, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96241, 'H.ASS2 52', 58, 'parcial'),\n+\n+# H.WP 59\n+(18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96223, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96217, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96241, 'H.WP 59', 59, 'parcial'),\n+\n+# H.WP 68\n+(18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96223, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96217, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96241, 'H.WP 68', 60, 'parcial'),\n+\n+\n+# H.WP 4\n+(18221, 2338, 'SA61', 97960, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97963, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97966, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97957, 'H.WP 4', 61, 'parcial'),\n+\n+# H.WP 10\n+(18222, 2338, 'SA62', 97960, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97963, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97966, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97957, 'H.WP 10', 62, 'parcial'),\n+\n+# H.WP 18\n+(18223, 2338, 'SA63', 97960, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97963, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97966, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97957, 'H.WP 18', 63, 'parcial'),\n+\n+# H.WP 29\n+(18224, 2338, 'SA64', 97960, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97963, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97966, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97957, 'H.WP 29', 64, 'parcial'),\n+\n+# H.ASS1 39\n+(18225, 2338, 'SA65', 97960, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97963, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97966, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97957, 'H.ASS1 39', 65, 'parcial'),\n+\n+# H.WP 53\n+(18226, 2338, 'SA66', 97960, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97963, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97966, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97957, 'H.WP 53', 66, 'parcial'),\n+\n+# H.WP 59\n+(18227, 2338, 'SA67', 97960, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97963, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97966, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97957, 'H.WP 59', 67, 'parcial'),\n+\n+# H.WP 64\n+(18228, 2338, 'SA68', 97960, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97963, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97966, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97957, 'H.WP 64', 68, 'parcial'),\n+\n+# H.ASS2 74\n+(18229, 2338, 'SA69', 97960, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97963, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97966, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97957, 'H.ASS2 74', 69, 'parcial'),\n+\n+# H.WP 79\n+(18230, 2338, 'SA6A', 97960, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97963, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97966, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97957, 'H.WP 79', 70, 'parcial'),\n+\n+# H.WP 82\n+(18231, 2338, 'SA6B', 97960, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97963, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97966, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97957, 'H.WP 82', 71, 'parcial'),\n+\n+\n+\n+\n+\n https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n \n idevento: 2338\n ID=7088\n Token=1fCjTmxrjD\n \n-ETAPA 3\n+ETAPA 4\n \n # H.WP 7\n-# (18161, 2338, 'SA31', 96233, 'H.WP 7', 31, 'parcial'),\n-# 392518|7-M|2800|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18161&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=7-N&waypoint_id=392518&cache=sarr-2025\"\n+# (18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n+# 392678|7-N|1760|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18201&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=7-N&waypoint_id=392678&cache=sarr-2025\"\n \n # H.WP 13\n-# (18162, 2338, 'SA32', 96233, 'H.WP 13', 32, 'parcial'),\n-# 392524|13-M|31040|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18162&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=13-M&waypoint_id=392524&cache=sarr-2025\"\n+# (18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n+# 392684|13-M|30890|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18202&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=13-M&waypoint_id=392684&cache=sarr-2025\"\n \n-# H.WP 22\n-# (18163, 2338, 'SA33', 96233, 'H.WP 22', 33, 'parcial'),\n-# 392533|22-N|70570|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18163&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=22-N&waypoint_id=392533&cache=sarr-2025\"\n+# H.WP 21\n+# (18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n+# 392692|21-M|67170|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18203&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=21-M&waypoint_id=392692&cache=sarr-2025\"\n \n-# H.WP 27\n-# (18164, 2338, 'SA34', 96233, 'H.WP 27', 34, 'parcial'),\n-# 392538|27-M|93190|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18164&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=27-M&waypoint_id=392538&cache=sarr-2025\"\n+# H.WP 32\n+# (18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n+# 392703|32-M|122650|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18204&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=32-M&waypoint_id=392703&cache=sarr-2025\"\n \n-# H.WP 36\n-# (18165, 2338, 'SA35', 96233, 'H.WP 36', 35, 'parcial'),\n-# 392547|36-N|121090|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18165&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=36-N&waypoint_id=392547&cache=sarr-2025\"\n+# H.ASS1 39\n+# (18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n+# 392710|ASS1|181030|INEU-ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18205&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=ASS1&waypoint_id=392710&cache=sarr-2025\"\n \n-# ASS1.WP 45\n-# (18166, 2338, 'SA36', 96233, 'ASS1.WP 45', 36, 'parcial'),\n-# 392556|ASS1|145100|ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18166&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=ASS1&waypoint_id=392556&cache=sarr-2025\"\n+# H.WP 45\n+# (18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n+# 392716|DSS2|317790|FNEU-DSS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18206&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=DSS2&waypoint_id=392716&cache=sarr-2025\"\n \n-# H.WP 52\n-# (18167, 2338, 'SA37', 96233, 'H.WP 52', 37, 'parcial'),\n-# 392563|52-M|169420|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18167&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=52-M&waypoint_id=392563&cache=sarr-2025\"\n+# H.ASS2 52\n+# (18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n+# 392723|52-N|331250|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18207&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=52-N&waypoint_id=392723&cache=sarr-2025\"\n \n-# H.WP 63\n-# (18168, 2338, 'SA38', 96233, 'H.WP 63', 38, 'parcial'),\n-# 392574|63-M|203670|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18168&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=63-M&waypoint_id=392574&cache=sarr-2025\"\n+# H.WP 58\n+# (18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n+# 392729|58-M|342700|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18208&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=58-M&waypoint_id=392729&cache=sarr-2025\"\n \n+# H.WP 65\n+# (18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n+# 392736|65-N|361810|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18209&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=65-N&waypoint_id=392736&cache=sarr-2025\"\n+\n # H.WP 71\n-# (18169, 2338, 'SA39', 96233, 'H.WP 71', 39, 'parcial'),\n-# 392582|71-M|234260|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18169&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=71-M&waypoint_id=392582&cache=sarr-2025\"\n+# (18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n+# 392742|71-N|369890|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18210&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=71-N&waypoint_id=392742&cache=sarr-2025\"\n \n-# H.WP 81\n-# (18170, 2338, 'SA3A', 96233, 'H.WP 81', 40, 'parcial'),\n-# 392592|81-M|297570|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18170&idRace=7088&token=1fCjTmxrjD&n_etapa=3&waypoint_code=81-M&waypoint_id=392592&cache=sarr-2025\"\n \n \n \n-\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}, {"date": 1740519973838, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -246,10 +246,8 @@\n (18231, 2338, 'SA6B', 97957, 'H.WP 82', 71, 'parcial'),\n \n \n \n-\n-\n https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n \n idevento: 2338\n"}, {"date": 1740619842553, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -0,0 +1,346 @@\n+*******************************************************************************\n+  SARR 2023\n+*******************************************************************************\n+\n+## CAMBIOS PARA F1 KART\n+\n+Enlace y QR para Inscripciones (se pueden inscribir los pilotos ellos mismos o usarlo ustedes para agregarlos más fácil que en el sistema a mano)\n+https://cronometrajeinstantaneo.com/inscripciones/f1-kart-san-juan\n+\n+Enlace y QR para los resultados de la carrera actual\n+https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/filtros\n+\n+Enlace el televisor con la carrera actual (se actualiza automáticamente)\n+https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/?actualizar=60\n+\n+Los enlaces de las carreras terminadas, para que sigan viendo sus tiempos al día siguiente, se envían por mail al mail con el que se registraron\n+\n+\n+## Copiar de acá\n+https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/map\n+https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/standings\n+\n+\n+## DESPUÉS\n+\n+- [ ] Mostrar los parciales en los filtros de etapas nada más\n+- [ ] Sistema de Inscripciones, sistema de acreditaciones y listado de participantes automáticos\n+- [ ] Configurar sistema de puntos: Los puntos es al final del evento, se les da puntaje por largar por llegar, por ganar cada etapa (1° , 2° , 3°) y al final por la carrera general.\n+- [ ] Herramienta para sacar el orden de largada\n+- [ ] Agregar Foto de los participantes a los resultados\n+\n+- [ ] Agregar la opción de filtros como selectores\n+- [ ] Ver si podemos poner parciales como un selector más\n+- [ ] Agregar íconos en los selectores (y si hay una sola no poner el botón)\n+- [ ] Importador de penas\n+\n+\n+\n+*******************************************************************************\n+  CONFIGURACIONES VARIAS\n+*******************************************************************************\n+\n+### CARRERAS\n+\n+11986 \t2338 \tMOTOS \t1\n+11990 \t2338 \tQUADS \t2\n+11991 \t2338 \tUTV \t3\n+11987 \t2338 \tAUTOS \t4\n+\n+### ETAPAS\n+\n+SELECT idetapa, nombre FROM etapas WHERE idevento = 2338 ORDER BY nombre;\n+\n+SELECT * FROM `controles` ORDER BY `controles`.`idcontrol` DESC LIMIT 1;\n+\n+18201\n+\n+INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n+\n+# H.WP 7\n+(18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96240, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96216, 'H.WP 7', 41, 'parcial'),\n+(18201, 2338, 'SA41', 96234, 'H.WP 7', 41, 'parcial'),\n+\n+# H.WP 13\n+(18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96240, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96216, 'H.WP 13', 42, 'parcial'),\n+(18202, 2338, 'SA42', 96234, 'H.WP 13', 42, 'parcial'),\n+\n+# H.WP 21\n+(18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96240, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96216, 'H.WP 21', 43, 'parcial'),\n+(18203, 2338, 'SA43', 96234, 'H.WP 21', 43, 'parcial'),\n+\n+# H.WP 32\n+(18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96240, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96216, 'H.WP 32', 44, 'parcial'),\n+(18204, 2338, 'SA44', 96234, 'H.WP 32', 44, 'parcial'),\n+\n+# H.ASS1 39\n+(18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96240, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96216, 'H.ASS1 39', 45, 'parcial'),\n+(18205, 2338, 'SA45', 96234, 'H.ASS1 39', 45, 'parcial'),\n+\n+# H.WP 45\n+(18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96240, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96216, 'H.WP 45', 46, 'parcial'),\n+(18206, 2338, 'SA46', 96234, 'H.WP 45', 46, 'parcial'),\n+\n+# H.ASS2 52\n+(18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96240, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96216, 'H.ASS2 52', 47, 'parcial'),\n+(18207, 2338, 'SA47', 96234, 'H.ASS2 52', 47, 'parcial'),\n+\n+# H.WP 58\n+(18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96240, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96216, 'H.WP 58', 48, 'parcial'),\n+(18208, 2338, 'SA48', 96234, 'H.WP 58', 48, 'parcial'),\n+\n+# H.WP 65\n+(18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96240, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96216, 'H.WP 65', 49, 'parcial'),\n+(18209, 2338, 'SA49', 96234, 'H.WP 65', 49, 'parcial'),\n+\n+# H.WP 71\n+(18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96240, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96216, 'H.WP 71', 50, 'parcial'),\n+(18210, 2338, 'SA4A', 96234, 'H.WP 71', 50, 'parcial'),\n+\n+\n+# H.WP 10\n+(18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96223, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96217, 'H.WP 10', 51, 'parcial'),\n+(18211, 2338, 'SA51', 96241, 'H.WP 10', 51, 'parcial'),\n+\n+# H.WP 19\n+(18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96223, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96217, 'H.WP 19', 52, 'parcial'),\n+(18212, 2338, 'SA52', 96241, 'H.WP 19', 52, 'parcial'),\n+\n+# H.WP 31\n+(18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96223, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96217, 'H.WP 31', 53, 'parcial'),\n+(18213, 2338, 'SA53', 96241, 'H.WP 31', 53, 'parcial'),\n+\n+# H.ASS1 34\n+(18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96223, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96217, 'H.ASS1 34', 54, 'parcial'),\n+(18214, 2338, 'SA54', 96241, 'H.ASS1 34', 54, 'parcial'),\n+\n+# H.WP 38\n+(18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96223, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96217, 'H.WP 38', 55, 'parcial'),\n+(18215, 2338, 'SA55', 96241, 'H.WP 38', 55, 'parcial'),\n+\n+# H.WP 42\n+(18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96223, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96217, 'H.WP 42', 56, 'parcial'),\n+(18216, 2338, 'SA56', 96241, 'H.WP 42', 56, 'parcial'),\n+\n+# H.WP 49\n+(18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96223, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96217, 'H.WP 49', 57, 'parcial'),\n+(18217, 2338, 'SA57', 96241, 'H.WP 49', 57, 'parcial'),\n+\n+# H.ASS2 52\n+(18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96223, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96217, 'H.ASS2 52', 58, 'parcial'),\n+(18218, 2338, 'SA58', 96241, 'H.ASS2 52', 58, 'parcial'),\n+\n+# H.WP 59\n+(18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96223, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96217, 'H.WP 59', 59, 'parcial'),\n+(18219, 2338, 'SA59', 96241, 'H.WP 59', 59, 'parcial'),\n+\n+# H.WP 68\n+(18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96223, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96217, 'H.WP 68', 60, 'parcial'),\n+(18220, 2338, 'SA5A', 96241, 'H.WP 68', 60, 'parcial'),\n+\n+\n+# H.WP 4\n+(18221, 2338, 'SA61', 97960, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97963, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97966, 'H.WP 4', 61, 'parcial'),\n+(18221, 2338, 'SA61', 97957, 'H.WP 4', 61, 'parcial'),\n+\n+# H.WP 10\n+(18222, 2338, 'SA62', 97960, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97963, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97966, 'H.WP 10', 62, 'parcial'),\n+(18222, 2338, 'SA62', 97957, 'H.WP 10', 62, 'parcial'),\n+\n+# H.WP 18\n+(18223, 2338, 'SA63', 97960, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97963, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97966, 'H.WP 18', 63, 'parcial'),\n+(18223, 2338, 'SA63', 97957, 'H.WP 18', 63, 'parcial'),\n+\n+# H.WP 29\n+(18224, 2338, 'SA64', 97960, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97963, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97966, 'H.WP 29', 64, 'parcial'),\n+(18224, 2338, 'SA64', 97957, 'H.WP 29', 64, 'parcial'),\n+\n+# H.ASS1 39\n+(18225, 2338, 'SA65', 97960, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97963, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97966, 'H.ASS1 39', 65, 'parcial'),\n+(18225, 2338, 'SA65', 97957, 'H.ASS1 39', 65, 'parcial'),\n+\n+# H.WP 53\n+(18226, 2338, 'SA66', 97960, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97963, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97966, 'H.WP 53', 66, 'parcial'),\n+(18226, 2338, 'SA66', 97957, 'H.WP 53', 66, 'parcial'),\n+\n+# H.WP 59\n+(18227, 2338, 'SA67', 97960, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97963, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97966, 'H.WP 59', 67, 'parcial'),\n+(18227, 2338, 'SA67', 97957, 'H.WP 59', 67, 'parcial'),\n+\n+# H.WP 64\n+(18228, 2338, 'SA68', 97960, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97963, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97966, 'H.WP 64', 68, 'parcial'),\n+(18228, 2338, 'SA68', 97957, 'H.WP 64', 68, 'parcial'),\n+\n+# H.ASS2 74\n+(18229, 2338, 'SA69', 97960, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97963, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97966, 'H.ASS2 74', 69, 'parcial'),\n+(18229, 2338, 'SA69', 97957, 'H.ASS2 74', 69, 'parcial'),\n+\n+# H.WP 79\n+(18230, 2338, 'SA6A', 97960, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97963, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97966, 'H.WP 79', 70, 'parcial'),\n+(18230, 2338, 'SA6A', 97957, 'H.WP 79', 70, 'parcial'),\n+\n+# H.WP 82\n+(18231, 2338, 'SA6B', 97960, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97963, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97966, 'H.WP 82', 71, 'parcial'),\n+(18231, 2338, 'SA6B', 97957, 'H.WP 82', 71, 'parcial'),\n+\n+\n+\n+https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n+http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n+\n+idevento: 2338\n+ID=7088\n+Token=1fCjTmxrjD\n+\n+ETAPA 4\n+\n+# H.WP 7\n+# (18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n+# 392678|7-N|1760|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18201&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=7-N&waypoint_id=392678&cache=sarr-2025\"\n+\n+\n+---\n+\n+UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n+UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n+\n+SELECT idcontrol, idparticipante, tiempo FROM lecturas WHERE idcontrol IN (9160, 9172) AND estado != 'eliminado'\n+AND (idparticipante > 100 OR idparticipante < 200)\n+ORDER BY idcontrol, tiempo;\n+\n+SELECT * FROM penas WHERE idetapa IN (6952, 6971, 6961, 6981) LIMIT 500;\n+\n+DELETE FROM lecturas WHERE idcontrol IN (12550);\n+\n+*******************************************************************************\n+\n+## CONFIGURACION JIMDO Y APP\n+https://cms.e.jimdo.com/app/s82a17b50740eef8e/p36f3ef62dc40b75f?cmsEdit=1\n+\n+<div style=\"position: relative; padding-bottom: 56.25%; min-height: 2500px;\">\n+    <iframe frameborder=\"0\" allowfullscreen=\"allowfullscreen\"\n+    style=\"position: absolute; top:0; left: 0; width: 100%; min-height: 2500px;\"\n+    src=\"https://cronometrajeinstantaneo.com/resultados/canav-rally-raid-3-fecha-2023/filtros\">\n+    </iframe>\n+</div>\n+\n+## COMANDOS\n+\n+```sql\n+ALTER TABLE `lecturas` CHANGE `tipo` `tipo` ENUM('app','rfid','fotocelula','anube') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'app';\n+\n+INSERT INTO datosxparticipantes SET idevento = 425, iddato = 'equipo', idinscripcion = 238601, dato = 'Duplessis Team';\n+\n+=CONCAT(\"INSERT INTO lecturas SET idevento = 425, idcontrol = 1680, estado = 'ok', tipo = 'fotocelula', idparticipante = \";B2;\", tiempo = '2021-02-20 \";I3;\"';\")\n+\n+UPDATE `datosxparticipantes` SET dato = 'Chile' WHERE idevento = 425 AND dato = 'Chile ';\n+\n+UPDATE `lecturas` SET tiempo = DATE_SUB(tiempo, INTERVAL 2 MINUTE) WHERE idcontrol = 1800;\n+\n+INSERT INTO penas SET idevento = 507, idetapa = 2231, idparticipante, tiempo, observacion\n+\n+SELECT idevento, idetapa, idparticipante, tiempo, observacion FROM penas\n+WHERE idetapa IN (2234,2235,2236,3503,2242,2243,2244,3501)\n+ORDER BY idparticipante\n+2239\n+2247\n+\n+```\n+\n+## CONFIGURACIONES resultados_js\n+\n+var contenedor_resultados = document.getElementById(\"contenedor_resultados\");\n+contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');\n+contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');\n+contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');\n+contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');\n+contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');\n+\n+\n+## TENER EN CUENTA PARA SARR\n+\n+- Presupuestar con tiempo y que Juanpi lo apruebe\n+- Alguien encargado de los tiempos que hable con los pilotos (reglas claras pre-escritas) así delega esa tarea\n+- Definir con tiempo como es todo el reglamento\n+  - Cuánto se completa de tiempo a los que no terminan uno, dos o más especiales\n+  - A quién se le completa el tiempo\n+  - Penalización o bonificación: ¿cómo es el proceso? ¿lo puedo hacer yo?\n+- Acordar cierre de tiempos por etapa, los participantes tienen que saberlo, se firma una hora diaria y se entrega a FIM y a los pilotos\n+- Ofrecer mejoras\n+- Cometí muchos errores manuales, hay que reducir al máximo el trabajo manual\n+\n+Mejoras para el SARR\n+- Mostrar en el ticket digital Fecha y hora de cada cambio de tiempo y cada penalización público para el participante\n+- Tarjetas digitales completas, parecido al ticket, pero con otro diseño\n+- Cada puesto de control con app y con tarjeta digital (ideal sería Beacons)\n+- Impresiones con logos y formato de impresión diferente al de pantalla (diseñar con tiempo, cada informe por etapa, FIM, generales, por categorías, participantes, etc.)\n+- Ocultar columna de subcategorías en Fechas FIM\n+- Separar Motos y Quads, autos y UTV (lo pidió FIM me parece que corresponde siempre)\n+- Informe con orden de largadas (sería ideal poder establecer la hora, aunque sea con parche con edicion por parámetro GET)\n+- Pensar cronometraje y backup a prueba de lluvia y a prueba de fallo de Stella\n+- Importación de penas\n+- Configurar todos los PCs (ASS y DSS) en el sistema, aunque no se muestren para poder utilizar el informe de PCs y controlar que los tiempos de enlace estén correctos\n+\n"}, {"date": 1740620090257, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -253,16 +253,62 @@\n idevento: 2338\n ID=7088\n Token=1fCjTmxrjD\n \n-ETAPA 4\n+ETAPA 5\n \n-# H.WP 7\n-# (18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n-# 392678|7-N|1760|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18201&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=7-N&waypoint_id=392678&cache=sarr-2025\"\n+# H.WP 10\n+# (18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n+# 392829|10-N|3060|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18211&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=10-N&waypoint_id=392829&cache=sarr-2025\"\n \n+# H.WP 19\n+# (18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n+# 392838|19-DZ|67930|DZ|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18212&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=19-DZ&waypoint_id=392838&cache=sarr-2025\"\n \n+# H.WP 31\n+# (18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n+# 392850|31-M|115530|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18213&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=31-M&waypoint_id=392850&cache=sarr-2025\"\n+\n+# H.ASS1 34\n+# (18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n+# 392853|ASS1|125740|INEU-ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18214&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=ASS1&waypoint_id=392853&cache=sarr-2025\"\n+\n+# H.WP 38\n+# (18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n+# 392857|38-M|144790|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18215&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=38-M&waypoint_id=392857&cache=sarr-2025\"\n+\n+# H.WP 42\n+# (18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n+# 392861|42-M|155250|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18216&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=42-M&waypoint_id=392861&cache=sarr-2025\"\n+\n+# H.WP 49\n+# (18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n+# 392868|49-N|174830|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18217&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=49-N&waypoint_id=392868&cache=sarr-2025\"\n+\n+# H.ASS2 52\n+# (18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n+# 392871|ASS2|192560|INEU-ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18218&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=ASS2&waypoint_id=392871&cache=sarr-2025\"\n+\n+# H.WP 59\n+# (18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n+# 392878|59-M|207310|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18219&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=59-M&waypoint_id=392878&cache=sarr-2025\"\n+\n+# H.WP 68\n+# (18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n+# 392887|68-M|243270|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18220&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=68-M&waypoint_id=392887&cache=sarr-2025\"\n+\n+\n+\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}, {"date": 1740697289894, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -56,130 +56,8 @@\n 18201\n \n INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n \n-# H.WP 7\n-(18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96240, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96216, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96234, 'H.WP 7', 41, 'parcial'),\n-\n-# H.WP 13\n-(18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96240, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96216, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96234, 'H.WP 13', 42, 'parcial'),\n-\n-# H.WP 21\n-(18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96240, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96216, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96234, 'H.WP 21', 43, 'parcial'),\n-\n-# H.WP 32\n-(18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96240, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96216, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96234, 'H.WP 32', 44, 'parcial'),\n-\n-# H.ASS1 39\n-(18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96240, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96216, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96234, 'H.ASS1 39', 45, 'parcial'),\n-\n-# H.WP 45\n-(18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96240, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96216, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96234, 'H.WP 45', 46, 'parcial'),\n-\n-# H.ASS2 52\n-(18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96240, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96216, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96234, 'H.ASS2 52', 47, 'parcial'),\n-\n-# H.WP 58\n-(18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96240, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96216, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96234, 'H.WP 58', 48, 'parcial'),\n-\n-# H.WP 65\n-(18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96240, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96216, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96234, 'H.WP 65', 49, 'parcial'),\n-\n-# H.WP 71\n-(18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96240, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96216, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96234, 'H.WP 71', 50, 'parcial'),\n-\n-\n-# H.WP 10\n-(18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96223, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96217, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96241, 'H.WP 10', 51, 'parcial'),\n-\n-# H.WP 19\n-(18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96223, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96217, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96241, 'H.WP 19', 52, 'parcial'),\n-\n-# H.WP 31\n-(18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96223, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96217, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96241, 'H.WP 31', 53, 'parcial'),\n-\n-# H.ASS1 34\n-(18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96223, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96217, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96241, 'H.ASS1 34', 54, 'parcial'),\n-\n-# H.WP 38\n-(18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96223, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96217, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96241, 'H.WP 38', 55, 'parcial'),\n-\n-# H.WP 42\n-(18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96223, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96217, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96241, 'H.WP 42', 56, 'parcial'),\n-\n-# H.WP 49\n-(18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96223, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96217, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96241, 'H.WP 49', 57, 'parcial'),\n-\n-# H.ASS2 52\n-(18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96223, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96217, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96241, 'H.ASS2 52', 58, 'parcial'),\n-\n-# H.WP 59\n-(18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96223, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96217, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96241, 'H.WP 59', 59, 'parcial'),\n-\n-# H.WP 68\n-(18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96223, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96217, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96241, 'H.WP 68', 60, 'parcial'),\n-\n-\n # H.WP 4\n (18221, 2338, 'SA61', 97960, 'H.WP 4', 61, 'parcial'),\n (18221, 2338, 'SA61', 97963, 'H.WP 4', 61, 'parcial'),\n (18221, 2338, 'SA61', 97966, 'H.WP 4', 61, 'parcial'),\n@@ -389,397 +267,4 @@\n - Pensar cronometraje y backup a prueba de lluvia y a prueba de fallo de Stella\n - Importación de penas\n - Configurar todos los PCs (ASS y DSS) en el sistema, aunque no se muestren para poder utilizar el informe de PCs y controlar que los tiempos de enlace estén correctos\n \n-*******************************************************************************\n-  SARR 2023\n-*******************************************************************************\n-\n-## CAMBIOS PARA F1 KART\n-\n-Enlace y QR para Inscripciones (se pueden inscribir los pilotos ellos mismos o usarlo ustedes para agregarlos más fácil que en el sistema a mano)\n-https://cronometrajeinstantaneo.com/inscripciones/f1-kart-san-juan\n-\n-Enlace y QR para los resultados de la carrera actual\n-https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/filtros\n-\n-Enlace el televisor con la carrera actual (se actualiza automáticamente)\n-https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/?actualizar=60\n-\n-Los enlaces de las carreras terminadas, para que sigan viendo sus tiempos al día siguiente, se envían por mail al mail con el que se registraron\n-\n-\n-## Copiar de acá\n-https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/map\n-https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/standings\n-\n-\n-## DESPUÉS\n-\n-- [ ] Mostrar los parciales en los filtros de etapas nada más\n-- [ ] Sistema de Inscripciones, sistema de acreditaciones y listado de participantes automáticos\n-- [ ] Configurar sistema de puntos: Los puntos es al final del evento, se les da puntaje por largar por llegar, por ganar cada etapa (1° , 2° , 3°) y al final por la carrera general.\n-- [ ] Herramienta para sacar el orden de largada\n-- [ ] Agregar Foto de los participantes a los resultados\n-\n-- [ ] Agregar la opción de filtros como selectores\n-- [ ] Ver si podemos poner parciales como un selector más\n-- [ ] Agregar íconos en los selectores (y si hay una sola no poner el botón)\n-- [ ] Importador de penas\n-\n-\n-\n-*******************************************************************************\n-  CONFIGURACIONES VARIAS\n-*******************************************************************************\n-\n-### CARRERAS\n-\n-11986 \t2338 \tMOTOS \t1\n-11990 \t2338 \tQUADS \t2\n-11991 \t2338 \tUTV \t3\n-11987 \t2338 \tAUTOS \t4\n-\n-### ETAPAS\n-\n-SELECT idetapa, nombre FROM etapas WHERE idevento = 2338 ORDER BY nombre;\n-\n-SELECT * FROM `controles` ORDER BY `controles`.`idcontrol` DESC LIMIT 1;\n-\n-18201\n-\n-INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n-\n-# H.WP 7\n-(18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96240, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96216, 'H.WP 7', 41, 'parcial'),\n-(18201, 2338, 'SA41', 96234, 'H.WP 7', 41, 'parcial'),\n-\n-# H.WP 13\n-(18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96240, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96216, 'H.WP 13', 42, 'parcial'),\n-(18202, 2338, 'SA42', 96234, 'H.WP 13', 42, 'parcial'),\n-\n-# H.WP 21\n-(18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96240, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96216, 'H.WP 21', 43, 'parcial'),\n-(18203, 2338, 'SA43', 96234, 'H.WP 21', 43, 'parcial'),\n-\n-# H.WP 32\n-(18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96240, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96216, 'H.WP 32', 44, 'parcial'),\n-(18204, 2338, 'SA44', 96234, 'H.WP 32', 44, 'parcial'),\n-\n-# H.ASS1 39\n-(18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96240, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96216, 'H.ASS1 39', 45, 'parcial'),\n-(18205, 2338, 'SA45', 96234, 'H.ASS1 39', 45, 'parcial'),\n-\n-# H.WP 45\n-(18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96240, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96216, 'H.WP 45', 46, 'parcial'),\n-(18206, 2338, 'SA46', 96234, 'H.WP 45', 46, 'parcial'),\n-\n-# H.ASS2 52\n-(18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96240, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96216, 'H.ASS2 52', 47, 'parcial'),\n-(18207, 2338, 'SA47', 96234, 'H.ASS2 52', 47, 'parcial'),\n-\n-# H.WP 58\n-(18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96240, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96216, 'H.WP 58', 48, 'parcial'),\n-(18208, 2338, 'SA48', 96234, 'H.WP 58', 48, 'parcial'),\n-\n-# H.WP 65\n-(18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96240, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96216, 'H.WP 65', 49, 'parcial'),\n-(18209, 2338, 'SA49', 96234, 'H.WP 65', 49, 'parcial'),\n-\n-# H.WP 71\n-(18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96240, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96216, 'H.WP 71', 50, 'parcial'),\n-(18210, 2338, 'SA4A', 96234, 'H.WP 71', 50, 'parcial'),\n-\n-\n-# H.WP 10\n-(18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96223, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96217, 'H.WP 10', 51, 'parcial'),\n-(18211, 2338, 'SA51', 96241, 'H.WP 10', 51, 'parcial'),\n-\n-# H.WP 19\n-(18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96223, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96217, 'H.WP 19', 52, 'parcial'),\n-(18212, 2338, 'SA52', 96241, 'H.WP 19', 52, 'parcial'),\n-\n-# H.WP 31\n-(18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96223, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96217, 'H.WP 31', 53, 'parcial'),\n-(18213, 2338, 'SA53', 96241, 'H.WP 31', 53, 'parcial'),\n-\n-# H.ASS1 34\n-(18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96223, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96217, 'H.ASS1 34', 54, 'parcial'),\n-(18214, 2338, 'SA54', 96241, 'H.ASS1 34', 54, 'parcial'),\n-\n-# H.WP 38\n-(18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96223, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96217, 'H.WP 38', 55, 'parcial'),\n-(18215, 2338, 'SA55', 96241, 'H.WP 38', 55, 'parcial'),\n-\n-# H.WP 42\n-(18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96223, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96217, 'H.WP 42', 56, 'parcial'),\n-(18216, 2338, 'SA56', 96241, 'H.WP 42', 56, 'parcial'),\n-\n-# H.WP 49\n-(18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96223, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96217, 'H.WP 49', 57, 'parcial'),\n-(18217, 2338, 'SA57', 96241, 'H.WP 49', 57, 'parcial'),\n-\n-# H.ASS2 52\n-(18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96223, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96217, 'H.ASS2 52', 58, 'parcial'),\n-(18218, 2338, 'SA58', 96241, 'H.ASS2 52', 58, 'parcial'),\n-\n-# H.WP 59\n-(18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96223, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96217, 'H.WP 59', 59, 'parcial'),\n-(18219, 2338, 'SA59', 96241, 'H.WP 59', 59, 'parcial'),\n-\n-# H.WP 68\n-(18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96223, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96217, 'H.WP 68', 60, 'parcial'),\n-(18220, 2338, 'SA5A', 96241, 'H.WP 68', 60, 'parcial'),\n-\n-\n-# H.WP 4\n-(18221, 2338, 'SA61', 97960, 'H.WP 4', 61, 'parcial'),\n-(18221, 2338, 'SA61', 97963, 'H.WP 4', 61, 'parcial'),\n-(18221, 2338, 'SA61', 97966, 'H.WP 4', 61, 'parcial'),\n-(18221, 2338, 'SA61', 97957, 'H.WP 4', 61, 'parcial'),\n-\n-# H.WP 10\n-(18222, 2338, 'SA62', 97960, 'H.WP 10', 62, 'parcial'),\n-(18222, 2338, 'SA62', 97963, 'H.WP 10', 62, 'parcial'),\n-(18222, 2338, 'SA62', 97966, 'H.WP 10', 62, 'parcial'),\n-(18222, 2338, 'SA62', 97957, 'H.WP 10', 62, 'parcial'),\n-\n-# H.WP 18\n-(18223, 2338, 'SA63', 97960, 'H.WP 18', 63, 'parcial'),\n-(18223, 2338, 'SA63', 97963, 'H.WP 18', 63, 'parcial'),\n-(18223, 2338, 'SA63', 97966, 'H.WP 18', 63, 'parcial'),\n-(18223, 2338, 'SA63', 97957, 'H.WP 18', 63, 'parcial'),\n-\n-# H.WP 29\n-(18224, 2338, 'SA64', 97960, 'H.WP 29', 64, 'parcial'),\n-(18224, 2338, 'SA64', 97963, 'H.WP 29', 64, 'parcial'),\n-(18224, 2338, 'SA64', 97966, 'H.WP 29', 64, 'parcial'),\n-(18224, 2338, 'SA64', 97957, 'H.WP 29', 64, 'parcial'),\n-\n-# H.ASS1 39\n-(18225, 2338, 'SA65', 97960, 'H.ASS1 39', 65, 'parcial'),\n-(18225, 2338, 'SA65', 97963, 'H.ASS1 39', 65, 'parcial'),\n-(18225, 2338, 'SA65', 97966, 'H.ASS1 39', 65, 'parcial'),\n-(18225, 2338, 'SA65', 97957, 'H.ASS1 39', 65, 'parcial'),\n-\n-# H.WP 53\n-(18226, 2338, 'SA66', 97960, 'H.WP 53', 66, 'parcial'),\n-(18226, 2338, 'SA66', 97963, 'H.WP 53', 66, 'parcial'),\n-(18226, 2338, 'SA66', 97966, 'H.WP 53', 66, 'parcial'),\n-(18226, 2338, 'SA66', 97957, 'H.WP 53', 66, 'parcial'),\n-\n-# H.WP 59\n-(18227, 2338, 'SA67', 97960, 'H.WP 59', 67, 'parcial'),\n-(18227, 2338, 'SA67', 97963, 'H.WP 59', 67, 'parcial'),\n-(18227, 2338, 'SA67', 97966, 'H.WP 59', 67, 'parcial'),\n-(18227, 2338, 'SA67', 97957, 'H.WP 59', 67, 'parcial'),\n-\n-# H.WP 64\n-(18228, 2338, 'SA68', 97960, 'H.WP 64', 68, 'parcial'),\n-(18228, 2338, 'SA68', 97963, 'H.WP 64', 68, 'parcial'),\n-(18228, 2338, 'SA68', 97966, 'H.WP 64', 68, 'parcial'),\n-(18228, 2338, 'SA68', 97957, 'H.WP 64', 68, 'parcial'),\n-\n-# H.ASS2 74\n-(18229, 2338, 'SA69', 97960, 'H.ASS2 74', 69, 'parcial'),\n-(18229, 2338, 'SA69', 97963, 'H.ASS2 74', 69, 'parcial'),\n-(18229, 2338, 'SA69', 97966, 'H.ASS2 74', 69, 'parcial'),\n-(18229, 2338, 'SA69', 97957, 'H.ASS2 74', 69, 'parcial'),\n-\n-# H.WP 79\n-(18230, 2338, 'SA6A', 97960, 'H.WP 79', 70, 'parcial'),\n-(18230, 2338, 'SA6A', 97963, 'H.WP 79', 70, 'parcial'),\n-(18230, 2338, 'SA6A', 97966, 'H.WP 79', 70, 'parcial'),\n-(18230, 2338, 'SA6A', 97957, 'H.WP 79', 70, 'parcial'),\n-\n-# H.WP 82\n-(18231, 2338, 'SA6B', 97960, 'H.WP 82', 71, 'parcial'),\n-(18231, 2338, 'SA6B', 97963, 'H.WP 82', 71, 'parcial'),\n-(18231, 2338, 'SA6B', 97966, 'H.WP 82', 71, 'parcial'),\n-(18231, 2338, 'SA6B', 97957, 'H.WP 82', 71, 'parcial'),\n-\n-\n-\n-https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD\n-http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD\n-\n-idevento: 2338\n-ID=7088\n-Token=1fCjTmxrjD\n-\n-ETAPA 4\n-\n-# H.WP 7\n-# (18201, 2338, 'SA41', 96222, 'H.WP 7', 41, 'parcial'),\n-# 392678|7-N|1760|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18201&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=7-N&waypoint_id=392678&cache=sarr-2025\"\n-\n-# H.WP 13\n-# (18202, 2338, 'SA42', 96222, 'H.WP 13', 42, 'parcial'),\n-# 392684|13-M|30890|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18202&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=13-M&waypoint_id=392684&cache=sarr-2025\"\n-\n-# H.WP 21\n-# (18203, 2338, 'SA43', 96222, 'H.WP 21', 43, 'parcial'),\n-# 392692|21-M|67170|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18203&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=21-M&waypoint_id=392692&cache=sarr-2025\"\n-\n-# H.WP 32\n-# (18204, 2338, 'SA44', 96222, 'H.WP 32', 44, 'parcial'),\n-# 392703|32-M|122650|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18204&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=32-M&waypoint_id=392703&cache=sarr-2025\"\n-\n-# H.ASS1 39\n-# (18205, 2338, 'SA45', 96222, 'H.ASS1 39', 45, 'parcial'),\n-# 392710|ASS1|181030|INEU-ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18205&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=ASS1&waypoint_id=392710&cache=sarr-2025\"\n-\n-# H.WP 45\n-# (18206, 2338, 'SA46', 96222, 'H.WP 45', 46, 'parcial'),\n-# 392716|DSS2|317790|FNEU-DSS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18206&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=DSS2&waypoint_id=392716&cache=sarr-2025\"\n-\n-# H.ASS2 52\n-# (18207, 2338, 'SA47', 96222, 'H.ASS2 52', 47, 'parcial'),\n-# 392723|52-N|331250|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18207&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=52-N&waypoint_id=392723&cache=sarr-2025\"\n-\n-# H.WP 58\n-# (18208, 2338, 'SA48', 96222, 'H.WP 58', 48, 'parcial'),\n-# 392729|58-M|342700|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18208&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=58-M&waypoint_id=392729&cache=sarr-2025\"\n-\n-# H.WP 65\n-# (18209, 2338, 'SA49', 96222, 'H.WP 65', 49, 'parcial'),\n-# 392736|65-N|361810|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18209&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=65-N&waypoint_id=392736&cache=sarr-2025\"\n-\n-# H.WP 71\n-# (18210, 2338, 'SA4A', 96222, 'H.WP 71', 50, 'parcial'),\n-# 392742|71-N|369890|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18210&idRace=7088&token=1fCjTmxrjD&n_etapa=4&waypoint_code=71-N&waypoint_id=392742&cache=sarr-2025\"\n-\n-\n-\n-\n----\n-\n-UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n-UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n-\n-SELECT idcontrol, idparticipante, tiempo FROM lecturas WHERE idcontrol IN (9160, 9172) AND estado != 'eliminado'\n-AND (idparticipante > 100 OR idparticipante < 200)\n-ORDER BY idcontrol, tiempo;\n-\n-SELECT * FROM penas WHERE idetapa IN (6952, 6971, 6961, 6981) LIMIT 500;\n-\n-DELETE FROM lecturas WHERE idcontrol IN (12550);\n-\n-*******************************************************************************\n-\n-## CONFIGURACION JIMDO Y APP\n-https://cms.e.jimdo.com/app/s82a17b50740eef8e/p36f3ef62dc40b75f?cmsEdit=1\n-\n-<div style=\"position: relative; padding-bottom: 56.25%; min-height: 2500px;\">\n-    <iframe frameborder=\"0\" allowfullscreen=\"allowfullscreen\"\n-    style=\"position: absolute; top:0; left: 0; width: 100%; min-height: 2500px;\"\n-    src=\"https://cronometrajeinstantaneo.com/resultados/canav-rally-raid-3-fecha-2023/filtros\">\n-    </iframe>\n-</div>\n-\n-## COMANDOS\n-\n-```sql\n-ALTER TABLE `lecturas` CHANGE `tipo` `tipo` ENUM('app','rfid','fotocelula','anube') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'app';\n-\n-INSERT INTO datosxparticipantes SET idevento = 425, iddato = 'equipo', idinscripcion = 238601, dato = 'Duplessis Team';\n-\n-=CONCAT(\"INSERT INTO lecturas SET idevento = 425, idcontrol = 1680, estado = 'ok', tipo = 'fotocelula', idparticipante = \";B2;\", tiempo = '2021-02-20 \";I3;\"';\")\n-\n-UPDATE `datosxparticipantes` SET dato = 'Chile' WHERE idevento = 425 AND dato = 'Chile ';\n-\n-UPDATE `lecturas` SET tiempo = DATE_SUB(tiempo, INTERVAL 2 MINUTE) WHERE idcontrol = 1800;\n-\n-INSERT INTO penas SET idevento = 507, idetapa = 2231, idparticipante, tiempo, observacion\n-\n-SELECT idevento, idetapa, idparticipante, tiempo, observacion FROM penas\n-WHERE idetapa IN (2234,2235,2236,3503,2242,2243,2244,3501)\n-ORDER BY idparticipante\n-2239\n-2247\n-\n-```\n-\n-## CONFIGURACIONES resultados_js\n-\n-var contenedor_resultados = document.getElementById(\"contenedor_resultados\");\n-contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');\n-contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');\n-contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');\n-contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');\n-contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');\n-\n-\n-## TENER EN CUENTA PARA SARR\n-\n-- Presupuestar con tiempo y que Juanpi lo apruebe\n-- Alguien encargado de los tiempos que hable con los pilotos (reglas claras pre-escritas) así delega esa tarea\n-- Definir con tiempo como es todo el reglamento\n-  - Cuánto se completa de tiempo a los que no terminan uno, dos o más especiales\n-  - A quién se le completa el tiempo\n-  - Penalización o bonificación: ¿cómo es el proceso? ¿lo puedo hacer yo?\n-- Acordar cierre de tiempos por etapa, los participantes tienen que saberlo, se firma una hora diaria y se entrega a FIM y a los pilotos\n-- Ofrecer mejoras\n-- Cometí muchos errores manuales, hay que reducir al máximo el trabajo manual\n-\n-Mejoras para el SARR\n-- Mostrar en el ticket digital Fecha y hora de cada cambio de tiempo y cada penalización público para el participante\n-- Tarjetas digitales completas, parecido al ticket, pero con otro diseño\n-- Cada puesto de control con app y con tarjeta digital (ideal sería Beacons)\n-- Impresiones con logos y formato de impresión diferente al de pantalla (diseñar con tiempo, cada informe por etapa, FIM, generales, por categorías, participantes, etc.)\n-- Ocultar columna de subcategorías en Fechas FIM\n-- Separar Motos y Quads, autos y UTV (lo pidió FIM me parece que corresponde siempre)\n-- Informe con orden de largadas (sería ideal poder establecer la hora, aunque sea con parche con edicion por parámetro GET)\n-- Pensar cronometraje y backup a prueba de lluvia y a prueba de fallo de Stella\n-- Importación de penas\n-- Configurar todos los PCs (ASS y DSS) en el sistema, aunque no se muestren para poder utilizar el informe de PCs y controlar que los tiempos de enlace estén correctos\n-\n"}, {"date": 1740845033553, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -131,62 +131,24 @@\n idevento: 2338\n ID=7088\n Token=1fCjTmxrjD\n \n-ETAPA 5\n+ETAPA 7\n \n-# H.WP 10\n-# (18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n-# 392829|10-N|3060|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18211&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=10-N&waypoint_id=392829&cache=sarr-2025\"\n \n-# H.WP 19\n-# (18212, 2338, 'SA52', 96235, 'H.WP 19', 52, 'parcial'),\n-# 392838|19-DZ|67930|DZ|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18212&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=19-DZ&waypoint_id=392838&cache=sarr-2025\"\n+# 18308 \t2338 \tYZPD \t97958 \tHst ASS4 \t84\n+# 393718|ASS4|176150|ASS|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18308&idRace=7088&token=1fCjTmxrjD&n_etapa=7&waypoint_code=ASS4&waypoint_id=393718&cache=sarr-2025\"\n \n-# H.WP 31\n-# (18213, 2338, 'SA53', 96235, 'H.WP 31', 53, 'parcial'),\n-# 392850|31-M|115530|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18213&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=31-M&waypoint_id=392850&cache=sarr-2025\"\n \n-# H.ASS1 34\n-# (18214, 2338, 'SA54', 96235, 'H.ASS1 34', 54, 'parcial'),\n-# 392853|ASS1|125740|INEU-ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18214&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=ASS1&waypoint_id=392853&cache=sarr-2025\"\n \n-# H.WP 38\n-# (18215, 2338, 'SA55', 96235, 'H.WP 38', 55, 'parcial'),\n-# 392857|38-M|144790|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18215&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=38-M&waypoint_id=392857&cache=sarr-2025\"\n \n-# H.WP 42\n-# (18216, 2338, 'SA56', 96235, 'H.WP 42', 56, 'parcial'),\n-# 392861|42-M|155250|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18216&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=42-M&waypoint_id=392861&cache=sarr-2025\"\n+# H.WP 10\n+# (18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),\n+# 392829|10-N|3060|TP|1\n+*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18211&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=10-N&waypoint_id=392829&cache=sarr-2025\"\n \n-# H.WP 49\n-# (18217, 2338, 'SA57', 96235, 'H.WP 49', 57, 'parcial'),\n-# 392868|49-N|174830|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18217&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=49-N&waypoint_id=392868&cache=sarr-2025\"\n \n-# H.ASS2 52\n-# (18218, 2338, 'SA58', 96235, 'H.ASS2 52', 58, 'parcial'),\n-# 392871|ASS2|192560|INEU-ASS|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18218&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=ASS2&waypoint_id=392871&cache=sarr-2025\"\n-\n-# H.WP 59\n-# (18219, 2338, 'SA59', 96235, 'H.WP 59', 59, 'parcial'),\n-# 392878|59-M|207310|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18219&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=59-M&waypoint_id=392878&cache=sarr-2025\"\n-\n-# H.WP 68\n-# (18220, 2338, 'SA5A', 96235, 'H.WP 68', 60, 'parcial'),\n-# 392887|68-M|243270|TP|1\n-*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18220&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=68-M&waypoint_id=392887&cache=sarr-2025\"\n-\n-\n-\n ---\n \n UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\n UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n"}], "date": 1735821689501, "name": "Commit-0", "content": "*******************************************************************************\n  SARR 2023\n*******************************************************************************\n\n## CAMBIOS PARA F1 KART\n\nEnlace y QR para Inscripciones (se pueden inscribir los pilotos ellos mismos o usarlo ustedes para agregarlos más fácil que en el sistema a mano)\nhttps://cronometrajeinstantaneo.com/inscripciones/f1-kart-san-juan\n\nEnlace y QR para los resultados de la carrera actual\nhttps://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/filtros\n\nEnlace el televisor con la carrera actual (se actualiza automáticamente)\nhttps://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/?actualizar=60\n\nLos enlaces de las carreras terminadas, para que sigan viendo sus tiempos al día siguiente, se envían por mail al mail con el que se registraron\n\n\n## Copiar de acá\nhttps://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/map\nhttps://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/standings\n\n\n## DESPUÉS\n\n- [ ] Mostrar los parciales en los filtros de etapas nada más\n- [ ] Sistema de Inscripciones, sistema de acreditaciones y listado de participantes automáticos\n- [ ] Configurar sistema de puntos: Los puntos es al final del evento, se les da puntaje por largar por llegar, por ganar cada etapa (1° , 2° , 3°) y al final por la carrera general.\n- [ ] Herramienta para sacar el orden de largada\n- [ ] Agregar Foto de los participantes a los resultados\n\n- [ ] Agregar la opción de filtros como selectores\n- [ ] Ver si podemos poner parciales como un selector más\n- [ ] Agregar íconos en los selectores (y si hay una sola no poner el botón)\n- [ ] Importador de penas\n\n\n\n*******************************************************************************\n  CONFIGURACIONES VARIAS\n*******************************************************************************\n\nINSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES\n\n(13340, 1630, 'SA70', 11576, 'Largada', 70, 'largada'),\n(13340, 1630, 'SA70', 11579, 'Largada', 70, 'largada'),\n(13340, 1630, 'SA70', 11582, 'Largada', 70, 'largada'),\n(13340, 1630, 'SA70', 11585, 'Largada', 70, 'largada'),\n\n(13341, 1630, 'SA71', 11576, 'H.WP 5', 71, 'parcial'),\n(13341, 1630, 'SA71', 11579, 'H.WP 5', 71, 'parcial'),\n(13341, 1630, 'SA71', 11582, 'H.WP 5', 71, 'parcial'),\n(13341, 1630, 'SA71', 11585, 'H.WP 5', 71, 'parcial'),\n\n(13342, 1630, 'SA72', 11576, 'H.WP 10', 72, 'parcial'),\n(13342, 1630, 'SA72', 11579, 'H.WP 10', 72, 'parcial'),\n(13342, 1630, 'SA72', 11582, 'H.WP 10', 72, 'parcial'),\n(13342, 1630, 'SA72', 11585, 'H.WP 10', 72, 'parcial'),\n\n(13343, 1630, 'SA73', 11576, 'H.WP 15', 73, 'parcial'),\n(13343, 1630, 'SA73', 11579, 'H.WP 15', 73, 'parcial'),\n(13343, 1630, 'SA73', 11582, 'H.WP 15', 73, 'parcial'),\n(13343, 1630, 'SA73', 11585, 'H.WP 15', 73, 'parcial'),\n\n(13344, 1630, 'SA74', 11576, 'H.WP 20', 74, 'parcial'),\n(13344, 1630, 'SA74', 11579, 'H.WP 20', 74, 'parcial'),\n(13344, 1630, 'SA74', 11582, 'H.WP 20', 74, 'parcial'),\n(13344, 1630, 'SA74', 11585, 'H.WP 20', 74, 'parcial'),\n\n(13345, 1630, 'SA75', 11576, 'H.WP 25 ASS1', 75, 'parcial'),\n(13345, 1630, 'SA75', 11579, 'H.WP 25 ASS1', 75, 'parcial'),\n(13345, 1630, 'SA75', 11582, 'H.WP 25 ASS1', 75, 'parcial'),\n(13345, 1630, 'SA75', 11585, 'H.WP 25 ASS1', 75, 'parcial'),\n\n(13346, 1630, 'SA76', 11576, 'H.WP 28', 76, 'parcial'),\n(13346, 1630, 'SA76', 11579, 'H.WP 28', 76, 'parcial'),\n(13346, 1630, 'SA76', 11582, 'H.WP 28', 76, 'parcial'),\n(13346, 1630, 'SA76', 11585, 'H.WP 28', 76, 'parcial'),\n\n(13347, 1630, 'SA77', 11576, 'H.WP 34', 77, 'parcial'),\n(13347, 1630, 'SA77', 11579, 'H.WP 34', 77, 'parcial'),\n(13347, 1630, 'SA77', 11582, 'H.WP 34', 77, 'parcial'),\n(13347, 1630, 'SA77', 11585, 'H.WP 34', 77, 'parcial'),\n\n(13348, 1630, 'SA78', 11576, 'H.WP 41', 78, 'parcial'),\n(13348, 1630, 'SA78', 11579, 'H.WP 41', 78, 'parcial'),\n(13348, 1630, 'SA78', 11582, 'H.WP 41', 78, 'parcial'),\n(13348, 1630, 'SA78', 11585, 'H.WP 41', 78, 'parcial'),\n\n(13349, 1630, 'SA79', 11576, 'H.WP 44 ASS2', 79, 'parcial'),\n(13349, 1630, 'SA79', 11579, 'H.WP 44 ASS2', 79, 'parcial'),\n(13349, 1630, 'SA79', 11582, 'H.WP 44 ASS2', 79, 'parcial'),\n(13349, 1630, 'SA79', 11585, 'H.WP 44 ASS2', 79, 'parcial'),\n\n(13350, 1630, 'SA7A', 11576, 'H.WP 54', 80, 'parcial'),\n(13350, 1630, 'SA7A', 11579, 'H.WP 54', 80, 'parcial'),\n(13350, 1630, 'SA7A', 11582, 'H.WP 54', 80, 'parcial'),\n(13350, 1630, 'SA7A', 11585, 'H.WP 54', 80, 'parcial'),\n\n(13351, 1630, 'SA7B', 11576, 'H.WP 60', 81, 'parcial'),\n(13351, 1630, 'SA7B', 11579, 'H.WP 60', 81, 'parcial'),\n(13351, 1630, 'SA7B', 11582, 'H.WP 60', 81, 'parcial'),\n(13351, 1630, 'SA7B', 11585, 'H.WP 60', 81, 'parcial'),\n\n(13352, 1630, 'SA7C', 11576, 'H.WP 67', 82, 'parcial'),\n(13352, 1630, 'SA7C', 11579, 'H.WP 67', 82, 'parcial'),\n(13352, 1630, 'SA7C', 11582, 'H.WP 67', 82, 'parcial'),\n(13352, 1630, 'SA7C', 11585, 'H.WP 67', 82, 'parcial'),\n\n(13353, 1630, 'SA7D', 11576, 'H.WP 70 ASS 3', 83, 'parcial'),\n(13353, 1630, 'SA7D', 11579, 'H.WP 70 ASS 3', 83, 'parcial'),\n(13353, 1630, 'SA7D', 11582, 'H.WP 70 ASS 3', 83, 'parcial'),\n(13353, 1630, 'SA7D', 11585, 'H.WP 70 ASS 3', 83, 'parcial'),\n\n(13354, 1630, 'SA7F', 11576, 'ASS', 84, 'final'),\n(13354, 1630, 'SA7F', 11579, 'ASS', 84, 'final'),\n(13354, 1630, 'SA7F', 11582, 'ASS', 84, 'final'),\n(13354, 1630, 'SA7F', 11585, 'ASS', 84, 'final')\n\n\n\n\nhttp://rest.anube.es/rallyrest/default/api/waypoint_times/5704/1.xml?token=RQlQysdXQ2\nhttps://html5.anube.es/?rally=rally5704&port=auto&token=RQlQysdXQ2\nidevento: 1630\nID=5704\nToken=RQlQysdXQ2\n\n\nETAPA 8\n\n285839|ASS1|134200|ASS|1\n285851|ASS2|266100|ASS|1\n285865|ASS3|297700|ASS|1\n\n13370 \t1630 \tSA80 \t11577 \tLargada \t80 \tlargada\n13371 \t1630 \tSA81 \t11577 \tH.ASS1 \t81 \tparcial\n13372 \t1630 \tSA82 \t11577 \tH.ASS2 \t82 \tparcial\n13373 \t1630 \tSA83 \t11577 \tH.ASS3 \t83 \tparcial\n13374 \t1630 \tSA8F \t11577 \tASS8 \t84 \tfinal\n\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13371&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS1&waypoint_id=285839&cache=sarr-2024-sertoes-series\"\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13372&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS2&waypoint_id=285851&cache=sarr-2024-sertoes-series\"\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13373&idRace=5704&token=RQlQysdXQ2&n_etapa=8&waypoint_code=ASS3&waypoint_id=285865&cache=sarr-2024-sertoes-series\"\n\nETAPA 7\n\n(13340, 1630, 'SA70', 11576, 'Largada', 70, 'largada'),\n\n# P1 WP5\n# (13341, 1630, 'SA71', 11576, 'H.WP 5', 71, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13341&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P2 WP10\n# (13342, 1630, 'SA72', 11576, 'H.WP 10', 72, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13342&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P3 WP15\n# (13343, 1630, 'SA73', 11576, 'H.WP 15', 73, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13343&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P4 WP20\n# (13344, 1630, 'SA74', 11576, 'H.WP 20', 74, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13344&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P5 WP25 ass1\n# (13345, 1630, 'SA75', 11576, 'H.WP 25 ASS1', 75, 'parcial'),\n285756\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13345&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=ASS1&waypoint_id=285756&cache=sarr-2024-sertoes-series\"\n\n# P6 WP28\n# (13346, 1630, 'SA76', 11576, 'H.WP 28', 76, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13346&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P7 WP34\n# (13347, 1630, 'SA77', 11576, 'H.WP 34', 77, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13347&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P8 WP41\n# (13348, 1630, 'SA78', 11576, 'H.WP 41', 78, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13348&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P9 WP44 ass2\n# (13349, 1630, 'SA79', 11576, 'H.WP 44 ASS2', 79, 'parcial'),\n# 285775|ASS2|162630|ASS|1\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13349&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285775&cache=sarr-2024-sertoes-series\"\n\n# P10 WP54\n# (13350, 1630, 'SA7A', 11576, 'H.WP 54', 80, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13350&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P11 WP60\n# (13351, 1630, 'SA7B', 11576, 'H.WP 60', 81, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13351&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P12 WP67\n# (13352, 1630, 'SA7C', 11576, 'H.WP 67', 82, 'parcial'),\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13352&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285667&cache=sarr-2024-sertoes-series\"\n\n# P13 WP70 ass3\n# (13353, 1630, 'SA7D', 11576, 'H.WP 70 ASS 3', 83, 'parcial'),\n285801|ASS3|314800|ASS|1\n*/5 * * * * wget --delete-after \"https://cronometrajeinstantaneo.com/api/anube.php?idevento=1630&idcontrol=13353&idRace=5704&token=RQlQysdXQ2&n_etapa=7&waypoint_code=7-5M&waypoint_id=285801&cache=sarr-2024-sertoes-series\"\n\nFINAL XAPP\n(13354, 1630, 'SA7F', 11576, 'ASS', 84, 'final'),\n\n\n\n---\n\nUPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;\nUPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;\n\nSELECT idcontrol, idparticipante, tiempo FROM lecturas WHERE idcontrol IN (9160, 9172) AND estado != 'eliminado'\nAND (idparticipante > 100 OR idparticipante < 200)\nORDER BY idcontrol, tiempo;\n\nSELECT * FROM penas WHERE idetapa IN (6952, 6971, 6961, 6981) LIMIT 500;\n\nDELETE FROM lecturas WHERE idcontrol IN (12550);\n\n*******************************************************************************\n\n## CONFIGURACION JIMDO Y APP\nhttps://cms.e.jimdo.com/app/s82a17b50740eef8e/p36f3ef62dc40b75f?cmsEdit=1\n\n<div style=\"position: relative; padding-bottom: 56.25%; min-height: 2500px;\">\n    <iframe frameborder=\"0\" allowfullscreen=\"allowfullscreen\"\n    style=\"position: absolute; top:0; left: 0; width: 100%; min-height: 2500px;\"\n    src=\"https://cronometrajeinstantaneo.com/resultados/canav-rally-raid-3-fecha-2023/filtros\">\n    </iframe>\n</div>\n\n## COMANDOS\n\n```sql\nALTER TABLE `lecturas` CHANGE `tipo` `tipo` ENUM('app','rfid','fotocelula','anube') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'app';\n\nINSERT INTO datosxparticipantes SET idevento = 425, iddato = 'equipo', idinscripcion = 238601, dato = 'Duplessis Team';\n\n=CONCAT(\"INSERT INTO lecturas SET idevento = 425, idcontrol = 1680, estado = 'ok', tipo = 'fotocelula', idparticipante = \";B2;\", tiempo = '2021-02-20 \";I3;\"';\")\n\nUPDATE `datosxparticipantes` SET dato = 'Chile' WHERE idevento = 425 AND dato = 'Chile ';\n\nUPDATE `lecturas` SET tiempo = DATE_SUB(tiempo, INTERVAL 2 MINUTE) WHERE idcontrol = 1800;\n\nINSERT INTO penas SET idevento = 507, idetapa = 2231, idparticipante, tiempo, observacion\n\nSELECT idevento, idetapa, idparticipante, tiempo, observacion FROM penas\nWHERE idetapa IN (2234,2235,2236,3503,2242,2243,2244,3501)\nORDER BY idparticipante\n2239\n2247\n\n```\n\n## CONFIGURACIONES resultados_js\n\nvar contenedor_resultados = document.getElementById(\"contenedor_resultados\");\ncontenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');\ncontenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');\ncontenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');\ncontenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');\ncontenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');\n\n\n## TENER EN CUENTA PARA SARR\n\n- Presupuestar con tiempo y que Juanpi lo apruebe\n- Alguien encargado de los tiempos que hable con los pilotos (reglas claras pre-escritas) así delega esa tarea\n- Definir con tiempo como es todo el reglamento\n  - Cuánto se completa de tiempo a los que no terminan uno, dos o más especiales\n  - A quién se le completa el tiempo\n  - Penalización o bonificación: ¿cómo es el proceso? ¿lo puedo hacer yo?\n- Acordar cierre de tiempos por etapa, los participantes tienen que saberlo, se firma una hora diaria y se entrega a FIM y a los pilotos\n- Ofrecer mejoras\n- Cometí muchos errores manuales, hay que reducir al máximo el trabajo manual\n\nMejoras para el SARR\n- Mostrar en el ticket digital Fecha y hora de cada cambio de tiempo y cada penalización público para el participante\n- Tarjetas digitales completas, parecido al ticket, pero con otro diseño\n- Cada puesto de control con app y con tarjeta digital (ideal sería Beacons)\n- Impresiones con logos y formato de impresión diferente al de pantalla (diseñar con tiempo, cada informe por etapa, FIM, generales, por categorías, participantes, etc.)\n- Ocultar columna de subcategorías en Fechas FIM\n- Separar Motos y Quads, autos y UTV (lo pidió FIM me parece que corresponde siempre)\n- Informe con orden de largadas (sería ideal poder establecer la hora, aunque sea con parche con edicion por parámetro GET)\n- Pensar cronometraje y backup a prueba de lluvia y a prueba de fallo de Stella\n- Importación de penas\n- Configurar todos los PCs (ASS y DSS) en el sistema, aunque no se muestren para poder utilizar el informe de PCs y controlar que los tiempos de enlace estén correctos\n\n"}]}