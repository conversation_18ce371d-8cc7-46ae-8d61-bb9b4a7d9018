{"sourceFile": "BRAIN/ROADS/CRONO/DEV Ideas Menores.md", "activeCommit": 0, "commits": [{"activePatchIndex": 0, "patches": [{"date": 1746023187874, "content": "Index: \n===================================================================\n--- \n+++ \n"}], "date": 1746023187874, "name": "Commit-0", "content": "# 🥇 CRONO > DEV Ideas Menores\n\n## ORDENAR:\n\n- En la app y en la api agregar compatibilidad para chips numerados sin tener en cuenta los PCs\n- Ver si combiene cambiar organización por empresa y incluir otros tipos: streaming, medallas, locutor, etc.\n- Los puestos de control pueden tener condiciones de exclusión: antes/ después de una hora, antes/después de un puesto, diferencia de tiempo entre otro puesto, etc. Puede haber una configuración si se excluye y/o si se genera alerta de revisión\n- Agregar un tipo de control backup \"BackUp Llegada\", para que si no se lee el chip en el control principal, se lea en el backup pero y genere una alerta si se especificó\n- En México le dicen Folio a la Ficha de inscripción\n- Agregar seguimiento a utm_source y utm_campaign\n- Inventar algo copado para los talles de las remeras\n- Y se puede que al completar el tiempo de 1.000m que le pido solo puedan poner en formato mm:ss?? Porque todos ponen diferente y después me vuelvo loco armando los grupos.. (Pedida por OWE)\n- Tiempo promedio de la carrera, sexo y categoría entre todos los participantes que llegaron para comparar en el ticket\n- Agregar main sponsor y colaboradores con distintos tamaños y apariciones\n- Agregar género a la importación de participantes, exportación y los informes\n- Agregar horas de vencimiento de inscripción para hacer como las reservas de alojamiento con respecto al pago\n- Agregar modificación del texto de datos extras así se pueden re-usar muchos.\n- Si es así sugiero q en la tabla la dupla la cuente como 2 personas y no como una, así da el número real de participantes\n- Aplicar fecha de cerrado (con mi revisión) y ahí no dejar resetear, ni modificar fecha, ni borrar participantes. Alertar al re-abrir evento.\n- Podios generales arriba de podios por categorías\n- Separar falsa partida y redondeo para que se pueda redondear siempre\n- En el nuevo modificador de tiempos, poder \"Agregar antes de x participante\"\n- Mejorar fuenta para las columnas de tiempos en los resultados (la del reloj de la app puede servir)\n- Columna whatsapp en seguridad en lugar de seguridad\n- Opción para cobrar al cambiar la distancia o cualquier cosa (ver que se puede cambiar)\n- Kit a la carta: que puedas seleccionar combos de distintas opciones y que eso incluya o no productos que tienen que tener un stock por talle\n- Agregar la opción de filtros como selectores\n- El informe de estadísticas de datos extras no tiene en cuenta los participantes de equipos y por ende no aparecen sus remeras\n- Banderas en el Vivo\n- Funcionalidad para generar sorteos\n- Acreditación por correo o QR\n- Mostrar la edad real al lado de la fecha de nacimiento en inscripciones y panel de usuario\n- Pasar los mails de usuarios siempre a minúscula\n- Podemos usar https://pickit.com.ar para la entrega de kits por correo\n- En equipos poder llamar al tipo de participante (acompañante, bici, etc.)\n- Copiar https://www.formassembly.com/\n- Agregar posibilidad de ingresar sus propios píxeles de Facebook o Google a los organizadores\n- Ajax loading del loguito de crono\n- Lindo diseño en https://wirebox.app\n- Datos extras del participante abajo del nombre\n- Diferencia primero y anterior abajo del tiempo total (ver PDF de Colombia)\n- Pasar las capacitaciones al sistema\n- Poder exportar las estadísticas de la app con chips y/o compararlo online (herramienta para calcular el % de lectura)\n- El cancelar la modificación del participante se debería poder aunque haya datos obligatorios incompletos\n- Resaltar repetidos en Informe PC\n- Resaltar los repetidos dentro de la app (puede ser opción en los filtros)\n- Revisar en el vivo que si una largada se pone mal el número, al corregirlo no se cambia\n- Agregar un tipo de dato check sin pregunta (para el tema del deslinde y otros revisar)\n- Sacar uuid de lo que no hace falta\n- Agregar 120 y 300 segundos en el cronómetro\n- Buscador de localidad desde una API así estandarizamos\n- El informe de seguridad debería estar en un panel del evento: cuantos hay, falta, etc. cuántos podios están completos (tener en cuenta que puede haber menos gente en algunas categorías)\n- Agregar opciones para cobrar por Whatsapp: https://onepay.ai/\n- El reloj en la app con múltiples carreras\n- La app de Windows debería poder re-dimensionarse\n- Estaría bueno poder generar scripts y dejarlos pre-cargados para ser ejecutados, para por ej lo del nacional de Enduro\n- Pruebas de pagos con MP https://groups.google.com/g/mercadopago-developers/c/oU0KdDPYu2Q/m/XY8-EdDmBwAJ?utm_medium=email&utm_source=footer\n- Agregar código QR para ver resultados y para inscripciones\n- Pasar localidad como dato extra\n- Varias ideas de https://results.sporthive.com/events/7042627784530419712/races/2/bib/11 : Íconos para las disciplinas en las etapas, Pos gen, cat y sexo en cada etapa y con el total de participantes, Gráfico de velocidad promedio de todos los participantes comparado con cada uno, Finish overview\n- Lindo diseño de streaming para replicar: https://youtu.be/8O8gr3OF1ug?t=170 / https://youtu.be/8O8gr3OF1ug?t=2163\n- Para copiar en las estadísticas de la app (ver también si sirve usar svg o )\n  https://codecanyon.net/item/sticky-mobile-phonegap-cordova-mobile-app/********\n  https://codecanyon.net/item/banking-one-mobile-app-template-ionic-6-capacitor-3/********\n  https://codecanyon.net/item/zekky-ionic-4-angular-7-ui-theme-template-app-multipurpose-starter-app/screenshots/********?_ga=2.********.*********.**********-**********.**********\n- Mostrar mail del equipo en el Panel de Control (ver que se mande mails a ambos)\n- Me sirve agregar el país a la generación de eventos (sacarlo de las organizaciones)\n- Separar el tema del admin a un services\n- La tabla contacts tendría que tener también eventos\n- Constant FILTER_SANITIZE_STRING is deprecated in /var/www/cronometrajeinstantaneo\n- Mira como el nombre está grande y ocupa 2 renglones y como queda el título arriba: https://runsignup.com/Race/Results/146424/#resultSetId-384367;perpage:100\n- Para el GPS https://html5.anube.es/?rally=rally4465&port=auto&token=boBV923HSI&map=satellite&type=animation&participants=251&participant_ids=1690596&from=**********&to=**********\n- WhatsApp flotando con el número de teléfono del organizador\n- Agregar más de un mail a las notificaciones del sistema. Ya sea con coma, o a modo de suscripción del cronometrador\n- Open source software para NVR en frigate.video\n- Agregar copiar enlace en las opciones para descargar y en la parte de widgets\n- Contador en la misma APP\n- Tengo en Sendgrid\n- Ver si sumamos seguros con Andres de Mendoza\n- Armar partidas desde informe de categorías alreves\n- https://marketing4ecommerce.net/herramientas-para-eventos-virtuales-con-las-que-sorprender-a-tu-publico/\n- Separar la activación de apps de las largadas\n- Porer pasar todos los parámetros del streaming por parámetro, especialmente el diff\n- Acomodar los títulos con colspan\n- Ver si podemos poner parciales como un selector más\n- Agregar la opción de filtros como selectores\n- Bloquear eventos terminados (no se puede resetear, ni modificar el nombre, ni fecha, ni nada masivo)\n- Revisar Meet Manager para natación https://www.youtube.com/watch?v=oM_3VyXTOds\n- Ver de copiar ideas y diseño de https://www.redbullxalps.com/\n- Tenés razón, hoy dice \"ERROR: no se encontró un participante\" pero tendría que salir algo mucho más amigable.\n- Ver si se puede Cambiar la ip del reader para tener 2 conectados a una misma app (o 2 pcs pero en la misma red)\n- Ver si se puede tener 2 apps para ver si podemos tener 2 readers con la misma PC o celu\n- Opcion para mostrar banderas o nombre de pais\n- Mover masivamente de categoría (en el panel de control con Livewire)\n- Terminar lo de pre-llegada en la meta como opcion\n- Agregar más estados a los eventos: pendiente, terminado, cancelado, suspendido, postergado (sin fecha)\n- Poder pasar el pago a otro evento\n- Cartel de eventos terminados para que lo cierren\n- El streaming tiene que funcionar también con los cambios de etapa\n- Tiempos a revisión (analizar la velocidad promedio por etapa dependiendo de la disciplina)\n- Ver si agregamos una aprobación de que el evento terminó bien por el cronometrador y otro por nosotros\n- Tiempos de Vueltas en un segundo renglón (https://www.endurofim.cl/wp-content/uploads/2021/08/Official-Classification-S1-2.pdf)\n- Informe de Datos extras con equipos con un equipo por cada línea\n- Preparar el vivo del locutor para que funcione en tablets y celulares como \"App para locutor/narrador\"\n- Fichas y pago con teclado\n- El modificador de tiempos sin mostrar hasta que se apliquen filtros (buscar y ordenar en un issue, sumarle los eliminados y poder modificar en masa)\n- Agregar diferencia de tiempo en streaming para no tener que configurar la hora de la PC del streamer\n- Agregar el nombre del evento en el título\n- En la sección filtro nos lleve directamente a las categorías (en usuario Worktime Chile, avisar)\n- Si en consulta de ticket pones número de participante pero hay algo en el buscador, hace la búsqueda igual sin prestar atención al botón que se apretó\n- Múltiples categorías\n- Filtra tiempos y participantes por GET (si vas atrás pide re-enviar)\n- Botón para bloquear y liberar los códigos de controles (pasan todos a 0000) y no mostrar los códigos 0000 sino como Liberado\n- Abreviación para las categorías\n- En el modificador de tiempos poder deshabilitar todo un cronometrador\n- Informe para exportar a excel como lo pide ITRA o UTMB\n- Revisar https://github.com/don/cordova-plugin-ble-central/issues/932#issuecomment-1307057842\n- Al agregar una hora de inicio y fin de cada carrera, podemos filtrar los chips que se leen antes y después y no computarlas (sería ideal meterlo junto con el tipo de largada en carreras)\n- both VLC and OBS and had OBS capture the webcam stream ... then I pointed VLC at the stream file on the hard drive\n- First, for the SMS results. Could you not send them to people who are marked DNS?\n- https://code-boxx.com/generate-qr-code-javascript/\n- Que se pueda leer QRs que sólo tengan el número del participante y QRs que tengan el ticket digital\n- Al exportar, que también se pueda bajar en formato texto y html para la prensa\n- Comando para activar streaming que tenga en cuenta el tipo de largada y que exista config_lives\n- Recuperar el hash en los js de admin\n- Volver a ver el laravel.log y warnings\n- La App en las playstore debería funcionarnos como otro punto de venta (explicar con algún vídeo, mandar a redes, dejar probar la app de alguna forma)\n- Control de que no se pueda guardar lo de un participante en otro evento si se cambia por el historial en otra pestaña\n- Cambiar localidad por provincia, equipo, etc.\n- Generar estadísticas desde logs\n- Constant FILTER_SANITIZE_STRING is deprecated to FILTER_SANITIZE_FULL_SPECIAL_CHARS\n- Optional parameter $view declared before required parameter $request is implicitly treated as a required parameter in /var/www/cronometrajeinstantaneo/admin/app/Http/Controllers/OldController.php on line 15\n- No calcular nunca con tiempo de largada en cero\n- Tengo algunas cosas para acomodar en el cuaderno que ya lo puedo vaciar\n- Información de nueva API WhatsApp https://www.facebookblueprint.com/student/collection/409587\n- Probar con mejores scanners ( https://github.com/bitpay/cordova-plugin-qrscanner )\n- Mejorar más todavía la forma de mostrar errores en pantalla. Pudiendo mostrar mensajes desde el through y con un diseño más lindo\n- Compartir resultados a Whatsapp, XLS y PDF con una linda barra\n- En la funcion convertir_final se pueden detectar tiempos negativos y muy grandes para avisar de errores\n- Para chusmear: https://www.fotomotor.es/competicion/2038/tramo/11/ y https://www.instagram.com/p/CLKxrQCng2d/?igshid=YmMyMTA2M2Y%3D\n- App para largador con participante y tiempo de descuento\n- Vídeos que muestran como configurar pantallas para mostrar resultados: https://www.youtube.com/watch?v=WYkPKiEepwA&list=PLMTIVfFlMEkUEN-5WHqfD-Hkcmcm-VPPt\n- Tiene que haber un demo para cada deporte y que no se pueda modificar nada de la configuración\n- Vídeos dentro del sistema, por ejemplo uno cuando recién se registra y otro cuando paga agradeciendo\n- Usuario Responsable de cronometraje en el sistema\n- https://imperavi.com/revolvapp/\n- Hay varias ideas para copiar de Rufus Cloud: https://mail.google.com/mail/u/0/#inbox/********************************\n- Ordenar en el modificador de tiempos las etapas (poner nombre de la carrera en la etapa)\n- Poder tomar tener tiempo de chip y tiempo de carrera en el ticket y resultados\n- Totales arriba en el inicio. Si hay más de 500 agregar filtros por carreras y avisar\n- Preparar que se pueda tener el sistema sólo para acreditaciones\n- En la nueva ventana de eventos: Resaltar el próximo, marcar con algo flotante y visible el que es HOY y el que es Esta semana (agregar fecha de inicio y fin del evento o si es evento de un sólo día, también tener en cuenta si ya lo dieron por terminado)\n- Audio diciendo quien llegó y su posición y tiempo (que se pueda poner al vídeo también)\n- Sistema de puntos con ecuaciones. Puede haber un dato extra (puntos anteriores) y que se sume como puntaje total. También se podría ordenar por puntos o tener un informe específico de orden por puntos\n- Separar la configuración de eventos (renombrar a js y css)\n- Avisarme si alguien carga js personalizado\n- Pasar los mensajes a notificaciones\n- Cambiar las autenticaciones a la API por Laravel Sanctum https://laravel.com/docs/8.x/sanctum\n- Resultados compartir en instagram o Facebook (también en el ticket)\n- Ver ideas en https://www.instagram.com/p/CbERImjsI7j/?igshid=MDJmNzVkMjY=\n- Laravel 9\n- Evaluar https://ably.com/\n- Que en las inscripciones se muestre un desplegable de países según la tabla y que reconozca el del navegador o el de la IP\n- Presentar como quiere la UCI\n  https://www.pinkbike.com/news/final-results-from-the-petrpolis-xc-world-cup-2022.html\n  https://www.uci.org/race-hub/2022-mercedes-benz-uci-mountain-bike-world-cup-xco-xcc-petropolis/2YgV3VbwPiNFsumC53IvuH?tab=xco-women-elite\n- Mostrar nacionalidad resumida (ARG), completa o bandera\n- https://ayudawp.com/alojar-localmente-fuentes-google-astra/\n- Un montón de Livewire components: https://www.youtube.com/watch?v=h1K9IFfPU68 y más en https://livewirekit.com\n- utm_source\tutm_campaign\n- flush(); en penas (por las dudas también en otros listados como modificador de tiempos)\n- Ticket sólo de la final\n- No mostrar tiempos negativos es mejor que no esté\n- Penalizaciones automáticas en FIM\n- Reondear a décimas o centécimas (cronometrar con la presición que se va a mostrar) truncado para que sea igual a tarjetas\n- Tarjetas/carnet de rally virtuales\n- Habilitar etapas para mostrar\n- Ver de habilitar firewall y cdn\n- https://www.datadoghq.com/dg/apm/laravel-application-performance\n- Penas como minutos en negativo se ve feo (sacar la aclaración de seg)\n- Impresión de penalizaciones por día\n- Seguro de devolución ( https://mail.google.com/mail/u/0/#inbox/******************************** )\n- Tiene que llevar los logos las impresiones y se firman como oficial\n- Diseño de impresión prolijo y diferente al de resultados\n- Lindos gifts, parecen intuitivos Technology and Software Companies - COR https://projectcor.com/technology-and-software-companies/product/\n- Para xco que en los listados esté la posición y si sigue o no tipo semaforo\n- Tomar varias tomas de un mismo chip o Beacon y generar cual es el correcto (subir online un paquete en json de esas lecturas)\n- Mejorar el cartel de error \"Sorry, the page ...\"\n- Configurar ticket digital para que incluya: todo lo necesario para una tarjeta de rally, para eso son los PCs, y que con los nombres de los PCs quede con esa info\n- Tipo de largada PC anterior para entrenamiento y eventos de varias pasadas\n- Revisar si se arregló el menú en celulares (probar en más)\n- Poner sub-menu para los pasos del wizard de configuraciones\n- Probar versión actual del sistema en nube híbrida\n- Laravel Logs: https://laravel-news.com/laravel-authentication-logs\n- Tener un tiempo oficial (Tiempo Bruto o Tiempo de Competencia) y tiempo de chip (Tiempo Neto o Tiempo Real).\n- Me gustaría cambiar el campo tiempo por crono en la tabla lecturas y en todo el código de app y admin\n- Poder mover cosas (por ejemplo categoría a otra carrera)\n- Revisar logs de apache en prod\n- Agregar verificación en la inscripción cruzando la fecha de nacimiento y la categoría (puede ir muy bien con Vue.js)\n- Pero hay que incorporar a los descalificados y fondo de la misma tabla de posiciones\n- Probar en otras máquinas y comparar tiempos (NTP)\n- 404 page\n- El campo eventos.equipo lo uso para el informe de extras (puede que en otros lados más) pero se puede matar\n- Quiere desafiar a otro participante\n- Hacer certificados\n- Subir fotos de los corredores\n- https://tiargentina.com/empresa-relet-srl-distribuidor-de-networking-em-palermo-1285 (Relet SRL Distribuidor de Networking)\n- App para mostrar tiempos y seguimientos (con hot sit)\n- Foto Finish\n- DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC \"DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC\" [DietPi, la distribución Linux ultraligera para Raspberry Pi (y otra media docena de SBCs)… que también puedes instalar en tu PC](https://www.genbeta.com/linux/dietpi-distribucion-linux-ultraligera-para-raspberry-pi-otra-media-docena-sbcs-que-tambien-puedes-instalar-tu-pc/amp)\n- Listado de actividad con posibilidad de deshacer\n- Datos de clima u otros factores externos\n- https://www.2checkout.com/payment-api\n- Agregar como estado de pago a liberado (también puede ser pendiente, pago a medias, saldo, etc.)\n- Bajar la importancia de localidad y ponerlo como dato extra\n- Googlear mucho y buscar también en Youtube distintas formas de cronometrar o fotocélulas\n- BORRASTE AAVLA, ¿Cómo hacer que no pase nunca más? => Terminar sincronización nube y sólo usar nube, nunca tu compu si no es beta de funcionalidad\n- En generales y ticket si las categorías tienen el mismo nombre las suma aunque sean diferentes (https://cronometrajeinstantaneo.com/resultados/desafio-del-lago-2021/generales)\n- Darle tiempo a la metralladora de tiempos\n- Cambiar la palabra sexo por género y agregar otro\n- PayU ver comisiones e implementación porque no es como ML\n- Para running quieren simplificar las cobranzas como objeción\n- Agregar registro del móvil a los logs para revisar posibles fraudes (lo pidieron en México)\n- Estaría bueno que las remeras del evento tengan control de stock\n- Cache bien hecho en Laravel: https://www.youtube.com/watch?v=Nmq3lmgi_Sk | https://twitter.com/themsaid/status/1369307734433275917?s=09\n- No dejar repetir nombre de categoría en una misma carrera\n- Versión para iPhone (https://volt.build)\n- Control de paso y alerta para saber cuándo es la última vuelta\n- Redondear largada para arriba a minuto cerrado o para abajo\n- Poder modificar el codigo de un evento o que no cambie cuando le cambias el nombre\n- Algo para ayudar a cargar las fechas desde y hasta\n- Los pasos del wizard no se entienden como botones\n- Laravel Vapor es Laravel común hecho serverless\n- Mail igual que el de Ride que es hermoso (https://mail.google.com/mail/u/0?ik=8d55b10a2d&view=om&permmsgid=msg-f%3A1696836627049194712)\n- Sacar la suscripción al newsletter O USARLA (pero igual ponerla más abajo)\n- Terminar diseño y agregar enlaces a Condiciones y Privacidad\n- Cerrar la funcionalidad de módulo médico y de seguridad (poner usuario o QR para habilitar y también anunciar temas médicos y que puedan reportar cosas)\n- The ideal solution would be to fix all the data in the database. That is, go through your database and update your datetime/timestamp fields so that they are nullable, and convert their data from '0000-00-00 00:00:00' to null.\n- Mensajes de respuestas en blade que sean flotantes\n- Trabajar con los participantes como si fuera un Excel (tiene que haber algún plugin para Laravel)\n- Cronotech cobra $32500 hasta 150 personas y de ahí $250 más por corredor, Claudio quizo sobrar $25000 menos lo mío le quedaría sólo $10000, hay que ver esa ecuación. A Damian le parecía caro y por eso no lo usaba.\n- Ver por productos con Lora en https://www.inipop.com/static_page/\n- Agregar orden interno a todo (modelo y tablas hoy)\n- Firma electrónica de Adobe\n- Ya están los campos orden en varias tablas\n- Revisar que por ahora sólo calcula cuando existe un idparticipante\n- Composer install --no-dev y bajar debug a true en produccion\n- Ver este video para ordenar el código: https://www.youtube.com/watch?v=ShrS3HXwzPg\n- Ver estas opciones para queues: https://github.com/php-enqueue/enqueue-dev y https://cloud.google.com/pubsub\n- Cómo quitar el JavaScript para responder comentarios en WordPress si no lo necesitas: https://ayudawp.com/quitar-javascript-comentarios-wordpress/\n- Opciones para compartir fácilmente el ticket digital en redes sociales\n- Agregar opción para desactivar un punto de control completo en el crono (para usar tiempos temporales de largada por ejemplo)\n- Mail al finalizar el evento\n- En SaaS el script no está marcando el generar el movimiento de saldo en las ventas\n- En la nube necesito saber que versión tengo\n- Necesito probar una actualización de la nube con algún usuario antes de la carrera\n- Hermoso diseño, campeonato e íconos https://xipgroc.cat/ca/lliga/copa-marnaton-2019\n- Informe por orden de llegada, sin importar categorías ni carreras\n- Para mejorar en subir\n- Agregar validacion de tipo al subir\n- Agregar compatibilidad con gif\n- Pasar diseño a Blade y Bootstrap\n- Para la academia se puede copiar la de timesense: https://timingsense.com/academy/\n- Cambiar slider en la web por una imágen estática con franjas\n- Se pueden hacer muchas más estadísticas\n- Gráfico con tiempos en los que se llena el pódio por categoría\n- Cantidad de llegadas por minuto por carrera\n- Evolución de inscriptos año a año (para eso hay que agrupar las carreras)\n- https://public.tableau.com/profile/matiaspatinomayer#!/vizhome/TriatlnEscapeIslaHuemul2020/EscapeIslaHuemulSponsors\n- Que se pueda configurar cuántos se quieren en el podio\n- Agregar más datos (incluyendo nacionalidad) al ticket\n- Pensar como controlar carrera de regularidad\n"}]}