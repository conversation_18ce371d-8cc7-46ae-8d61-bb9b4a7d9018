{"sourceFile": "BRAIN/TODO.md", "activeCommit": 0, "commits": [{"activePatchIndex": 2, "patches": [{"date": 1747178720861, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1747178927572, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,28 +1,55 @@\n ## NEXT MILESTONES\n \n * Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n+\n SAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\n+\n Escribir ventajas de SaaS + Qloud para informática @focus\t- [https://mail.google.com/mail/u/0/#inbox/********************************](https://mail.google.com/mail/u/0/#inbox/********************************) - <PERSON><PERSON><PERSON> lo del mail de Diego sobre impuestos de productos\n+\n CRONO Definir Framework Grabaciones @flow\tGrabar material de inscripciones y sports para ahora  Grabar todo para páginas de deportes  Dejar ordenado próximas grabaciones\n+\n Grabar los vídeos de deportes\n-Armar curso fácil\n+\n+- [  ] Armar curso fácil\n+\n+[ ]  A ver\n+\n+```html\n+lalalala\n+lalalala\n+```\n+\n+\n+\n+\n+\n Ver y resumir consejos y los procesos que va a llevar (herramientas de AI)\n+\n Probar técnicas de grabación (mkt y cursos)\n+\n Encontrar buen ángulo para la cámara\n+\n Probar fondo verde\n+\n Errores Felipe y minors @flow\t- Calcula mal el descuento - Ver usuario - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático - Ya podemos hacer la carga en SaaS automática - Revisar el error de Rally Bs.As. y sólo errores urgentes - Mostrar totales de todas las cajas [https://scripts.saasargentina.com/?script=4](https://scripts.saasargentina.com/?script=4) - Igualar sueldos Crono y dividendos negativos - Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir. - De alguna forma se está pudiendo repetir los nombres de los eventos.\n+\n [EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46) @flow\n+\n [Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303) @flow\n+\n AGENTE MCP Consultas @flow\tHasta tener un sincronizador y re ordenar proyecto\n+\n SAAS Ayuda @flow\n+\n CRONO Chips sólo n° @flow\n+\n [Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n \n-\n ## FRAMEWORK\n \n Estoy jugando a reformular mí BRAIN\n+\n Con el nuevo libro, todo texto en VSC\n \n - Buscar filtro de tags con colores\n - Pasar Week matándola\n@@ -32,36 +59,38 @@\n - Toggl en vsc\n - Calendario compartido\n - Toggle text copiloto\n - color en p\n-\n-\n - Deploy BETA a PROD\n - Ver si me queda más cómodo Obsidian\n-\t- [ ] Buscar un par de plugins\n-\t- [ ] Probando📅 2025-05-13 🔽 🔁 every day\n-\t- [ ] Acostumbrar a los teclados\n-\t- Ordenar esta semana\n-\t- Que funcione en celu\n+    - [ ] Buscar un par de plugins\n+    - [ ] Probando📅 2025-05-13 🔽 🔁 every day\n+    - [ ] Acostumbrar a los teclados\n+    - Ordenar esta semana\n+    - Que funcione en celu\n - Quiero planear como empezar con el entrenamiento\n - Ordenar la oficina\n \n Ok anduvo algo\n+- [ ] Algo @tag @p1\n+- [ ] lala\n \n+## PRIORIDA\n+\n Desde el celular\n \n Bueno cambio algo más\n \n [Algo]()\n \n ## TODOIST\n+\n (La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n+\n ```todoist\n filter: \"today | overdue\"\n ```\n \n-\n-\n Averiguar por ALGE Timing\n \n Today\n \n"}, {"date": 1747179103680, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,6 +1,10 @@\n ## NEXT MILESTONES\n \n+- [ ] Primera\n+- [ ] Segunda\n+\n+\n * Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\n \n SAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\n \n"}], "date": 1747178720861, "name": "Commit-0", "content": "## NEXT MILESTONES\n\n* Framework FLOW\t- Tratar de terminar en orden cada flow - Planear al iniciar con AI 10 80 10 - Terminar con doc y MKT - No hay week sino flujos interminables - Toggl con @\nSAAS Estadísticas, Landings y primeros pasos de Plan Micro Nichos @flow @now\tHasta terminar todos los planes y Landings\nEscribir ventajas de SaaS + Qloud para informática @focus\t- [https://mail.google.com/mail/u/0/#inbox/********************************](https://mail.google.com/mail/u/0/#inbox/********************************) - <PERSON><PERSON><PERSON> lo del mail de Diego sobre impuestos de productos\nCRONO Definir Framework Grabaciones @flow\tGrabar material de inscripciones y sports para ahora  Grabar todo para páginas de deportes  Dejar ordenado próximas grabaciones\nGrabar los vídeos de deportes\nArmar curso fácil\nVer y resumir consejos y los procesos que va a llevar (herramientas de AI)\nProbar técnicas de grabación (mkt y cursos)\nEncontrar buen ángulo para la cámara\nProbar fondo verde\nErrores Felipe y minors @flow\t- Calcula mal el descuento - Ver usuario - Ver si podemos poner la compra de promos que las cargue Juli y que se carguen en SaaS Automático - Ya podemos hacer la carga en SaaS automática - Revisar el error de Rally Bs.As. y sólo errores urgentes - Mostrar totales de todas las cajas [https://scripts.saasargentina.com/?script=4](https://scripts.saasargentina.com/?script=4) - Igualar sueldos Crono y dividendos negativos - Ver que los códigos de las organizaciones se generen y actualicen bien (correr un comando para arreglar todos). Ver también que no se puedan repetir. - De alguna forma se está pudiendo repetir los nombres de los eventos.\n[EVENTOS Listado (#46)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/46) @flow\n[Transferencias Inteligentes (#303)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/303) @flow\nAGENTE MCP Consultas @flow\tHasta tener un sincronizador y re ordenar proyecto\nSAAS Ayuda @flow\nCRONO Chips sólo n° @flow\n[Picos de consumo (#300)](https://gitlab.com/cronometrajeinstantaneo/admin/-/issues/300) @flow\n\n\n## FRAMEWORK\n\nEstoy jugando a reformular mí BRAIN\nCon el nuevo libro, todo texto en VSC\n\n- Buscar filtro de tags con colores\n- Pasar Week matándola\n- Audio a texto y archivo\n- Nuevo teclado y atajos con machete\n- Mas memoria en mail\n- Toggl en vsc\n- Calendario compartido\n- Toggle text copiloto\n- color en p\n\n\n- Deploy BETA a PROD\n- Ver si me queda más cómodo Obsidian\n\t- [ ] Buscar un par de plugins\n\t- [ ] Probando📅 2025-05-13 🔽 🔁 every day\n\t- [ ] Acostumbrar a los teclados\n\t- Ordenar esta semana\n\t- Que funcione en celu\n- Quiero planear como empezar con el entrenamiento\n- Ordenar la oficina\n\nOk anduvo algo\n\nDesde el celular\n\nBueno cambio algo más\n\n[Algo]()\n\n## TODOIST\n(La doc está en https://jamiebrynes7.github.io/obsidian-todoist-plugin/docs/query-blocks )\n```todoist\nfilter: \"today | overdue\"\n```\n\n\n\nAveriguar por ALGE Timing\n\nToday\n\nquick\n\nSOPORTE 🥇 / VENTAS\n\nArmar mi teclado Corne\n\nAv la plata 61 10B entre Rivadavia y Chaco CP 1184 CABA...\n\nToday\n\nplay\n\nNIRVANA 👌 / UBUNTU\n\nDeploy BETA a PROD\n\n- Hay algo de la API para revisar...\n\nToday\n\nfocus\n\nDEV 📦\n\nVuelvo a la pileta 👏💪\n\nToday"}]}