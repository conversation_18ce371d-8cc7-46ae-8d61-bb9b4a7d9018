{"sourceFile": "CONFIG/unison_brain.prf", "activeCommit": 0, "commits": [{"activePatchIndex": 6, "patches": [{"date": 1726425637838, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726426145127, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n root = /home/<USER>/MEGA/BRAIN\n-root = ssh://<EMAIL>/home/<USER>/BRAIN\n+root = ssh://<EMAIL>//home/<USER>/BRAIN\n \n auto = true\n batch = true\n\\ No newline at end of file\n"}, {"date": 1726426163264, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,4 @@\n root = /home/<USER>/MEGA/BRAIN\n root = ssh://<EMAIL>//home/<USER>/BRAIN\n \n-auto = true\n-batch = true\n\\ No newline at end of file\n+auto = true\n\\ No newline at end of file\n"}, {"date": 1726426461242, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,4 +1,5 @@\n root = /home/<USER>/MEGA/BRAIN\n root = ssh://<EMAIL>//home/<USER>/BRAIN\n \n-auto = true\n\\ No newline at end of file\n+auto = true\n+batch = true\n\\ No newline at end of file\n"}, {"date": 1726426587167, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n root = /home/<USER>/MEGA/BRAIN\n-root = ssh://<EMAIL>//home/<USER>/BRAIN\n+root = ssh://<EMAIL>//home/<USER>\n \n auto = true\n batch = true\n\\ No newline at end of file\n"}, {"date": 1726426711109, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n root = /home/<USER>/MEGA/BRAIN\n-root = ssh://<EMAIL>//home/<USER>\n+root = ssh://<EMAIL>//home/<USER>/BRAIN\n \n auto = true\n batch = true\n\\ No newline at end of file\n"}, {"date": 1729837240898, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -1,5 +1,5 @@\n-root = /home/<USER>/MEGA/BRAIN\n-root = ssh://<EMAIL>//home/<USER>/BRAIN\n+root = /home/<USER>/MEGA/\n+root = ssh://<EMAIL>//home/<USER>/MEGA\n \n auto = true\n batch = true\n\\ No newline at end of file\n"}], "date": 1726425637838, "name": "Commit-0", "content": "root = /home/<USER>/MEGA/BRAIN\nroot = ssh://<EMAIL>/home/<USER>/BRAIN\n\nauto = true\nbatch = true"}]}