{"sourceFile": "CONFIG/sites-enabled/virtualhosts.conf", "activeCommit": 0, "commits": [{"activePatchIndex": 4, "patches": [{"date": 1726005954452, "content": "Index: \n===================================================================\n--- \n+++ \n"}, {"date": 1726427583413, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -155,10 +155,10 @@\n    DocumentRoot /home/<USER>/www/visiteangostura/public_html\n </VirtualHost>\n \n <VirtualHost *:80>\n-    ServerName simply.des\n-    DocumentRoot /home/<USER>/www/simply\n+    ServerName simplementeviviendo.des\n+    DocumentRoot /home/<USER>/www/simplementeviviendo/public\n </VirtualHost>\n \n <VirtualHost *:80>\n    ServerName audiologiccorp.des\n"}, {"date": 1726838164377, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -118,25 +118,25 @@\n \n # ####### ANDRESMAIDEN #######\n \n <VirtualHost *:80>\n-    ServerName andresmaiden.des\n-    DocumentRoot /home/<USER>/www/andresmaiden/public\n+    ServerName andresmisiak.des\n+    DocumentRoot /home/<USER>/www/andresmisiak/public\n </VirtualHost>\n \n <VirtualHost *:80>\n-    ServerName tools.andresmaiden.des\n-    DocumentRoot /home/<USER>/www/andresmaiden/tools\n+    ServerName tools.andresmisiak.des\n+    DocumentRoot /home/<USER>/www/andresmisiak/tools\n </VirtualHost>\n \n <VirtualHost *:80>\n-    ServerName brain.andresmaiden.des\n-    DocumentRoot /home/<USER>/www/andresmaiden/brain\n+    ServerName brain.andresmisiak.des\n+    DocumentRoot /home/<USER>/www/andresmisiak/brain\n </VirtualHost>\n \n <VirtualHost *:80>\n-    ServerName ai.andresmaiden.des\n-    DocumentRoot /home/<USER>/www/andresmaiden/ai\n+    ServerName ai.andresmisiak.des\n+    DocumentRoot /home/<USER>/www/andresmisiak/ai\n </VirtualHost>\n \n # ####### OTROS #######\n \n"}, {"date": 1738851430753, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -32,8 +32,13 @@\n     DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public\n </VirtualHost>\n \n <VirtualHost *:80>\n+    ServerName api-dev.saasargentina.des\n+    DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public\n+</VirtualHost>\n+\n+<VirtualHost *:80>\n     ServerName api.saasargentina.des\n     DocumentRoot /home/<USER>/www/saasargentina/services/api/public\n     <Directory \"/home/<USER>/www/saasargentina/services/api/public/\">\n         Options FollowSymLinks Indexes\n"}, {"date": 1738851565534, "content": "Index: \n===================================================================\n--- \n+++ \n@@ -33,9 +33,9 @@\n </VirtualHost>\n \n <VirtualHost *:80>\n     ServerName api-dev.saasargentina.des\n-    DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public\n+    DocumentRoot /home/<USER>/www/saasargentina/services/lambda/api/public\n </VirtualHost>\n \n <VirtualHost *:80>\n     ServerName api.saasargentina.des\n"}], "date": 1726005954452, "name": "Commit-0", "content": "####### SAAS #######\n<VirtualHost *:80>\n    ServerName saasargentina.des\n    ServerAlias www.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/www/public\n    <Directory \"/home/<USER>/www/saasargentina/services/www/public/\">\n        Options FollowSymLinks Indexes\n        AllowOverride None\n        RewriteEngine On\n\n        RewriteRule google28a12d46db9cf0d6.html google28a12d46db9cf0d6.html [L]\n        RewriteRule BingSiteAuth.xml BingSiteAuth.xml [L]\n        RewriteRule sitemap.xml sitemap.xml [L]\n        RewriteRule favicon.ico favicon.ico [L]\n        RewriteRule robots.txt robots.txt [L]\n        RewriteRule ^css/.*$ - [L]\n        RewriteRule ^img/.*$ - [L]\n        RewriteRule ^js/.*$ - [L]\n\n        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]\n        Require all granted\n    </Directory>\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName app.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/app/public\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName scripts.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName api.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/api/public\n    <Directory \"/home/<USER>/www/saasargentina/services/api/public/\">\n        Options FollowSymLinks Indexes\n        AllowOverride None\n        RewriteEngine On\n        RewriteRule ^v0.1/(.*)$ v0.1/index.php?url=$1 [QSA,L]\n        RewriteRule ^v0.2/(.*)$ v0.2/index.php?url=$1 [QSA,L]\n        RewriteRule ^v1/(.*)$ v1/index.php?url=$1 [QSA,L]\n        RewriteRule ^ml/(.*)$ ml/index.php?url=$1 [QSA,L]\n        RewriteRule ^ index.php [L]\n\n        RewriteCond %{HTTP:Authorization} .\n        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]\n    </Directory>\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName login.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/login/public\n    <Directory \"/home/<USER>/www/saasargentina/services/login/public/\">\n        RewriteEngine On\n        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]\n    </Directory>\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName informes.saasargentina.des\n    DocumentRoot /home/<USER>/www/saasargentina/services/informes/public\n    <Directory \"/home/<USER>/www/saasargentina/services/informes/public/\">\n        RewriteEngine On\n        # Redirect Trailing Slashes If Not A Folder...\n        RewriteCond %{REQUEST_FILENAME} !-d\n        RewriteRule ^(.*)/$ /$1 [L,R=301]\n\n        # Handle Front Controller...\n        RewriteCond %{REQUEST_FILENAME} !-d\n        RewriteCond %{REQUEST_FILENAME} !-f\n        RewriteRule ^ index.php [L]\n\n        # Handle Authorization Header\n        RewriteCond %{HTTP:Authorization} .\n        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]\n    </Directory>\n</VirtualHost>\n\n\n# ####### CRONO #######\n<VirtualHost *:80>\n    ServerName cronometrajeinstantaneo.lan\n    ServerAlias cronometrajeinstantaneo.des, crono.des\n    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/www\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName admin.cronometrajeinstantaneo.lan\n    ServerAlias admin.cronometrajeinstantaneo.des, admin.crono.des\n    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/admin/public\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName beta.cronometrajeinstantaneo.lan\n    ServerAlias beta.cronometrajeinstantaneo.des, beta.crono.des\n    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/filament/public\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName app.cronometrajeinstantaneo.lan\n    ServerAlias app.cronometrajeinstantaneo.des, app.crono.des\n    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/app/www\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName vivo.cronometrajeinstantaneo.lan\n    ServerAlias vivo.cronometrajeinstantaneo.des, vivo.crono.des\n    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/vivo/src\n</VirtualHost>\n\n# <VirtualHost *:80>\n#     ServerName server.cronometrajeinstantaneo.des\n#     DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/server-local-vivo\n# </VirtualHost>\n\n# ####### ANDRESMAIDEN #######\n\n<VirtualHost *:80>\n    ServerName andresmaiden.des\n    DocumentRoot /home/<USER>/www/andresmaiden/public\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName tools.andresmaiden.des\n    DocumentRoot /home/<USER>/www/andresmaiden/tools\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName brain.andresmaiden.des\n    DocumentRoot /home/<USER>/www/andresmaiden/brain\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName ai.andresmaiden.des\n    DocumentRoot /home/<USER>/www/andresmaiden/ai\n</VirtualHost>\n\n# ####### OTROS #######\n\n<VirtualHost *:80>\n   ServerName laravel.des\n   DocumentRoot /home/<USER>/www/laravel/public\n</VirtualHost>\n\n<VirtualHost *:80>\n   ServerName woocommerce.des\n   DocumentRoot /home/<USER>/www/woocommerce\n</VirtualHost>\n\n<VirtualHost *:80>\n   ServerName visiteangostura.des\n   DocumentRoot /home/<USER>/www/visiteangostura/public_html\n</VirtualHost>\n\n<VirtualHost *:80>\n    ServerName simply.des\n    DocumentRoot /home/<USER>/www/simply\n</VirtualHost>\n\n<VirtualHost *:80>\n   ServerName audiologiccorp.des\n   DocumentRoot /home/<USER>/www/audiologiccorp/public\n</VirtualHost>\n\n# <VirtualHost *:80>\n#    ServerName todoangostura.des\n#    DocumentRoot /home/<USER>/www/todoangostura\n# </VirtualHost>\n"}]}