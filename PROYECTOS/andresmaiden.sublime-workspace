{"auto_complete": {"selected_items": [["dis", "disabled=\"disabled"], ["pendien", "pendiente"], ["cer", "cerrarpedidoconitems"], ["i18n_des", "i18n_descarga_alicuotas"], ["venta", "ventasxclientes"], ["scrip", "script"], ["muev", "muevestock"], ["pro", "productosxventas"], ["produc", "productosxcombos"], ["listas", "listasxextras"], ["cate", "categorias"], ["cateo", "categorias_cajas_sql))"], ["consulta", "consulta_sql"], ["str_re", "str_replace()"], ["hi", "historial"], ["tabla", "tabla_seleccionada"], ["wh", "where_fechas"], ["fecha", "fecha_hasta"], ["fech", "fecha_desde"], ["tipo", "tipofacturacion"], ["tipove", "tipoventa_pedido"], ["tipoventa", "tipoventa_factura"], ["idtipoventa_", "idtipoventa_pedido"], ["idtipo", "idtipoventa_factura"], ["enlac", "enlace_consulta"], ["traslad", "traslados idd\t⌬ tabnine"], ["targ", "target=\"_blank\"\t⌬ tabnine"], ["j", "json_decode\t⌬ tabnine"], ["nuev", "nuevo_informes"], ["saasP", "saasPersistence->\t⌬ tabnine"], ["for", "foreach\tforeach …"], ["arra", "array_sql($\t⌬ tabnine"], ["_yo", "_yoast_wpseo_metadesc\t⌬ tabnine"], ["posib", "posibles_idcomportamiento_asoc"], ["posibles_", "posibles_idcomportamiento_asoc\tabc"], ["repla", "replace_array\tabc"], ["mul", "multiplicador_12\tabc"], ["idlista_", "idlista_12\tabc"], ["pre", "preciofinal\tabc"], ["repl", "replace_str\tabc"], ["idlis", "idlista\tabc"], ["consul", "consulta_sql\tabc"], ["conta", "contar_sql\tabc"], ["ec", "ec2-user\tabc"], ["SAAS", "SAAS_ID\tabc"], ["anu", "anulado', obscae =\t⌬ tabnine"], ["RECHAZA", "RECHAZADO\t(funciones_wsfe.php)"], ["compl", "completar_cuit\tabc"], ["PHP_", "PHP_EOL\t⌬ tabnine"], ["saas", "saasargentina\tabc"]]}, "buffers": [], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["menu", "View: <PERSON><PERSON>"], ["Snippet: ", "Snippet: fn …"], ["men", "View: <PERSON><PERSON>"], ["remo", "Package Control: Remove Package"], ["prefe", "Preferences: Settings"], ["inst", "Package Control: Install Package"], ["insta", "Package Control: Install Package"], ["json", "Pretty JSON: Format JSON"], ["remov", "Package Control: Remove Package"], ["tabni", "⌬ tabnine: Disable Current View"], ["remove", "Package Control: Remove Package"], ["subli", "Sublime Merge: File History"], ["markdo pre", "Markdown Preview: Preview in Browser"], ["mark", "Set Syntax: Markdown Extended"], ["key", "Preferences: Key Bindings"], ["manual", "DocPHP: Search Manual"], ["manua", "DocPHP: Open Manual Index Page"], ["align", "Preferences: Alignment Key Bindings – <PERSON><PERSON><PERSON>"], ["sho ", "DocPHP: Show Definition"], ["doc man", "DocPHP: Search Manual"], ["install", "Package Control: Install Package"], ["proje", "Project: Edit"], ["ui co", "UI: Select Color Scheme"], ["ui th", "UI: Select Theme"], ["ins", "Package Control: Install Package"], ["remor", "Package Control: Remove Repository"], ["them", "UI: Select Theme"], ["sett", "Preferences: Settings"], ["comm", "Toggle Comment"], ["togg", "Toggle Comment"], ["instal", "Package Control: Install Package"], ["ui col", "UI: Select Color Scheme"], ["ui", "UI: Select Theme"], ["color", "UI: Select Color Scheme"], ["color the", "UI: Select Color Scheme"], ["theme", "UI: Select Theme"], ["sideb", "View: Toggle Open Files in Side Bar"], ["settin", "Preferences: Settings"], ["packa", "Install Package Control"], ["pref", "Preferences: Settings"]], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/home/<USER>/MEGA", "/home/<USER>/www/andresmaiden", "/home/<USER>/www/andresmaiden/herramientas"], "file_history": ["/home/<USER>/www/andresmaiden/herramientas/memory-hack.php", "/home/<USER>/www/andresmaiden/herramientas/migrar.php", "/home/<USER>/MEGA/MEMORY/hoy.memory", "/home/<USER>/MEGA/MEMORY/inversor.memory", "/home/<USER>/MEGA/MEMORY/ideas.memory", "/home/<USER>/www/saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba7.php", "/home/<USER>/www/saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba6.php", "/home/<USER>/www/saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoAFIP.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/sistemas.sql", "/home/<USER>/MEGA/PROYECTOS/SAAS.todo", "/home/<USER>/www/saasargentina/services/app/public/ventanas/saas_mailing.php", "/home/<USER>/www/saasargentina/services/acc/funciones_basicas.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_modelo.php", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_pdf_predeterminado.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_pdf_ticket.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_pdf_viejo.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_predeterminado.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_ticket.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_1/css/ventas_viejo.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_pdf_predeterminado.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_pdf_ticket.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_pdf_viejo.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_predeterminado.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_ticket.css", "/home/<USER>/www/saasargentina/services/app/public/estilos/estilo_2/css/ventas_viejo.css", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compras_txtafip.php", "/home/<USER>/MEGA/PROYECTOS/CRONO.todo", "/home/<USER>/MEGA/PROYECTOS/MACHETES.todo", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_wsfe.php", "/home/<USER>/www/saasargentina/services/api/public/v0.1/SaasPersistence.php", "/home/<USER>/www/saasargentina/services/www/public/js/login.js", "/home/<USER>/www/saasargentina/services/login/funciones_login.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_alta.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/clientes_listar_pedidos.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210727130214_1360.sql", "/home/<USER>/MEGA/PROYECTOS/NODO.todo", "/home/<USER>/Descargas/codecanyon-HbCigO4u-woocommerce-pdf-catalog/READ THIS.txt", "/home/<USER>/MEGA/WIKI/Configuración de eventos.sql", "/home/<USER>/MEGA/WIKI/Letsencrypt.txt", "/home/<USER>/MEGA/PROYECTOS/DEPLOY.todo", "/home/<USER>/Descargas/AR-27258678481-2021071-6-LOTE1", "/home/<USER>/www/saasargentina/services/scripts/manual/txtafip/WrapperBD.php", "/home/<USER>/MEGA/WIKI/Extraer (recuperar, restaurar) db de backup mysqldump.txt", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_html.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210803161830_1391.sql", "/home/<USER>/www/saasargentina/services/app/public/flotantes/configuraciones_categorias_tributos.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/categorias_tributos_alta.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/categorias_tributos_mod.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/categorias_tributos_baja.php", "/home/<USER>/www/saasargentina/services/app/public/flotantes/productos_alta.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_ventanas.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_configuraciones.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_productos.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_productos.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210803144705_1430.sql", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_ver.php", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_161_script_1.php", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_161_script_2.php", "/home/<USER>/MEGA/WIKI/Wordpress.md", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_874_script_1.php", "/home/<USER>/MEGA/PROYECTOS/crono_dev.txt", "/home/<USER>/www/saasargentina/services/informes/config/informe_31.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_mod.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_comprobantes.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_ver.php", "/home/<USER>/www/saasargentina/services/scripts/crontab/antiafip.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_ventas.php", "/home/<USER>/www/saasargentina/services/acc/empresas/logs/sincae.csv", "/home/<USER>/www/saasargentina/services/app/public/ventas.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/clientes_listar_ventas.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/clientes_listar_facturas.php", "/home/<USER>/MEGA/CONFIG/zshrc", "/home/<USER>/MEGA/CONFIG/mysql.cnf", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210420120817_1330.sql", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210727101607_1403.sql", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_txtafip.php", "/home/<USER>/MEGA/WIKI/Revisar numeración de caes.txt", "/home/<USER>/www/saasargentina/services/acc/archivos/AR-27258678481-2021060-D7-LOTE1_32c91fb0479169d4a71e1be68ba3e876/AR-27258678481-2021060-D7-LOTE1.txt", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_compras.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20210722065009_1391.sql", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compraspagos_ver.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compraspagos_altamod_vista.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compras_mod.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compraspagos_altamod.php", "/home/<USER>/www/saasargentina/services/app/public/compras.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/validaciones/validacion_ventaspagos.php", "/home/<USER>/MEGA/WIKI/Eliminar clientes.sql", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_funciones.php", "/home/<USER>/www/saasargentina/services/app/public/flotantes/productos_log.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventaspagos_exportar.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/productosxventas_alta.php", "/home/<USER>/www/saasargentina/services/acc/acc.php", "/home/<USER>/www/saasargentina/services/app/public/flotantes/mail.php", "/home/<USER>/www/saasargentina/services/acc/empresas/logs/mail_caido.csv", "/home/<USER>/MEGA/WIKI/ZZ Emprendedor.md", "/home/<USER>/MEGA/WIKI/ZZ Educación.md", "/home/<USER>/www/saasargentina/services/app/public/vendor/guzzle/guzzle/phar-stub.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventaspagos_altamod.php", "/home/<USER>/MEGA/WIKI/ZZ Music.md", "/home/<USER>/MEGA/PROYECTOS/HACER.todo", "/home/<USER>/Descargas/AR-27258678481-202106-P7-LOTE1", "/home/<USER>/.cache/.fr-yRI3u3/AR-27258678481-202106-P7-LOTE1.txt", "/home/<USER>/.cache/.fr-qv6Mxx/AR-27258678481-202106-P7-LOTE1.txt", "/home/<USER>/.cache/.fr-gprb8D/home/<USER>/www/saasargentina/services/acc/archivos/AR-27258678481-202106-P7-LOTE1.txt", "/tmp/mozilla_andresmaiden0/AR-27258678481-202106-P7-LOTE1-1.zip", "/tmp/mozilla_andresmaiden0/AR-27258678481-202106-P7-LOTE1.zip", "/home/<USER>/Descargas/AR-27258678481-202106-P7-LOTE1.zip", "/home/<USER>/www/prueba.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_listar.php", "/home/<USER>/Documentos/material-dashboard-laravel-master/src/material-stubs/resources/views/dashboard.blade.php", "/home/<USER>/Documentos/material-dashboard-laravel-master/src/MaterialPresetServiceProvider.php", "/home/<USER>/Documentos/material-dashboard-laravel-master/src/MaterialPreset.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/configuraciones_ML.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/configuraciones_ventas_listar.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/tiposdeventas_baja.php", "/home/<USER>/www/saasargentina/services/GenerarArchivoAFIP.php", "/tmp/mozilla_andresmaiden0/arba-percepcion-14.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-11.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-12.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-13.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-10.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-6.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-9.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-8.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-7.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-5.txt", "/tmp/mozilla_andresmaiden0/arba-percepcion-4.txt"], "find": {"height": 55.0}, "find_in_files": {"height": 127.0, "where_history": ["", "informes", "", "*.css", ""]}, "find_state": {"case_sensitive": false, "find_history": [".memory", "$i", "$cuenta", "#com_cliente_No_Responsable_IVA {", "com_cliente_Consumidor_final", "</p>\n<!-- /wp:paragraph -->", "</li><li>", "<!-- wp:list -->", "<p>", "<!-- wp:paragraph -->", "gmail", "numero", "<label", "527, 528, 529", "277", "cronometrajeinstantaneo.com", "ventas_herra", "arba", "HEAD", "ventas_her", "HEAD", "arba", "categorias_tribu", "nombre", "auto", "tribu", "migra", "10143", "numero", "ventas", "idiva", "migr", "lecturas2", "perfil_ventas_herramientas", "ventas_herr", "<PERSON><PERSON><PERSON><PERSON>", "estadocombo", "$cae", "cae", "estado", "http://app.saasargentina.des/ventas.php?a=mod&id=506636", "$discri", "numero", "No se puede modificar facturas electrónicas autorizadas por AFIP", "cae", "ca", "CAE", "where ive", "ML_user_id", "WHERE", "where", "anular", "venta", "idventa", "fecha", "cae != ''", "fecha", "numero", "estadocae", "rece1_validar", "estadocae", "CAE", "cae", "concepto", "cae", "estado", "==", "situacion", "''", "estado_cae", "`ventas`", "costo", "modifi", "updat", "Descripción: ", "tablas_", "tar", "ventas", "servicios", "acredit", "<PERSON>unt<PERSON>a", "punto de venta", "compra", "puntodevent", "numero", "ultimo", "ultimonu", "ultimonume", "comprapago", "retencion_ob", "observacion", "idtributo", "idtribu", "$datos", "idtributo", "){", "if(", "reten", "número", "$i18n[256]", "$", "numero", "retenciones", "|", "Factura rechazada por afip porque el neto", "ventas", "productos", "id ", "retencion", "cuit", "527, 528, 529", "$i18n[363]", "histori", "(SELECT idventa FROM", "arba-reten", "(SELECT idventa FROM ventas", "arba-reten", "arpa-reten", "compraspagos", "include '../ventanas/ventaspagos_exportar.php';", "pagos", "<PERSON><PERSON>   ", "q=", "'q'", "retencion", "idretencion", "retenciones", "10143"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["#com_cliente_No_Responsable_IVA, #com_cliente_IVA_No_Alcanzado {", ") {", "if (", "'quincena'", "extrasx<PERSON><PERSON><PERSON>", "$tabla", "$idventa", "    ", ",", "", ",", "", "*************", "Google Negocio", "Facebook", "Instagram", ",", "j", "", "☐ ", "')) ON DUPLICATE", "idlistaxextra)", "idlistaxextra=(SELECT", "');", "listasxextras", "id<PERSON>oc<PERSON><PERSON>", "SET", ",", "INSERT INTO", " AND meta_key", "9878", "1991", "INSERT INTO"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": []}], "incremental_find": {"height": 27.0}, "input": {"height": 52.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": false, "output.SFTP": {"height": 0.0}, "output.SublimeLinter": {"height": 0.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "pinned_build_system": "", "project": "andresmaiden.sublime-project", "replace": {"height": 91.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["memo", "andresmaiden/herramientas/memory-hack.php"], ["migrar", "andresmaiden/herramientas/migrar.php"], ["hoy", "MEGA/MEMORY/hoy.memory"], ["i", "MEGA/MEMORY/inversor.memory"], ["sis", "saasargentina/services/app/public/migrations/sistemas.sql"], ["mailing", "saasargentina/services/app/public/ventanas/saas_mailing.php"], ["sa", "MEGA/PROYECTOS/SAAS.todo"], ["ventas css", "saasargentina/services/app/public/estilos/estilo_1/css/ventas_predeterminado.css"], ["persi", "saasargentina/services/api/public/v0.1/SaasPersistence.php"], ["funcion wsf", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["mach", "MEGA/PROYECTOS/MACHETES.todo"], ["crono", "MEGA/PROYECTOS/CRONO.todo"], ["login js", "saasargentina/services/www/public/js/login.js"], ["funcion login", "saasargentina/services/login/funciones_login.php"], ["saas", "MEGA/PROYECTOS/SAAS.todo"], ["produ alta", "saasargentina/services/app/public/ventanas/productos_alta.php"], ["cro", "MEGA/PROYECTOS/CRONO.todo"], ["clie list pedi", "saasargentina/services/app/public/procesos/clientes_listar_pedidos.php"], ["funcionwsfe", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["nodo", "MEGA/PROYECTOS/NODO.todo"], ["confi", "MEGA/WIKI/Configuración de eventos.sql"], ["let", "MEGA/WIKI/Letsencrypt.txt"], ["mache", "MEGA/PROYECTOS/MACHETES.todo"], ["depl", "MEGA/PROYECTOS/DEPLOY.todo"], ["txtafip", "saasargentina/services/app/public/ventanas/compras_txtafip.php"], ["wrap", "saasargentina/services/scripts/manual/txtafip/WrapperBD.php"], ["recupe", "MEGA/WIKI/Extraer (recuperar, restaurar) db de backup mysqldump.txt"], ["FUNCION HTM", "saasargentina/services/app/public/librerias/funciones_html.php"], ["funcion html", "saasargentina/services/app/public/librerias/funciones_html.php"], ["1391", "saasargentina/services/app/public/migrations/20210803161830_1391.sql"], ["produ al", "saasargentina/services/app/public/flotantes/productos_alta.php"], ["cate tri baj", "saasargentina/services/app/public/procesos/categorias_tributos_baja.php"], ["cate trib mod", "saasargentina/services/app/public/procesos/categorias_tributos_mod.php"], ["cate tribu", "saasargentina/services/app/public/procesos/categorias_tributos_alta.php"], ["idio confi", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_configuraciones.php"], ["pedi", "saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php"], ["configu cate tri", "saasargentina/services/app/public/flotantes/configuraciones_categorias_tributos.php"], ["idio po", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_productos.php"], ["dep", "MEGA/PROYECTOS/DEPLOY.todo"], ["word", "MEGA/WIKI/Wordpress.md"], ["scrip 16", "saasargentina/services/scripts/empresas/empresa_161_script_2.php"], ["scrip 161", "saasargentina/services/scripts/empresas/empresa_161_script_1.php"], ["874", "saasargentina/services/scripts/empresas/empresa_874_script_1.php"], ["161", "saasargentina/services/scripts/empresas/empresa_161_script_1.php"], ["funcin mo", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["cronode", "MEGA/PROYECTOS/crono_dev.txt"], ["crono.", "MEGA/PROYECTOS/CRONO.todo"], ["idio pro", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_productos.php"], ["produ ver", "saasargentina/services/app/public/ventanas/productos_ver.php"], ["saas.", "MEGA/PROYECTOS/SAAS.todo"], ["crono de", "MEGA/PROYECTOS/crono_dev.txt"], ["c", "MEGA/PROYECTOS/CRONO.todo"], ["funcion ws", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["inform", "saasargentina/services/informes/config/informe_31.php"], ["antiafip", "saasargentina/services/scripts/crontab/antiafip.php"], ["ventas ver", "saasargentina/services/app/public/ventanas/ventas_ver.php"], ["ventas.", "saasargentina/services/app/public/ventas.php"], ["idio v", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_ventas.php"], ["IDIO VEN", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_ventas.php"], ["venta mod", "saasargentina/services/app/public/ventanas/ventas_mod.php"], ["cliente lista ven", "saasargentina/services/app/public/ventanas/clientes_listar_ventas.php"], ["z", "MEGA/CONFIG/zshrc"], ["cliente lista", "saasargentina/services/app/public/procesos/clientes_listar_facturas.php"], ["idio ven", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_ventas.php"], ["my", "MEGA/CONFIG/mysql.cnf"], ["ventas_mod", "saasargentina/services/app/public/ventanas/ventas_mod.php"], ["1360", "saasargentina/services/app/public/migrations/20210727130214_1360.sql"], ["produc ver", "saasargentina/services/app/public/ventanas/productos_ver.php"], ["funcion mod", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["funcion ", "saasargentina/services/acc/funciones_basicas.php"], ["saas.to", "MEGA/PROYECTOS/SAAS.todo"], ["1403", "saasargentina/services/app/public/migrations/20210727101607_1403.sql"], ["num", "MEGA/WIKI/Revisar numeración de caes.txt"], ["saas.todo", "MEGA/PROYECTOS/SAAS.todo"], ["crono dev", "MEGA/PROYECTOS/crono_dev.txt"], ["genera", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoAFIP.php"], ["archivoarba 7", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba7.php"], ["compras mod", "saasargentina/services/app/public/ventanas/compras_mod.php"], ["comprapago altamo", "saasargentina/services/app/public/ventanas/compraspagos_altamod_vista.php"], ["compras.php", "saasargentina/services/app/public/compras.php"], ["compraspagos mo", "saasargentina/services/app/public/ventanas/compraspagos_altamod.php"], ["idicompras", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_compras.php"], ["funcion b", "saasargentina/services/acc/funciones_basicas.php"], ["compraspagos ver", "saasargentina/services/app/public/ventanas/compraspagos_ver.php"], ["siste", "saasargentina/services/app/public/migrations/sistemas.sql"], ["saas.t", "MEGA/PROYECTOS/SAAS.todo"], ["elimin", "MEGA/WIKI/Eliminar clientes.sql"], ["config eve", "MEGA/WIKI/Configuración de eventos.sql"], ["arba 6", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba6.php"], ["idio fun", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_funciones.php"], ["produ log", "saasargentina/services/app/public/flotantes/productos_log.php"], ["de", "MEGA/PROYECTOS/DEPLOY.todo"], ["func html", "saasargentina/services/app/public/librerias/funciones_html.php"], ["ventaspago expor", "saasargentina/services/app/public/ventanas/ventaspagos_exportar.php"], ["flotante mail", "saasargentina/services/app/public/flotantes/mail.php"], ["log mail", "saasargentina/services/acc/empresas/logs/mail_caido.csv"], ["acc.php", "saasargentina/services/acc/acc.php"], ["funciones ba", "saasargentina/services/acc/funciones_basicas.php"], ["produxven alta", "saasargentina/services/app/public/procesos/productosxventas_alta.php"], ["cron", "MEGA/PROYECTOS/CRONO.todo"], ["saa", "MEGA/PROYECTOS/SAAS.todo"], ["zz", "MEGA/WIKI/ZZ Emprendedor.md"], ["zz estu", "saasargentina/services/app/public/vendor/guzzle/guzzle/phar-stub.php"], ["generaarchi arba", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba7.php"], ["venpago al", "saasargentina/services/app/public/ventanas/ventaspagos_altamod.php"], ["zz ep", "MEGA/WIKI/ZZ Emprendedor.md"], ["zz mu", "MEGA/WIKI/ZZ Music.md"], ["hace", "MEGA/PROYECTOS/HACER.todo"], ["generar archivo ar", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoArba7.php"], ["idio comp", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_compras.php"], ["ventas txt", "saasargentina/services/app/public/ventanas/ventas_txtafip.php"], ["compra txt", "saasargentina/services/app/public/ventanas/compras_txtafip.php"], ["ACC.PHP", "saasargentina/services/acc/acc.php"], ["venta tx", "saasargentina/services/app/public/ventanas/ventas_txtafip.php"], ["confi eve", "MEGA/WIKI/Configuración de eventos.sql"], ["ventas lista", "saasargentina/services/app/public/ventanas/ventas_listar.php"], ["confi ml", "saasargentina/services/app/public/ventanas/configuraciones_ML.php"], ["tiposd", "saasargentina/services/app/public/procesos/tiposdeventas_baja.php"], ["configura venta", "saasargentina/services/app/public/ventanas/configuraciones_ventas_listar.php"], ["ma", "MEGA/PROYECTOS/MACHETES.todo"], ["generarar", "saasargentina/services/scripts/manual/txtafip/generadores/GeneradorArchivoAFIP.php"], ["funcion ven", "saasargentina/services/app/public/librerias/funciones_ventanas.php"], ["funcio mod", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["funion", "saasargentina/services/acc/funciones_basicas.php"], ["productoxven mod", "saasargentina/services/app/public/procesos/productosxventas_mod.php"], ["ven", "saasargentina/services/app/public/ventas.php"], ["venta lista", "saasargentina/services/app/public/ventanas/ventas_listar.php"], ["saas<PERSON>i", "saasargentina/services/api/public/v0.2/SaasPersistence.php"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "~/MEGA/PROYECTOS/saas.sublime-project"], ["wo", "~/MEGA/PROYECTOS/woocommerce.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": true, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 312.0, "status_bar_visible": true, "template_settings": {}}