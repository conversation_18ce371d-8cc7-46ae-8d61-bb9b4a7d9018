{"auto_complete": {"selected_items": [["json", "json_encode()"], ["Wo", "WooCommerce"], ["pro", "producto->get_\t⌬ tabnine"], ["so", "solución\tabc"], ["targe", "target=\"_blank\" data-preference-id=\"\t⌬ tabnine"], ["saas", "saasargentina\tabc"]]}, "buffers": [{"file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsyncsaas.php", "settings": {"buffer_size": 11402, "line_ending": "Windows"}}, {"file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/config.php", "settings": {"buffer_size": 13292, "encoding": "UTF-8", "line_ending": "Windows"}, "undo_stack": [[10, 1, "insert", {"characters": " Se"}, "AwAAAP4jAAAAAAAA/yMAAAAAAAAAAAAA/yMAAAAAAAAAJAAAAAAAAAAAAAAAJAAAAAAAAAEkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAD+IwAAAAAAAP4jAAAAAAAAAAAAAAAA8L8"], [11, 1, "insert", {"characters": " utiliza"}, "CAAAAAEkAAAAAAAAAiQAAAAAAAAAAAAAAiQAAAAAAAADJAAAAAAAAAAAAAADJAAAAAAAAAQkAAAAAAAAAAAAAAQkAAAAAAAABSQAAAAAAAAAAAAABSQAAAAAAAAGJAAAAAAAAAAAAAAGJAAAAAAAAAckAAAAAAAAAAAAAAckAAAAAAAACCQAAAAAAAAAAAAACCQAAAAAAAAJJAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAABJAAAAAAAAAEkAAAAAAAAAAAAAAAA8L8"], [12, 1, "insert", {"characters": " para"}, "BQAAAAkkAAAAAAAACiQAAAAAAAAAAAAACiQAAAAAAAALJAAAAAAAAAAAAAALJAAAAAAAAAwkAAAAAAAAAAAAAAwkAAAAAAAADSQAAAAAAAAAAAAADSQAAAAAAAAOJAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAJJAAAAAAAAAkkAAAAAAAAAAAAAAAA8L8"], [13, 1, "insert", {"characters": " "}, "AQAAAA4kAAAAAAAADyQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAOJAAAAAAAAA4kAAAAAAAAAAAAAAAA8L8"], [15, 1, "left_delete", null, "AQAAAP4jAAAAAAAA/iMAAAAAAAABAAAAIA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAD/IwAAAAAAAP8jAAAAAAAAAAAAAAAA8L8"], [16, 1, "insert", {"characters": "<br>"}, "BAAAAP4jAAAAAAAA/yMAAAAAAAAAAAAA/yMAAAAAAAAAJAAAAAAAAAAAAAAAJAAAAAAAAAEkAAAAAAAAAAAAAAEkAAAAAAAAAiQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAD+IwAAAAAAAP4jAAAAAAAAAAAAAAAA8L8"], [22, 1, "insert", {"characters": "s<PERSON><PERSON>"}, "BQAAAA0kAAAAAAAADiQAAAAAAAAAAAAADiQAAAAAAAAOJAAAAAAAAAQAAABwYXJhDiQAAAAAAAAPJAAAAAAAAAAAAAAPJAAAAAAAABAkAAAAAAAAAAAAABAkAAAAAAAAESQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAARJAAAAAAAAA0kAAAAAAAAAAAAAAAA8L8"], [23, 1, "insert", {"characters": " para"}, "BQAAABEkAAAAAAAAEiQAAAAAAAAAAAAAEiQAAAAAAAATJAAAAAAAAAAAAAATJAAAAAAAABQkAAAAAAAAAAAAABQkAAAAAAAAFSQAAAAAAAAAAAAAFSQAAAAAAAAWJAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAARJAAAAAAAABEkAAAAAAAAAAAAAAAA8L8"], [24, 1, "insert", {"characters": " sincronziar"}, "DAAAABYkAAAAAAAAFyQAAAAAAAAAAAAAFyQAAAAAAAAYJAAAAAAAAAAAAAAYJAAAAAAAABkkAAAAAAAAAAAAABkkAAAAAAAAGiQAAAAAAAAAAAAAGiQAAAAAAAAbJAAAAAAAAAAAAAAbJAAAAAAAABwkAAAAAAAAAAAAABwkAAAAAAAAHSQAAAAAAAAAAAAAHSQAAAAAAAAeJAAAAAAAAAAAAAAeJAAAAAAAAB8kAAAAAAAAAAAAAB8kAAAAAAAAICQAAAAAAAAAAAAAICQAAAAAAAAhJAAAAAAAAAAAAAAhJAAAAAAAACIkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAWJAAAAAAAABYkAAAAAAAAAAAAAAAA8L8"], [25, 1, "insert", {"characters": " stock."}, "BwAAACIkAAAAAAAAIyQAAAAAAAAAAAAAIyQAAAAAAAAkJAAAAAAAAAAAAAAkJAAAAAAAACUkAAAAAAAAAAAAACUkAAAAAAAAJiQAAAAAAAAAAAAAJiQAAAAAAAAnJAAAAAAAAAAAAAAnJAAAAAAAACgkAAAAAAAAAAAAACgkAAAAAAAAKSQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAiJAAAAAAAACIkAAAAAAAAAAAAAAAA8L8"], [26, 1, "insert", {"characters": " Para"}, "BQAAACkkAAAAAAAAKiQAAAAAAAAAAAAAKiQAAAAAAAArJAAAAAAAAAAAAAArJAAAAAAAACwkAAAAAAAAAAAAACwkAAAAAAAALSQAAAAAAAAAAAAALSQAAAAAAAAuJAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAApJAAAAAAAACkkAAAAAAAAAAAAAAAA8L8"], [27, 1, "insert", {"characters": " descontar"}, "CgAAAC4kAAAAAAAALyQAAAAAAAAAAAAALyQAAAAAAAAwJAAAAAAAAAAAAAAwJAAAAAAAADEkAAAAAAAAAAAAADEkAAAAAAAAMiQAAAAAAAAAAAAAMiQAAAAAAAAzJAAAAAAAAAAAAAAzJAAAAAAAADQkAAAAAAAAAAAAADQkAAAAAAAANSQAAAAAAAAAAAAANSQAAAAAAAA2JAAAAAAAAAAAAAA2JAAAAAAAADckAAAAAAAAAAAAADckAAAAAAAAOCQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAuJAAAAAAAAC4kAAAAAAAAAAAAAAAA8L8"], [28, 1, "insert", {"characters": " "}, "AQAAADgkAAAAAAAAOSQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA4JAAAAAAAADgkAAAAAAAAAAAAAAAA8L8"], [29, 1, "insert", {"characters": "de"}, "AgAAADkkAAAAAAAAOiQAAAAAAAAAAAAAOiQAAAAAAAA7JAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA5JAAAAAAAADkkAAAAAAAAAAAAAAAA8L8"], [30, 1, "insert", {"characters": " un"}, "AwAAADskAAAAAAAAPCQAAAAAAAAAAAAAPCQAAAAAAAA9JAAAAAAAAAAAAAA9JAAAAAAAAD4kAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA7JAAAAAAAADskAAAAAAAAAAAAAAAA8L8"], [31, 1, "insert", {"characters": " stock"}, "BgAAAD4kAAAAAAAAPyQAAAAAAAAAAAAAPyQAAAAAAABAJAAAAAAAAAAAAABAJAAAAAAAAEEkAAAAAAAAAAAAAEEkAAAAAAAAQiQAAAAAAAAAAAAAQiQAAAAAAABDJAAAAAAAAAAAAABDJAAAAAAAAEQkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAA+JAAAAAAAAD4kAAAAAAAAAAAAAAAA8L8"], [32, 1, "insert", {"characters": " especí"}, "BwAAAEQkAAAAAAAARSQAAAAAAAAAAAAARSQAAAAAAABGJAAAAAAAAAAAAABGJAAAAAAAAEckAAAAAAAAAAAAAEckAAAAAAAASCQAAAAAAAAAAAAASCQAAAAAAABJJAAAAAAAAAAAAABJJAAAAAAAAEokAAAAAAAAAAAAAEokAAAAAAAASyQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABEJAAAAAAAAEQkAAAAAAAAAAAAAAAA8L8"], [33, 1, "insert", {"characters": "fico"}, "BAAAAEskAAAAAAAATCQAAAAAAAAAAAAATCQAAAAAAABNJAAAAAAAAAAAAABNJAAAAAAAAE4kAAAAAAAAAAAAAE4kAAAAAAAATyQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABLJAAAAAAAAEskAAAAAAAAAAAAAAAA8L8"], [34, 1, "insert", {"characters": " "}, "AQAAAE8kAAAAAAAAUCQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABPJAAAAAAAAE8kAAAAAAAAAAAAAAAA8L8"], [35, 1, "insert", {"characters": "durante"}, "BwAAAFAkAAAAAAAAUSQAAAAAAAAAAAAAUSQAAAAAAABSJAAAAAAAAAAAAABSJAAAAAAAAFMkAAAAAAAAAAAAAFMkAAAAAAAAVCQAAAAAAAAAAAAAVCQAAAAAAABVJAAAAAAAAAAAAABVJAAAAAAAAFYkAAAAAAAAAAAAAFYkAAAAAAAAVyQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABQJAAAAAAAAFAkAAAAAAAAAAAAAAAA8L8"], [36, 1, "insert", {"characters": " la"}, "AwAAAFckAAAAAAAAWCQAAAAAAAAAAAAAWCQAAAAAAABZJAAAAAAAAAAAAABZJAAAAAAAAFokAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABXJAAAAAAAAFckAAAAAAAAAAAAAAAA8L8"], [37, 1, "insert", {"characters": " venta,"}, "BwAAAFokAAAAAAAAWyQAAAAAAAAAAAAAWyQAAAAAAABcJAAAAAAAAAAAAABcJAAAAAAAAF0kAAAAAAAAAAAAAF0kAAAAAAAAXiQAAAAAAAAAAAAAXiQAAAAAAABfJAAAAAAAAAAAAABfJAAAAAAAAGAkAAAAAAAAAAAAAGAkAAAAAAAAYSQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABaJAAAAAAAAFokAAAAAAAAAAAAAAAA8L8"], [38, 1, "insert", {"characters": " d"}, "AgAAAGEkAAAAAAAAYiQAAAAAAAAAAAAAYiQAAAAAAABjJAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABhJAAAAAAAAGEkAAAAAAAAAAAAAAAA8L8"], [39, 1, "insert", {"characters": "ebe"}, "AwAAAGMkAAAAAAAAZCQAAAAAAAAAAAAAZCQAAAAAAABlJAAAAAAAAAAAAABlJAAAAAAAAGYkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABjJAAAAAAAAGMkAAAAAAAAAAAAAAAA8L8"], [40, 1, "insert", {"characters": " configurar<PERSON>"}, "DQAAAGYkAAAAAAAAZyQAAAAAAAAAAAAAZyQAAAAAAABoJAAAAAAAAAAAAABoJAAAAAAAAGkkAAAAAAAAAAAAAGkkAAAAAAAAaiQAAAAAAAAAAAAAaiQAAAAAAABrJAAAAAAAAAAAAABrJAAAAAAAAGwkAAAAAAAAAAAAAGwkAAAAAAAAbSQAAAAAAAAAAAAAbSQAAAAAAABuJAAAAAAAAAAAAABuJAAAAAAAAG8kAAAAAAAAAAAAAG8kAAAAAAAAcCQAAAAAAAAAAAAAcCQAAAAAAABxJAAAAAAAAAAAAABxJAAAAAAAAHIkAAAAAAAAAAAAAHIkAAAAAAAAcyQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABmJAAAAAAAAGYkAAAAAAAAAAAAAAAA8L8"], [41, 1, "insert", {"characters": " en"}, "AwAAAHMkAAAAAAAAdCQAAAAAAAAAAAAAdCQAAAAAAAB1JAAAAAAAAAAAAAB1JAAAAAAAAHYkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAABzJAAAAAAAAHMkAAAAAAAAAAAAAAAA8L8"], [42, 1, "insert", {"characters": " el"}, "AwAAAHYkAAAAAAAAdyQAAAAAAAAAAAAAdyQAAAAAAAB4JAAAAAAAAAAAAAB4JAAAAAAAAHkkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB2JAAAAAAAAHYkAAAAAAAAAAAAAAAA8L8"], [43, 1, "insert", {"characters": " Tipo"}, "BQAAAHkkAAAAAAAAeiQAAAAAAAAAAAAAeiQAAAAAAAB7JAAAAAAAAAAAAAB7JAAAAAAAAHwkAAAAAAAAAAAAAHwkAAAAAAAAfSQAAAAAAAAAAAAAfSQAAAAAAAB+JAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB5JAAAAAAAAHkkAAAAAAAAAAAAAAAA8L8"], [44, 1, "insert", {"characters": " de"}, "AwAAAH4kAAAAAAAAfyQAAAAAAAAAAAAAfyQAAAAAAACAJAAAAAAAAAAAAACAJAAAAAAAAIEkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAB+JAAAAAAAAH4kAAAAAAAAAAAAAAAA8L8"], [45, 1, "insert", {"characters": " <PERSON><PERSON><PERSON>"}, "BgAAAIEkAAAAAAAAgiQAAAAAAAAAAAAAgiQAAAAAAACDJAAAAAAAAAAAAACDJAAAAAAAAIQkAAAAAAAAAAAAAIQkAAAAAAAAhSQAAAAAAAAAAAAAhSQAAAAAAACGJAAAAAAAAAAAAACGJAAAAAAAAIckAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACBJAAAAAAAAIEkAAAAAAAAAAAAAAAA8L8"], [46, 1, "insert", {"characters": " dentro"}, "BwAAAIckAAAAAAAAiCQAAAAAAAAAAAAAiCQAAAAAAACJJAAAAAAAAAAAAACJJAAAAAAAAIokAAAAAAAAAAAAAIokAAAAAAAAiyQAAAAAAAAAAAAAiyQAAAAAAACMJAAAAAAAAAAAAACMJAAAAAAAAI0kAAAAAAAAAAAAAI0kAAAAAAAAjiQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACHJAAAAAAAAIckAAAAAAAAAAAAAAAA8L8"], [47, 1, "insert", {"characters": " de"}, "AwAAAI4kAAAAAAAAjyQAAAAAAAAAAAAAjyQAAAAAAACQJAAAAAAAAAAAAACQJAAAAAAAAJEkAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACOJAAAAAAAAI4kAAAAAAAAAAAAAAAA8L8"], [48, 1, "insert", {"characters": " "}, "AQAAAJEkAAAAAAAAkiQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACRJAAAAAAAAJEkAAAAAAAAAAAAAAAA8L8"], [49, 1, "insert_snippet", {"contents": "'$0'"}, "AQAAAJIkAAAAAAAAlCQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACSJAAAAAAAAJIkAAAAAAAAAAAAAAAA8L8"], [50, 1, "left_delete", null, "AQAAAJIkAAAAAAAAkiQAAAAAAAABAAAAJw", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACTJAAAAAAAAJMkAAAAAAAAAAAAAAAA8L8"], [51, 1, "insert", {"characters": "'.SV"}, "BAAAAJIkAAAAAAAAkyQAAAAAAAAAAAAAkyQAAAAAAACUJAAAAAAAAAAAAACUJAAAAAAAAJUkAAAAAAAAAAAAAJUkAAAAAAAAliQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACSJAAAAAAAAJIkAAAAAAAAAAAAAAAA8L8"], [52, 1, "insert_completion", {"completion": "SV_SYNC_ERP", "format": "snippet", "keep_prefix": false, "must_insert": false, "trigger": "SV_SYNC_ERP"}, "AgAAAJQkAAAAAAAAlCQAAAAAAAACAAAAU1aUJAAAAAAAAJ8kAAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACWJAAAAAAAAJYkAAAAAAAAAAAAAAAA8L8"], [53, 1, "insert", {"characters": "."}, "AQAAAJ8kAAAAAAAAoCQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACfJAAAAAAAAJ8kAAAAAAAAAAAAAAAA8L8"], [55, 1, "insert", {"characters": "."}, "AQAAAKEkAAAAAAAAoiQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAChJAAAAAAAAKEkAAAAAAAAAAAAAAAA8L8"], [56, 1, "right_delete", null, "AQAAAKIkAAAAAAAAoiQAAAAAAAABAAAAIA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACiJAAAAAAAAKIkAAAAAAAAAAAAAAAA8L8"], [72, 1, "insert", {"characters": "cambiar"}, "CAAAAHYUAAAAAAAAdxQAAAAAAAAAAAAAdxQAAAAAAAB3FAAAAAAAAAoAAABjb25maWd1cmFydxQAAAAAAAB4FAAAAAAAAAAAAAB4FAAAAAAAAHkUAAAAAAAAAAAAAHkUAAAAAAAAehQAAAAAAAAAAAAAehQAAAAAAAB7FAAAAAAAAAAAAAB7FAAAAAAAAHwUAAAAAAAAAAAAAHwUAAAAAAAAfRQAAAAAAAAAAAAA", "AgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAACAFAAAAAAAAHYUAAAAAAAAAAAAAAAA8L8"]]}, {"file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsynctango.php", "settings": {"buffer_size": 4757, "line_ending": "Windows"}, "undo_stack": []}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["men", "View: <PERSON><PERSON>"], ["menu", "View: <PERSON><PERSON>"], ["the", "UI: Select Theme"], ["insta", "Package Control: Install Package"], ["proje", "Project: Edit"], ["ui co", "UI: Select Color Scheme"], ["ui th", "UI: Select Theme"], ["ins", "Package Control: Install Package"], ["remo", "Package Control: Remove Package"], ["remor", "Package Control: Remove Repository"], ["them", "UI: Select Theme"], ["key", "Preferences: Key Bindings"], ["sett", "Preferences: Settings"], ["comm", "Toggle Comment"], ["togg", "Toggle Comment"], ["instal", "Package Control: Install Package"], ["ui col", "UI: Select Color Scheme"], ["remove", "Package Control: Remove Package"], ["ui", "UI: Select Theme"], ["color", "UI: Select Color Scheme"], ["color the", "UI: Select Color Scheme"], ["theme", "UI: Select Theme"], ["sideb", "View: Toggle Open Files in Side Bar"], ["settin", "Preferences: Settings"], ["packa", "Install Package Control"], ["pref", "Preferences: Settings"]], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/home/<USER>/www/woocommerce", "/home/<USER>/www/woocommerce/wp-content", "/home/<USER>/www/woocommerce/wp-content/plugins", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes"], "file_history": ["/home/<USER>/MEGA/CONFIG/MACHETES.todo", "/home/<USER>/MEGA/OBSIDIAN/WIKI/MUSICA.txt", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Bancos.txt", "/home/<USER>/www/woocommerce/wp-config.php", "/home/<USER>/MEGA/PROYECTOS/NODO.todo", "/home/<USER>/MEGA/WIKI/Configuración de eventos.sql", "/home/<USER>/MEGA/WIKI/Bancos.txt", "/home/<USER>/MEGA/PROYECTOS/PLUGINS.todo", "/home/<USER>/MEGA/PROYECTOS/MACHETES.todo", "/home/<USER>/www/sv-woo-sync-erp/includes/class.svsyncsaas.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/sv-woo-sync-erp.php", "/home/<USER>/MEGA/WIKI/ZZ Music.md", "/home/<USER>/MEGA/WIKI/MUSICA.txt", "/home/<USER>/.cache/.fr-WZmavr/sv-woo-sync-erp/sv-woo-sync-erp.php", "/home/<USER>/.ssh/config", "/home/<USER>/.ssh/known_hosts", "/home/<USER>/.ssh/authorized_keys", "/home/<USER>/www/sv-woo-sync-erp/sv-woo-sync-erp.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/sv-woo-sync-erp.log", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-entregas/includes/envios.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsyncsaas.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/config.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-entregas/operadores/index.php", "/home/<USER>/SOPORTE/api-v0_2_2021-06-09.log", "/home/<USER>/SOPORTE/api-v0_2_2021-06-07.log", "/home/<USER>/MEGA/PROYECTOS/DEPLOY.todo", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-entregas/includes/informes.php", "/tmp/mozilla_andresmaiden0/sv-woo-entregas-2.log", "/tmp/mozilla_andresmaiden0/sv-woo-entregas-1.log", "/tmp/mozilla_andresmaiden0/sv-woo-entregas.log", "/home/<USER>/MEGA/PROYECTOS/SAAS.todo", "/tmp/mozilla_andresmaiden0/sv-woo-sync-erp.log", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/functions.php", "/home/<USER>/www/woocommerce/wp-includes/class-http.php", "/home/<USER>/www/woocommerce/wp-includes/http.php", "/home/<USER>/MEGA/CONFIG/sites-enabled/virtualhosts.conf", "/home/<USER>/www/saasargentina/services/api/public/v0.2/.htaccess", "/tmp/mozilla_andresmaiden0/ejemplo.php", "/home/<USER>/www/prueba.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsynctango.php", "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/index.php", "/home/<USER>/MEGA/PROYECTOS/HACER.todo", "/home/<USER>/MEGA/WIKI/Wordpress.md", "/home/<USER>/MEGA/PROYECTOS/CRONO.todo", "/home/<USER>/CRONO/Diseño/Propuestas/Aguas Abiertas.txt", "/home/<USER>/MEGA/CONFIG/hosts", "/home/<USER>/MEGA/PROYECTOS/woocommerce.sublime-project", "/home/<USER>/MEGA/CONFIG/zshrc", "/home/<USER>/www/cronometrajeinstantaneo/.gitignore", "/home/<USER>/www/cronometrajeinstantaneo/app/Helpers/FuncionesComunes.php", "/home/<USER>/www/cronometrajeinstantaneo/routes/web.php", "/home/<USER>/Descargas/importar_488.csv", "/home/<USER>/www/cronometrajeinstantaneo/app/Http/Controllers/ParticipantesController.php", "/home/<USER>/www/cronometrajeinstantaneo/public/eventos/config_evento.php", "/home/<USER>/www/cronometrajeinstantaneo/public/eventos/nuevo_evento.php", "/home/<USER>/www/saasargentina/services/informes/.env", "/home/<USER>/MEGA/WIKI/Modificar inscripciones y resultados en Crono.txt", "/home/<USER>/MEGA/PROYECTOS/crono.sublime-project", "/home/<USER>/MEGA/PROYECTOS/SV.todo", "/home/<USER>/.local/share/applications/firefox-developer.desktop", "/home/<USER>/www/cronometrajeinstantaneo/storage/logs/laravel.log", "/home/<USER>/www/cronometrajeinstantaneo/.env", "/home/<USER>/Escritorio/antiafip.txt", "/home/<USER>/www/cronometrajeinstantaneo/public/index.php", "/home/<USER>/www/cronometrajeinstantaneo/cronometrajeinstantaneo.env", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_wsfe.php", "/home/<USER>/www/saasargentina/services/acc/funciones_basicas.php", "/home/<USER>/www/saasargentina/services/informes/vendor/laravel/framework/src/Illuminate/Routing/Route.php", "/home/<USER>/MEGA/CONFIG/saas.conf", "/home/<USER>/www/saasargentina/services/acc/acc.php", "/home/<USER>/www/phpmyadmin/index.php", "/home/<USER>/www/info.php", "/home/<USER>/MEGA/CONFIG/sites-enabled/default.conf", "/home/<USER>/MEGA/CONFIG/sites-enabled/saas.conf", "/home/<USER>/www/phpmyadmin/libraries/common.inc.php", "/home/<USER>/Documentos/Instalacion", "/home/<USER>/www/phpmyadmin/config.inc.php", "/usr/share/applications/five-or-more.desktop", "/usr/share/applications/filezilla.desktop", "/home/<USER>/.bashrc", "/usr/share/applications/firefox-developer.desktop", "/home/<USER>/.local/share/applications/firefox.desktop", "/home/<USER>/.zshrc", "/home/<USER>/.local/share/applications/firefox", "/home/<USER>/.bash", "/home/<USER>/firefox-developer.desktop"], "find": {"height": 55.0}, "find_in_files": {"height": 127.0, "where_history": [""]}, "find_state": {"case_sensitive": false, "find_history": ["debe ser un númeo", "categor", "estado", "merakibambu", "MERAKI-SOFT-1", "n503vbMTERExvOy0IOdU", "pedido", "idlista", "wc_get_product", "SELECT productos.idproducto, codigo", "277", "update", "actua", "unique", "Error en la importación", "Error en la importacion", "idorgani", "005CA3", "4FCF51", "14e74beb850425e6c7f9e15e00122bb8", "<PERSON><PERSON><PERSON><PERSON> se suma a crono", "Trail Running Tungurahua", "<EMAIL>", "277", "com", "https", "var/www/", "saas/customer/", "https", "a.com", "new", "autojum", "path", ","], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": [], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsyncsaas.php", "semi_transient": false, "settings": {"buffer_size": 11402, "regions": {}, "selection": [[2132, 2132]], "settings": {"bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {"1": [3400, 3401]}, "icon": {"1": ["Packages/BracketHighlighter/icons/curly_bracket.png", "region.purplish"]}, "open": {"1": [2130, 2131]}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content"], "function_name_status_row": 67, "incomplete_sync": null, "remote_loading": false, "synced": false, "syntax": "Packages/PHP/PHP.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 811.0, "zoom_level": 1.0}, "stack_index": 2, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/config.php", "semi_transient": false, "settings": {"buffer_size": 13292, "regions": {}, "selection": [[5245, 5245]], "settings": {"auto_complete": false, "bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {"1": [5278, 5279]}, "icon": {"1": ["Packages/BracketHighlighter/icons/single_quote.png", "region.greenish"]}, "open": {"1": [5134, 5135]}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content"], "function_name_status_row": 135, "incomplete_sync": null, "remote_loading": false, "synced": false, "syntax": "Packages/PHP/PHP.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 2318.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 2, "file": "/home/<USER>/www/woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsynctango.php", "selected": true, "semi_transient": false, "settings": {"buffer_size": 4757, "regions": {}, "selection": [[1792, 1792]], "settings": {"bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {"1": [3010, 3011]}, "icon": {"1": ["Packages/BracketHighlighter/icons/curly_bracket.png", "region.purplish"]}, "open": {"1": [1768, 1769]}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content"], "function_name_status_row": 68, "incomplete_sync": null, "remote_loading": false, "synced": false, "syntax": "Packages/PHP/PHP.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 1026.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 27.0}, "input": {"height": 52.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": false, "output.SFTP": {"height": 0.0}, "output.SublimeLinter": {"height": 0.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "pinned_build_system": "", "project": "woocommerce.sublime-project", "replace": {"height": 50.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["mach", "MEGA/CONFIG/MACHETES.todo"], ["banco", "MEGA/OBSIDIAN/WIKI/Bancos.txt"], ["MUSI", "MEGA/OBSIDIAN/WIKI/MUSICA.txt"], ["tango", "woocommerce/wp-content/plugins/sv-woo-sync-erp/includes/class.svsynctango.php"], ["nodo", "MEGA/PROYECTOS/NODO.todo"], ["confi", "woocommerce/wp-config.php"], ["b", "MEGA/WIKI/Bancos.txt"], ["plu", "MEGA/PROYECTOS/PLUGINS.todo"], ["plugi", "MEGA/PROYECTOS/PLUGINS.todo"], ["sync", "woocommerce/wp-content/plugins/sv-woo-sync-erp/sv-woo-sync-erp.php"], ["zz mu", "MEGA/WIKI/ZZ Music.md"], ["mus", "MEGA/WIKI/MUSICA.txt"], ["sv-wor", "woocommerce/wp-content/plugins/sv-woo-sync-erp/sv-woo-sync-erp.log"], ["depl", "MEGA/PROYECTOS/DEPLOY.todo"], ["saas", "MEGA/PROYECTOS/SAAS.todo"], ["saa", "MEGA/PROYECTOS/SAAS.todo"], ["vir", "MEGA/CONFIG/sites-enabled/virtualhosts.conf"], ["mache", "MEGA/PROYECTOS/MACHETES.todo"], ["woosyn", "woocommerce/wp-content/plugins/sv-woo-sync-erp/sv-woo-sync-erp.php"], ["hac", "MEGA/PROYECTOS/HACER.todo"], ["crono", "MEGA/PROYECTOS/CRONO.todo"], ["configu ", "MEGA/WIKI/Configuración de eventos.sql"], ["ha", "MEGA/PROYECTOS/HACER.todo"], ["wor", "MEGA/WIKI/Wordpress.md"], ["host", "MEGA/CONFIG/hosts"], ["wo", "MEGA/WIKI/Wordpress.md"], ["zs", "MEGA/CONFIG/zshrc"], ["word", "MEGA/WIKI/Wordpress.md"], ["mac", "MEGA/PROYECTOS/MACHETES.todo"], ["funcionescom", "cronometrajeinstantaneo/app/Helpers/FuncionesComunes.php"], ["zshr", "MEGA/CONFIG/zshrc"], ["gitignore", "cronometrajeinstantaneo/.gitignore"], ["rout web", "cronometrajeinstantaneo/routes/web.php"], ["nuevo even", "cronometrajeinstantaneo/public/eventos/nuevo_evento.php"], ["confi even", "cronometrajeinstantaneo/public/eventos/config_evento.php"], ["modi", "MEGA/WIKI/Modificar inscripciones y resultados en Crono.txt"], ["configu", "MEGA/WIKI/Configuración de eventos.sql"], ["SV.TO", "MEGA/PROYECTOS/SV.todo"], ["wordpr", "MEGA/WIKI/Wordpress.md"], [".env", "cronometrajeinstantaneo/.env"], ["hacer", "MEGA/PROYECTOS/HACER.todo"], ["inde", "cronometrajeinstantaneo/public/index.php"], ["deploy", "MEGA/PROYECTOS/DEPLOY.todo"], ["cronome .env", "cronometrajeinstantaneo/.env"], ["route w", "saasargentina/services/informes/vendor/laravel/framework/src/Illuminate/Routing/Route.php"], ["saas.to", "MEGA/PROYECTOS/SAAS.todo"], ["funcion wsfe", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["acc.php", "saasargentina/services/acc/acc.php"], ["fun", "saasargentina/services/acc/funciones_basicas.php"], ["virtu", "MEGA/CONFIG/sites-enabled/virtualhosts.conf"], ["fncion", "saasargentina/services/acc/funciones_basicas.php"], ["defau", "MEGA/CONFIG/sites-enabled/default.conf"], ["funcion ba", "saasargentina/services/acc/funciones_basicas.php"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "~/MEGA/PROYECTOS/saas.sublime-project"], ["crono", "~/MEGA/PROYECTOS/crono.sublime-project"], ["sa", "~/MEGA/PROYECTOS/saas.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": true, "show_tabs": true, "side_bar_visible": true, "side_bar_width": 317.0, "status_bar_visible": true, "template_settings": {}}