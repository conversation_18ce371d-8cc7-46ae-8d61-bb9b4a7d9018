{"auto_complete": {"selected_items": [["strto", "str<PERSON><PERSON>per()"], ["org", "organizaciones"], ["canav-ETAPA", "canav-etapa-2-vivo"], ["get", "getIdParticiptanteByTag"], ["PATH", "PATH_LOGS"], ["back", "background-color"], ["tiene", "tiene_vueltas"], ["largada_individuale", "largadas_individuales"], ["nube", "nube_hibrida"], ["segun", "<PERSON><PERSON><PERSON>delp<PERSON><PERSON>"], ["partic", "participantes"], ["lar", "largadas_individuales"], ["id", "idparticipante"], ["larga", "largada_individual"], ["array", "array_key_exists"], ["font", "font-size"], ["wi", "width"], ["arc", "archivo"], ["archi", "archivo"], ["basena", "basename"], ["base", "basename_exploded"], ["list", "lista_imagenes"], ["array_key", "array_key_exists()"], ["Partic", "Participantes"], ["key", "keypress"], ["reen", "reenviarBtn"], ["reenviar", "reenviar-cantidad"], ["backg", "background-color"], ["usar", "minuto_cerrado"], ["temp", "temp_milisegundos"], ["inputHo", "inputHora4"], ["ses", "session_unset"], ["session_", "session_destroy"], ["session", "session_unset"], ["cron<PERSON><PERSON>", "cronopic6"], ["_se", "$_SESSION"], ["mail", "mail_preinscripto"], ["remitn", "remitente_mail"], ["validar", "validarTiempoMuerto"], ["ultimo", "ultimo_corte"], ["nb", "nbsp;"], ["lectu", "lectura2_sql"], ["Re", "Request $request\t⌬ tabnine"], ["men", "mensaje_error\t⌬ tabnine"], ["tex", "text-right\tabc"], ["acredi", "acreditaciones\t(CRONO.todo)"], ["largada", "largada_carrera\tabc"], ["font-e", "font-weight\tproperty"], ["<PERSON><PERSON><PERSON><PERSON>", "getMilliseconds();\t⌬ tabnine"], ["verti", "vertical-align\tproperty"], ["pad", "padding-left\tproperty"], ["font-f", "font-feature-settings\tproperty"], ["font-ma", "font-family\tproperty"], ["repetidos", "repetidos[] = $\t⌬ tabnine"], ["terminado_", "terminados_todos);\t⌬ tabnine"], ["elimi", "eliminarRepetidos\tabc"], ["resul", "resultado\tabc"], ["even", "eventosactivos\t⌬ tabnine"], ["<PERSON><PERSON>", "Nuevo ícono\t⌬ tabnine"], ["andr", "and<PERSON>maiden\t(MACHETES.todo)"], ["targe", "target=\"_blank\" data-preference-id=\"\t⌬ tabnine"], ["saas", "saasargentina\tabc"]]}, "buffers": [{"file": "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md", "redo_stack": [[8, 1, "mde_indent_list_item", null, "AQAAALACAAAAAAAAsAIAAAAAAAAEAAAAICAgIA", "BgAAAAAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAADwvwAAAAABAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAPC/AAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA8L8AAAAAAQAAALQCAAAAAAAAFQMAAAAAAAAAAAAAAADwvw"]], "settings": {"buffer_size": 7455, "encoding": "UTF-8", "line_ending": "Unix"}}, {"file": "/home/<USER>/.config/sublime-text-3/Packages/User/SFTP.errors.log", "settings": {"buffer_size": 573, "line_ending": "Unix"}}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["ins", "Package Control: Install Package"], ["remo", "Package Control: Remove Package"], ["json", "Pretty JSON: Format and Sort JSON"], ["sav", "File: Save All"], ["save", "File: Save All"], ["spaces", "Indentation: Convert to Spaces"], ["convert", "Indentation: Convert to Spaces"], ["laravel vie", "Laravel Docs: Views"], ["menu", "View: <PERSON><PERSON>"], ["insta", "Package Control: Install Package"], ["inst", "Package Control: Install Package"], ["install", "Package Control: Install Package"], ["men", "View: <PERSON><PERSON>"], ["key", "Preferences: Key Bindings"], ["the", "UI: Select Theme"], ["proje", "Project: Edit"], ["ui co", "UI: Select Color Scheme"], ["ui th", "UI: Select Theme"], ["remor", "Package Control: Remove Repository"], ["them", "UI: Select Theme"], ["sett", "Preferences: Settings"], ["comm", "Toggle Comment"], ["togg", "Toggle Comment"], ["instal", "Package Control: Install Package"], ["ui col", "UI: Select Color Scheme"], ["remove", "Package Control: Remove Package"], ["ui", "UI: Select Theme"], ["color", "UI: Select Color Scheme"], ["color the", "UI: Select Color Scheme"], ["theme", "UI: Select Theme"], ["sideb", "View: Toggle Open Files in Side Bar"], ["settin", "Preferences: Settings"], ["packa", "Install Package Control"], ["pref", "Preferences: Settings"]], "width": 0.0}, "console": {"height": 141.0, "history": ["cd", "ls", "art"]}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/home/<USER>/www/cronometrajeinstantaneo/admin", "/home/<USER>/www/cronometrajeinstantaneo/admin/app", "/home/<USER>/www/cronometrajeinstantaneo/admin/bootstrap", "/home/<USER>/www/cronometrajeinstantaneo/admin/config", "/home/<USER>/www/cronometrajeinstantaneo/app"], "file_history": ["/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/mujeres.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/hombres.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/DEV.md", "/home/<USER>/www/cronometrajeinstantaneo/app/www/js/crono.js", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/api/mod.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/api/baja.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/api/lectura.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Http/Controllers/LecturasController.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/index.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/artisan", "/home/<USER>/www/cronometrajeinstantaneo/admin/routes/api.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/ajax.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/crono_largadas.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/resources/views/streaming.blade.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/vendor/livewire/livewire/src/HydrationMiddleware/PerformActionCalls.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Http/Controllers/StreamingController.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/routes/web.php", "/home/<USER>/www/cronometrajeinstantaneo/app/config.xml", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/generales.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md", "/home/<USER>/MEGA/OBSIDIAN/HACER.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/etapas.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/inscripciones/index.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Modificar inscripciones y resultados en Crono.txt.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/ticket.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/encabezado.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/consulta.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/index.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/HARDWARE.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/memory.memory.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/config_controles.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/participantes.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Wordpress.md", "/home/<USER>/MEGA/CONFIG/MACHETES.todo", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/equipo_manual.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/resultados/informes/extras.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/parti_manual.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_07_28_144123_create_participantes_table.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Revisar numeración de caes.txt.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Imports/ParticipantesImport.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/sesion.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/config_datos.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/encabezado.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Http/Controllers/AdminController.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/inscripciones/inscripciones.js", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_12_11_181621_add_tipo_nombre_eventos.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Http/Controllers/DatosController.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/config_categorias.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_07_28_144123_create_organizaciones_table.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_12_04_192953_add_estado_eventos.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Models/Datos.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_12_11_180052_create_eventos_inscripciones_table.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_08_09_232859_modify_organizaciones_table.php", "/home/<USER>/MEGA/PROYECTOS/crono.sublime-project", "/home/<USER>/www/cronometrajeinstantaneo/admin/resources/views/historial.blade.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Http/Controllers/HistorialController.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/login.php", "/home/<USER>/www/cronometrajeinstantaneo/www/login.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/resources/views/old_admin/fin.blade.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/resources/views/inscripciones/fin.blade.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/historial.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/resources/views/old_admin/head.blade.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/vendor/symfony/routing/Route.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/seeders/TablasDeportesPaises.php", "/home/<USER>/www/cronometrajeinstantaneo/www/api/lectura.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/api/anube.php", "/home/<USER>/MEGA/OBSIDIAN/DATOS/Bancos.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/inversor.memory.md", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Anube.txt.md", "/home/<USER>/Escritorio/DOMINGO/participantes_brut.sql", "/home/<USER>/Escritorio/CANAV/lecturas_antes_borrar.sql", "/home/<USER>/www/cronometrajeinstantaneo/www/admin/resultados/index.php", "/home/<USER>/Escritorio/paises", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_12_04_201107_add_redes.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/database/migrations/2021_12_04_193204_tablas_deportes_paises.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Models/Eventos.php", "/home/<USER>/www/cronometrajeinstantaneo/www/admin/api/anube.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/old-includes/funciones.php", "/home/<USER>/MEGA/CONFIG/zshrc", "/home/<USER>/MEGA/CONFIG/acc", "/home/<USER>/www/cronometrajeinstantaneo/app/www/js/app.js", "/home/<USER>/www/cronometrajeinstantaneo/app/www/index.html", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/crono_apps.php", "/home/<USER>/MEGA/OBSIDIAN/FRAMEWORKS/Phonegap.md", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Helpers/FuncionesComunes.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Imports/LecturasImport.php", "/home/<USER>/www/cronometrajeinstantaneo/app/www/js/hardwares/reader1.js", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/config_pagar.php", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/salud.memory.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/datos.memory.md", "/home/<USER>/MEGA/OBSIDIAN/ROADS/SV/NODO.md", "/home/<USER>/MEGA/OBSIDIAN/ROADS/SAAS/SAAS.md", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Letsencrypt.txt.md", "/home/<USER>/CRONO/Dev/crono_migrate/cronometrajeinstantaneo_data.sql", "/home/<USER>/CRONO/Dev/crono_migrate/seed_cronometrajeinstantaneo.sql", "/home/<USER>/CRONO/Nube/nube_sync_down/cronometrajeinstantaneo_redbull_489.sql", "/home/<USER>/CRONO/Dev/crono_migrate/lecturas.sql", "/home/<USER>/www/cronometrajeinstantaneo/app/voltbuilder.json", "/home/<USER>/SOPORTE/ut3q_categorias_despues.sql", "/home/<USER>/MEGA/OBSIDIAN/ROADS/NIRVANA/Musica.md", "/tmp/mozilla_andresmaiden0/errorgoogle.txt", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/music.memory.md", "/home/<USER>/Escritorio/ROLLERS/Rollers.txt", "/home/<USER>/www/cronometrajeinstantaneo/admin/node_modules/@tailwindcss/forms/index.html", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/PLAN.md", "/home/<USER>/Escritorio/Rollers.txt", "/home/<USER>/CRONO/RFID/Macsha/Tiempos para Importar/20211113-155627.csv", "/home/<USER>/www/cronometrajeinstantaneo/app/www/js/hardwares/fotocelula1.js", "/tmp/mozilla_andresmaiden0/participantes.sql", "/tmp/mozilla_andresmaiden0/participantes-1.sql", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/eventos/nuevo_evento.php", "/home/<USER>/www/cronometrajeinstantaneo/app/www/js/helpers.js", "/home/<USER>/www/cronometrajeinstantaneo/admin/logs/.gitkeep", "/home/<USER>/www/cronometrajeinstantaneo/admin/logs/.gitignore", "/home/<USER>/www/cronometrajeinstantaneo/admin/logs/api-log_2021-11-13.log", "/home/<USER>/www/cronometrajeinstantaneo/admin/public/api/log.php", "/home/<USER>/www/cronometrajeinstantaneo/admin/cronometrajeinstantaneo.env", "/home/<USER>/www/cronometrajeinstantaneo/admin/app/Models/Lecturas.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/CRONODEV.md", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql", "/home/<USER>/MEGA/WIKI/Configuración de eventos.sql", "/home/<USER>/.cache/.fr-j3s3fS/Workflows/02 PARA_Starter_Kit_v2/START HERE!.md", "/home/<USER>/.cache/.fr-brmYqh/Workflows/02 PARA_Starter_Kit_v2/START HERE!.md", "/home/<USER>/MEGA/PROYECTOS/CRONODEV.md", "/home/<USER>/MEGA/PROYECTOS/CRONODEV.todo", "/home/<USER>/MEGA/PROYECTOS/SAAS.todo"], "find": {"height": 27.0}, "find_in_files": {"height": 127.0, "where_history": ["", "/home/<USER>/MEGA", "/home/<USER>/www/cronometrajeinstantaneo/", "/home/<USER>/www/cronometrajeinstantaneo/eventos", "/home/<USER>/www/cronometrajeinstantaneo/www/", "", "*.css", ""]}, "find_state": {"case_sensitive": true, "find_history": ["460, 530", "largadas_individuales_con_etapas_continuas", "etapas_con_largadas_vueltas", "<PERSON><PERSON> y <PERSON>", "UPDATE", "<EMAIL>", "(", "-01-01'", "false", "$limpio", "css", "El Domingo 19 el usuario ", "El Sábado 18 el usuario", "<EMAIL>", "order", "<EMAIL>", "resultados/", "https", "migrar", "(strtotime", "strtotime", " > 1472688000 // Fecha de implementación de la funcionalidad", "created_at", "fecha insc", "order", "<EMAIL>", "pago_ok", "migrar", "1\\'", "1\\", "1\\'", "1\\']", "\\']", "\\'", "'apellido']", "'nombre']", "texto", "ucwords", "nombre", "libre", "simple", "nombre", "Nombre", "nombre", "datos", "libre", "masculino", "select", "nombre", "estado", "historial", "tiene_mili", "$evento", "evento", "$datos", "$datos_evento", "datos", "<?", "acredi", "Ecuador", "1b894ad31105db1df7b126f06caa5f4f", "surbikepark", "Super Bike Park", "<EMAIL>", "Super Bike Park", "527, 528, 529", "SELECT", "474", ",", " 5/12/2021", "[10:06 a. m., 5/12", ",", "$\\303\\263", "$\\303\\255", "$\\303\\201", "$\\303\\261", "$\\303\\272", "$\\303\\251", ".png", "$\\303\\241", "'", "-rwxr-xr-x  1 and<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", " 437, 1766", ",", "<PERSON><PERSON>   ", "527, 528, 529", "    <PERSON><PERSON>", ",", "133173", "<PERSON><PERSON>   ", ",", "    <PERSON><PERSON>", "Crono Rally San Juan:", "idlectura", "array_sql", "segundos_transcurridos", "$evento['tiempo_vuelta']", "$lecturas", "    <PERSON><PERSON>", "<PERSON><PERSON>   ", "# ASS", "    <PERSON><PERSON>", "ASS", "(", "catego", ",", "movil", "(3040, 636, 'NE8L', 2910, 'SS2 / Largada', 0, 'largada', 'movil', 0, '2021-10-30 00:38:46', 0, '2021-10-30 03:38:46', '2021-10-30 03:38:46'),\n", "527, 528, 529", "\n-", "-", "Herrera Crono Rally San Juan", ".focus", "<EMAIL>", "paypal", "1b894ad31105db1df7b126f06caa5f4f", "surbikepark", "Super Bike Park", "<EMAIL>", "neuro", "comparacion', 'reloj'", "lecturas2", "lecturas_ar", "lecturas2", "archivad", "archivada", "= 38"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["orden", "ú", "é", "", "lecturas_archivadas", "lecturas", "- [ ]", "- []", "- [x]", "", ";", "this.config.enter == 'esperar", ".esperando", "", "$etapas_participantes", "$etapas", "$etapas_participantes", "", "$idparticipante", "%20", "$this->admin", "archivo", "tipos_archivo", "tipos_archivos", "$tipos_archivos", "", " LIMIT 1;", "exportar", "$this->folder", "confirma", "$this->folder", "span", "    ", "telefono", "equipo_dato", ",", ", 597,", ", 596,", "estilos/", "eventos/estilos/", "estilos/", "$cantidad = $llegadas_ordenadas !== false ? count($llegadas_ordenadas) : 0;", "", "lecturas", "", "js", "css", "", "LIMIT 1;", " ", "-", "masculino", "femenino", ",", "2021-05-23", ".des", "http://cronometrajeinstantaneo.lan"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md", "semi_transient": false, "settings": {"buffer_size": 7455, "regions": {}, "selection": [[688, 785]], "settings": {"auto_complete": false, "bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {}, "icon": {}, "open": {}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content"], "function_name_status_row": 15, "incomplete_sync": null, "mde.match_heading_hashes": false, "remote_loading": false, "synced": false, "syntax": "Packages/MarkdownEditing/syntaxes/Markdown.sublime-syntax", "tab_size": 4, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 1, "stack_multiselect": false, "type": "text"}, {"buffer": 1, "file": "/home/<USER>/.config/sublime-text-3/Packages/User/SFTP.errors.log", "selected": true, "semi_transient": false, "settings": {"buffer_size": 573, "regions": {}, "selection": [[0, 0]], "settings": {"bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {}, "icon": {}, "open": {}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content"], "function_name_status_row": 0, "incomplete_sync": null, "remote_loading": false, "synced": false, "syntax": "Packages/zzz A File Icon zzz/aliases/Plain Text (Log).sublime-syntax", "tab_size": 2, "translate_tabs_to_spaces": true}, "translation.x": 0.0, "translation.y": 0.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 27.0}, "input": {"height": 52.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.SFTP": {"height": 0.0}, "output.SublimeLinter": {"height": 0.0}, "output.find_results": {"height": 0.0}, "pinned_build_system": "", "project": "crono.sublime-project", "replace": {"height": 50.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["configur even", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["hombre", "admin/public/resultados/informes/hombres.php"], ["muje", "admin/public/resultados/informes/mujeres.php"], ["arti", "admin/artisan"], ["index.p", "admin/public/index.php"], ["lecturacontr", "admin/app/Http/Controllers/LecturasController.php"], ["api", "admin/routes/api.php"], ["aja", "admin/public/eventos/ajax.php"], ["CRONO lar", "admin/public/eventos/crono_largadas.php"], ["crono.md", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["crono", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["crono.js", "app/www/js/crono.js"], ["peform action", "admin/vendor/livewire/livewire/src/HydrationMiddleware/PerformActionCalls.php"], ["stremi bla", "admin/resources/views/streaming.blade.php"], ["streamicon", "admin/app/Http/Controllers/StreamingController.php"], ["route we", "admin/routes/web.php"], ["baja", "admin/public/api/baja.php"], ["mod.ph", "admin/public/api/mod.php"], ["lectu", "admin/public/api/lectura.php"], ["Configur de ", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["con", "app/config.xml"], ["dev", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["generales", "admin/public/resultados/informes/generales.php"], ["etapa", "admin/public/resultados/informes/etapas.php"], ["config nube", "MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md"], ["confi even", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["crono dev", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["CRONO DEV MD", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["confi de ev", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["inscrip inde", "admin/public/inscripciones/index.php"], ["modifi", "MEGA/OBSIDIAN/WIKI/Modificar inscripciones y resultados en Crono.txt.md"], ["hac", "MEGA/OBSIDIAN/HACER.md"], ["consulta", "admin/public/resultados/informes/consulta.php"], ["resulta index", "admin/public/resultados/index.php"], ["resulta enca", "admin/public/resultados/encabezado.php"], ["tick", "admin/public/resultados/informes/ticket.php"], ["cro", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["memor", "MEGA/OBSIDIAN/MEMORY/memory.memory.md"], ["hardw", "MEGA/OBSIDIAN/ROADS/CRONO/HARDWARE.md"], ["modi", "MEGA/OBSIDIAN/WIKI/Modificar inscripciones y resultados en Crono.txt.md"], ["inform participan", "admin/public/resultados/informes/participantes.php"], ["config cont", "admin/public/eventos/config_controles.php"], ["resul index", "admin/public/resultados/index.php"], ["crono md", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["mach", "MEGA/CONFIG/MACHETES.todo"], ["result index", "admin/public/resultados/index.php"], ["word", "MEGA/OBSIDIAN/WIKI/Wordpress.md"], ["extras", "admin/public/resultados/informes/extras.php"], ["participanes", "admin/public/resultados/informes/participantes.php"], ["part<PERSON>u", "admin/public/eventos/parti_manual.php"], ["numera", "MEGA/OBSIDIAN/WIKI/Revisar numeración de caes.txt.md"], ["create participante ", "admin/database/migrations/2021_07_28_144123_create_participantes_table.php"], ["inscrip index", "admin/public/inscripciones/index.php"], ["particiim<PERSON>", "admin/app/Imports/ParticipantesImport.php"], ["ajax", "admin/public/eventos/ajax.php"], ["INSCRI", "admin/public/inscripciones/inscripciones.js"], ["configda<PERSON>", "admin/public/eventos/config_datos.php"], ["add tipo nombre", "admin/database/migrations/2021_12_11_181621_add_tipo_nombre_eventos.php"], ["sesion", "admin/public/eventos/sesion.php"], ["confi dato", "admin/public/eventos/config_datos.php"], ["encab", "admin/public/eventos/encabezado.php"], ["admin cont", "admin/app/Http/Controllers/AdminController.php"], ["inscrip i", "admin/public/inscripciones/inscripciones.js"], ["cronomd", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["inscip index", "admin/public/inscripciones/index.php"], ["admin routes api", "admin/routes/api.php"], ["route web", "admin/routes/web.php"], ["datoscontr", "admin/app/Http/Controllers/DatosController.php"], ["confi cate", "admin/public/eventos/config_categorias.php"], ["rout web", "admin/routes/web.php"], ["datos", "admin/app/Models/Datos.php"], ["config datos", "admin/public/eventos/config_datos.php"], ["migration organi", "admin/database/migrations/2021_08_09_232859_modify_organizaciones_table.php"], ["migra<PERSON><PERSON><PERSON><PERSON>", "admin/database/migrations/2021_07_28_144123_create_organizaciones_table.php"], ["dev.md", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["old_admin fin ", "cronometrajeinstantaneo/admin/resources/views/old_admin/fin.blade.php"], ["historial.php", "cronometrajeinstantaneo/admin/public/eventos/historial.php"], ["fin.bla", "cronometrajeinstantaneo/admin/resources/views/inscripciones/fin.blade.php"], ["old admin head blade.php", "cronometrajeinstantaneo/admin/resources/views/old_admin/head.blade.php"], ["historiacontro", "cronometrajeinstantaneo/admin/app/Http/Controllers/HistorialController.php"], ["routes web", "cronometrajeinstantaneo/admin/routes/web.php"], ["route", "cronometrajeinstantaneo/admin/vendor/symfony/routing/Route.php"], ["histor bla", "cronometrajeinstantaneo/admin/resources/views/historial.blade.php"], ["tabla d", "cronometrajeinstantaneo/admin/database/seeders/TablasDeportesPaises.php"], ["lect", "cronometrajeinstantaneo/admin/public/api/lectura.php"], ["mod.php", "cronometrajeinstantaneo/admin/public/api/mod.php"], ["baja.", "cronometrajeinstantaneo/admin/public/api/baja.php"], ["anub", "cronometrajeinstantaneo/admin/public/api/anube.php"], ["tabla r", "cronometrajeinstantaneo/admin/database/seeders/TablasDeportesPaises.php"], ["banc", "MEGA/OBSIDIAN/DATOS/Bancos.md"], ["inver", "MEGA/OBSIDIAN/MEMORY/inversor.memory.md"], ["confi ev", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["lectura", "cronometrajeinstantaneo/www/api/lectura.php"], ["crono dev md", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["anu", "MEGA/OBSIDIAN/WIKI/Anube.txt.md"], ["resul inde", "cronometrajeinstantaneo/admin/public/resultados/index.php"], ["gener", "cronometrajeinstantaneo/admin/public/resultados/informes/generales.php"], ["genera", "cronometrajeinstantaneo/admin/public/resultados/informes/generales.php"], ["confi nub", "MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md"], ["configu", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["evento", "cronometrajeinstantaneo/admin/app/Models/Eventos.php"], ["ANUB", "cronometrajeinstantaneo/www/admin/api/anube.php"], ["CONFIGU", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["lec", "cronometrajeinstantaneo/admin/public/api/lectura.php"], ["RESULTADO informe genera", "cronometrajeinstantaneo/admin/public/resultados/informes/generales.php"], ["conf nube", "MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md"], ["configu ev", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["anube", "cronometrajeinstantaneo/admin/public/api/anube.php"], ["resulta inde", "cronometrajeinstantaneo/admin/public/resultados/index.php"], ["z", "MEGA/CONFIG/zshrc"], ["acc", "MEGA/CONFIG/acc"], ["crono app", "cronometrajeinstantaneo/admin/public/eventos/crono_apps.php"], ["confi", "cronometrajeinstantaneo/app/config.xml"], ["app.js", "cronometrajeinstantaneo/app/www/js/app.js"], ["inde", "cronometrajeinstantaneo/app/www/index.html"], ["resultado ind", "cronometrajeinstantaneo/admin/public/resultados/index.php"], ["config pagar", "cronometrajeinstantaneo/admin/public/eventos/config_pagar.php"], ["configu ", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["salu", "MEGA/OBSIDIAN/MEMORY/salud.memory.md"], ["NODO", "MEGA/OBSIDIAN/ROADS/SV/NODO.md"], ["hardware read", "cronometrajeinstantaneo/app/www/js/hardwares/reader1.js"], ["resultao index", "cronometrajeinstantaneo/admin/public/resultados/index.php"], ["letse", "MEGA/OBSIDIAN/WIKI/Letsencrypt.txt.md"], ["saas.md", "MEGA/OBSIDIAN/ROADS/SAAS/SAAS.md"], ["parti impor", "cronometrajeinstantaneo/admin/app/Imports/ParticipantesImport.php"], ["lectura impor", "cronometrajeinstantaneo/admin/app/Imports/LecturasImport.php"], ["DEV", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["musi", "MEGA/OBSIDIAN/ROADS/NIRVANA/Musica.md"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "~/MEGA/PROYECTOS/saas.sublime-project"], ["sa", "~/MEGA/PROYECTOS/saas.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": true, "show_tabs": true, "side_bar_visible": false, "side_bar_width": 276.0, "status_bar_visible": true, "template_settings": {}}