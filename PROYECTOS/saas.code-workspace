{"folders": [{"path": ".."}, {"path": "../../www/saasargentina"}, {"path": "../../Docker/docker-compose-lamp"}], "settings": {"files.exclude": {}, "CodeGPT.apiKey": "CodeGPT Plus Beta", "CodeGPT.Autocomplete.enabled": true}, "launch": {"version": "0.2.0", "configurations": [{"args": ["--extensionDevelopmentPath=${workspaceFolder}"], "name": "Launch Extension", "outFiles": ["${workspaceFolder}/out/**/*.js"], "preLaunchTask": "npm", "request": "launch", "type": "extensionHost"}]}}