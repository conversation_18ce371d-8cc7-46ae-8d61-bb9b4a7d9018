{
	"folders": [
		{
			"path": "../../www/cronometrajeinstantaneo/admin"
		},
		{
			"path": "../../www/cronometrajeinstantaneo/filament"
		},
		{
			"path": "../../www/cronometrajeinstantaneo/app"
		},
		{
			"path": "../../www/cronometrajeinstantaneo/vivo"
		},
		{
			"path": ".."
		},
		{
			"path": "../../www/cronometrajeinstantaneo/functions"
		},
		{
			"path": "../../www/cronometrajeinstantaneo/code"
		}
	],
	"settings": {
		"gitlab.repositories": {
			"/home/<USER>/www/cronometrajeinstantaneo/admin": {
				"preferredRemoteName": "origin"
			}
		},
		"files.exclude": {
			"**/.git": true,
			"**/.DS_Store": true,
			"**/node_modules": true,
			"**/vendor": true,
			"**/.vscode": true,
			"**/storage": true
	},
	"launch": {
		"version": "0.2.0",
		"configurations": [
		{
			"name": "Listen for Xdebug",
			"type": "php",
			"request": "launch",
			"port": 9003
		},
			{
				"name": "Listen for Xdebug",
				"type": "php",
				"request": "launch",
				"port": 9003
			},
			{
				"name": "Launch currently open script",
				"type": "php",
				"request": "launch",
				"program": "${file}",
				"cwd": "${fileDirname}",
				"port": 0,
				"runtimeArgs": [
					"-dxdebug.start_with_request=yes"
				],
				"env": {
					"XDEBUG_MODE": "debug,develop",
					"XDEBUG_CONFIG": "client_port=${port}"
				}
			},
			{
				"name": "Launch Built-in web server",
				"type": "php",
				"request": "launch",
				"runtimeArgs": [
					"-dxdebug.mode=debug",
					"-dxdebug.start_with_request=yes",
					"-S",
					"localhost:0"
				],
				"program": "",
				"cwd": "${workspaceRoot}",
				"port": 9003,
				"serverReadyAction": {
					"pattern": "Development Server \\(http://localhost:([0-9]+)\\) started",
					"uriFormat": "http://localhost:%s",
					"action": "openExternally"
				}
			}
		]
	}
}