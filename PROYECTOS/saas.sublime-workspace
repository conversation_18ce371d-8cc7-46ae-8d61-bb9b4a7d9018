{"auto_complete": {"selected_items": [["actuali", "actualizar_ultimonumero_presupuesto"], ["parti", "participantes"], ["men", "mensaje_"], ["mens", "mensaje"], ["url", "urldecode〔function〕"], ["an", "antiafip"], ["resul", "resultado_sql"], ["fla", "flag_queue_activo"], ["ir_", "ir_ahora"], ["guarda", "guardar_sql"], ["selle", "seller_sku"], ["estado", "estadocae"], ["venci", "vencimiento"], ["cerrar", "cerrar_mensaje_flotante"], ["esta", "estadocae"], ["mueve", "<PERSON>ue<PERSON><PERSON>"], ["dis", "disabled=\"disabled"], ["pendien", "pendiente"], ["cer", "cerrarpedidoconitems"], ["i18n_des", "i18n_descarga_alicuotas"], ["venta", "ventasxclientes"], ["scrip", "script"], ["muev", "muevestock"], ["pro", "productosxventas"], ["produc", "productosxcombos"], ["listas", "listasxextras"], ["cate", "categorias"], ["cateo", "categorias_cajas_sql))"], ["consulta", "consulta_sql"], ["str_re", "str_replace()"], ["hi", "historial"], ["tabla", "tabla_seleccionada"], ["wh", "where_fechas"], ["fecha", "fecha_hasta"], ["fech", "fecha_desde"], ["tipo", "tipofacturacion"], ["tipove", "tipoventa_pedido"], ["tipoventa", "tipoventa_factura"], ["idtipoventa_", "idtipoventa_pedido"], ["idtipo", "idtipoventa_factura"], ["enlac", "enlace_consulta"], ["traslad", "traslados idd\t⌬ tabnine"], ["targ", "target=\"_blank\"\t⌬ tabnine"], ["j", "json_decode\t⌬ tabnine"], ["nuev", "nuevo_informes"], ["saasP", "saasPersistence->\t⌬ tabnine"], ["for", "foreach\tforeach …"], ["arra", "array_sql($\t⌬ tabnine"], ["_yo", "_yoast_wpseo_metadesc\t⌬ tabnine"], ["posib", "posibles_idcomportamiento_asoc"], ["posibles_", "posibles_idcomportamiento_asoc\tabc"], ["repla", "replace_array\tabc"], ["mul", "multiplicador_12\tabc"], ["idlista_", "idlista_12\tabc"], ["pre", "preciofinal\tabc"], ["repl", "replace_str\tabc"], ["idlis", "idlista\tabc"], ["consul", "consulta_sql\tabc"], ["conta", "contar_sql\tabc"], ["ec", "ec2-user\tabc"], ["SAAS", "SAAS_ID\tabc"], ["anu", "anulado', obscae =\t⌬ tabnine"], ["RECHAZA", "RECHAZADO\t(funciones_wsfe.php)"], ["compl", "completar_cuit\tabc"], ["PHP_", "PHP_EOL\t⌬ tabnine"], ["saas", "saasargentina\tabc"]]}, "buffers": [{"file": "/home/<USER>/Escritorio/OCEANMAN/participantes.sql", "settings": {"buffer_size": 162021, "encoding": "UTF-8", "line_ending": "Unix"}, "undo_stack": []}], "build_system": "", "build_system_choices": [], "build_varint": "", "command_palette": {"height": 0.0, "last_filter": "", "selected_items": [["json", "Pretty JSON: Format JSON"], ["inst", "Package Control: Install Package"], ["remo", "Package Control: Remove Package"], ["instal", "Package Control: Install Package"], ["mark", "Markdown Preview: Preview in Browser"], ["remov", "Package Control: Remove Package"], ["install", "Package Control: Install Package"], ["forma", "Pretty JSON: Format JSON"], ["xml", "Set Syntax: XML"], ["Snippet: ", "Snippet: fn …"], ["men", "View: <PERSON><PERSON>"], ["prefe", "Preferences: Settings"], ["insta", "Package Control: Install Package"], ["menu", "View: <PERSON><PERSON>"], ["tabni", "⌬ tabnine: Disable Current View"], ["remove", "Package Control: Remove Package"], ["subli", "Sublime Merge: File History"], ["markdo pre", "Markdown Preview: Preview in Browser"], ["key", "Preferences: Key Bindings"], ["manual", "DocPHP: Search Manual"], ["manua", "DocPHP: Open Manual Index Page"], ["align", "Preferences: Alignment Key Bindings – <PERSON><PERSON><PERSON>"], ["sho ", "DocPHP: Show Definition"], ["doc man", "DocPHP: Search Manual"], ["proje", "Project: Edit"], ["ui co", "UI: Select Color Scheme"], ["ui th", "UI: Select Theme"], ["ins", "Package Control: Install Package"], ["remor", "Package Control: Remove Repository"], ["them", "UI: Select Theme"], ["sett", "Preferences: Settings"], ["comm", "Toggle Comment"], ["togg", "Toggle Comment"], ["ui col", "UI: Select Color Scheme"], ["ui", "UI: Select Theme"], ["color", "UI: Select Color Scheme"], ["color the", "UI: Select Color Scheme"], ["theme", "UI: Select Theme"], ["sideb", "View: Toggle Open Files in Side Bar"], ["settin", "Preferences: Settings"], ["packa", "Install Package Control"], ["pref", "Preferences: Settings"]], "width": 0.0}, "console": {"height": 0.0, "history": []}, "distraction_free": {"menu_visible": true, "show_minimap": false, "show_open_files": false, "show_tabs": false, "side_bar_visible": false, "status_bar_visible": false}, "expanded_folders": ["/home/<USER>/www/saasargentina", "/home/<USER>/www/saasargentina/services", "/home/<USER>/www/saasargentina/services/acc", "/home/<USER>/www/saasargentina/services/acc/scripts", "/home/<USER>/www/saasargentina/services/app", "/home/<USER>/www/saasargentina/services/app/public", "/home/<USER>/www/saasargentina/services/app/public/flotantes", "/home/<USER>/www/saasargentina/services/app/public/migrations", "/home/<USER>/www/saasargentina/services/informes", "/home/<USER>/www/saasargentina/services/informes/app", "/home/<USER>/www/saasargentina/services/informes/app/Http", "/home/<USER>/www/saasargentina/services/informes/app/Http/Controllers", "/home/<USER>/www/saasargentina/services/informes/app/Http/Requests", "/home/<USER>/www/saasargentina/services/scripts", "/home/<USER>/www/saasargentina/services/scripts/crontab", "/home/<USER>/MEGA", "/home/<USER>/MEGA/PROYECTOS"], "file_history": ["/home/<USER>/Escritorio/OCEANMAN/TYP7-2024-02-24.json", "/home/<USER>/www/simply/comments/feed/index.xml", "/home/<USER>/Escritorio/SARR/UCI DH VIERNES/viernes cam 1-20230505T185635Z-001/viernes cam 1/C2495M01.XML", "/home/<USER>/Descargas/Oceanman Neuquén Argentina 2023 - Listado de participantes con datos extras.xls", "/home/<USER>/Descargas/dexie-export.json", "/home/<USER>/Descargas/Oceanman Neuquén Argentina 2023 - Listado para la aseguradora.xls", "/home/<USER>/Descargas/Ushuaia Trail Race Edición Diciembre - Resultados generales.xls", "/home/<USER>/.config/obs-studio/basic/profiles/Sin Título/service.json", "/home/<USER>/Escritorio/Mundial/Propuesta.md", "/home/<USER>/Descargas/XML y XSD Factura/factura_V2.1.0.xsd", "/home/<USER>/Descargas/XML y XSD Factura/factura_V2.1.0.xml", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_874_script_2.php", "/home/<USER>/www/cronometrajeinstantaneo/app/config.xml", "/home/<USER>/.cache/.fr-Gl0AbT/app/config.xml", "/home/<USER>/www/cronometrajeinstantaneo/admin/diff.diff", "/home/<USER>/Escritorio/Necochea.txt", "/home/<USER>/Escritorio/SARR/INSCRIPTOS SARR 2022.xlsx", "/home/<USER>/Escritorio/acceso_anterior.log", "/home/<USER>/Escritorio/acceso_posterior.log", "/home/<USER>/Descargas/Preparatorio U.T.R 2022 - Listado para la aseguradora.xls", "/home/<USER>/.cache/.fr-CcYa46/install_profile.json", "/home/<USER>/.cache/.fr-3fm79W/install_profile.json", "/home/<USER>/MEGA/OBSIDIAN/ROADS/NIRVANA/Musica.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/music.memory.md", "/home/<USER>/MEGA/OBSIDIAN/ROADS/SAAS/SAAS.md", "/home/<USER>/www/saasargentina/services/acc/defines.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md", "/home/<USER>/CRONO/Dev/crono_migrate/eventos_sin_lecturas.sql", "/home/<USER>/SOPORTE/api-v0_1_2022-01-17.log", "/home/<USER>/MEGA/CONFIG/zshrc", "/home/<USER>/SOPORTE/2022-01-07.log", "/home/<USER>/MEGA/OBSIDIAN/ROADS/CRONO/DEV.md", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_modelo.php", "/home/<USER>/www/saasargentina/services/informes/app/Http/routes.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md", "/home/<USER>/MEGA/OBSIDIAN/ROADS/INVERSOR/Lecciones de inversión.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/linux.memory.md", "/home/<USER>/www/saasargentina/saas-chef-prod/README.md", "/home/<USER>/www/saasargentina/services/login/funciones_login.php", "/home/<USER>/www/saasargentina/services/informes/app/Http/Controllers/ReportController.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md", "/home/<USER>/MEGA/OBSIDIAN/HACER.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/frases.memory.md", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/nirvana.memory.md", "/home/<USER>/MEGA/OBSIDIAN/ASSETS/Emojis.md", "/home/<USER>/www/saasargentina/services/app/public/migrations/sistemas.sql", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/memory.memory.md", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_6801_script_1.php", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_6801_script_2.php", "/home/<USER>/MEGA/CONFIG/MACHETES.todo", "/home/<USER>/www/saasargentina/services/acc/command.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_wsfe.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_pagos_mp.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_ventanas.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_ml.php", "/home/<USER>/www/saasargentina/services/scripts/crontab/sincronizar.php", "/home/<USER>/www/saasargentina/services/scripts/public/numeracion.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_productos.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/mensajes_listar.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_mod.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_mensajes.php", "/home/<USER>/www/saasargentina/services/app/public/mensajes.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/mensajes_ver.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/mensajes_agrandar.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/tickets_ver.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/tickets_baja.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/tickets_baja.php", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_saas.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/clientes_mod.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/saas_tickets_mod.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/saas_tickets_alta.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20211001121944_1014.sql", "/home/<USER>/Escritorio/log_pago_98.txt", "/home/<USER>/www/saasargentina/services/scripts/crontab/limpiar_eliminadas.php", "/home/<USER>/www/saasargentina/services/app/public/migrations/20211202004309_1491.sql", "/home/<USER>/www/saasargentina/services/informes/config/informe_16.php", "/home/<USER>/www/saasargentina/services/app/public/productos.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_duplicar.php", "/home/<USER>/Escritorio/ROLLERS/Rollers.txt", "/home/<USER>/www/saasargentina/services/acc/funciones_basicas.php", "/home/<USER>/www/saasargentina/services/app/public/flotantes/productos_alta.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_alta.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_baja.php", "/home/<USER>/www/saasargentina/services/acc/scripts/2021-11-29-myisam.php", "/home/<USER>/www/saasargentina/services/scripts/manual/vaciar.php", "/home/<USER>/www/saasargentina/services/acc/scripts/sincae.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Vaciar base de datos.sql.md", "/home/<USER>/www/saasargentina/services/acc/scripts/numeracion.php", "/home/<USER>/www/saasargentina/services/app/public/saas.php", "/home/<USER>/MEGA/OBSIDIAN/ROADS/SV/TODOANGOSTURA.md", "/home/<USER>/MEGA/CONFIG/acc", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_6206_script_1.php", "/home/<USER>/www/saasargentina/services/api/public/v0.2/index.php", "/home/<USER>/www/saasargentina/services/api/public/v0.1/SaasPersistence.php", "/home/<USER>/www/prueba.php", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_4052_script_1.php", "/home/<USER>/MEGA/OBSIDIAN/MEMORY/paises.memory.md", "/home/<USER>/www/saasargentina/services/app/public/procesos/productosxventas_alta.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_mod.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/compras_mod.php", "/home/<USER>/www/saasargentina/services/acc/empresas/logs/api-v0.2/2021-11-15.log", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_comprobantes.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/clientes_ver.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/ventas_ver.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/productosxtraslados_mod.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Eliminar movimientos (limpiar vaciar).sql.md", "/home/<USER>/www/saasargentina/services/api/public/v0.1/index.php", "/home/<USER>/MEGA/OBSIDIAN/WIKI/Revisar numeración de caes.txt.md", "/media/andresmaiden/penfat/Rufus/Test con chips.ror", "/home/<USER>/MEGA/WIKI/Modificar inscripciones y resultados en Crono.txt", "/home/<USER>/MEGA/WIKI/Configuración de eventos.sql", "/home/<USER>/www/saasargentina/services/app/public/ventanas/importar_4.php", "/home/<USER>/MEGA/PROYECTOS/CRONO.todo", "/home/<USER>/MEGA/PROYECTOS/SAAS.todo", "/home/<USER>/MEGA/PROYECTOS/CRONODEV.todo", "/home/<USER>/MEGA/WIKI/Bancos.txt", "/home/<USER>/www/saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_configuraciones.php", "/home/<USER>/www/saasargentina/services/app/public/index.php", "/home/<USER>/MEGA/WIKI/Revisar numeración de caes.txt", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_99_script_2.php", "/home/<USER>/MEGA/PROYECTOS/MACHETES.todo", "/home/<USER>/www/saasargentina/services/scripts/empresas/empresa_161_script_1.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/clientes_buscar.php", "/home/<USER>/www/saasargentina/services/app/public/ventanas/productos_ajustar.php", "/home/<USER>/www/saasargentina/services/app/public/librerias/funciones_fullsearch.php", "/home/<USER>/www/saasargentina/services/scripts/manual/backup.php", "/home/<USER>/www/saasargentina/services/app/public/procesos/clientes_seleccionar.php"], "find": {"height": 55.0}, "find_in_files": {"height": 127.0, "where_history": ["", "informes", "", "*.css", ""]}, "find_state": {"case_sensitive": false, "find_history": ["763282", "999", "subl", "host", "db_host", "Ecuador", "1b894ad31105db1df7b126f06caa5f4f", "surbikepark", "<PERSON><PERSON><PERSON> morel", "Super Bike Park", "<EMAIL>", "|", "1410261", "update", "SELECT ventas.idventa, ventas.numero", "backup", "mysql", "mysqldu", "consulta_sql", "consulta", "rubro", "ultimo", "527, 528, 529", "<EMAIL>", "char<PERSON><PERSON><PERSON><PERSON>@hotmail.com", "<EMAIL>", "253", "$i18n[253]", "253", "mensaje_flotante", "$headers", "='\".$id.\"' LIMIT 1", "estado", "salto", "50", "33", "50", "mensaje", "ticket", "contacto", "estado", "notifica", "mensaje", "$i18n[55]", "estado", "INSERT INTO mens", "head", "MyIsam", "utf8;", "527, 528, 529", "MyISAM", "10231", "InnoDB", "<PERSON>ue<PERSON><PERSON>", "SUM(productosxventas.preciofinal) AS total", "SELECT SUM(productosxventas.preciofinal) AS total, categorias_rubros.nombre FROM productosxvent", "idlista", "precios", "preciox", "nuevo_idrelacion", "$id", "FROM ", "mysqli_fetch_array", "InnoDB", "cheque", "concili", "insertar", "10268", "productos", "mercadopago", "10286", "productos", "saldo", "productos", "cronometrajeinstantaneo", "alias dl", "alis dl", "dl", ". \"", "$key_productos", "idrubro", "(", "dominic", "10231", "duplica", "talle", "Su certificado digital para emitir facturas electrónicas se encuentra vencido", "INSERT IGNORE INTO", "INSERT IGNORE INTO precios SET idproducto", "reglamento", "<EMAIL>", "Ecuador", "1b894ad31105db1df7b126f06caa5f4f", "surbikepark", "Super Bike Park", "<EMAIL>", "FFFFFF", "B29513", "176", "9f3311ce402f8c5334ff87322de6a239", "Super Bike Park Baja", "Super Bike Park", "Super Bike Park Baja", "Baja Concordia", "<EMAIL>", "archivos", "<EMAIL>", "backup", "$boton", "clientes.idcliente, clientes.estado, clientes.nombre, clientes.contacto, clientes.razonsocial", "SELECT clientes.idcliente, clientes.estado", "SELECT clientes.idcliente, clientes.estado, clientes.nombre, clientes.contacto, clientes.razonsocial", "<EMAIL>", "mysqldu", "6149", "rece1", "rece1_va", "myisam", "mYISAM", "YISAM", "ENGINE = MYISAM", "myisam", "myisamm", "utf8", "mysqli_set_charset", "6419", "SELECT idrubro, idrubropadre, nombre, padres", "SELECT idrubro"], "highlight": true, "in_selection": false, "preserve_case": false, "regex": false, "replace_history": ["code", "", "InnoDB", "MyISAM", "ENGINE=InnoDB", "utf8mb4", "CHARSET=utf8mb4 ", "", "charSET=utf8mb4;", "ENGINE=InnoDB", "comprasxp<PERSON>ed<PERSON>", "mensajes_efimeros()", "idcategoria = (SELECT idcategoria FROM categorias WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 587)", ",", "lecturas", "</b>", "newsletters", "Newsletters", "</b>", "INSERT IGNORE", ", iddeposito = 3;", "INSERT INTO stock SET idproducto=", "#com_cliente_No_Responsable_IVA, #com_cliente_IVA_No_Alcanzado {", ") {", "if (", "'quincena'", "extrasx<PERSON><PERSON><PERSON>", "$tabla", "$idventa", "    ", ",", "", ",", "", "*************", "Google Negocio", "Facebook", "Instagram", ",", "j", "", "☐ ", "')) ON DUPLICATE", "idlistaxextra)", "idlistaxextra=(SELECT", "');", "listasxextras", "id<PERSON>oc<PERSON><PERSON>", "SET", ",", "INSERT INTO", " AND meta_key", "9878", "1991", "INSERT INTO"], "reverse": false, "scrollbar_highlights": true, "show_context": true, "use_buffer2": true, "use_gitignore": true, "whole_word": false, "wrap": true}, "groups": [{"sheets": [{"buffer": 0, "file": "/home/<USER>/Escritorio/OCEANMAN/participantes.sql", "selected": true, "semi_transient": false, "settings": {"buffer_size": 162021, "regions": {}, "selection": [[151226, 151462]], "settings": {"bracket_highlighter.busy": false, "bracket_highlighter.locations": {"close": {"1": [151721, 151722]}, "icon": {"1": ["Packages/BracketHighlighter/icons/round_bracket.png", "region.yellowish"]}, "open": {"1": [151462, 151463]}, "unmatched": {}}, "bracket_highlighter.regions": ["bh_regex", "bh_regex_center", "bh_regex_open", "bh_regex_close", "bh_regex_content", "bh_square", "bh_square_center", "bh_square_open", "bh_square_close", "bh_square_content", "bh_double_quote", "bh_double_quote_center", "bh_double_quote_open", "bh_double_quote_close", "bh_double_quote_content", "bh_angle", "bh_angle_center", "bh_angle_open", "bh_angle_close", "bh_angle_content", "bh_c_define", "bh_c_define_center", "bh_c_define_open", "bh_c_define_close", "bh_c_define_content", "bh_curly", "bh_curly_center", "bh_curly_open", "bh_curly_close", "bh_curly_content", "bh_default", "bh_default_center", "bh_default_open", "bh_default_close", "bh_default_content", "bh_round", "bh_round_center", "bh_round_open", "bh_round_close", "bh_round_content", "bh_single_quote", "bh_single_quote_center", "bh_single_quote_open", "bh_single_quote_close", "bh_single_quote_content", "bh_tag", "bh_tag_center", "bh_tag_open", "bh_tag_close", "bh_tag_content", "bh_unmatched", "bh_unmatched_center", "bh_unmatched_open", "bh_unmatched_close", "bh_unmatched_content"], "function_name_status_row": 666, "incomplete_sync": null, "remote_loading": false, "synced": false, "syntax": "Packages/SQL/SQL.sublime-syntax"}, "translation.x": 0.0, "translation.y": 11736.0, "zoom_level": 1.0}, "stack_index": 0, "stack_multiselect": false, "type": "text"}]}], "incremental_find": {"height": 27.0}, "input": {"height": 52.0}, "layout": {"cells": [[0, 0, 1, 1]], "cols": [0.0, 1.0], "rows": [0.0, 1.0]}, "menu_visible": true, "output.SFTP": {"height": 0.0}, "output.SublimeLinter": {"height": 0.0}, "output.find_results": {"height": 0.0}, "output.mdpopups": {"height": 0.0}, "pinned_build_system": "", "project": "saas.sublime-project", "replace": {"height": 50.0}, "save_all_on_build": true, "select_file": {"height": 0.0, "last_filter": "", "selected_items": [["874", "saasargentina/services/scripts/empresas/empresa_874_script_2.php"], ["MUSI", "MEGA/OBSIDIAN/ROADS/NIRVANA/Musica.md"], ["z", "MEGA/CONFIG/zshrc"], ["dev.", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["crono", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["defin", "saasargentina/services/acc/defines.php"], ["nube", "MEGA/OBSIDIAN/WIKI/Configuración manual de nube híbrida.md"], ["linu", "MEGA/OBSIDIAN/MEMORY/linux.memory.md"], ["funcion log", "saasargentina/services/login/funciones_login.php"], ["route", "saasargentina/services/informes/app/Http/routes.php"], ["funcion login", "saasargentina/services/login/funciones_login.php"], ["informe report con", "saasargentina/services/informes/app/Http/Controllers/ReportController.php"], ["funcion mod", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["saas.md", "MEGA/OBSIDIAN/ROADS/SAAS/SAAS.md"], ["confi", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["fra", "MEGA/OBSIDIAN/MEMORY/frases.memory.md"], ["hac", "MEGA/OBSIDIAN/HACER.md"], ["musi", "MEGA/OBSIDIAN/ROADS/NIRVANA/Musica.md"], ["nir", "MEGA/OBSIDIAN/MEMORY/nirvana.memory.md"], ["crono.md", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["emo", "MEGA/OBSIDIAN/ASSETS/Emojis.md"], ["crono.m", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["sis", "saasargentina/services/app/public/migrations/sistemas.sql"], ["empres 6801 2", "saasargentina/services/scripts/empresas/empresa_6801_script_2.php"], ["6801", "saasargentina/services/scripts/empresas/empresa_6801_script_1.php"], ["memor", "MEGA/OBSIDIAN/MEMORY/memory.memory.md"], ["comma", "saasargentina/services/acc/command.php"], ["mach", "MEGA/CONFIG/MACHETES.todo"], ["venta pago", "saasargentina/services/app/public/ventanas/ventas_pagos_mp.php"], ["funcion venta", "saasargentina/services/app/public/librerias/funciones_ventanas.php"], ["m", "MEGA/CONFIG/MACHETES.todo"], ["linux", "MEGA/OBSIDIAN/MEMORY/linux.memory.md"], ["ventas_ml", "saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php"], ["sincron", "saasargentina/services/scripts/crontab/sincronizar.php"], ["funcion ws", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["num", "saasargentina/services/scripts/public/numeracion.php"], ["crono dev", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["config", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["crono md", "MEGA/OBSIDIAN/ROADS/CRONO/CRONO.md"], ["crono dev ", "MEGA/OBSIDIAN/ROADS/CRONO/DEV.md"], ["idio produ", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_productos.php"], ["mensj ver", "saasargentina/services/app/public/ventanas/mensajes_ver.php"], ["mens", "saasargentina/services/app/public/mensajes.php"], ["mensajes ver", "saasargentina/services/app/public/ventanas/mensajes_ver.php"], ["mensaj agra", "saasargentina/services/app/public/procesos/mensajes_agrandar.php"], ["ticket ver", "saasargentina/services/app/public/ventanas/tickets_ver.php"], ["ticket baja", "saasargentina/services/app/public/ventanas/tickets_baja.php"], ["idioma mensaj", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_mensajes.php"], ["mens lis", "saasargentina/services/app/public/ventanas/mensajes_listar.php"], ["saas ticke alta", "saasargentina/services/app/public/ventanas/saas_tickets_alta.php"], ["cliente mod", "saasargentina/services/app/public/ventanas/clientes_mod.php"], ["idio saas", "saasargentina/services/app/public/sistemas/sistema_1_idioma_1/idiomas/idiomas_saas.php"], ["saas tick", "saasargentina/services/app/public/ventanas/saas_tickets_mod.php"], ["configu even", "MEGA/OBSIDIAN/WIKI/Configuración de eventos.sql.md"], ["elimina", "saasargentina/services/scripts/crontab/limpiar_eliminadas.php"], ["1491", "saasargentina/services/app/public/migrations/20211202004309_1491.sql"], ["productos_alta", "saasargentina/services/app/public/ventanas/productos_alta.php"], ["producto alta", "saasargentina/services/app/public/flotantes/productos_alta.php"], ["dupli", "saasargentina/services/app/public/ventanas/productos_duplicar.php"], ["productos.php", "saasargentina/services/app/public/productos.php"], ["produ baja", "saasargentina/services/app/public/ventanas/productos_baja.php"], ["fun mod", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["vacia", "saasargentina/services/scripts/manual/vaciar.php"], ["duplicar", "saasargentina/services/app/public/ventanas/productos_duplicar.php"], ["sist", "saasargentina/services/app/public/migrations/sistemas.sql"], ["saasmd", "MEGA/OBSIDIAN/ROADS/SAAS/SAAS.md"], ["VACI", "MEGA/OBSIDIAN/WIKI/Vaciar base de datos.sql.md"], ["numeracion", "saasargentina/services/scripts/public/numeracion.php"], ["saas", "saasargentina/services/app/public/saas.php"], ["sincae", "saasargentina/services/acc/scripts/sincae.php"], ["acc", "MEGA/CONFIG/acc"], ["ma", "MEGA/CONFIG/MACHETES.todo"], ["todo", "MEGA/OBSIDIAN/ROADS/SV/TODOANGOSTURA.md"], ["sincr", "saasargentina/services/scripts/crontab/sincronizar.php"], ["api v2 ind", "saasargentina/services/api/public/v0.2/index.php"], ["4", "saasargentina/services/scripts/empresas/empresa_4052_script_1.php"], ["6206", "saasargentina/services/scripts/empresas/empresa_6206_script_1.php"], ["sincro", "saasargentina/services/scripts/crontab/sincronizar.php"], ["pais", "MEGA/OBSIDIAN/MEMORY/paises.memory.md"], ["api v0.2", "saasargentina/services/api/public/v0.2/index.php"], ["compras mod", "saasargentina/services/app/public/ventanas/compras_mod.php"], ["venta mod", "saasargentina/services/app/public/ventanas/ventas_mod.php"], ["cliente ver", "saasargentina/services/app/public/ventanas/clientes_ver.php"], ["vent ver", "saasargentina/services/app/public/ventanas/ventas_ver.php"], ["productosxven a", "saasargentina/services/app/public/procesos/productosxventas_alta.php"], ["produc", "saasargentina/services/app/public/procesos/productosxtraslados_mod.php"], ["api v0.2 in", "saasargentina/services/api/public/v0.2/index.php"], ["sincroni", "saasargentina/services/scripts/crontab/sincronizar.php"], ["4052", "saasargentina/services/scripts/empresas/empresa_4052_script_1.php"], ["siste", "saasargentina/services/app/public/migrations/sistemas.sql"], ["paise", "MEGA/OBSIDIAN/MEMORY/paises.memory.md"], ["limpiar", "MEGA/OBSIDIAN/WIKI/Eliminar movimientos (limpiar vaciar).sql.md"], ["numera", "MEGA/OBSIDIAN/WIKI/Revisar numeración de caes.txt.md"], ["api v0.1 inde", "saasargentina/services/api/public/v0.1/index.php"], ["modif", "MEGA/WIKI/Modificar inscripciones y resultados en Crono.txt"], ["cronode", "MEGA/PROYECTOS/CRONODEV.todo"], ["ban", "MEGA/WIKI/Bancos.txt"], ["nume", "MEGA/WIKI/Revisar numeración de caes.txt"], ["saas.", "MEGA/PROYECTOS/SAAS.todo"], ["modifi", "MEGA/WIKI/Modificar inscripciones y resultados en Crono.txt"], ["banco", "MEGA/WIKI/Bancos.txt"], ["cronodev", "MEGA/PROYECTOS/CRONODEV.todo"], ["configu", "MEGA/WIKI/Configuración de eventos.sql"], ["ventas ml", "saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php"], ["scrip 99", "saasargentina/services/scripts/empresas/empresa_99_script_2.php"], ["scrip 161", "saasargentina/services/scripts/empresas/empresa_161_script_1.php"], ["comm", "saasargentina/services/acc/command.php"], ["backup", "saasargentina/services/scripts/manual/backup.php"], ["cliente sele", "saasargentina/services/app/public/procesos/clientes_seleccionar.php"], ["productos aj", "saasargentina/services/app/public/ventanas/productos_ajustar.php"], ["antiafip", "saasargentina/services/scripts/crontab/antiafip.php"], ["cliente buscar", "saasargentina/services/app/public/procesos/clientes_buscar.php"], ["cliente bus", "saasargentina/services/app/public/ventanas/clientes_buscar.php"], ["full", "saasargentina/services/acc/scripts/fullsearch.php"], ["saa", "MEGA/PROYECTOS/SAAS.todo"], ["venta ml", "saasargentina/services/app/public/ventanas/ventas_pedidos_ml.php"], ["ventas mod", "saasargentina/services/app/public/ventanas/ventas_mod.php"], ["funcion wsf", "saasargentina/services/app/public/librerias/funciones_wsfe.php"], ["importa 2", "saasargentina/services/app/public/ventanas/importar_2.php"], ["impor", "saasargentina/services/app/public/ventanas/importar_1.php"], ["login funio", "saasargentina/services/login/funciones_login.php"], ["funcion mode", "saasargentina/services/app/public/librerias/funciones_modelo.php"], ["scrip 6149", "saasargentina/services/scripts/empresas/empresa_6149_script_1.php"], ["cronod", "MEGA/PROYECTOS/CRONODEV.todo"], ["fullsear", "saasargentina/services/acc/scripts/fullsearch.php"], ["sa", "MEGA/PROYECTOS/SAAS.todo"], ["importa 3", "saasargentina/services/app/public/ventanas/importar_3.php"], ["importar 4", "saasargentina/services/app/public/ventanas/importar_4.php"]], "width": 0.0}, "select_project": {"height": 500.0, "last_filter": "", "selected_items": [["", "~/MEGA/PROYECTOS/saas.sublime-project"], ["cro", "~/MEGA/PROYECTOS/crono.sublime-project"], ["cron", "~/MEGA/PROYECTOS/crono.sublime-project"], ["woo", "~/MEGA/PROYECTOS/woocommerce.sublime-project"], ["wo", "~/MEGA/PROYECTOS/woocommerce.sublime-project"]], "width": 380.0}, "select_symbol": {"height": 0.0, "last_filter": "", "selected_items": [], "width": 0.0}, "selected_group": 0, "settings": {}, "show_minimap": true, "show_open_files": true, "show_tabs": true, "side_bar_visible": false, "side_bar_width": 297.0, "status_bar_visible": true, "template_settings": {}}