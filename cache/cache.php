#!/usr/bin/php
<?php
error_reporting(E_ALL ^ E_NOTICE);
require __DIR__.'/../cronometrajeinstantaneo.env';

// Si está cacheando otro proceso me voy
if (file_exists(PATH_CACHE.'/cache.block'))
    exit();

// Si no hay nada que cachear me voy
if (!file_exists(PATH_CACHE.'/cache.txt'))
    exit();

// Bloqueo otros procesos de cache
touch(PATH_CACHE.'/cache.block');
$filas = file(PATH_CACHE.'/cache.txt');

$codigos = array_unique($filas);
foreach ($codigos as $codigo) {
    $codigo = trim($codigo);
    file_get_contents(URL.'resultados/'.$codigo.'/generales?cache=1&&utm_source=cron');
}

unlink(PATH_CACHE.'/cache.block');
file_put_contents(PATH_CACHE.'/cache.txt', "");
exit();
