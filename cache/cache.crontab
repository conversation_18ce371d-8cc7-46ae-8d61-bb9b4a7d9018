# CADA 1 MINUTO
* * * * * /var/www/cronometrajeinstantaneo/code/cache/cache.php

# CADA 3 MINUTOS
*/3 * * * * /var/www/cronometrajeinstantaneo/code/cache/cache.php

# CADA 10 segundos
* * * * * /var/www/cronometrajeinstantaneo/code/cache/cache.php
* * * * * ( sleep 10 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 20 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 30 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 40 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 50 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

# CADA 1 segundo
* * * * * ( sleep 00 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 01 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 02 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 03 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 04 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 05 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 06 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 07 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 08 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 09 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

* * * * * ( sleep 10 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 11 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 12 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 13 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 14 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 15 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 16 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 17 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 18 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 19 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

* * * * * ( sleep 20 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 21 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 22 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 23 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 24 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 25 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 26 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 27 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 28 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 29 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

* * * * * ( sleep 30 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 31 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 32 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 33 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 34 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 35 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 36 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 37 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 38 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 39 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

* * * * * ( sleep 40 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 41 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 42 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 43 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 44 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 45 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 46 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 47 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 48 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 49 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )

* * * * * ( sleep 50 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 51 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 52 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 53 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 54 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 55 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 56 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 57 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 58 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
* * * * * ( sleep 59 ; /var/www/cronometrajeinstantaneo/code/cache/cache.php )
