<!DOCTYPE html>
<html>

<head>
  <title>Actualización automática</title>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    .container {
        display: flex;
        align-items: center;
        height: 100vh; /* altura del 100% del viewport */
        margin-left: 15px;
        margin-right: 15px;
    }
    h1 {
        text-align: center;
    }
    .countdown {
        font-weight: bold;
        color: orange;
        animation: countdown 1s linear infinite;
  }

  @keyframes countdown {
    0% {
      opacity: 1;
    }

    100% {
      opacity: 0;
    }
  }
  </style>
</head>

<body>
  <div class="container">
    <h1>Se está procesando otro informe de este evento, espere unos segundos y vuelva a intentar.<br><br>
      Esta página se actualizará automáticamente en<br><span class="countdown">10</span> segundos.</h1>
  </div>

  <script>
    var countdown = document.querySelector('.countdown');
    var counter = 10;

    setInterval(function() {
      counter--;
      countdown.textContent = counter;
      if (counter === 0) {
        location.reload();
      }
    }, 1000);
  </script>
</body>

</html>
