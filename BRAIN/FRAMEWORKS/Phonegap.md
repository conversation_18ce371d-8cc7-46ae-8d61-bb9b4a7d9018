# CLI

## Instalación

```bash
sudo apt install curl nodejs npm gradle
npm install -g phonegap@latest
download from [<https://developer.android.com/studio/>](<https://developer.android.com/studio/>) the [<https://dl.google.com/android/repository/sdk-tools-linux-4333796.zip>](<https://dl.google.com/android/repository/sdk-tools-linux-4333796.zip>) un unzip
check in .zshrc the paths to export android-sdk-linux
sudo add-apt-repository ppa:openjdk-r/ppa
sudo apt-get update
sudo apt-get install openjdk-8-jdk
sdkmanager "platform-tools" "platforms;android-28"
```

[](https://www.taringa.net/posts/hazlo-tu-mismo/19784708/Como-instalar-cordova-phonegap-e-ionic-en-debian-stretch.html)[https://www.taringa.net/posts/hazlo-tu-mismo/19784708/Como-instalar-cordova-phonegap-e-ionic-en-debian-stretch.html](https://www.taringa.net/posts/hazlo-tu-mismo/19784708/Como-instalar-cordova-phonegap-e-ionic-en-debian-stretch.html)

## Commands

```bash
phonegap create myApp --id "org.myapp.sample" --name "appSample"
phonegap platform add android
phonegap platform update android@latest
```

## Build

-   Create the android-distribution.keystore with the command (use the same [keystore_name] an [alias_name]: keytool -genkey -v -keystore [keystore_name].keystore -alias [alias_name] -keyalg RSA -keysize 2048 -validity 10000
-   Convert key keytool -importkeystore -srckeystore [keystore_name].keystore -destkeystore [keystore_name].keystore -deststoretype pkcs12
-   Build for release phonegap build --release android
-   Copy the keystore to the folder platforms/android/app/build/outputs/apk/release or create a symbolic link ln -s /home/<USER>/www/cronometrajeinstantaneo/app/phonegap/android-certs/cronometrajeinstantaneo.keystore platforms/android/app/build/outputs/apk/release/cronometrajeinstantaneo.keystore
-   Sign the apk with the command: jarsigner -verbose -sigalg SHA1withRSA -digestalg SHA1 -keystore <keystore_name> <Unsigned APK file> <Keystore Alias name> -tsa [timestamp.digicert.com](http://timestamp.digicert.com/)
-   Optimize with zipalign: ~/Apps/android-sdk-linux/build-tools/28.0.3/zipalign -v 4 input.apk output.apk
-   Completar todo en PlayStore
-   Probar en beta [](https://play.google.com/apps/testing/com.turismobtc.app)[https://play.google.com/apps/testing/com.turismobtc.app](https://play.google.com/apps/testing/com.turismobtc.app)

# INFO PHONEGAP

-   I've used this to make the certificates on Linux: [](https://patrickshuff.com/generatingsigning-ios-development-keys-on-linux-with-phonegap-build.html)[https://patrickshuff.com/generatingsigning-ios-development-keys-on-linux-with-phonegap-build.html](https://patrickshuff.com/generatingsigning-ios-development-keys-on-linux-with-phonegap-build.html)
-   Compile for ios (trying PhoneGap Build) [](https://wiki.nsbasic.com/Submitting_to_the_iOS_App_Store)[https://wiki.nsbasic.com/Submitting_to_the_iOS_App_Store](https://wiki.nsbasic.com/Submitting_to_the_iOS_App_Store)
-   Try it on browserstack (see what you have to upload)
-   Create Simple Splash screen [](https://www.joshmorony.com/preparing-screenshots-splashscreens-icons-for-app-store-submission/)[https://www.joshmorony.com/preparing-screenshots-splashscreens-icons-for-app-store-submission/](https://www.joshmorony.com/preparing-screenshots-splashscreens-icons-for-app-store-submission/)

## RECUPERAR CERT DE TODOANGOSTURA

```bash
keytool -genkey -v -keystore upload-keystore.jks -keyalg RSA -keysize 2048 -validity 10000 -alias upload
keytool -export -rfc -keystore upload-keystore.jks -alias upload -file upload_certificate.pem
usGdOQZ8dCxiiFS4YZbfN6nRAoa85gSP
```