# Manual de procedimientos para el cronometraje de un Running

## Inscripciones

## Asignación de chips

## Acreditación

- Vamos a usar 2 PCs con 2 lectores RFID USB en modo Acreditación con el código. Si quieren poner una tercera también suma.

- [ ] 2 PCs con Windows 10
- [x] 2 readers RFID USB (Andres lleva y está el de Javi también)
- [ ] Conexión a Internet mínimo de 10Mbps (Sería ideal tener acceso al router por si se cuelga el WIFI)
- [x] Conexión 4G para el caso de que se corte el Internet Wifi (tenemos en nuestros celulares)

## Cronometraje

- [x] UPS, cables, zapatillas y alargues (ya consigui<PERSON> Tatu)
- [x] 1 PC con Windows 10 para el Reader (<PERSON><PERSON>)
- [x] Reader con 8 antenas y cables (todo el equipo de Javi)
- [ ] Caja estanco para el reader (les paso opción A en una foto, pero sino lo podemos poner dentro de una caja de plástico)
- [x] Truss (ya consigui<PERSON>)
- [x] PC para control e impresiones (<PERSON><PERSON> lleva pero si tienen una con la impresora instalada mejor)
- [x] Tablet para el locutor (Andres lleva)
- [ ] Impresora (Tatu ¿podrás conseguir?, sino les pasamos mi tablet para la entrega de premios)

## Kiosco

Ya tengo funcionando el kiosco, lo voy a mejorar, pero ya lo tenemos. El tema es que va con una PC con Windows, por lo que hay que tener una compu en algún lugar para que consulten los tiempos. Yo le hago una imágen de fondo explicativa y la dejamos funcionando sola. Aunque sería bueno que esté en algún lugar donde no la puedan romper, ni robar, o cerca de alguien que pueda ayudar a los corredores por cualquier cosa.

- [ ] Terminar el kiosco (Andres lo está haciendo)
- [ ] PC con Windows 10 (con algo para protegerla)
- [ ] Reader RFID USB (el de Javi)

## PCs

Todavía no pude hacer funcionar la pistola, tengo un plan B, pero voy a seguir intentando. Tenemos que tener en cuenta una reunión con las 4 personas que las van a usar (3 en puestos de control en montaña y uno en pre-llegada)
