# 📦 ROADS > SAAS > GROWTH

## ORDENAR

- Arca se cae, tu negocio sigue


## INTRODUCCIÓN

Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.

Principalmente se me ocurren las siguiente:

- *Plan de integraciones*, apalancándonos con usuarios de otros sistemas
- *Plan SEO para AI desde FAQs*: mejorando nuestro posicionamiento orgánico orientado a AI
- *Plan de micro nicho*: generando landings y quizás opciones del sistema específicas para algunos rubros
- *Plan de expansión offline*: probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)

Para estos planes van un posible escribir un resúmen de objetivos, métricas y tareas.futuro. Para tener una comparativa, también hay que plasmar algunas estadísticas de hoy. Sería ideal preparar un informe automático con esta información.


## PLAN SEO PARA AI

La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un sistema parecido al de Google para indexar la información de sitios web durante el entrenamiento del LLM, pero hay un interés particular en lo que es preguntas y respuestas. Es por esto que se le llama *Optimización de Motores de Respuestas*, o AEO (Answer Engine Optimization). Nosotros tenemos un listado de FAQs que además de para poder mejorar la ayuda, nos puede servir para este propósito.

Además si tenemos la información ordenada de esta forma, es mucho más fácil para entrenar un agente que haga de soporte con un RAG.

*Puntualmente los objetivos son:*

- Tener una herramienta completa y cómoda para poder gestionar esta información dentro del sistema sin programar.
- Poder generar una web con esa información para que los usuarios puedan ver esa ayuda de forma ordenada.
- Que Gilda pueda compartir enlaces por whatsapp a una pregunta específica de esta ayuda.
- Poder mostrar esa información en ventanas flotantes de nuestro sistema.
- Poder utilizar toda esa información para posicionamiento orgánico en SEO/AEO.
- Poder utilizar esa información para entrenar un Agente de AI RAG para atención a usuarios.

*Tareas a realizar:*

- Agregar campos a nuestra tabla de preguntas frecuentes en el issue [FAQS Agregar campos](https://gitlab.com/saasargentina/app/-/issues/2103)
- Agregar mejoras en la ayuda dentro del sistema en el issue [AYUDA Ideas para actualizarla](https://gitlab.com/saasargentina/app/-/issues/1162)
- Agregar un sector en la web con el listado de ayuda ordenado [AYUDA Web pública](https://gitlab.com/saasargentina/app/-/issues/2104)
- Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.
- Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer
- Hacer un checkeo de que tengamos bien configurado todo lo de SEO: https://neilpatel.com/blog/seo-checklist/
- En la ayuda agregar un botón de "Simplifica tu gestión con SaaS" llevando a una landing. La idea es que si entran desde algún enlace de AI de ver más información y se registren, podemos medirlo.

*Métricas:*

- La principal métrica va a ser que a Gilda le ayude con el soporte, lo que ella sienta.
- Al usar una landing podemos medir quienes se registraron desde ver una ayuda.
- Luego hay que encontrar una forma de medir nuestro posicionamiento antes y después del cambio.


## PLAN DE MICRO NICHO

La idea es poder apuntar en campañas de redes sociales a micro nichos. Vamos a probar contratando a la empresa Beside, pero más allá de eso, podemos hacer un replanteamiento de nuestra propuesta.

Podemos arrancar con un replanteo de marca [MARCA](./MARCA.md)

*Tareas a realizar:*

Antes de comenzar, vamos a analizar los siguientes datos:

- Cuántos hay en FE y cuánto emiten
- Cuántos FE pasaron a Full y viceversa
- Estadísticas de logs completos

Luego la idea es generar distintas versiones del producto, detallado en el issue [VERSIONES Generar primeras](https://gitlab.com/saasargentina/app/-/issues/2105)

- Versión reducida para emprendedores que están comenzando (en realidad es para los ratas o los que les está yendo mal)
- Versión de comercio online: ventajas de integraciones varias, todo online, 24hs, etc.
- Versión para informática
- Versión para talleres y autopartes
- Versión para ferreterías


Hay que hacer páginas para cada uno de los casos de uso primero como landings y luego como subproductos en un menú y con su propia página (Por ej https://gestioo.com/producto/software-para-talleres ). Detallo este trabajo en el issue [VERSIONES Landings](https://gitlab.com/saasargentina/app/-/issues/2106)

Si empieza a funcionar, podemos filtrar entre nuestros clientes y pasarlos. También podemos enviar mails específicos.

*Métricas:*

- Hay que hacer un informe o board para poder ver fácilmente cuales de las versiones están funcionando mejor.


## PLAN DE INTEGRACIONES

Las integraciones nos ayudan porque son clientes que vienen recomendados de otra plataforma, sin que hagamos inversión en publicidad y con una alta probabilidad de que sigan como clientes. Si bien tenemos una fuerte inversión en desarrollo para que esto suceda, creo que vale la pena.

*Tareas a realizar:*

- Tener reuniones con cada una de las integraciones
- Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra
- Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue [LANDINGS Actualizar](https://gitlab.com/saasargentina/app/-/issues/1965)
- Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue [LANDINGS Más Pedidos](https://gitlab.com/saasargentina/app/-/issues/2107)
- Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.
- Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.
- Enviar notificación y/o mail avisando de todas las nuevas integraciones.
- Buscar alguna integración con algún sistema de logística de entregas o envíos.

*Métricas:*

- Tenemos que hacer una métrica sobre todas las landings en general, que nos va a servir para esta y todas las estrategias.


## PLAN DE EXPANSIÓN OFFLINE

La idea es probar con publicidad en medios tradicionales de Neuquén. Además podemos Visitar Centro Pyme, Repartir folletos, Averiguar por radios y cartelería. Podemos empezar por averiguar en https://primamultimedios.com que es uno de los medios más grandes de la zona. Si funciona lo seguimos en otras ciudades.
