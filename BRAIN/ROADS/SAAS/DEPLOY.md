---
# DEPLOY
---

# NEXT DEPLOY

- Avisar a Fernando de idempresa 12541 cuando esté lo de sincronizar productos con idlista y iddeposito (https://gitlab.com/saasargentina/app/-/issues/2147)


## MENSAJES


***************************************************************************************

# DEPLOY SAAS

- [ ] Revisar las clases nuevo-menu
- [ ] Revisar TODOS los nuevos migrations y pasarlos al sistemas.sql
- [ ] Actualizar el archivo version
- [ ] Actualizar saas_hash

- [ ] Tag a la versión y merge donde corresponda
- [ ] Deploy app
- [ ] DEPLOY INFORMES
- [ ] Ejecutar el script de MIGRATIONS

- [ ] Mover los issues en el board

- [ ] Escribir el mensaje de actualización con capturas de pantalla
- [ ] Escribir el historial de actualizaciones (https://app.saasargentina.com/conocimientos.php?a=mod&id=4)
- [ ] Mailing desde dentro de SaaS con capturas de pantalla

- [ ] Informar en chat de desarrollo de la actualización a modo informativo
- [ ] Preparar información para MKT
- [ ] Escribir en mis redes como estuvo el sprint


***************************************************************************************

# MENSAJES

PROD
./command.php prod migrate mensaje
'<p><b>NUEVA ACTUALIZACIÓN</b> </p><p>Las novedades en esta versión son: </p><ul>
</ul><p>Puede ver el <a href="ayudas.php?a=actualizaciones">historial de actualizaciones</a>.</p>'


BETA
./command.php beta migrate mensaje
'<p><b>NUEVA ACTUALIZACIÓN</b> - ¡Gracias por ser parte de las instancias beta de nuestro software!. </p><p>Las novedades en esta versión son: </p><ul>
</ul><p>Puede ver el <a href="ayudas.php?a=actualizaciones">historial de actualizaciones</a>.</p><p><b>Información detallada:</b></p><p><i>Si encuentra algún problema referido a estas nuevas funciones, o tiene sugerencias sobre el sistema, le agradecemos que nos lo informe a <a href="mailto:<EMAIL>"><EMAIL></a></i></p>'


MENSAJE PARA HISTORIAL
<h4>Versión 3.13 (14-11-2024)</h4><ul>
<li></li>
</ul>
<p><i>La versión 3.12 fue agrupada junto con la versión 3.13.</i></p>
<hr>

```mysql
UPDATE saas_99.conocimientos SET texto = CONCAT('<h4>Versión 3.18 (12-12-2024)</h4><ul><li></li></ul>', texto) WHERE idconocimiento = 4;
```
<li>Agregamos información de multimoneda a algunos informes.</li><li>Cambiamos AFIP por ARCA según las nuevas reglamentaciones.</li><li>Arreglamos algunos errores menos surgidos desde multimoneda</li>


***************************************************************************************

# BASH ONLINE

alias l="ls -lha"
alias wsfe="cd /saas/customer/services/acc/empresas/wsfe/"
alias rece="sudo python /saas/customer/services/acc/tools/pyafipws/rece1.py"
alias antiafip="sudo php -f /saas/customer/services/scripts/crontab/antiafip.php"

alias saas="cd /saas/customer/services"
alias acc="cd /saas/customer/services/acc"
alias api="cd /saas/customer/services/api"
alias app="cd /saas/customer/services/app"
alias informes="cd /saas/customer/services/informes"
alias login="cd /saas/customer/services/login"
alias scripts="cd /saas/customer/services/scripts"
alias www="cd /saas/customer/services/www"

function deploy () {
  sudo su
  chmod +x /saas/customer/services/informes/vendor/h4cc/wkhtmltopdf-amd64/bin/wkhtmltopdf-amd64
  chmod +x /saas/customer/services/acc/command.php
  exit
}

