# NODO

Nueva contraseña FTP entregas: 8gUTQ135

## ENTREGAS

DELETE FROM `sv_viajes` WHERE fecha <= '2023-12-01';
DELETE FROM `sv_pedidos` WHERE fecha_pedido <= '2023-12-01';
DELETE FROM `sv_entregas` WHERE pedido_id NOT IN (SELECT id FROM sv_pedidos) OR viaje_id NOT IN (SELECT id FROM sv_viajes)

- Para dar de alta un vehículo sólo hay que dar de alta en la tabla sv_vehiculos
- Para dar de alta un chofer, hay que generar el usuario en WP, fijarse el id y luego dar de alta en la tabla sv_operadores

## ORDENAR

mysql -h localhost -u zonanodo_wordpre -p
sibqfhg1Pglztnx

Productos de prueba:
- Cruz del sur https://nodomateriales.com/tienda/masilla-durlock-x-7-kg/
- Co<PERSON>o https://nodomateriales.com/tienda/conector-columna-88x103x54-mm-barbieri/
- <PERSON><PERSON><PERSON> https://nodomateriales.com/tienda/arena-1-m3-con-bolson-incluido/
- Sin https://nodomateriales.com/tienda/malla-nueva-6-mm-15x15-q-188/



REGLAS
Tienda:
- Sólo se muestran los productos marcados (ya está con plugin propio)
- Usar sólo Cruz del Sur para envíos
- Usar sólo MercadoPago para pagos
- Agregar explicación cuando vas a envíos redireccionado desde buscador (usar get)
- No hace falta clase de envío

Profesional
- Muestran todos los productos
- Mostrar resto de formas de pago menos MP (https://ayudawp.com/desactivar-pasarelas-pago-segun-perfil-usuario/)
- No hace falta clase de envío
- El mensaje es Controlar Stock y cotizar envío


TAREAS
- Configurar envío y pago para Tienda y Profesional
- Actualizar índice de tarjeta
- Mandar listado de productos para re-importar (revisar marcas)
- Se repiten de vuelta los productos en catálogo

- Probar MP
- Probar el Pro del plugin buscador AJAX

- Bariloche y Traful también tienen cotizar
- Si estás registrado no se puede usar mercadopago
- Si van a ser 2 páginas nodomaterialestienda.com
- Los productos que no tienen clase de envío van con un tercero con cotizar, el coreo pasa a cruz del sur
- Este plugin puede servir: https://boluda.com/plugins/restringir-metodos-de-envio-y-de-pago-segun-el-usuario-en-woocommerce/




## AHORA EN MIGRACION


Hola Guille, ya volví de vacaciones y me estoy poniendo al día con varias cosas. Me parece que vos estabas en estos días, así que te dejo esto escrito para cuando lo puedas ver.

Te cuento lo que estuve haciendo:
- Ya está configurado y funcionando el plugin de Cruz Del Sur. O sea que están los 2 funcionando y todos los envíos configurados. Lo único que no pude hacer es que los unifique cuando por ej. hay uno de Correo y uno de Cruz que lo ponga todo en este último. Pero lo tengo pendiente y voy a seguir investigando.
- Le saqué a la página lo del Mundial
- Agregué el boton de "Vaciar Carrito" abajo del de "Actualizar Carrito"
- Activé los carruseles que faltaban y los dejé todos en 3 seg para que se vean siempre que se mueven. No estando logueado hay un sólo producto más vendido por ahora y ese no gira hasta que haya más.
- Si bien te dice arriba ¡Hola Usuario! en el celu no se ve porque se oculta por el espacio de la pantalla, así que le programé un mini código que también se lo agrega en la pantalla principal donde está lo del Área profesional y en esa página también

- Después en tu feedback dice que no se ven bien algunas cosas, pero no puedo encontrarlas, veo todo bien y claro. Algunas cosas están en un gris un poco más claro pero porque no tienen que resaltar, pero las veo perfecto. Si querés mandarme capturas de lo que ves mal o el vídeo que me soles mandar, quizás es en algún lado que no lo encuentro.
- Por el tema del mail al usuario registrado, te cuento que todos los mails se pueden modificar, así que pasame el texto que querés que diga y lo ponemos.


HECHO EN DEMO:

- Cambié todo el home, hay que copiar las configuraciones manualmente
- El slider se puede exportar e importar
- Instalar el plugin modificado desde woo-products-restricted-users-nodo.zip
- Correr SQLs para eliminar lo de VLA y BARI
- En DEFAULT SUB-HEADER OPTIONS poner Yes to all en Hide page subheader?
- Cambiar algunas traducciones (con palabra cotizacion y ver stock)
- Agregar página de Área Profesional
- Importar productos: comas a punto, a utf8, clases de envío como mayúscula inicial, borrar columnas
- Limpie el tema de si era para Angostura con script
- Para sacar IVA, Comentar la última línea en wp-content/themes/kallyas-sv/functions.php
- Actualizar porcentajes con opciones de pago
- Instalar y configurar el plugin Customize My Account for WooCommerce


PRIMERA ENTREGA:
- [x] Migración básica
- [x] Revisar que siga funcionando como antes
- [x] Buscador arriba de todo
- [x] Revisar que se duplica el catálogo
- [x] Instalar pago MP
- [x] Area profesional con todos los productos pero no se pueda comprar (Venta online y NO SE VENDE ONLINE)
  - WooCommerce Products Restricted Users ( https://wordpress.org/plugins/woo-products-restricted-users/ )
  - Página explicando y llevando al login o crear usuario ( https://www.vpsbasics.com/cms/how-to-add-login-and-logout-buttons-to-the-wordpress-sidebar/ )
  - Que marque cuales productos no se pueden comprar online

DISEÑO:
- [x] Agregar Inicio
- [x] Traducir a castellano el login de la barra del menú
- [x] Ocultar blog
- [x] Sacar calculadoras del inicio
- [x] Intercambiar productos destacados con más vendidos
- [x] Sacar catálogo de las tienda (se desactiva desde WooCommerce > PDF Catalog > Buttons > Category PDF)
- [x] Agregar Villa la Angostura en como llegar
- [x] Nuestras marcas con logos y scroll en celu

PRODUCTOS:
- [x] Importar los productos nuevos con el modo profesional
- [x] Importar medidas
- [x] Sacar precio más iva, lista 10

PROFESIONAL:
- [x] Estando logueado cambiar todo lo de area profesional
- [x] Estando logueado aparece descargas
- [x] Agregar Área Profesiona al inicio
- [x] Agregar Mi cuenta, calculadoras y catálogos sólo al area profesional
  - http://sd-1723455-h00011.ferozo.net/categoria/construccion-humeda/?pdf-catalog=882
  - http://sd-1723455-h00011.ferozo.net/categoria/construccion-steel-framing/?pdf-catalog=883
  - http://sd-1723455-h00011.ferozo.net/categoria/construccion-madera/?pdf-catalog=884
  - http://sd-1723455-h00011.ferozo.net/categoria/hormigon-elaborado/?pdf-catalog=889
  - http://sd-1723455-h00011.ferozo.net/categoria/techos/?pdf-catalog=900
  - http://sd-1723455-h00011.ferozo.net/categoria/ferreteria/?pdf-catalog=886

FEEDBACK / ENTREGA 2:
- [x] Los productos destacados se tienen que mover solos son (6).
- [x] El Interés a poner es 12 cuotas 31 % y 18 cuotas 52 %. Esto es ahora 12 y 18, hay que ver como ponerlo en MERCADO PAGO.
- [x] La tarjeta confiable vá sólo en 1 pago
- [x] Hay que poner en la Home Page. El área profesional arriba.
- [x] En la compra no te pide la dirección.
- [x] Luego de botón comprar, me dice: Recibimos la verificación de Stock. Te vamos a contestar por Whatsapp. No se entiende como sigue.
- [x] Al Generar el Pdf en el carrito, ver formato. Sale muy feo y están en Inglés los títulos
- [x] Al entrar al área profesional, te tiene que re-enviar ahí mismo logueado


---
FEEDBACK POST MIGRADO Y PRE-MUNDIAL

- [x] Agregar enlaces al juego del mundial
- [x] "Registrándose"
- [x] Sacar botones del slider
- [x] Enlace a ¿Como llegar? que abra Maps
  - https://www.google.com/maps/place/Zona+Nodo/@-40.7669115,-71.6376829,17z/data=!3m1!4b1!4m5!3m4!1s0x9610bf5cf194451f:0xfdd6a6ec87292ac7!8m2!3d-40.7669115!4d-71.6376829
- [x] Actualizar las marcas
  - https://nodomateriales.com/categoria/construccion-steel-framing/?filter_marca=eternit

  - Barbieri,
  - Victoria Maderas
  - Durlock
  - Eternit
  - weber
  - Cunmalleu

- [x] No se puede agregar al carrito
- [x] Recuperar uso de area profesional

- [x] Eliminar nodo new y dejar bien las dbs
- [x] Revisar la salud del sitio y dejarla ok

- [x] Precargar las fotos de los productos más vendidos
- [x] Estamos llamando a alguna imagen sin https

- [x] Re-activar Jetpack (migrar dominio)
- [x] Activar y reconfigurar Wordfence
- [x] Activar y reconfigurar Yoast SEO
- [x] Revisar todos los plugins
- [x] Actualizar cache

- [ ] Test completo de envío
  - Angostura Gratis
  - Bari, Dina y Traful un 6% del total
  - Agregr 10% a Traful
  - Sino según clase de envío va por Cruz del Sur o Correo Hay un plugin para esconder métodos de envío, tenemos que usar ese
  - https://ayudawp.com/varios-metodos-envio-segun-producto/?ml_subscriber=2079594473972567782&ml_subscriber_hash=g3d0
  - https://ayudawp.com/la-solucion-definitiva-para-los-gastos-de-envio-en-tu-tienda-woocommerce/?ml_subscriber=2085422867603786946&ml_subscriber_hash=e8a1



---
POST MUNDIAL:
- [x] Ocultar productos de área profesional del buscador por ajax
- [x] Revisar variable de sesión (En la compra, mientras pensaba me dejó afuera muy rápido)
- [x] Configurar conversiones en GA4 y Ads (en Configuración > Conversiones)

SI SE PUEDE DESPUÉS:
- [ ] Reflotar remarketing con Prisci
- [ ] Agregar Recomendacion Suba listas de Segmentación por clientes
- [ ] Productos más vendidos con la misma altura
- [ ] Agregar social login y mejorar login con un plugin de popup ( https://wordpress.org/plugins/easy-login-woocommerce/ )



---

## DATOS

WANDERLUST API KEY: rhcStRF9kPdBkAKp7

Usuario: nodonew
Contraseña: 4/yrYD26cT
Dominio Alt: sd-1723455-h00011.ferozo.net
Usuario <EMAIL>
Contraseña: 4/yrYD26cT
Servidor: ftp://nodomateriales.com.ar


ENVÍO POR CORREO
https://shop.wanderlust-webdesign.com/shop/woocommerce-correo-argentino-shipping/
Peso máximo permitido: 25 kilos.
Medidas máximas permitidas: 250 cm sumados el mayor largo, el mayor ancho y el mayor alto (ninguno debe ser superior a 150 cm).

ENVÍO POR CRUZ DEL SUR
https://shop.wanderlust-webdesign.com/shop/woocommerce-cruz-del-sur/


## INFO GENERAL

TOKEN DE FACEBOOK: EAALj2yV3tIwBACVzWJ902v5PmB8AHbEpbUHcEZAJfNKZBkARJg8ixBXaoUNbvYXP5LaS6PoZCZASJZCvbeVgmZA9P2Pf8CLOqahyHUgXmxJ0841IZAGCs4ZApQW1GOYDQqJaQBF3fMBYPTigrS5tUnfgj7BAXDlPueHIsFhjbZAbZAdZA9fii4zvKqP

ACTIVAR PRODUCTOS EN AREA PROFESIONAL:
INSERT INTO wp_postmeta SET
  post_id = 2874,
  meta_key = 'wpru_enable',
  meta_value = 1;

ELIMINAR LA VENTA DE VLA Y BARI:
DELETE FROM `wp_term_relationships` WHERE  `wp_term_relationships`.`term_taxonomy_id` IN (555, 556, 557);

UPDATE wp_postmeta
SET meta_value = REPLACE(meta_value,
    's:15:"pa_en-angostura";a:6:{s:4:"name";s:15:"pa_en-angostura";s:5:"value";s:0:"";s:8:"position";i:1;s:10:"is_visible";i:1;s:12:"is_variation";i:0;s:11:"is_taxonomy";i:1;}', '')
WHERE meta_key = '_product_attributes' AND meta_value LIKE '%s:15:"pa_en-angostura";a:6:{s:4:"name";s:15:"pa_en-angostura";s:5:"value";s:0:"";s:8:"position";i:1;s:10:"is_visible";i:1;s:12:"is_variation";i:0;s:11:"is_taxonomy";i:1;}%';

UPDATE wp_postmeta
SET meta_value = REPLACE(meta_value,
    's:28:"pa_en-bariloche-y-dina-huapi";a:6:{s:4:"name";s:28:"pa_en-bariloche-y-dina-huapi";s:5:"value";s:0:"";s:8:"position";i:2;s:10:"is_visible";i:1;s:12:"is_variation";i:0;s:11:"is_taxonomy";i:1;}', '')
WHERE meta_key = '_product_attributes' AND meta_value LIKE '%s:28:"pa_en-bariloche-y-dina-huapi";a:6:{s:4:"name";s:28:"pa_en-bariloche-y-dina-huapi";s:5:"value";s:0:"";s:8:"position";i:2;s:10:"is_visible";i:1;s:12:"is_variation";i:0;s:11:"is_taxonomy";i:1;}%';

UPDATE wp_postmeta
SET meta_value = REPLACE(meta_value,
    'a:3:{', 'a:1:{')
WHERE meta_key = '_product_attributes' AND meta_value LIKE 'a:3:{%';

UPDATE `wp_postmeta` SET post_title = CONTACT(post_title, ' (SÓLO AREA PROFESIONAL)')
WHERE (`post_id`) IN


## Restricted User

El campo es wp_postmeta.wpru_enable con el post_id cono id del producto


## ANYDESK

ID: ***********


## INFO PARA MKT

REMARKETING:
  - Sesión de rebote:
    - Todas las zonas (medir por zona)
    - Cambiar mensaje según zona
    - Medir con utm
    - Landing con whatsapp y tienda que diga entregamos en ...

  - Sesión sin rebote:
    - Excluir tráfico directo y el que ya compró, para evitar el recurrente
    - Enviar directamente a whatsapp o con landing para whatsapp






Contraseñas varias

<NAME_EMAIL>: 2308Cami

AFIP ASETTEPASSI
Usuario: 20180097563
Clave: Cristian10

Tarjetas Settepassi Alejandro

N°: 4937 7000 0018 2589 | Ven: 07/22 | 793
N°: 5215 9322 4204 0750 | Ven: 01/23 | 605
Mercado Libre
Usuario: <EMAIL>
Contraseña: Belve20
Mercado Libre - Real Trends
Usuario: <EMAIL>
Contraseña: 9gGWZ$j*5Qt9

Integración con Tango
https://github.com/TangoSoftware/ApiTiendas
https://tiendas.axoft.com/api/v2/Aperture/dummy
API KEY: 5fa24943-e71b-4a1c-a3cf-e93d3819975b_11172
d75ae580-3212-45b5-9b9a-84d21158fe07_11311

Contraseñas que usan en el Tango
Zona$532
<EMAIL>
secreta
secreta9
Axoft1988    Usuario sa del SQL Server
hlkBKL5LK4L3KNlkjsadflk    Usuario <EMAIL> del FTP
Usuario Administrador del Servidor: Zona$532
Usuario Administrador en Terminales: zona42UP


## MAILS

*Correo Entrante*
Servidor: l0011761.ferozo.com
Certificado de seguridad SSL: Sí
IMAP Puerto: 993 POP3 Puerto: 995

*Correo Saliente*
Servidor: l0011761.ferozo.com
Certificado de seguridad SSL: Sí
SMTP Puerto: 465

*Correos con nuevas contraseñas*
<EMAIL> | D*YLUBu7gM
<EMAIL> | m0*pjzY7qH
<EMAIL> | P/WO4CK6qW
<EMAIL> | QR/0Q@r4vV
<EMAIL> | mVDU/U53hP
<EMAIL> | *wxp31/2yH
<EMAIL> | r4Safj*0oK
<EMAIL> | Kr*F3@s8uB
<EMAIL> | Xw/geja2fJ
<EMAIL> | K1*YGCT7dZ
<EMAIL> | D@a2Dk19kG
<EMAIL> | @H2ZpdI1fH
<EMAIL> | v@@vv0Z2eO
<EMAIL> | ddE/@Bw8pE
<EMAIL> | HDY*HCg3tT
<EMAIL> | ZkF6@LJ6aJ
<EMAIL> | v/Zpx2t5nY
<EMAIL> | 8dTztU@4rO
