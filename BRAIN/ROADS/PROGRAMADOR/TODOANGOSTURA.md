CONEXIÓN:
backupimagen.com.ar/phpmyadmin
User y pass en conexion.php

CERTIFICADO:
- En el PlayStore, el certificado que funciona es el 356160 androiddeploykey (no 512, ni 2048) en build.phonegap.com para app.todoangostura20.app

NUEVO UPDATE:
* 2hs Armar proyecto y modificar menú en admin
* 2hs Modificar subir imágen y publicidad en modal y mostrar modal en buscador del site
* 1hs Modificar app y re-compilar (hay más hs en Phonegap)

* 2hs Feedback y varios
* 1hs Deploy (hay más en Google Play)
- Test y deploy final
  - Cuando esté el lanzamiento de abierto: test, commit y avisar a Charly y Miguel
- Cambiar texto de app 1.0 como que la dejamos para que sea compatible con celulares antiguos y no la vamos a actualizar, te recomendamos la nueva


PRÓXIMO SPRINT:
- Ver que pasó que se pisó el 2019
- Revisar error al eliminar cosas
  - x ejemplo el almacén (que lo borré hace un mes)
  - y borré columbia, no columbia store en abonados particulares y en la vieja sigue saliendo
- Revisar que parece que no andan los tags
- Fecha para configurar avisos
- Agregar whatsapp
- Eliminar imágenes al eliminar aviso
- GPS que hizo Miguel

- Métricas en la app
- Agregar Google Analytics
- Marketing digital

MIND:
- Pasar los consejos de Mati a algún lado (avisar que hice grilla)
- Quiero volver a probar el reloj hoy
- Niveles de frecuencia cardíaca

accion=alta_aviso&id=1261&idrubroxcontacto=1619&idtipoaviso=2&ano=2022&costo=200&tipodiseno=Repite&costodiseno=
INSERT INTO avisos (idrubroxcontacto, idtipoaviso, fecha, ano, costo, tipodiseno, costodiseno) VALUES ('1619', '2', '2021-11-17', '2022', '200', 'Repite', '')
