VISITE:
✔ Lograr bajar la base de datos @done (19/2/2020 14:57:45)
✔ Contratar plan Cloud 10 con tarjeta de ellos (<NAME_EMAIL>) @done (19/2/2020 14:57:49)
✔ Lograr un hello word en el nuevo server @done (21/2/2020 15:08:35)
✔ Instalar visite sin API @done (21/2/2020 22:49:37)
✔ Instalar API @done (25/2/2020 11:50:32)
https://gitlab.com/backupimagen/api-reservas.git
✔ Pasar todas las fotos por FTP (con wifi) @done (25/2/2020 11:50:34)
✔ Ingresar al back @done (25/2/2020 17:14:08)
✔ No aparecen disponibilidades @done (25/2/2020 17:49:25)
✔ Test @done (25/2/2020 17:49:27)
✔ Configurar mails @done (4/3/2020 10:12:56)
✔ Cambiar DNS @done (4/3/2020 10:12:57)
✔ Hacer funcionar admin2 @done (4/3/2020 10:37:14)
✔ Dar de baja cuenta en Amazon @done (29/3/2020 19:02:37)
✔ Cobrar y recomendaciones futuras @done (29/3/2020 19:02:38)


NUEVO SERVER:

https://ferozo.host/
user: c2311331
pass: ruwi45SOzo


MYSQL:
DB: apivis_reservas
User: apivis_reservas
Pass: luKOno48no@


fBmQV6jHWyfbDHemVfrqKreBtavXM7Tb


URL: http://www.visiteangostura.com/admin/
User: <EMAIL>
Pass: 2UYre4qkURe2papzWtw5mm7x


NUEVAS CUENTAS DE CORREO:


*Correo Entrante*

Servidor: c2311331.ferozo.com
Certificado de seguridad SSL: Sí
IMAP Puerto: 993 POP3 Puerto: 995

*Correo Saliente*

Servidor: c2311331.ferozo.com
Certificado de seguridad SSL: Sí
SMTP Puerto: 465

*Cuentas con nuevas contraseñas*

<EMAIL> / @ew*MoV4iJ
<EMAIL> / BwO/EXW1rP
<EMAIL> / PU8OEx@5xY
<EMAIL> / hWKHxg@9yO
<EMAIL> / OSOqTa@1sJ
<EMAIL> / fx*z*6T2zR
<EMAIL> / GM@GZcZ8jB
<EMAIL> / NHVH9*g8sW
<EMAIL> / k@*g5Jn4fJ
<EMAIL> / 2@6JW@16kH


TRABAJO 04-2024

Se propone hacer una adaptación del sitio visiteangostura.com con cambios para poder integrar un módulo de reservas desde el sitio reservaralojamiento.com . La programación presupuestada incluye:

- [x] Generar un acceso para un único usuario al AMB de contactos que ya tiene el sitio, recuperando la función de CRM que tuvo, pero sin la integración con el motor de reservas que seguirá anulado.
- [x] Ocultar todas las opciones del menú y las páginas que han quedado obsoletas o que no se mostrarán durante el transcurso de este proyecto.
- [x] Agregar un campo adicional en la base de datos y en el AMB de contactos con el id que tendrá cada hotel en el sitio de reservaralojamiento.com más una configuración para habilitar la integración.

- [ ] Modificar la presentación del listado de hoteles y la ventana de información del hotel para sacar botones que no se utilizarán y agregar un nuevo botón de \"Reserva Online\".
- [ ] Desarrollar una integración mediante un iframe con el formulario de reserva de reservaralojamiento.com en cada hotel. Para esta integración se contemplan reuniones con los programadores de reservaralojamiento.com para acordar los detalles técnicos y de diseño.

ALTER TABLE `contactos`  ADD `idreservar` INT NOT NULL  AFTER `pinterest`;


Texto al cliente

Para empezar, modifiqué el accedo a la administración para recuperar las funciones de edición de la página que tenían con el usuario administrador. Recuperé incluso las funciones para poder cambiar las fotos del sitio, las novedades y las noticias destacadas.

Además pude recuperar el acceso de los socios que quedó en el sistema anterior, para que cada uno pueda modificar los datos como lo hacían antes.

Además agregué una contraseña maestra "Con esto puedo modificar todo" para que ustedes puedan acceder a cualquiera de los usuarios que tengan en el sistema, en caso de que se les olvide la contraseña o que quieran modificar datos ustedes.

Luego adapté para el sitio ocultando el menú lateral, los accesos a servicios turísticos, actividades, eventos, vacaciones a medida, clima, buscador y el resto de información no actualizada. El sector de Vacaciones a medida en el home la dejé sin enlaces porque creo que suma a nivel estético.

También adapté el sitio para que se oculten las secciones de novedades y noticias destacadas, para que puedan volver a utilizarlas si lo necesitan, simplemente agregando información, pero que mientras tanto no se vea nada antiguo.

Oculté los Listados Completos de Alojamientos y Restaurantes para que sólo se muestren los que ustedes activen en el AMB de contactos. Si necesitan que se vean todos, me avisan y los vuelvo a activar.

Agregué un campo N° de hotel en reservaralojamiento.com para que puedan vincular cada hotel con su id en el sistema de reservas. Ese N° / id lo tienen que obtener de ellos y lo pueden agregar en el sector de contactos, en la ficha de cada hotel.

En el sector de cada hotel, saqué el mapa (ahora hay que pagar mensualmente a Google para activarlo) y lo reemplacé por un botón a Google Maps que es mucho más práctico.





