## ACTUALIZACIONES

### ACTUALIZACIÓN actualizacion versión 1.6:

- Nueva compatibilidad con Wordpress 5.9
- Sincronización de una segunda lista de precios para ofertas
- Mejoras en la velocidad de sincronización

### ACTUALIZACIÓN versión 1.7:

- Actualización para continuar con la sincronización de imágenes. Por una actualización en la API de SaaS Argentina, las versiones anteriores del plugin no seguiran sincronizando las imágenes, por lo que es necesario actualizar el plugin para que siga funcionando correctamente. El resto de los campos siguen sincronizandose sin problemas.
- Nueva opción para sincronizar categorías.
- Nueva opción para conversión de cotización del dólar.
- Nuevo método de sincronización de bajo consumo para servidores de bajos recursos y/o grandes cantidades de productos.

### ACTUALIZACIÓN versión 1.7.2:

- Nueva compatibilidad con Wordpress 6.4.*
- Corrección de compresión del plugin para Wordpress ^6.4
- Corrección en sincronización de imágenes


---

## DEPLOY

git commit
git push origin
cd ..
rm sv-woo-sync-saas*
zip -r sv-woo-sync-saas-1-7-2_kj4tglk.zip sv-woo-sync-erp -x "*.git/*" "sv-woo-sync-erp/includes/class.svsynctango.php"
scp -i ~/.ssh/andresmaiden2 sv-woo-sync-saas-1-7-2_kj4tglk.zip <EMAIL>:/var/www/svwordpressplugins/public/descargas/sv-woo-sync-saas-1-7-2_kj4tglk.zip

El plugin lo podés descargar desde:
https://svwordpressplugins.com/descargas/sv-woo-sync-saas-1-7-2_kj4tglk.zip
https://svwordpressplugins.com/descargas/sv-woo-sync-tango-1-7-kuGJksJQ.zip


## PARA AGREGAR A PREGUNTAS FRECUENTES

- Como funciona
- No sincroniza
- WP Cron

- Ocurrió el siguiente error en el plugin con token TOKEN<br>Error getting products | URL: https://api.saasargentina.com/v0.2/productos?iue=gijfdsVhRxBCy9u82kYJ&modificados=24&desde=0&cantidad=100 | ERROR: cURL error 28: Resolving timed out after 5000 milliseconds
SOLUCIÓN: Actualizar el plugin que tiene más time out

- Parse error: syntax error, unexpected 'string' (T_STRING), expecting function (T_FUNCTION) or const (T_CONST) in /media2/sadhanagarden/public_html/wp-content/plugins/sv-woo-sync-erp/includes/class.svsyncerp.php on line 11
SOLUCIÓN: El plugin necesita una versión mínima de php 7.4


## PLANTILLAS DE MAILS

### OFRECER

Hola, contamos con una API que permite conectar tiendas y sistemas a su base de datos de SaaS Argentina. Específicamente veo que tienen un sitio web con Wordpress, por lo que pueden desarrollar un plugin o script de sincronización con cualquier programador de su confianza. O también pueden adquirir el plugin que desarrollé por fuera de SaaS Argentina y que atiendo personalmente. Para más información sobre esta segunda opción les paso un sitio web con las explicaciones:

https://svwordpressplugins.com/woo-sync-saas-argentina/

Quedo a su disposición por cualquier consulta.

---

Hola, SaaS cuenta con una API que permite conectar tiendas y sistemas externos, entre ellos Wordpress y WooCommerce. La API tiene un costo mensual adicional del 25% del abono base.

Puntualmente para las tiendas con WooCommerce tengo un plugin que tiene la sincronización de productos y de pedidos. Te paso un enlace donde está todo explicado: https://svwordpressplugins.com/woo-sync-saas-argentina

Quedo a su disposición por cualquier consulta.


## INSTALACIÓN NUEVA VENTA

UPDATE saas_5954.productos SET mostrartienda = 1;
UPDATE saas_5954.productos SET updated_at = NOW();
UPDATE saas_5954.tienda SET tienda_sinstock = 1, API_estado = 1, tienda_estado = 1;
UPDATE saasargentina.empresas SET idsistema = 3 WHERE idempresa = 5954;
SELECT iue FROM saasargentina.empresas WHERE idempresa = 5954;

Ya está configurada tu instancia de SaaS para que se pueda conectar a WooCommerce.

El plugin lo podés descargar desde https://svwordpressplugins.com/descargas/sv-woo-sync-saas-1-7-2_kj4tglk.zip

Una vez instalado y activado, vas a ver en el Menú del Escritorio de Wordpress una nueva opción "SaaS Argentina", entrando ahí tenés la configuración del plugin donde cada opción está explicada. Te va a pedir un token que el tuyo es: XWAxoqcbMS8ZCqZGpvWP

Si querés que instalemos el plugin nosotros no hay ningún problema, sólo tenés que pasarnos cuando tengas la tienda el usuario y contraseña de algún usuario con permiso de administrador en Wordpress y nosotros nos encargamos.

Te adjunto la factura por el Plugin Woo Sync SaaS.


## AYUDAS PARA WHATSAPP

### NO SINCRONIZA

La mayoría de las veces que no se sincronizan los productos, no es un problema del plugin, por lo que te pedimos que revises lo siguiente:

- Identifica uno o más productos que no se sincronizan (anotate los códigos). También sería ideal si hay productos que se están sincronizando correctamente, tener identificado uno o más de ellos. (No hay forma de revisar problemas técnicos sin los detalles específicos 😉).
- Verifica que esos productos estén habilitados en SaaS Argentina, para eso entra a ver el producto y en la sección de *Comportamiento* tienen que tener marcado: *Habilitado*, *Disponible para la venta* y también *Mostrar en la tienda*.
- Verifica que esté habilitada la opción de *Mostrar en tienda productos sin stock* que podes verlo en *Configuraciones > API*.
- Proba una sincronización completa en Wordpress, haciendo un clic sobre el botón *Sincronizar* dentro del sector *Sincronización manual* al fondo de la configuración del plugin.
- Verifica que la sincronización se haya realizado, en la misma configuración del plugin de Wordpress, haciendo un clic sobre *Ver registros* del sector Registros.

Si todo esto está correctamente configurado, mandame por acá el Registro y los códigos de algunos de los productos que no se sincronizan (no es necesario que sean todos) que primero vamos a verificar que se muestren en la API de SaaS Argentina.

### WP CRON

Para que el plugin funcione correctamente, es necesario que el WP Cron esté funcionando y principalmente que se ejecute, ya que si todavía estás haciendo la web o tiene pocas visitas se acumulan los procesos para hacer en background y no se ejecutan.

Antes que nada siempre recomiendo entender bien como funciona el wp-cron y desactivar la función interna de WordPress para que se ejecuta en cada carga de página y configurar una función cron real. Este sitio tiene una buena explicación de cómo hacerlo: https://ayudawp.com/wp-cron

También que no tengas activado en el plugin la opción de *Sincronización de bajo consumo*, porque ahí necesitas varias ejecuciones del wp-cron para que se sincronicen los productos. Por lo menos mientras estés haciendo estas pruebas.

Otra forma de verificar si el WP Cron está funcionando es instalar un plugin que se llama *WP Crontrol* y en la pestaña *Cron Events* vas a ver si hay eventos programados para el WP Cron. Si no hay eventos programados, tenés que verificar que el WP Cron esté habilitado en tu servidor. Para eso podés consultar con tu proveedor de hosting.
