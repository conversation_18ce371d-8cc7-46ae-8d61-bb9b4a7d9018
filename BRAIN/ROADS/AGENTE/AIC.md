# INTRODUCCIÓN

- Todo va a cambiar

## Tipos de comercio

### Comercio tradicional

Cliente -> Charl<PERSON> | Proveedor <-> ERP

### Comercio electrónico ( e-commerce )

Cliente <-> Tienda | ERP <-> Proveedor

### Comercio electrónico con Chatbot

Cliente <-> Cha<PERSON>bot <-> Tienda | ERP <-> Proveedor

### Comercio electrónico con Chatbot y AI

Cliente <-> AI <-> Tienda | ERP <-> AI <-> Proveedor

## Infraestructura

### Comercio tradicional

- ERP instalado en la PC o a los sumo servidor local

### Comercio electrónico ( e-commerce )

- ERP en la nube con modalidad SaaS

### Comercio electrónico con AI

- ERP 100% serverless

## Tipo de cobro

### Comercio tradicional

- Se cobra una licencia y luego actualizaciones

### Comercio electrónico ( e-commerce )

- Se cobra un abono mensual

### Comercio electrónico con AI

- Se cobra por uso o por cierre

## Más temas para ampliar

- Debería ser automático también el soporte, no hace falta vídeos ni manual
- Se van a utilizar cada vez menos las interfaces gráficas, entre el Vision Pro de Apple y Neuralink de Musk, se van a poder hacer muchas cosas sin tocar nada
- Whatsapp va a terminar como Wechat en China, donde se puede hacer de todo, desde pagar impuestos hasta pedir un taxi, por ende también como sistema de gestión
- Yo arranco haciendo una prueba de concepto con las inscripciones de crono y una ayuda para SaaS. Pero creo que hay oportunidad para generar otra empresa sólo con esto.
- Sin usuario y contraseña, se valida el mail o número de whatsapp y listo
- Existe un sistema de embeddings en vector databases para coordinar el contexto y los datos van todos a No-SQL
- Para no quedar pegado a ChatGPT, en el futuro hay que desarrollar conexiones a Brad u otros sistemas de AI. Aunque está claro que el futuro es ofrecer un servicio de AI a través de APIs
- Se puede armar sistemas para distintos mercados, solamente adaptando el modelo de lenguaje y la base de datos para cada uno

## Temas para ver

https://www.cliengo.com/chatbot-gpt