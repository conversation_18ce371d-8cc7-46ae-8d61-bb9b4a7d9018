# 📊 Estadísticas Marketing - Documentación Técnica

## 🏗️ Implementación Actual

### Estructura de Base de Datos

#### Tabla `marketing_tracking`
```sql
CREATE TABLE marketing_tracking (
    idtracking INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    idevento INT UNSIGNED NOT NULL,
    idinscripcion INT UNSIGNED NULL,
    utm_source VARCHAR(100) NULL,
    utm_medium VARCHAR(100) NULL,
    utm_campaign VARCHAR(100) NULL,
    conversion_step ENUM('event_visit', 'form_complete', 'payment_complete') NOT NULL,
    conversion_value DECIMAL(10,2) NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_evento (idevento),
    INDEX idx_inscripcion (idinscripcion),
    INDEX idx_conversion (conversion_step),
    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE,
    FOREIGN KEY (idinscripcion) REFERENCES participantes(idinscripcion) ON DELETE SET NULL
);
```

#### Tabla `marketing_pixels`
```sql
CREATE TABLE marketing_pixels (
    idpixel INT UNSIGNED AUTO_INCREMENT PRIMARY KEY,
    idevento INT UNSIGNED NOT NULL,
    platform ENUM('meta', 'google') NOT NULL,
    pixel_id VARCHAR(100) NOT NULL,
    conversion_step ENUM('event_visit', 'form_complete', 'payment_complete') NOT NULL,
    enabled TINYINT DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    INDEX idx_evento (idevento),
    INDEX idx_platform (platform),
    INDEX idx_step (conversion_step),
    FOREIGN KEY (idevento) REFERENCES eventos(idevento) ON DELETE CASCADE
);
```

### Clases Implementadas

#### MarketingTracking.php
- **Optimización**: Solo se inicializa si `eventos.marketing = 1`
- **Lógica**: Solo guarda registros si hay UTM o es paso de conversión importante
- **Tracking**: Basado en `idinscripcion` (no session_id)

#### MarketingPixels.php
- **Carga dinámica**: Pixels desde base de datos
- **Configuración granular**: Por paso de conversión
- **Código reutilizable**: Meta Pixel en archivo JS externo

## 🎛️ Ventanas de Configuración (Futuras)

### 1. Panel de Configuración de Evento

#### Sección: Marketing
```
☑️ Habilitar tracking de marketing para este evento

📊 Pixels de Conversión
┌─────────────────────────────────────────────────────────┐
│ Plataforma │ ID del Pixel │ Paso de Conversión │ Estado │
├─────────────────────────────────────────────────────────┤
│ Meta       │ 729777596... │ Pago completado    │ ✅     │
│ Google     │ G-XXXXXXXXX  │ Formulario complet │ ✅     │
│ Meta       │ 729777596... │ Visita al evento  │ ❌     │
└─────────────────────────────────────────────────────────┘

[+ Agregar Pixel]
```

#### Campos por Pixel:
- **Plataforma**: Dropdown (Meta, Google)
- **ID del Pixel**: Input text (validación por formato)
- **Paso de Conversión**: Dropdown (event_visit, form_complete, payment_complete)
- **Estado**: Toggle (enabled/disabled)
- **Valor de Conversión**: Checkbox (incluir valor del pago)


## 📈 Reportes y Estadísticas

### 1. Funnel de Conversión

#### Query Principal:
```sql
SELECT
    idevento,
    conversion_step,
    COUNT(*) as total,
    COUNT(DISTINCT idinscripcion) as inscripciones_unicas,
    AVG(conversion_value) as valor_promedio,
    SUM(conversion_value) as valor_total
FROM marketing_tracking
WHERE idevento = ?
GROUP BY conversion_step
ORDER BY
    CASE conversion_step
        WHEN 'event_visit' THEN 1
        WHEN 'form_complete' THEN 2
        WHEN 'payment_complete' THEN 3
    END;
```

#### Métricas a Mostrar:
- **Visitas**: Total de visitas al formulario
- **Formularios Completados**: Tasa de conversión (form/visitas)
- **Pagos Completados**: Tasa de conversión (pagos/form)
- **Valor Total**: Suma de todos los pagos
- **Valor Promedio**: Promedio por inscripción

### 2. Análisis por Canal (UTM)

#### Query:
```sql
SELECT
    utm_source,
    utm_medium,
    utm_campaign,
    COUNT(*) as total_visitas,
    COUNT(CASE WHEN conversion_step = 'form_complete' THEN 1 END) as formularios,
    COUNT(CASE WHEN conversion_step = 'payment_complete' THEN 1 END) as pagos,
    AVG(conversion_value) as valor_promedio,
    SUM(conversion_value) as valor_total
FROM marketing_tracking
WHERE idevento = ? AND utm_source IS NOT NULL
GROUP BY utm_source, utm_medium, utm_campaign
ORDER BY pagos DESC;
```

#### Métricas por Canal:
- **Costo por Inscripción**: (Costo campaña / Inscripciones)
- **ROI**: (Valor total - Costo campaña) / Costo campaña
- **Tasa de Conversión**: Inscripciones / Visitas
- **Valor por Visita**: Valor total / Visitas

### 3. Tendencias Temporales

#### Query:
```sql
SELECT
    DATE(created_at) as fecha,
    conversion_step,
    COUNT(*) as total,
    SUM(conversion_value) as valor_total
FROM marketing_tracking
WHERE idevento = ?
    AND created_at >= DATE_SUB(NOW(), INTERVAL 30 DAY)
GROUP BY DATE(created_at), conversion_step
ORDER BY fecha DESC;
```

#### Gráficos:
- **Línea temporal**: Visitas por día
- **Stacked bars**: Pasos de conversión por día
- **Heatmap**: Actividad por hora del día

### 4. Comparación entre Eventos

#### Query:
```sql
SELECT
    e.nombre as evento,
    e.fecha,
    COUNT(mt.idtracking) as total_tracking,
    COUNT(DISTINCT mt.idinscripcion) as inscripciones,
    AVG(mt.conversion_value) as valor_promedio,
    SUM(mt.conversion_value) as valor_total
FROM eventos e
LEFT JOIN marketing_tracking mt ON e.idevento = mt.idevento
WHERE e.marketing = 1
GROUP BY e.idevento
ORDER BY e.fecha DESC;
```

## 🎨 Interfaz de Usuario

### 1. Dashboard Principal

#### Layout:
```
┌─────────────────────────────────────────────────────────┐
│ 📊 Funnel de Conversión                                │
│ Visitas: 1,234 | Formularios: 456 | Pagos: 123        │
│ Tasa: 37% → 27% | Valor Total: $12,345                │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ 🎯 Canales de Marketing                               │
│ Facebook: 45 inscripciones | Google: 32 | Email: 28   │
│ ROI: 340% | 280% | 420%                               │
└─────────────────────────────────────────────────────────┘

┌─────────────────────────────────────────────────────────┐
│ 📈 Tendencias (Últimos 30 días)                       │
│ [Gráfico de líneas]                                   │
└─────────────────────────────────────────────────────────┘
```

### 2. Reportes Detallados

#### Filtros Disponibles:
- **Rango de fechas**: Date picker
- **Paso de conversión**: Multi-select
- **Canal UTM**: Dropdown con opciones
- **Valor mínimo**: Input numérico

#### Exportación:
- **CSV**: Datos crudos para análisis externo
- **PDF**: Reporte formateado
- **Excel**: Con gráficos y fórmulas

### 3. Configuración de Pixels

#### Interfaz:
```
┌─────────────────────────────────────────────────────────┐
│ ⚙️ Configuración de Pixels                            │
│                                                        │
│ Meta Pixel: [729777596302705] [✅ Habilitado]        │
│ Google Analytics: [G-XXXXXXXXXX] [✅ Habilitado]     │
│                                                        │
│ Eventos a trackear:                                   │
│ ☑️ Visita al formulario                              │
│ ☑️ Formulario completado                             │
│ ☑️ Pago completado                                   │
└─────────────────────────────────────────────────────────┘
```

## 🔧 Implementación Técnica

### 1. Nuevas Clases Necesarias

#### MarketingReports.php
```php
class MarketingReports {
    public function getFunnelData($idevento, $fecha_inicio, $fecha_fin);
    public function getChannelData($idevento, $fecha_inicio, $fecha_fin);
    public function getTrendData($idevento, $dias);
    public function getEventComparison($organizador_id);
}
```

#### MarketingConfig.php
```php
class MarketingConfig {
    public function savePixel($idevento, $platform, $pixel_id, $steps);
    public function getPixels($idevento);
    public function updatePixelStatus($idpixel, $enabled);
    public function deletePixel($idpixel);
}
```

### 2. Endpoints API

#### GET /api/marketing/funnel/{idevento}
```json
{
    "event_visit": {"total": 1234, "rate": 100},
    "form_complete": {"total": 456, "rate": 37},
    "payment_complete": {"total": 123, "rate": 27, "value": 12345}
}
```

#### GET /api/marketing/channels/{idevento}
```json
[
    {
        "utm_source": "facebook",
        "utm_medium": "social",
        "visits": 234,
        "conversions": 45,
        "value": 4500,
        "roi": 340
    }
]
```

### 3. Integración con Frontend

#### JavaScript para Gráficos:
```javascript
// Chart.js para gráficos
const funnelChart = new Chart(ctx, {
    type: 'doughnut',
    data: {
        labels: ['Visitas', 'Formularios', 'Pagos'],
        datasets: [{
            data: [1234, 456, 123],
            backgroundColor: ['#36A2EB', '#FFCE56', '#FF6384']
        }]
    }
});
```

## 🚀 Roadmap de Desarrollo

### Fase 1: Configuración UI
- [ ] Panel de configuración de pixels
- [ ] Validación de IDs de pixels
- [ ] Gestión de estados (enabled/disabled)

### Fase 2: Reportes Básicos
- [ ] Dashboard con métricas principales
- [ ] Funnel de conversión
- [ ] Análisis por canal

### Fase 3: Reportes Avanzados
- [ ] Tendencias temporales
- [ ] Comparación entre eventos
- [ ] Exportación de datos

### Fase 4: Optimizaciones
- [ ] Caching de reportes
- [ ] Filtros avanzados
- [ ] Alertas automáticas

## 📊 KPIs Importantes

### Para Organizadores:
- **Costo por Inscripción**: < $10
- **Tasa de Conversión**: > 25%
- **ROI de Marketing**: > 300%

### Para la Plataforma:
- **Eventos con Marketing**: > 20%
- **Datos de Tracking**: > 10,000 registros/mes
- **Uso de Pixels**: > 80% de eventos activos

---

**Nota**: Esta documentación debe actualizarse conforme se implementen las nuevas funcionalidades.