# 🏅 ROADS > CRONO > KPIs

- Listado con explicación
- Framework para producirlos
- Enlaces para verlos


A nivel Económico las métricas van a ser
- Cantidad de eventos
- Valor promedio por evento
- Inversiones
- Gasto fijo
- Rentabilidad



Crecimiento y Adquisición de Clientes:

Número de nuevos clientes por mes/trimestre (separados por tipo: cronometradores, organizadores, auto-cronometraje)
Tasa de conversión de leads a clientes
Costo de adquisición de cliente (CAC)
Tiempo promedio del ciclo de ventas


Retención y Satisfacción del Cliente:

Tasa de retención de clientes
Tasa de churning (clientes que dejan de usar el servicio)
Net Promoter Score (NPS) o índice de satisfacción del cliente
Número de eventos cronometrados por cliente por año


Rendimiento Financiero:

Ingresos mensuales recurrentes (MRR)
Ingresos por cliente
Margen bruto
Rentabilidad por tipo de servicio (cronometraje, inscripciones, hardware, etc.)


Métricas de Producto y Desarrollo:

Tiempo de desarrollo para nuevas características
Tasa de adopción de nuevas características
Número de errores reportados por mes
Tiempo promedio de resolución de problemas técnicos


Marketing y Ventas:

Tráfico web y tasa de conversión
Engagement en redes sociales y tasa de conversión
ROI de campañas de marketing
Número de leads generados por canal de marketing


Operaciones:

Tiempo promedio de respuesta a consultas de soporte
Tasa de resolución de problemas en el primer contacto
Número de eventos exitosamente cronometrados sin errores
Tiempo promedio de configuración de un nuevo evento


Innovación:

Uso de CronoChat (cuando esté disponible)
Adopción de EventosIA (cuando esté disponible)
Número de micrositios creados gratuitamente y tasa de conversión a servicios pagos


Expansión Internacional:

Número de países con presencia activa
Crecimiento de ingresos por país/región


Eficiencia Operativa:

Ingresos por empleado
Gastos operativos como porcentaje de los ingresos


Hardware:

Ventas de equipos de hardware (fotocélulas, equipos RFID)
Margen de beneficio en ventas de hardware

---

Cant. de organizadores y cronometradores

Cant. de eventos x deporte y x país

Cant. de posteos, notas de blog y newsletter

Retención de usuarios: se trata de calcular el porcentaje de usuarios que se mantiene fiel a la plataforma o producto durante un periodo de tiempo determinado.

Metrica del Costo por Adquisición de Cliente (CAC): mide el costo que representa para una empresa conseguir un nuevo cliente. Esta métricas se utiliza para calcular la relación de retorno de inversión y medir la efectividad de una estrategia de marketing.

Uptime: se trata de una métrica para medir los tiempos de actividad y la fiabilidad de una aplicación web. Mide el porcentaje de tiempo en el que una web está en línea.

Recurrencia: mide el porcentaje de usuarios que han usado la plataforma o servicio SaaS durante un periodo de tiempo determinado.

Facturación Total: esta métrica muestra el ingreso total generado por la empresa, siendo una de las principales métricas de éxito para los negocios SaaS.

Tiempo de uso promedio: nos muestra el promedio de tiempo que los usuarios han dedicado a usar el producto durante un periodo determinado.

NPS: se trata de una métrica que mide el nivel de satisfacción del usuario. Esta métrica es de gran ayuda para saber cómo evoluciona el grado de satisfacción de los usuarios con respecto al producto.