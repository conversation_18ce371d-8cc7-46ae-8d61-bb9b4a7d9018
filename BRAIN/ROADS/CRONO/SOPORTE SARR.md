*******************************************************************************
  SARR 2023
*******************************************************************************

## CAMBIOS PARA F1 KART

Enlace y QR para Inscripciones (se pueden inscribir los pilotos ellos mismos o usarlo ustedes para agregarlos más fácil que en el sistema a mano)
https://cronometrajeinstantaneo.com/inscripciones/f1-kart-san-juan

Enlace y QR para los resultados de la carrera actual
https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/filtros

Enlace el televisor con la carrera actual (se actualiza automáticamente)
https://cronometrajeinstantaneo.com/resultados/f1-kart-san-juan/?actualizar=60

Los enlaces de las carreras terminadas, para que sigan viendo sus tiempos al día siguiente, se envían por mail al mail con el que se registraron


## Copiar de acá
https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/map
https://www.abudhabi.live.worldrallyraidchampionship.com/en/bike/standings


## DESPUÉS

- [ ] Mostrar los parciales en los filtros de etapas nada más
- [ ] Sistema de Inscripciones, sistema de acreditaciones y listado de participantes automáticos
- [ ] Configurar sistema de puntos: Los puntos es al final del evento, se les da puntaje por largar por llegar, por ganar cada etapa (1° , 2° , 3°) y al final por la carrera general.
- [ ] Herramienta para sacar el orden de largada
- [ ] Agregar Foto de los participantes a los resultados

- [ ] Agregar la opción de filtros como selectores
- [ ] Ver si podemos poner parciales como un selector más
- [ ] Agregar íconos en los selectores (y si hay una sola no poner el botón)
- [ ] Importador de penas



*******************************************************************************
  CONFIGURACIONES VARIAS
*******************************************************************************

### CARRERAS

11986 	2338 	MOTOS 	1
11990 	2338 	QUADS 	2
11991 	2338 	UTV 	3
11987 	2338 	AUTOS 	4

### ETAPAS

SELECT idetapa, nombre FROM etapas WHERE idevento = 2338 ORDER BY nombre;

SELECT * FROM `controles` ORDER BY `controles`.`idcontrol` DESC LIMIT 1;

18201

INSERT INTO `controles` (`idcontrol`, `idevento`, `codigo`, `idetapa`, `nombre`, `orden`, `tipo`) VALUES

# H.WP 4
(18221, 2338, 'SA61', 97960, 'H.WP 4', 61, 'parcial'),
(18221, 2338, 'SA61', 97963, 'H.WP 4', 61, 'parcial'),
(18221, 2338, 'SA61', 97966, 'H.WP 4', 61, 'parcial'),
(18221, 2338, 'SA61', 97957, 'H.WP 4', 61, 'parcial'),

# H.WP 10
(18222, 2338, 'SA62', 97960, 'H.WP 10', 62, 'parcial'),
(18222, 2338, 'SA62', 97963, 'H.WP 10', 62, 'parcial'),
(18222, 2338, 'SA62', 97966, 'H.WP 10', 62, 'parcial'),
(18222, 2338, 'SA62', 97957, 'H.WP 10', 62, 'parcial'),

# H.WP 18
(18223, 2338, 'SA63', 97960, 'H.WP 18', 63, 'parcial'),
(18223, 2338, 'SA63', 97963, 'H.WP 18', 63, 'parcial'),
(18223, 2338, 'SA63', 97966, 'H.WP 18', 63, 'parcial'),
(18223, 2338, 'SA63', 97957, 'H.WP 18', 63, 'parcial'),

# H.WP 29
(18224, 2338, 'SA64', 97960, 'H.WP 29', 64, 'parcial'),
(18224, 2338, 'SA64', 97963, 'H.WP 29', 64, 'parcial'),
(18224, 2338, 'SA64', 97966, 'H.WP 29', 64, 'parcial'),
(18224, 2338, 'SA64', 97957, 'H.WP 29', 64, 'parcial'),

# H.ASS1 39
(18225, 2338, 'SA65', 97960, 'H.ASS1 39', 65, 'parcial'),
(18225, 2338, 'SA65', 97963, 'H.ASS1 39', 65, 'parcial'),
(18225, 2338, 'SA65', 97966, 'H.ASS1 39', 65, 'parcial'),
(18225, 2338, 'SA65', 97957, 'H.ASS1 39', 65, 'parcial'),

# H.WP 53
(18226, 2338, 'SA66', 97960, 'H.WP 53', 66, 'parcial'),
(18226, 2338, 'SA66', 97963, 'H.WP 53', 66, 'parcial'),
(18226, 2338, 'SA66', 97966, 'H.WP 53', 66, 'parcial'),
(18226, 2338, 'SA66', 97957, 'H.WP 53', 66, 'parcial'),

# H.WP 59
(18227, 2338, 'SA67', 97960, 'H.WP 59', 67, 'parcial'),
(18227, 2338, 'SA67', 97963, 'H.WP 59', 67, 'parcial'),
(18227, 2338, 'SA67', 97966, 'H.WP 59', 67, 'parcial'),
(18227, 2338, 'SA67', 97957, 'H.WP 59', 67, 'parcial'),

# H.WP 64
(18228, 2338, 'SA68', 97960, 'H.WP 64', 68, 'parcial'),
(18228, 2338, 'SA68', 97963, 'H.WP 64', 68, 'parcial'),
(18228, 2338, 'SA68', 97966, 'H.WP 64', 68, 'parcial'),
(18228, 2338, 'SA68', 97957, 'H.WP 64', 68, 'parcial'),

# H.ASS2 74
(18229, 2338, 'SA69', 97960, 'H.ASS2 74', 69, 'parcial'),
(18229, 2338, 'SA69', 97963, 'H.ASS2 74', 69, 'parcial'),
(18229, 2338, 'SA69', 97966, 'H.ASS2 74', 69, 'parcial'),
(18229, 2338, 'SA69', 97957, 'H.ASS2 74', 69, 'parcial'),

# H.WP 79
(18230, 2338, 'SA6A', 97960, 'H.WP 79', 70, 'parcial'),
(18230, 2338, 'SA6A', 97963, 'H.WP 79', 70, 'parcial'),
(18230, 2338, 'SA6A', 97966, 'H.WP 79', 70, 'parcial'),
(18230, 2338, 'SA6A', 97957, 'H.WP 79', 70, 'parcial'),

# H.WP 82
(18231, 2338, 'SA6B', 97960, 'H.WP 82', 71, 'parcial'),
(18231, 2338, 'SA6B', 97963, 'H.WP 82', 71, 'parcial'),
(18231, 2338, 'SA6B', 97966, 'H.WP 82', 71, 'parcial'),
(18231, 2338, 'SA6B', 97957, 'H.WP 82', 71, 'parcial'),



https://anubesport.com/tracking/?rally=rally7088&port=auto&token=1fCjTmxrjD
http://rest.anube.es/rallyrest/default/api/waypoint_times/7088/1.xml?token=1fCjTmxrjD

idevento: 2338
ID=7088
Token=1fCjTmxrjD

ETAPA 7


# 18308 	2338 	YZPD 	97958 	Hst ASS4 	84
# 393718|ASS4|176150|ASS|1
*/5 * * * * wget --delete-after "https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18308&idRace=7088&token=1fCjTmxrjD&n_etapa=7&waypoint_code=ASS4&waypoint_id=393718&cache=sarr-2025"




# H.WP 10
# (18211, 2338, 'SA51', 96235, 'H.WP 10', 51, 'parcial'),
# 392829|10-N|3060|TP|1
*/5 * * * * wget --delete-after "https://cronometrajeinstantaneo.com/api/anube.php?idevento=2338&idcontrol=18211&idRace=7088&token=1fCjTmxrjD&n_etapa=5&waypoint_code=10-N&waypoint_id=392829&cache=sarr-2025"


---

UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 10976;
UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;

SELECT idcontrol, idparticipante, tiempo FROM lecturas WHERE idcontrol IN (9160, 9172) AND estado != 'eliminado'
AND (idparticipante > 100 OR idparticipante < 200)
ORDER BY idcontrol, tiempo;

SELECT * FROM penas WHERE idetapa IN (6952, 6971, 6961, 6981) LIMIT 500;

DELETE FROM lecturas WHERE idcontrol IN (12550);

*******************************************************************************

## CONFIGURACION JIMDO Y APP
https://cms.e.jimdo.com/app/s82a17b50740eef8e/p36f3ef62dc40b75f?cmsEdit=1

<div style="position: relative; padding-bottom: 56.25%; min-height: 2500px;">
    <iframe frameborder="0" allowfullscreen="allowfullscreen"
    style="position: absolute; top:0; left: 0; width: 100%; min-height: 2500px;"
    src="https://cronometrajeinstantaneo.com/resultados/canav-rally-raid-3-fecha-2023/filtros">
    </iframe>
</div>

## COMANDOS

```sql
ALTER TABLE `lecturas` CHANGE `tipo` `tipo` ENUM('app','rfid','fotocelula','anube') CHARACTER SET utf8 COLLATE utf8_general_ci NOT NULL DEFAULT 'app';

INSERT INTO datosxparticipantes SET idevento = 425, iddato = 'equipo', idinscripcion = 238601, dato = 'Duplessis Team';

=CONCAT("INSERT INTO lecturas SET idevento = 425, idcontrol = 1680, estado = 'ok', tipo = 'fotocelula', idparticipante = ";B2;", tiempo = '2021-02-20 ";I3;"';")

UPDATE `datosxparticipantes` SET dato = 'Chile' WHERE idevento = 425 AND dato = 'Chile ';

UPDATE `lecturas` SET tiempo = DATE_SUB(tiempo, INTERVAL 2 MINUTE) WHERE idcontrol = 1800;

INSERT INTO penas SET idevento = 507, idetapa = 2231, idparticipante, tiempo, observacion

SELECT idevento, idetapa, idparticipante, tiempo, observacion FROM penas
WHERE idetapa IN (2234,2235,2236,3503,2242,2243,2244,3501)
ORDER BY idparticipante
2239
2247

```

## CONFIGURACIONES resultados_js

var contenedor_resultados = document.getElementById("contenedor_resultados");
contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');
contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');
contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');
contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');
contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');


## TENER EN CUENTA PARA SARR

- Presupuestar con tiempo y que Juanpi lo apruebe
- Alguien encargado de los tiempos que hable con los pilotos (reglas claras pre-escritas) así delega esa tarea
- Definir con tiempo como es todo el reglamento
  - Cuánto se completa de tiempo a los que no terminan uno, dos o más especiales
  - A quién se le completa el tiempo
  - Penalización o bonificación: ¿cómo es el proceso? ¿lo puedo hacer yo?
- Acordar cierre de tiempos por etapa, los participantes tienen que saberlo, se firma una hora diaria y se entrega a FIM y a los pilotos
- Ofrecer mejoras
- Cometí muchos errores manuales, hay que reducir al máximo el trabajo manual

Mejoras para el SARR
- Mostrar en el ticket digital Fecha y hora de cada cambio de tiempo y cada penalización público para el participante
- Tarjetas digitales completas, parecido al ticket, pero con otro diseño
- Cada puesto de control con app y con tarjeta digital (ideal sería Beacons)
- Impresiones con logos y formato de impresión diferente al de pantalla (diseñar con tiempo, cada informe por etapa, FIM, generales, por categorías, participantes, etc.)
- Ocultar columna de subcategorías en Fechas FIM
- Separar Motos y Quads, autos y UTV (lo pidió FIM me parece que corresponde siempre)
- Informe con orden de largadas (sería ideal poder establecer la hora, aunque sea con parche con edicion por parámetro GET)
- Pensar cronometraje y backup a prueba de lluvia y a prueba de fallo de Stella
- Importación de penas
- Configurar todos los PCs (ASS y DSS) en el sistema, aunque no se muestren para poder utilizar el informe de PCs y controlar que los tiempos de enlace estén correctos

