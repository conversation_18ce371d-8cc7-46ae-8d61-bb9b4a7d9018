CAMBIO DE PALETA DE COLORES DE INSCRIPCIONES:
header { background-color: #B29513; }
h2, label { color: black; }
form, .form {}
input[type=submit] { background-color: #B29513; color: #FFFFFF; }
footer { background-color:#B29513; color: #FFFFFF; }


INSCRIPCIONES CON FOTO Y TRANSPARENTE:
body {
    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/567/imagen_2.jpeg);
    background-attachment: fixed;
    background-size: contain;
    background-position: center;
}
form, .form { padding: 25px; background: white; border-radius: 25px; opacity: 0.9; }
header { background-color: #008CDE; }
label { color: black }
h2 {color: #008CDE; }
input[type=submit] { background-color: #008CDE; color: #FFFFFF; }
footer { background-color:#008CDE; color: #FFFFFF; }


NUEVO BOTÓN DE MP:
No hace falta cargar un script desde el sistema, sino copiar así
<p>Pagar 10k: <script src="https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js"
data-preference-id="432553423-ed2921a0-046b-4449-aa45-1b307f9c3fb4" data-source="button"></script></p>
<p>Pagar 6k Iniciales: <script src="https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js"
data-preference-id="432553423-e030e6ae-2801-45da-bb80-07299a55d56e" data-source="button"></script></p>
<p>Pagar 6k Avanzados: <script src="https://www.mercadopago.com.ar/integrations/v1/web-payment-checkout.js"
data-preference-id="432553423-dac490ec-ef82-4769-bb08-44cf57374da0" data-source="button"></script></p>

https://mpago.la/2swCzDg



BOTÓN DE PAGO ECUADOR:
https://docs.livepayphone.com/knowledge-base/express-checkout/


DESABILITAR CATEGORIA O CARRERA:
$(function() { $("label[for=1225]").append("<font color=red> CUPO COMPLETO</font>"); $("input[value=1225]").prop("disabled", true); });


CAMBIOS DE TEXTOS:
window.addEventListener("load", function () {
document.body.innerHTML = document.body.innerHTML.replace(/Mostrar todas/g, 'Clasificación General');
});

window.addEventListener("load", function () {
    var contenedor_participantes = document.getElementById("informe_participantes");
    if (contenedor_participantes != null) {
        contenedor_participantes.innerHTML = contenedor_participantes.innerHTML.replace(/Categoría/g, 'Cat.');
        contenedor_participantes.innerHTML = contenedor_participantes.innerHTML.replace(/ - /g, '<br>');
    }
});

// Para filtros
function resultados_js()
{
    var contenedor_resultados = document.getElementById("contenedor_resultados");
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Categoria/g, 'Cat.');
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Nacionalidad/g, 'Nac.');
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Penas Bonus/g, 'Penas');
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Tiempo Total/g, 'Total');
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/Diferencia primero/g, 'Dif.');
    contenedor_resultados.innerHTML = contenedor_resultados.innerHTML.replace(/ - /g, '<br>');
}



OCULTAR GENERALES EN FILTROS
window.addEventListener("load", function () {
$(".tipo_generales").hide();
setTimeout(() => {
select_etapa('6060');
select_tipo('categorias');
}, 500);
});


AGREGAR DISCIPLINA A DUPLA / POSTA:
$(function() {
$(".datos h2")[1].append(' Corredor');
$(".datos h2")[2].append(' Ciclista');
$(".datos").eq(1).find("h2").html('Datos del PILOTO');
});

OCULTAR FILTRO DE GENERALES (ÚTIL PARA DH)
window.addEventListener("load", function () {
$(".tipo_generales").hide();
select_tipo('categorias');
select_etapa('6059');
});

ELIMINAR / OCULTAR CUARTO PARTICIPANTE EN POSTAS
Con CSS
hr:nth-of-type(5), .datos:nth-of-type(5).datos {
  display: none;
}
Con JS
$(function() {
$(".datos").eq(4).hide();
$("hr").eq(4).hide();
document.body.innerHTML = document.body.innerHTML.replace(/(5K Open)/g, '');
document.body.innerHTML = document.body.innerHTML.replace(/(5K Equipo)/g, '');
document.body.innerHTML = document.body.innerHTML.replace(/()/g, '');
});



OCULTAR
$(function() {
$(function() {  });
});
$("label[for=reglamento] label[for=NO]").hide();
$("label[for=reglamento] input[name=reglamento]").hide();


OCULTAR SEXO:
$(function() { $("label[for=sexo]").hide(); });

OCULTAR CATEGORIA:
$(function() { $("label[for=idcategoria]").hide(); });

OCULTAR CARRERA O PARTICIPA EN Y SELECCIONAR PRIMERO:
// Carrera sola
$(function() { $("label[for=idcarrera]").hide(); });
// Una carrera de varias
$(function() { $("label[for=idcarrera]").hide(); $("input[value=1305]").prop('checked', true); });

NO OFICILA:
.no-oficial { display: block; }

FONDO CON DEGRADE GRADIENTE:
    background: linear-gradient(30deg, #000024, #000010);



RESULTADOS CON FOTO DE FONDO
body {
    font-family: "Days One",sans-serif,"google";
    font-size: 14px;
    background-color: white;
    background: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/2397/imagen_1.png);
    background-size: cover;
    background-attachment: fixed;
    background-position: 0 0;
    background-position: center;
    background-repeat: repeat-y;
}

header {
    background: none;
    width: 100%;
}

hr { border: #002D55; }
.head1, .head2 { color: #002D55; }
.tabla_interna { border: #53C1FC; padding: 20px; }
.tabla_interna tbody tr:nth-child(even) td { background: #EEEEEE; color: #000}
.tabla_interna thead { background: #002D55; }
.tabla_interna tbody tr:hover td, .tabla_interna tbody td { background-color: #53C1FC; color: #000; }
tr { border-radius: 10px;}



RESULTADOS SIN FONDO Y COLORES SEGÚN PALETA:
body { background: #FFFFFF; }
header, footer { display:none; }

hr { border: #002D55; }
.head1, .head2 { color: #002D55; }
.tabla_interna { border: #53C1FC}
.tabla_interna tbody tr:nth-child(even) td { background: #EEEEEE; }
.tabla_interna tbody tr:nth-child(odd) td { }
.tabla_interna thead { background: #002D55; }
.tabla_interna tbody tr:hover td, .tabla_interna tbody td { background-color: #53C1FC; color: #ffffff; }


RESULTADOS CON SPONSORS A TODO LO ANCHO
.sponsors img { width: 100%; }

OCULTAR / ESCONDER SEXO Y LOCALIDAD EN GENERALES:
#informe_generales .tabla_interna th:nth-child(2),
#informe_generales .tabla_interna td:nth-child(2),
#informe_generales .tabla_interna th:nth-child(7),
#informe_generales .tabla_interna td:nth-child(7) {
    display: none;
}

MÍNIMO WIDTH EN NOMBRE:
#informe_generales .tabla_interna th:nth-child(3) {
    min-width: 200px;
}


MEDIA QUERYS:
 /* Extra small devices (phones, 600px and down) */
@media only screen and (max-width: 600px) {...}

/* Small devices (portrait tablets and large phones, 600px and up) */
@media only screen and (min-width: 600px) {...}

/* Medium devices (landscape tablets, 768px and up) */
@media only screen and (min-width: 768px) {...}

/* Large devices (laptops/desktops, 992px and up) */
@media only screen and (min-width: 992px) {...}

/* Extra large devices (large laptops and desktops, 1200px and up) */
@media only screen and (min-width: 1200px) {...}


TICKET:
#informe_ticket {
    background-image: url(https://storage.googleapis.com/cronometrajeinstantaneo-eventos/465/imagen_5.jpeg);
    background-attachment: fixed;
    background-size: cover;
}

.ticket_logo img { width: 50%; }

.ticket_content, .ticket_etapas { background: rgb(255, 255, 255, 0.5); }

.ticket_etapas { display: block; }

.contenedor_ticket .tiempo_nro,
.contenedor_ticket .pos_general_nro,
.contenedor_ticket .pos_sexo_nro,
.contenedor_ticket .pos_categoria_nro,
.ticket_etapas .pena_nro {
    color: #ED6B23;
}

.pos_sexo { display: none; }

.contenedor_filtros .filtros a {
    font-family: "PT Sans Narrow",sans-serif,"google";
    font-weight: normal;
    font-style: normal;
    color: #03a9f4;
    background-color: white;
    border-radius: 6px;
    text-transform: uppercase;
}

.filtro_seleccionado {
    background-color: #026E9F !important;
    color: white !important;
}

.no-oficial { display: block; }

## STREAMING VIVO META

```css
.chroma { background: #000 !important; }
.meta, .podio, .vivo {
  width: 800px;
  max-height: 515px;
  overflow: hidden;
}

.vueltas {
  display: none;
}

table.podio {
  background-color: white;
  color: #0D6777;
  font-size: 25px;
}

thead tr, tbody tr {
  height: 50px;
}

thead.bg-naranja, thead.bg-azul {
  background-color: #577149;
  font-size: 25px;
}

td.puesto-destacado, table.podio tr:nth-child(2n+1) td {
  background: none;
}
```

## DISEÑOS LINDOS

https://cronometrajeinstantaneo.com/resultados/carrera-84-aniversario-de-la-creacion-de-gendarmeria-nacional
https://cronometrajeinstantaneo.com/resultados/valle-de-los-perdidos-trail/generales

## ELIMINO CARRERAS SIN CUPO
```js
$(function() {
    $('label:contains("SIN CUPO")').each(function() {
        if ($(this).attr('for') != 'idcarrera')
            $(this).remove();
    });
    $('input[name="idcarrera"]').each(function() {
        $(this).next('br').remove();
        if ($(this).attr('disabled'))
            $(this).remove();
    });
```

## FORMAR NOMBRE DE EQUIPO CON APELLIDOS
```css
label[for=equipo] { visibility: hidden }
```

```js
const apellido1 = document.querySelector('input[name="apellido1"]');
const apellido2 = document.querySelector('input[name="apellido2"]');
const nombre = document.querySelector('input[name="nombre"]');

[apellido1, apellido2].forEach((apellido) => {
  apellido.addEventListener('change', () => {
    nombre.value = [apellido1.value, apellido2.value].filter(Boolean).map(name => name.toUpperCase()).join(' / ');
  });
});
```

## RESULTADOS CON UNA IMAGEN EN TODO EL FONDO

```js
$(function() {
$('header .head-logo img').attr('src', 'https://storage.googleapis.com/cronometrajeinstantaneo-eventos/2026/imagen_1.jpg');
});
```

```css
header {
  display: block;
  padding: 0;
  margin: 0;
}

.head-logo {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0;
  margin: 0;
}

.head-logo img {
  width: 100%;
  margin: 0;
}

.head-info {
  display: none;
}
```

## INTERCAMBIAR COLUMNAS DE LUGAR

```js
$('#informe_participantes table tr').each(function() {
  var $row = $(this);
  var $cell1 = $row.find('td:eq(8), th:eq(8)');
  var $cell2 = $row.find('td:eq(9), th:eq(9)');
  var temp = $cell1.html();
  $cell1.html($cell2.html());
  $cell2.html(temp);
});
```