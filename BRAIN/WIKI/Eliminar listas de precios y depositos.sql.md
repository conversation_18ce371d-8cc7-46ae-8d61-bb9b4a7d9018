### ELIMINAR LISTA DE PRECIOS
# idlista = 666
# idlista_predeterminada = 777

UPDATE ventas SET idlista = 777 WHERE idlista = 666;
UPDATE categorias_clientes SET idlista = 777 WHERE idlista = 666;
DELETE FROM precios WHERE idlista = 666;
DELETE FROM listas WHERE idlista = 666;

### ELIMINAR LISTA DE PRECIOS
# iddeposito IN (8, 9, 10, 16, 18)
# iddeposito_predeterminado = 1

UPDATE ventas SET iddeposito = 1 WHERE iddeposito IN (8, 9, 10, 16, 18);
UPDATE compras SET iddeposito = 1 WHERE iddeposito IN (8, 9, 10, 16, 18);
UPDATE categorias_proveedores SET iddeposito = 1 WHERE iddeposito IN (8, 9, 10, 16, 18);
UPDATE categorias_ventas SET iddeposito = 1 WHERE iddeposito IN (8, 9, 10, 16, 18);
UPDATE traslados SET iddepositoinicio = 1 WHERE iddepositoinicio IN (8, 9, 10, 16, 18);
UPDATE traslados SET iddepositofin = 1 WHERE iddepositofin IN (8, 9, 10, 16, 18);
DELETE FROM stock WHERE iddeposito IN (8, 9, 10, 16, 18);
DELETE FROM depositos WHERE iddeposito IN (8, 9, 10, 16, 18);


### ELIMINAR PRODUCTOS POR CÓDIGO
DELETE FROM precios WHERE idproducto IN (SELECT idproducto FROM productos WHERE codigo IN ('cod1', 'cod2'));
DELETE FROM depositos WHERE idproducto IN (SELECT idproducto FROM productos WHERE codigo IN ('cod1', 'cod2'));
DELETE productos WHERE codigo IN ('cod1', 'cod2');
