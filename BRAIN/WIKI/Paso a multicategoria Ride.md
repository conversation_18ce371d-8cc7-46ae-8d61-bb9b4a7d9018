# OSE + ARG

Van a tener un evento doble, junto con el Argentino. Kittu me debe un excel con las categorías de cada uno (el Argentino tiene menos y se compensan). Voy a configurar 3 carreras: OSE, ARG, OSE + ARG con las categorías compensadas.

El día antes del evento, voy a mover a mano las de OSE + ARG a cada uno. Ver si podes preparar los SQL antes.

idevento: 2175
idcarreras
OSE: 10773
ARG: 10881
OSE+ARG: 10882

## AGREGAR A LA MULTICATEGORIA ANTES DEL EVENTO

```sql
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55211, idinscripcion FROM participantes WHERE idcategoria = 55211);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55212, idinscripcion FROM participantes WHERE idcategoria = 55212);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55213, idinscripcion FROM participantes WHERE idcategoria = 55213);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55214, idinscripcion FROM participantes WHERE idcategoria = 55214);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55215, idinscripcion FROM participantes WHERE idcategoria = 55215);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55216);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55217);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55216, idinscripcion FROM participantes WHERE idcategoria = 55218);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55218);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55219);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55220);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55219, idinscripcion FROM participantes WHERE idcategoria = 55221);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55221);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55222, idinscripcion FROM participantes WHERE idcategoria = 55222);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55223);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55224);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55223, idinscripcion FROM participantes WHERE idcategoria = 55225);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55224, idinscripcion FROM participantes WHERE idcategoria = 55225);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55226);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55227);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55226, idinscripcion FROM participantes WHERE idcategoria = 55228);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55227, idinscripcion FROM participantes WHERE idcategoria = 55228);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55229);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55230);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55229, idinscripcion FROM participantes WHERE idcategoria = 55231);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55230, idinscripcion FROM participantes WHERE idcategoria = 55231);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55232);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55233);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55232, idinscripcion FROM participantes WHERE idcategoria = 55234);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55234);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55235);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55236);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55235, idinscripcion FROM participantes WHERE idcategoria = 55237);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55238);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55238);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55239);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55238, idinscripcion FROM participantes WHERE idcategoria = 55240);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55240);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55241);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55242);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55241, idinscripcion FROM participantes WHERE idcategoria = 55243);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55243);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55244);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55245);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55244, idinscripcion FROM participantes WHERE idcategoria = 55246);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55246);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55247);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55248);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55247, idinscripcion FROM participantes WHERE idcategoria = 55249);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55249);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55250);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55250, idinscripcion FROM participantes WHERE idcategoria = 55251);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55251);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55242, idinscripcion FROM participantes WHERE idcategoria = 55252);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55253);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55254);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55255);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55256);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55252, idinscripcion FROM participantes WHERE idcategoria = 55257);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55245, idinscripcion FROM participantes WHERE idcategoria = 55253);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55248, idinscripcion FROM participantes WHERE idcategoria = 55254);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55233, idinscripcion FROM participantes WHERE idcategoria = 55255);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55236, idinscripcion FROM participantes WHERE idcategoria = 55256);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55239, idinscripcion FROM participantes WHERE idcategoria = 55257);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55258);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55258, idinscripcion FROM participantes WHERE idcategoria = 55259);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55217, idinscripcion FROM participantes WHERE idcategoria = 55259);

INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55260);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55260, idinscripcion FROM participantes WHERE idcategoria = 55261);
INSERT INTO categoriasxparticipantes (idcategoria, idinscripcion) VALUES
    (SELECT 55220, idinscripcion FROM participantes WHERE idcategoria = 55261);

UPDATE participantes SET idcategoria = 55213 WHERE idcategoria = 55215;
UPDATE participantes SET idcategoria = 55216 WHERE idcategoria = 55218;
UPDATE participantes SET idcategoria = 55219 WHERE idcategoria = 55221;
UPDATE participantes SET idcategoria = 55223 WHERE idcategoria = 55225;
UPDATE participantes SET idcategoria = 55226 WHERE idcategoria = 55228;
UPDATE participantes SET idcategoria = 55229 WHERE idcategoria = 55231;
UPDATE participantes SET idcategoria = 55232 WHERE idcategoria = 55234;
UPDATE participantes SET idcategoria = 55235 WHERE idcategoria = 55237;
UPDATE participantes SET idcategoria = 55238 WHERE idcategoria = 55240;
UPDATE participantes SET idcategoria = 55241 WHERE idcategoria = 55243;
UPDATE participantes SET idcategoria = 55244 WHERE idcategoria = 55246;
UPDATE participantes SET idcategoria = 55247 WHERE idcategoria = 55249;
UPDATE participantes SET idcategoria = 55250 WHERE idcategoria = 55251;
UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55253;
UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55254;
UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55255;
UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55256;
UPDATE participantes SET idcategoria = 55252 WHERE idcategoria = 55257;
UPDATE participantes SET idcategoria = 55258 WHERE idcategoria = 55259;
UPDATE participantes SET idcategoria = 55260 WHERE idcategoria = 55261;

```

## CONFIGURACIÓN DE LAS CATEGORIAS PARA ABRIR INSCRIPCIONES

```sql

DELETE FROM categorias WHERE idevento = 2175 AND idcarrera = 10773;

INSERT INTO `categorias` (`idcategoria`, `idevento`, `idcarrera`, `orden`, `nombre`, `sexo`, `nacimiento_desde`, `nacimiento_hasta`) VALUES
(55211, 2175, 10773, 1, 'Debutantes Men', 'masculino', '1910-01-01', '2008-12-31'),
(55212, 2175, 10773, 2, 'Debutantes Women', 'femenino', '1910-01-01', '2008-12-31'),

(55213, 2175, 10773, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),
(55214, 2175, 10881, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),
(55215, 2175, 10882, 16, 'Damas Master', 'femenino', '1910-01-01', '1989-12-31'),

(55216, 2175, 10773, 5, 'Damas Amateur', 'femenino', '0000-00-00', '0000-00-00'),
(55217, 2175, 10881, 18, 'Damas Elite', 'femenino', '0000-00-00', '0000-00-00'),
(55218, 2175, 10882, 5, 'Damas Amateur (OSE) / Damas Elite (ARG)', 'femenino', '0000-00-00', '0000-00-00'),

(55219, 2175, 10773, 4, 'Amateur Men (19+)', 'masculino', '1910-01-01', '2005-12-31'),
(55220, 2175, 10881, 19, 'Elite', 'masculino', '1910-01-01', '2005-12-31'),
(55221, 2175, 10882, 4, 'Amateur Men (OSE) / Elite (ARG)', 'masculino', '1910-01-01', '2005-12-31'),

(55222, 2175, 10773, 3, 'Infantiles Mixto (11-12)', 'mixto', '2012-01-01', '2013-12-31'),

(55223, 2175, 10773, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),
(55224, 2175, 10881, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),
(55225, 2175, 10882, 6, 'Menores Mixto (13-14)', 'masculino', '2010-01-01', '2011-12-31'),

(55226, 2175, 10773, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),
(55227, 2175, 10881, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),
(55228, 2175, 10882, 10, 'Cadetes (15-16)', 'masculino', '2008-01-01', '2009-12-31'),

(55229, 2175, 10773, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),
(55230, 2175, 10881, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),
(55231, 2175, 10882, 11, 'Junior (17-18)', 'masculino', '2006-01-01', '2007-12-31'),

(55232, 2175, 10773, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),
(55233, 2175, 10881, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),
(55234, 2175, 10882, 9, 'Master A (35-39)', 'masculino', '1985-01-01', '1989-12-31'),

(55235, 2175, 10773, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),
(55236, 2175, 10881, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),
(55237, 2175, 10882, 8, 'Master B (40-49)', 'masculino', '1975-01-01', '1984-12-31'),

(55238, 2175, 10773, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),
(55239, 2175, 10881, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),
(55240, 2175, 10882, 7, 'Master C (50+)', 'masculino', '1950-01-01', '1974-12-31'),

(55241, 2175, 10773, 12, 'E-Amateur (16+)', 'mixto', '1910-01-01', '2008-12-31'),
(55242, 2175, 10881, 12, 'E-Elite', 'mixto', '1910-01-01', '2008-12-31'),
(55243, 2175, 10882, 12, 'E-Amateur (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),

(55244, 2175, 10773, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),
(55245, 2175, 10881, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),
(55246, 2175, 10882, 13, 'E-Master B', 'masculino', '1975-01-01', '1984-12-31'),

(55247, 2175, 10773, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),
(55248, 2175, 10881, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),
(55249, 2175, 10882, 14, 'E-Master C', 'masculino', '1950-01-01', '1974-12-31'),

(55250, 2175, 10773, 15, 'E-PRO', 'masculino', '1910-01-01', '2008-12-31'),
(55251, 2175, 10882, 12, 'E-PRO (OSE) / E-Elite (ARG)', 'mixto', '1910-01-01', '2008-12-31'),

(55252, 2175, 10773, 17, 'Master PRO (35+)', 'masculino', '1910-01-01', '1989-12-31'),
(55253, 2175, 10882, 13, 'Master PRO (OSE) / E-Master B (ARG)', 'masculino', '1975-01-01', '1984-12-31'),
(55254, 2175, 10882, 14, 'Master PRO (OSE) / E-Master C (ARG)', 'masculino', '1950-01-01', '1974-12-31'),
(55255, 2175, 10882, 9, 'Master PRO (OSE) / Master A (ARG)', 'masculino', '1985-01-01', '1989-12-31'),
(55256, 2175, 10882, 8, 'Master PRO (OSE) / Master B (ARG)', 'masculino', '1975-01-01', '1984-12-31'),
(55257, 2175, 10882, 7, 'Master PRO (OSE) / Master C (ARG)', 'masculino', '1950-01-01', '1974-12-31'),

(55258, 2175, 10773, 18, 'Damas PRO', 'femenino', '0000-00-00', '0000-00-00'),
(55259, 2175, 10882, 18, 'Damas PRO (OSE) / Damas Elite (ARG)', 'femenino', '0000-00-00', '0000-00-00'),

(55260, 2175, 10773, 19, 'Elite Pro (19-34)', 'masculino', '1970-01-01', '2005-12-01'),
(55261, 2175, 10882, 19, 'Elite Pro (OSE) / Elite (ARG)', 'masculino', '1970-01-01', '2005-12-01');

```
