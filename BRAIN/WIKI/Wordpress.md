
## INSTALAR WORDPRESS EN SERVER CRONO

1. Instalar Wordpress

sudo su
cd /var/www/
mkdir travesiadeloscerros
cd travesiadeloscerros
mkdir public
wget https://es-ar.wordpress.org/latest-es_AR.tar.gz
tar -zxvf latest-es_AR.tar.gz
mv wordpress wp
mkdir wp/wp-content/upgrade
mkdir wp/wp-content/uploads
chown -R andresmaiden:www-data wp
chmod -R 775 wp/wp-content/plugins
chmod -R 775 wp/wp-content/themes
chmod -R 775 wp/wp-content/upgrade
chmod -R 775 wp/wp-content/uploads
chmod -R 775 wp/wp-content/languages
cd wp
htpasswd -c .htpasswd travesiadeloscerros
mv wp-config{-sample,}.php
sed -i 's/put your unique phrase here/frase secreta de travesiadeloscerros/g' wp-config.php
echo "define('FS_METHOD', 'direct');" >> wp-config.php

1. Crear base de datos y usuario
CREATE DATABASE travesiadeloscerros;
GRANT ALL PRIVILEGES ON travesiadeloscerros.* TO 'travesiadeloscerros'@'localhost' IDENTIFIED BY 'FVB-8VpZSjMCCataKXC';
FLUSH PRIVILEGES;

1. Delegar dominio temporal o definitivo
2. Entrar al sitio, seguir el wizard y configurar usuarios
3. Configurar apache y letsencrypt
    Instalar WP FREE SSL y activar
    Generar Certificado
    Copiar y pegar el contenido del Certificate y el Private key en el Panel del hosting en Ferozo
    Anotarme mensaje antes de que venza




## TUTORIALES ÚTILES:
Cómo igualar la altura de las imágenes de los productos: https://ayudawp.com/igualar-altura-imagenes-productos/
Cómo limitar el número de pedidos por día, hora o lo que sea: https://ayudawp.com/como-limitar-el-numero-de-pedidos-por-dia-hora-o-lo-que-sea-semanawoocommerce/
Cómo hacer obligatorios u opcionales los campos al finalizar compra en WooCommerce: https://ayudawp.com/campos-obligatorios-opcionales-finalizar-compra-woocommerce/
https://ayudawp.com/redirection/
WooCommerce: Cómo mostrar productos comprados juntos habitualmente: https://ayudawp.com/productos-comprados-juntos-habitualmente-woocommerce/
Cómo configurar y asignar pagos a varias cuentas de PayPal en WooCommerce: https://ayudawp.com/varias-cuentas-paypal-woocommerce/
Convierte automáticamente tus entradas de blog en cursos online de Sensei LMS: https://ayudawp.com/convertir-entradas-cursos/
Cómo compensar el desplazamiento a anclajes en el tema Astra si tienes cabecera fija: https://ayudawp.com/como-compensar-el-desplazamiento-a-anclajes-en-el-tema-astra-si-tienes-cabecera-fija/

## CAMBIOS DE CÓDIGO:

// Cambiar h2 por h3 en los widgets de Astra para mejorar SEO
// Pie de página Widget 1 add_filter( 'astra_advanced_footer_widget_1_args', 'widget_title_footer_1_tag', 10, 1 ); function widget_title_footer_1_tag( $atts ) { $atts['before_title'] = '<h3 class="widget-title">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 2 add_filter( 'astra_advanced_footer_widget_2_args', 'widget_title_footer_2_tag', 10, 1 ); function widget_title_footer_2_tag( $atts ) { $atts['before_title'] = '<h3 class="widget-title">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 3 add_filter( 'astra_advanced_footer_widget_3_args', 'widget_title_footer_3_tag', 10, 1 ); function widget_title_footer_3_tag( $atts ) { $atts['before_title'] = '<h3 class="widget-title">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 4 add_filter( 'astra_advanced_footer_widget_4_args', 'widget_title_footer_4_tag', 10, 1 ); function widget_title_footer_4_tag( $atts ) { $atts['before_title'] = '<h3 class="widget-title">'; $atts['after_title'] = '</h3>'; return $atts; } // Pie de página Widget 5 add_filter( 'astra_advanced_footer_widget_5_args', 'widget_title_footer_5_tag', 10, 1 ); function widget_title_footer_5_tag( $atts ) { $atts['before_title'] = '<h3 class="widget-title">'; $atts['after_title'] = '</h3>'; return $atts; }




### ORDENAR

- La combinación perfecta de plugins SEO gratuitos para un Wordpress: https://ayudawp.com/plugins-seo-woocommerce
---

define('WP_HOME', 'https://misuperweb.com');
define('WP_SITEURL', 'https://misuperweb.com/wordpress');

GRANT ALL PRIVILEGES ON simplementeviviendo.* TO 'simplementeviviendo'@'localhost' IDENTIFIED BY '827e9DEA1a3caadb@287678fae%e1e9fd8$';
FLUSH PRIVILEGES;

827e9DEA1a3caadb@287678fae%e1e9fd8$

FVB8VpZSjMCCataKXCYYXqgqC4q6KtQz

andresmaiden | 2wqTY$N@CJWsMnD3ya

Compré plantilla en [https://themeforest.net/downloads](https://themeforest.net/downloads) / [https://preview.themeforest.net/item/blacksilver-photography-theme-for-wordpress/full_screen_preview/23717875?_ga=2.23205362.995292608.1584328036-818213767.1578006226](https://preview.themeforest.net/item/blacksilver-photography-theme-for-wordpress/full_screen_preview/23717875?_ga=2.23205362.995292608.1584328036-818213767.1578006226)

Empresa de India que ofrece trabajos de Wordpress a U$D11 la hora: [https://wayinfotechsolutions.com/](https://wayinfotechsolutions.com/)

# PROCESOS

## REVISAR ERROR
El error aparece como Ha habido un error crítico en este sitio

define( 'WP_DEBUG', true );
define( 'WP_DEBUG_DISPLAY', true );
define( 'WP_DEBUG_LOG', true );

Más info en: https://kinsta.com/es/base-de-conocimiento/ha-habido-un-error-critico-en-su-sitio-web/

### INSTALAR WOOCOMMERCE DESDE WP-CLI

---

1. Instalar desde WP-CLI
curl -O [https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar](https://raw.githubusercontent.com/wp-cli/builds/gh-pages/phar/wp-cli.phar)
chmod +x wp-cli.phar
sudo mv wp-cli.phar /usr/local/bin/wp
wp core download
wp core config --dbname=mydbname --dbuser=mydbuser --dbpass=mydbpass --dbhost=localhost --dbprefix=whebfubwef_ --extra-php <<PHP
define( 'WP_DEBUG', true );
define( 'WP_DEBUG_LOG', true );
PHP
wp db create
wp core install --url=http://siteurl.com --title=SiteTitle --admin_user=username --admin_password=mypassword [--admin_email=<EMAIL>](mailto:--admin_email=<EMAIL>)

wp option update home '[http://example.com](http://example.com/)'
wp option update siteurl '[http://example.com](http://example.com/)'
wp search-replace oldstring newstring

wp plugin list
wp theme install twentyseventeen --activate
wp plugin install advanced-custom-fields jetpack ninja-forms --activate

### INSTALAR WOOCOMMERCE DESDE SSH

---

1. Instalar Wordpress
mkdir tienda & cd tienda
wget [https://es-ar.wordpress.org/latest-es_AR.tar.gz](https://es-ar.wordpress.org/latest-es_AR.tar.gz)
tar -zxvf latest-es_AR.tar.gz
mv wordpress public
sudo mkdir public/wp-content/upgrade
sudo mkdir public/wp-content/uploads
sudo chown -R andresmaiden:www-data public
sudo chmod -R 775 public/wp-content/plugins
sudo chmod -R 775 public/wp-content/themes
sudo chmod -R 775 public/wp-content/upgrade
sudo chmod -R 775 public/wp-content/uploads
sudo chmod -R 775 public/wp-content/languages
cd public

    mv wp-config{-sample,}.php
    echo "define('FS_METHOD', 'direct');" >> wp-config.php

2. Crear base de datos y usuario
CREATE DATABASE wp1;
GRANT ALL PRIVILEGES ON wp1.* TO 'wp1'@'localhost' IDENTIFIED BY 'B48OqXTx5Y$%3PGosIyIUeQGeCB3ek2D';
FLUSH PRIVILEGES;
3. Entrar al sitio, seguir el wizard y configurar usuarios
4. Configurar apache y letsencrypt
    Instalar WP FREE SSL y activar
    Generar Certificado
    Copiar y pegar el contenido del Certificate y el Private key en el Panel del hosting en Ferozo
    Anotarme mensaje antes de que venza

PASAR WP-CRON A UNIX-CRON:

- [https://spinupwp.com/doc/understanding-wp-cron/](https://spinupwp.com/doc/understanding-wp-cron/)
- [https://ayudawp.com/wp-cron/](https://ayudawp.com/wp-cron/)
- define( 'DISABLE_WP_CRON', true );
- 0 0 * * * wget --delete-after http://YOUR_SITE_URL/wp-cron.php

## PLUGINS NECESARIOS

---

1. Ver tema templates, diseño, otros plugins, etc.
2. Para Tiendas:
    - wooCommerce
    - MercadoPago
    - Jetpack
    - LiteSpeed
    - WP Mail SMTP by WPForms
    - Revisar si hacen falta más: [https://blog.hubspot.com/website/must-have-wordpress-plugins](https://blog.hubspot.com/website/must-have-wordpress-plugins) y [https://www.wpbeginner.com/showcase/24-must-have-wordpress-plugins-for-business-websites/](https://www.wpbeginner.com/showcase/24-must-have-wordpress-plugins-for-business-websites/)
    - Carritos abandonados: [https://es.wordpress.org/plugins/woo-cart-abandonment-recovery/](https://es.wordpress.org/plugins/woo-cart-abandonment-recovery/)
3. MKT
    - Yoast SEO o Rank Math SEO
    - Akismet Anti-spam
    - Broken Link Checker
    - AMP for WP Accelerated Mobile Pages
    - Google Analytics
    - Pixel de Facebook
    - Framework SEO
    - [https://yoast.com/wordpress/plugins/yoast-woocommerce-seo/](https://yoast.com/wordpress/plugins/yoast-woocommerce-seo/)
4. Mejorar velocidad y cache
    - [https://deliciousbrains.com/wp-offload-media/](https://deliciousbrains.com/wp-offload-media/)
    - Plugins para cache por órden de profesionalidad:
        1. WP Super Cache (obligatorio mínimo, es de Automatic y perfecto para static webs)
        2. Smush (comprimir imágenes)
        3. W3 Total cache
        4. WPRocket
        5. Batcache
        6. WP Fastest Cache
5. Ver salud del sitio, seguridad y configurar sistema de BackUp
    - All In One WordPress Security and Firewall Plugin (creo que es el mejor)
    - Wordfence Security o iThemes Security
    - BackWPUp

### IMPORTACIONES

Categorías:

- Dejar sólo las columnas SKU y Categorías
- REEMPLAZAR , POR .
- Revisar que las categorías no tengan comas y que existan exactamente igual
- Agregar en la segunda línea los nombres de las cat generales
CONSTRUCCIÓN HÚMEDA	CONSTRUCCIÓN STEEL FRAMING	CONSTRUCCIÓN EN MADERA	HORMIGÓN ELABORADO	TECHOS	FERRETERIA. HERRAMIENTAS Y MAQUINARIAS
- Concatenar cat con:
=SI(ESBLANCO(C3);"";CONCAT(C$2;" > ";C3;", "))
=CONCAT(I3;J3;K3;L3;M3;N3)
=SI(LARGO(O3)<3;"";REEMPLAZAR(O3;LARGO(O3)-1;2;""))
- Copiar y pegar sólo texto (borrar columnas usadas temporalmente)
- Exportar como csv (separado por ;) y listo

Imágenes

- Subir imágenes por FTP (especificar bien carpeta, permisos y extensión .jpg)
- Listar imágenes desde consola y acomodarlas (sólo se puede un SKU por línea separando imágenes con coma ,)
[https://nodomateriales.com/fotos/11SUPE01_1.jpg](https://nodomateriales.com/fotos/11SUPE01_1.jpg)

# PLUGINS

---

### PLUGINS PUNTUALES PROBADOS

- Instalación automatizada con WP Quick Install: [https://www.wpkube.com/automate-wordpress-installs-setup](https://www.wpkube.com/automate-wordpress-installs-setup)
- Plugin para duplicar wordpress: [https://www.wpkube.com/move-backup-website-wordpress-duplicator-plugin](https://www.wpkube.com/move-backup-website-wordpress-duplicator-plugin)
- Easy Google Fonts: [https://www.getdrip.com/deliveries/4w89v6brbhim8mwjbfvi](https://www.getdrip.com/deliveries/4w89v6brbhim8mwjbfvi) [https://wordpress.org/plugins/easy-google-fonts](https://wordpress.org/plugins/easy-google-fonts)
- Para sincronizar con ML se pueden usar estos plugins: [https://www.woosync.com.ar/](https://www.woosync.com.ar/) y [https://woomelly.com/](https://woomelly.com/)
- Para traducir templates o plugins: Loco Translate (Mover a carpeta languages/loco antes de traducir)
- Ordenar productos en woocommerce: [https://wordpress.org/support/topic/impossible-sort-by-sku/](https://wordpress.org/support/topic/impossible-sort-by-sku/)
- Migrar WP a otro server MIGRATE GURU ES LO MEJOR https://wordpress.org/support/plugin/migrate-guru/
- Cambiar el dominio: Better Search Replace
- Para agregar más formas de pago: [https://wpfactory.com/item/custom-payment-gateways-woocommerce/](https://wpfactory.com/item/custom-payment-gateways-woocommerce/)

    Se podría programar con este tutorial: [https://www.skyverge.com/blog/how-to-create-a-simple-woocommerce-payment-gateway/](https://www.skyverge.com/blog/how-to-create-a-simple-woocommerce-payment-gateway/) y este [https://stackoverflow.com/questions/17081483/custom-payment-method-in-woocommerce/37631908](https://stackoverflow.com/questions/17081483/custom-payment-method-in-woocommerce/37631908)

- Para agregar intereses y descuentos por forma de pago: [https://es-ar.wordpress.org/plugins/woocommerce-pay-for-payment/](https://es-ar.wordpress.org/plugins/woocommerce-pay-for-payment/)
- Para reservas y turnos: [https://ayudawp.com/plugin-reservas-woocommerce/](https://ayudawp.com/plugin-reservas-woocommerce/)

### PLUGINS PARA PROBAR

[https://quadlayers.com/portfolio/woocommerce-direct-checkout](https://quadlayers.com/portfolio/woocommerce-direct-checkout)

[https://wordpress.org/plugins/woocommerce-checkout-manager/](https://wordpress.org/plugins/woocommerce-checkout-manager/)

[https://elementor.com/blog/wordpress-security-plugins/](https://elementor.com/blog/wordpress-security-plugins/)

[https://updraftplus.com/updraftcentral/](https://updraftplus.com/updraftcentral/)

[https://wp-rocket.me/](https://wp-rocket.me/)

[https://premmerce.com/](https://premmerce.com/)

Listado de shortcodes [https://es.wordpress.org/plugins/shortcodes-ultimate/](https://es.wordpress.org/plugins/shortcodes-ultimate/)

Adapta RGPD (para páginas con accesos desde Europa)

Table of Contents Plus

ManageWp (para manejar varios WPs desde un solo lugar)

Wordfence Central (Para manejar seguridad de varios WP [https://www.wordfence.com/try-central/](https://www.wordfence.com/try-central/))

Cookie Law Info (para mostrar el cartelito de cookie)

Nota sobre Schemas [https://ayudawp.com/desactivar-schema-astra/](https://ayudawp.com/desactivar-schema-astra/)

Schemas [https://es.wordpress.org/plugins/schema-and-structured-data-for-wp/](https://es.wordpress.org/plugins/schema-and-structured-data-for-wp/)

Instragra Feed: [https://spotlightwp.com/](https://spotlightwp.com/)

# MEJORAS

---

### APRENDER E IMPLEMENTAR

### TUTORIALES PARA LEER

- Medición de conversiones: [https://www.wpbeginner.com/beginners-guide/wordpress-conversion-tracking-made-simple-a-step-by-step-guide/](https://www.wpbeginner.com/beginners-guide/wordpress-conversion-tracking-made-simple-a-step-by-step-guide/)
- Cambiar página Mi Cuenta: [https://ayudawp.com/personalizar-mi-cuenta-woocommerce/](https://ayudawp.com/personalizar-mi-cuenta-woocommerce/)
- Mejorar Wishlist: [https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/](https://yithemes.com/themes/plugins/yith-woocommerce-wishlist/)
- Queues: [https://wpshout.com/quick-guides/use-wp_enqueue_script-include-javascript-wordpress-site/](https://wpshout.com/quick-guides/use-wp_enqueue_script-include-javascript-wordpress-site/)
add_action('wp_enqueue_scripts', 'qg_enqueue');
function qg_enqueue() {
wp_enqueue_script(
'qgjs',
plugin_dir_url(**FILE**).'quick-guide.js'
);
}

### RENOVAR EN WP ENCRPYT
Está explicado en Letsencrypt.txt


### IMPORTAR YOAST META DATOS PARA PRODUCTOS
DELETE FROM wp_postmeta WHERE meta_key = '_yoast_wpseo_metadesc';
=CONCAT("UPDATE wp_postmeta SET meta_value = '";I1;"' WHERE post_id = ";A1;" AND meta_key = '_yoast_wpseo_metadesc';")

### PLUGIN PARA PASAR WP A HTML STATICO

Simply Static