SELECT idcliente, idtipoiva, cuit, dni, razonsocial, domicilio, idlocalidad, nombre, saldo, mail
FROM clientes
    LEFT JOIN saldos ON saldos.tiporelacion = 'clientes' AND clientes.idcliente = saldos.idrelacion
WHERE estado = '1'
    AND idtipocliente = '1'
    AND idcliente > 1
    AND (ISNULL(saldo) OR saldo < 4000)

SELECT * FROM clientes
WHERE estado = '1'
    AND idtipocliente = '1'
    AND idcliente > 1
    AND NOT EXISTS (SELECT idventa FROM ventas WHERE ventas.idcliente = clientes.idcliente AND fecha > MONTH(now()))
ORDER BY idcliente
