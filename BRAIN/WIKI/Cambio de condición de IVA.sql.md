# LAS CAJAS HAY QUE ACOMODARLAS A MANO, DEJANDO UNA ABIERTA POR CADA CATEGORÍA DE CAJA
TRUNCATE movimientosxcajas;

# VACIAR VENTAS
TRUNCATE ventas;
TRUNCATE ventaspagos;
TRUNCATE ventasxventas;
TRUNCATE ventasxclientes;
TRUNCATE productosxventas;

# VACIAR TIPOS DE VENTAS
TRUNCATE categorias_ventas;

# LLENAR TIPOS DE VENTAS PARA RESPONSABLES INSCRIPTOS
INSERT INTO categorias_ventas (idcomportamiento, estado, nombre, letra, puntodeventa, ultimonumero, discrimina, muevestock, muevesaldo, operacioninversa, tipofacturacion, tipoimpresion) VALUES
(1, '1', 'Factura A', 'A', 1, 0, 'A', 1, 1, 0, 'manual', 'predeterminado'),
(6, '1', 'Factura B', 'B', 1, 0, 'B', 1, 1, 0, 'manual', 'predeterminado'),
(101, '1', 'Remito', 'R', 1, 0, 'R', 0, 0, 0, 'interno', 'predeterminado'),
(102, '1', 'Presupuesto', 'P', 1, 0, 'A', 0, 0, 0, 'interno', 'predeterminado'),
(103, '1', 'Pedido', 'P', 1, 0, 'A', 0, 0, 0, 'interno', 'predeterminado'),
(104, '1', 'Remito de devolución', 'R', 1, 0, 'R', 0, 0, 1, 'interno', 'predeterminado'),
(2, '1', 'Nota de Débito A', 'A', 1, 0, 'A', 1, 1, 0, 'manual', 'predeterminado'),
(3, '1', 'Nota de Crédito A', 'A', 1, 0, 'A', 1, 1, 1, 'manual', 'predeterminado'),
(7, '1', 'Nota de Débito B', 'B', 1, 0, 'B', 1, 1, 0, 'manual', 'predeterminado'),
(8, '1', 'Nota de Crédito B', 'B', 1, 0, 'B', 1, 1, 1, 'manual', 'predeterminado');

# CAMBIAR EN CONFIGURACIONES
UPDATE configuraciones SET idtipoiva = '1';

# AJUSTAR PRODUCTOS MANTENIENDO EL PRECIO COMO PRECIOFINAL
UPDATE productos SET
	preciofinal = precio,
	idiva = 2,
	precio = preciofinal / 1.21,
	utilidad = ((precio / costo - 1) * 100);
