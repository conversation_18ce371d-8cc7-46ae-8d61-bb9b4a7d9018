# AYUDAS TEMPORALES

## Libre Office

=CONCAT("INSERT INTO participantes SET idevento = 562, estado='acreditado', idparticipante=";A57;", nombre='";B57;" ";C57;"', idcategoria=";E57;";")
=CONCAT("INSERT INTO lecturas SET idevento = 425, idcontrol = 1697, estado = 'ok', tipo = 'app', idparticipante = ";A9;", tiempo = '2021-02-24 04:00:00';")
SELECT idcategoria, nombre FROM participantes WHERE idevento = 565 GROUP BY idcategoria;

## Carreras

SELECT * FROM carreras WHERE idevento =

INSERT INTO carreras (idcarrera, idevento, nombre, largada, observacion, etapas) VALUES
(NULL, 35, '21K', '', NULL, 1),
(NULL, 35, '10K', '', NULL, 1),
(NULL, 35, 'Participativa 5K', '', NULL, 1);

## Categor<PERSON>

SELECT * FROM categorias WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 29)

INSERT INTO categorias (idcarrera, idevento, nombre, sexo, equipo, nacimiento_desde, nacimiento_hasta) VALUES
(7412, 1716, 'Caballeros - Juveniles (16-19 años)', 'masculino', '', '2004-02-18', '2008-02-18'),
(7412, 1716, 'Caballeros - Mayores (20-29 años)', 'masculino', '', '1994-02-18', '2004-02-18'),
(7412, 1716, 'Caballeros - Pre-veteranos (30-34 años)', 'masculino', '', '1989-02-18', '1994-02-18'),
(7412, 1716, 'Caballeros - Veteranos (35-39 años)', 'masculino', '', '1984-02-18', '1989-02-18'),
(7412, 1716, 'Caballeros - Veteranos A (40-44 años)', 'masculino', '', '1979-02-18', '1984-02-18'),
(7412, 1716, 'Caballeros - Veteranos B (45-49 años)', 'masculino', '', '1974-02-18', '1979-02-18'),
(7412, 1716, 'Caballeros - Veteranos C (50-54 años)', 'masculino', '', '1969-02-18', '1974-02-18'),
(7412, 1716, 'Caballeros - Veteranos D (55-59 años)', 'masculino', '', '1964-02-18', '1969-02-18'),
(7412, 1716, 'Caballeros - Veteranos E (60-64 años)', 'masculino', '', '1959-02-18', '1964-02-18'),
(7412, 1716, 'Caballeros - Veteranos F (+65 años)', 'masculino', '', '1900-02-18', '1959-02-18'),
(7412, 1716, 'Damas - Juveniles (16-19 años)', 'femenino', '', '2004-02-18', '2008-02-18'),
(7412, 1716, 'Damas - Mayores (20-29 años)', 'femenino', '', '1994-02-18', '2004-02-18'),
(7412, 1716, 'Damas - Pre-veteranos (30-34 años)', 'femenino', '', '1989-02-18', '1994-02-18'),
(7412, 1716, 'Damas - Veteranos (35-39 años)', 'femenino', '', '1984-02-18', '1989-02-18'),
(7412, 1716, 'Damas - Veteranos A (40-44 años)', 'femenino', '', '1979-02-18', '1984-02-18'),
(7412, 1716, 'Damas - Veteranos B (45-49 años)', 'femenino', '', '1974-02-18', '1979-02-18'),
(7412, 1716, 'Damas - Veteranos C (50-54 años)', 'femenino', '', '1969-02-18', '1974-02-18'),
(7412, 1716, 'Damas - Veteranos D (55-59 años)', 'femenino', '', '1964-02-18', '1969-02-18'),
(7412, 1716, 'Damas - Veteranos E (60-64 años)', 'femenino', '', '1959-02-18', '1964-02-18'),
(7412, 1716, 'Damas - Veteranos F (+65 años)', 'femenino', '', '1900-02-18', '1959-02-18');


## Etapas

SELECT * FROM etapas WHERE idcarrera IN (SELECT idcarrera FROM carreras WHERE idevento = 29)

INSERT INTO etapas (idcarrera, idevento, nombre) VALUES
(35, 100, 'Final 21K'),
(36, 100, 'Final 10K'),
(37, 100, 'Final Participativa 5K')

## Controles

SELECT * FROM controles WHERE idetapa IN
(SELECT idetapa FROM etapas WHERE idcarrera IN
(SELECT idcarrera FROM carreras WHERE idevento = 40));

INSERT INTO controles (idcontrol, idevento, codigo, idetapa, nombre, tipo, tipolectura) VALUES
(53, 100, 'BOS1', 38, 'Final', 'final', 'movil')


## DatosXEventos

SELECT * FROM datosxeventos WHERE idevento = 29;

INSERT INTO datosxeventos (iddato, idevento, obligatorio, orden) VALUES

## Lecturas

SELECT * FROM lecturas WHERE idcontrol IN
(SELECT idcontrol FROM controles WHERE idetapa IN
(SELECT idetapa FROM etapas WHERE idcarrera IN
(SELECT idcarrera FROM carreras WHERE idevento = 40)));

## Remeras

SELECT dato, COUNT(*) FROM datosxparticipantes WHERE iddato = 'talle' AND idinscripcion IN
    (SELECT idinscripcion FROM participantes WHERE idcategoria IN
        (SELECT idcategoria FROM categorias WHERE idevento = 476 AND sexo = 'masculino'))
GROUP BY dato

## OWA: COPIA DE ficha_ok según DNI

```sql
SELECT idevento, fecha, nombre, resultados_estilo FROM eventos WHERE idorganizacion = 45 ORDER BY fecha DESC;

UPDATE datosxparticipantes SET dato = REPLACE(dato,'.','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);
UPDATE datosxparticipantes SET dato = REPLACE(dato,' ','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);
UPDATE datosxparticipantes SET dato = REPLACE(dato,'-','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);
UPDATE datosxparticipantes SET dato = REPLACE(dato,'dni','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);
UPDATE datosxparticipantes SET dato = REPLACE(dato,'Dni','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);
UPDATE datosxparticipantes SET dato = REPLACE(dato,'DNI','') WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762);

SELECT dato FROM datosxparticipantes WHERE iddato = 'dni' AND idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762)
    AND idinscripcion IN (SELECT idinscripcion FROM participantes WHERE idevento IN (1473, 1472, 1471, 1470, 1467, 1435, 1438, 1425, 1762) AND (estado = 'acreditado' OR ficha_ok = 1))
GROUP BY dato
ORDER BY dato;

UPDATE participantes SET ficha_ok = 1 WHERE idinscripcion IN
(SELECT idinscripcion FROM datosxparticipantes WHERE idevento = 1473 AND iddato = 'dni' AND dato IN (

))
```

## PORCENTAJE LECTURA CHIPS

SELECT * FROM `controles` WHERE idevento = 1626 GROUP BY idcontrol;
SELECT COUNT(*) AS cantidad, timer FROM `lecturas` WHERE idcontrol = 12529 GROUP BY timer;
SELECT
(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0) AS total,
(SELECT COUNT(DISTINCT idparticipante) FROM lecturas WHERE idcontrol = 12529 AND idparticipante > 0 AND tipo = 'rfid') AS chips,
(SELECT chips * 100 / total) AS porcentaje;


## VIDEOS

INSERT INTO videos (`idevento`, `idcontrol`, `nombre`, `tipo`, `url`, `inicio`, `fin`) VALUES
(1425, 10707, 'Mirá tu llegada', 'youtube', 'https://www.youtube.com/watch?v=yC3cUSfXOFc', '2024-06-09 15:46:27.000', '2024-06-09 16:02:49.000');


## AGREGAR TAGS

TagID/EPC de los comprados
15k en 2024 en rollos: 000000001234 (con 8 ceros)
10k en 2025 en rollos: 000000000000000000001235 (con 20 ceros)

INSERT INTO tags SET estado=1, idorganizacion=173, codigo='', tagID='';
=CONCAT("INSERT INTO tags SET estado=1, idorganizacion=25, idevento=1275, codigo='";A1;"', tagID='00000000000000000000";A1;"';")

INSERT INTO tags SET idevento=1078, tagID='', estado=1, idorganizacion=242, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=1078 AND estado != 'eliminado' AND idparticipante = ''), 0));
=CONCAT("INSERT INTO tags SET idevento=2350, tagID='', estado=1, idorganizacion=519, codigo='', idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento=2350 AND estado != 'eliminado' AND idparticipante = ''), 0));")


UPDATE tags SET idevento = 1233, idinscripcion = (SELECT COALESCE((SELECT idinscripcion FROM participantes WHERE idevento = 1233 AND estado != 'eliminado' AND idparticipante = ''), 0)) WHERE codigo = 'COD001';

SELECT codigo, COUNT(*) AS cantidad FROM tags GROUP BY codigo;
SELECT tagID, COUNT(*) AS cantidad FROM tags GROUP BY tagID;

## AGREGAR PRECIOS

INSERT INTO plataformas (idorganizacion, titulo, plataforma, descripcion, `key`, secret) VALUES
(593, 'MercadoPago', 'mercadopago', 'Paga con todas las opciones de MercadoPago', 'APP_USR-169bf06c-e54f-4971-a15e-c56ee1a95457', 'APP_USR-7368465733403202-051219-2e58996441707a0da6a5f620924add55-549597078');

INSERT INTO precios (idprecio, idevento, idplataforma, precio, url) VALUES
(NULL, 2679, 19, '40000', ''),
(NULL, 2679, 19, '50000', '');

INSERT INTO precios (idprecio, idevento, idplataforma, precio, url) VALUES
(13, 1894, 8, 102.978, '<a class="button" href="https://mpago.la/2xpeazK" target="_blank">Pagar 42km Corredores Nacionales</a>'),
(14, 1894, 8, 84.409, '<a class="button" href="https://mpago.la/2ZauKmm" target="_blank">Pagar 21km Corredores Nacionales</a>'),
(15, 1894, 8, 65.855, '<a class="button" href="https://mpago.la/1FKy54C" target="_blank">Pagar 10km Corredores Nacionales</a>');

INSERT INTO preciosxcarreras (idevento, idprecio, idcarrera) VALUES
(1894, 13, 8962),
(1894, 14, 8963),
(1894, 15, 8979);


## LISTAR PAGOS TOTALES POR PRECIO Y PLATAFORMA

SELECT
pagos.fecha,
eventos.nombre AS Evento,
plataformas.key AS MP_Key,
participantes.nombre as Participante,
precios.precio as Total
FROM `pagos`
LEFT JOIN eventos ON pagos.idevento = eventos.idevento
LEFT JOIN precios ON pagos.idprecio = precios.idprecio
LEFT JOIN plataformas ON precios.idplataforma = plataformas.idplataforma
LEFT JOIN participantes ON pagos.idinscripcion = participantes.idinscripcion
WHERE pagos.idevento IN (2507, 2494)


## TOTALES ANUALES (Cantidades en el año)

SELECT idevento FROM `eventos` WHERE fecha > '2023-01-01' AND fecha < '2024-01-01';
SELECT
(SELECT COUNT(*) FROM lecturas WHERE idevento IN ()) AS lecturas,
(SELECT COUNT(*) FROM participantes WHERE idevento IN ()) AS participantes,
(SELECT COUNT(*) FROM eventos WHERE idevento IN ()) AS eventos,
(SELECT COUNT(DISTINCT idorganizacion) FROM eventos WHERE idevento IN ()) AS organizaciones,
(SELECT COUNT(DISTINCT iddisciplina) FROM eventos WHERE idevento IN ()) AS disciplinas,
(SELECT COUNT(DISTINCT idpais) FROM eventos WHERE idevento IN ()) AS paises;


## ELIMINAR EVENTO COMPLETO

## Limpiar eliminados
DELETE FROM datosxparticipantes WHERE idinscripcion IN
    (SELECT idinscripcion FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado');
DELETE FROM participantes WHERE idevento IN (1915, 1816, 1884, 2006, 1979) AND estado = 'eliminado';

## RESTAR HORAS

UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 1440 MINUTE) WHERE idcontrol = 12554;


## En Crono

DELETE FROM lecturas WHERE idcontrol IN
(SELECT idcontrol FROM controles WHERE idetapa IN
(SELECT idetapa FROM etapas WHERE idcarrera IN
 (SELECT idcarrera FROM carreras WHERE idevento IN (2043))));

DELETE FROM datosxparticipantes WHERE idevento IN (2043);
DELETE FROM participantes WHERE idevento IN (2043);

DELETE FROM datosxeventos WHERE idevento IN (2043);
DELETE FROM categorias WHERE idcarrera IN
    (SELECT idcarrera FROM carreras WHERE idevento IN (2043));
DELETE FROM controles WHERE idetapa IN
    (SELECT idetapa FROM etapas WHERE idcarrera IN
        (SELECT idcarrera FROM carreras WHERE idevento IN (2043)));
DELETE FROM etapas WHERE idcarrera IN
    (SELECT idcarrera FROM carreras WHERE idevento IN (2043));
DELETE FROM carreras WHERE idevento IN (2043);
DELETE FROM config_vivo WHERE idevento IN (2043);
DELETE FROM config_organizacion WHERE idevento IN (2043);
DELETE FROM config_cronometraje WHERE idevento IN (2043);
DELETE FROM eventos WHERE idevento IN (2043);

## En SaaS

DELETE FROM saas_161.productosxventas WHERE idventa IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));
DELETE FROM saas_161.ventasxclientes WHERE idtipoventa > 0 AND id IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));
DELETE FROM saas_161.ventasxventas WHERE idventa IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));
DELETE FROM saas_161.ventasxventas WHERE idrelacion IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043));
DELETE FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (2043);

## CAMBIAR ID DE EVENTO

### En Crono

UPDATE participantes SET idevento = 527 WHERE idevento = 530;
UPDATE datosxparticipantes SET idevento = 527 WHERE idevento = 530;
UPDATE categorias SET idevento = 527 WHERE idevento = 530;
UPDATE carreras SET idevento = 527 WHERE idevento = 530;
UPDATE eventos SET idevento = 527 WHERE idevento = 530;

### En SaaS

UPDATE saas_161.ventas SET idrelacion = 614 WHERE tiporelacion = 'servicio' AND idrelacion = 531;


## Últimos

SELECT
    (SELECT idorganizacion FROM organizaciones ORDER BY idorganizacion DESC LIMIT 1) AS idorganizacion,
    (SELECT idevento FROM eventos ORDER BY idevento DESC LIMIT 1) AS idevento,
    (SELECT idcarrera FROM carreras ORDER BY idcarrera DESC LIMIT 1) AS idcarrera,
    (SELECT idcategoria FROM categorias ORDER BY idcategoria DESC LIMIT 1) AS idcategoria,
    (SELECT idetapa FROM etapas ORDER BY idetapa DESC LIMIT 1) AS idetapa,
    (SELECT idcontrol FROM controles ORDER BY idcontrol DESC LIMIT 1) AS idcontrol


## VARIOS

SELECT idservicio, fechasolicitado, c.nombre AS cliente, titulo, cat.nombre AS categoria,
    (SELECT SUM(total) FROM ventas WHERE muevesaldo = 1 AND idrelacion = s.idservicio) AS total
FROM servicios AS s
    JOIN clientes AS c ON s.idcliente = c.idcliente
    JOIN categorias_servicios AS cat ON s.idtiposervicio = cat.idtiposervicio
WHERE idservicio > 10 AND s.estado != 5
ORDER BY idservicio

SELECT idevento, COUNT(*) AS cantidad
FROM participantes
WHERE estado IN ('inscripto', 'acreditado', 'descalificado', 'abandono')
GROUP BY idevento
ORDER BY idevento


#### SUMAR AÑOS A LA CATEGORIA

UPDATE categorias SET
nacimiento_desde = DATE_ADD(nacimiento_desde, INTERVAL 1 YEAR),
nacimiento_hasta = DATE_ADD(nacimiento_hasta, INTERVAL 1 YEAR)
WHERE idevento = 2301;

#### CONFIGURAR PUNTOS

UPDATE carreras SET
puntaje = 'categorias',
puntos = '25,21,18,16,14,13,12,11,10,9,8,7,6,5,4,3,2,1'
WHERE idevento = 2522;

##### CAMBIAR / MOVER EVENTO DE CLIENTE

UPDATE cronometrajeinstantaneo.eventos SET user_id = 456, idorganizacion = 456 WHERE idevento IN (807);

UPDATE saas_161.ventas SET idcliente = 456 WHERE tiporelacion = 'servicio' AND idrelacion IN (807);
UPDATE saas_161.ventaspagos SET idcliente = 456 WHERE idventa IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807));
UPDATE saas_161.ventasxclientes SET idcliente = 456 WHERE idtipoventa = 4 AND id IN
    (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807));

UPDATE saas_161.ventasxclientes SET idcliente = 456 WHERE idtipoventa = 0 AND id IN
    (SELECT idventapago FROM saas_161.ventaspagos WHERE idventa IN
        (SELECT idventa FROM saas_161.ventas WHERE tiporelacion = 'servicio' AND idrelacion IN (807)));

##### RECUPERAR LECTURAS

UPDATE lecturas_archivadas SET idevento = (SELECT idevento FROM carreras WHERE idcarrera = (SELECT idcarrera FROM etapas WHERE idetapa = (SELECT idetapa FROM controles WHERE controles.idcontrol = lecturas_archivadas.idcontrol)))


##### NUEVO USUARIO ####

## Datos para completar

Organización: DH Chorde
Usuario: <EMAIL>
Contraseña: Dhchorde
Cronometrador: Ecuador
Pais: Ecuador
idorganizacion = 388

## En SaaS

INSERT INTO saas_161.clientes SET
    nombre = 'DH Chorde',
    mail = '<EMAIL>',
    contacto = 'Ecuador',
    idlocalidad = (SELECT idlocalidad FROM saas_161.categorias_localidades WHERE nombre = 'Ecuador' LIMIT 1),
    obsinterna = '<p>
Usuario: <EMAIL><br>
Contraseña: Dhchorde
</p>';

## En Crono

INSERT INTO cronometrajeinstantaneo.organizaciones SET
    idorganizacion = 388,
    pass = md5('Dhchorde'),
    nombre = 'DH Chorde',
    idpais = (SELECT id FROM cronometrajeinstantaneo.paises WHERE nombre_es = 'Ecuador'),
    estado = 1,
    mail = '<EMAIL>';

$new=new App\Models\User();$new->id=388;$new->email="<EMAIL>";$new->password=Hash::make("Dhchorde");$new->save();


## RECUPERAR CONTRASEÑA UPDATE PASSWORD
https://admin.cronometrajeinstantaneo.com/forgot-password
$user = App\Models\User::where('id', 313)->update(['password' => Hash::make('backup')]);


## HORARIOS DE LARGADAS

Para la que genera sólo el informe, pueden ir a Resultados > Etapas y elegir la etapa de la Qualy y abrir ese informe (puede ser por categorías o no). Luego agregarle al final de la url lo siguiente: &largadas=32400_60_180 que significa 32400 son los segundos del día para las 9 de la mañana, 60 segundos son entre cada participante y 180 segundos entre cada categoría. No está fácil, pero la idea es que se va a generar solo desde una configuración
Por ej:
https://cronometrajeinstantaneo.com/resultados/baja-chiara-300/etapas?idetapas=13880,13881&largadas=36000_60_60