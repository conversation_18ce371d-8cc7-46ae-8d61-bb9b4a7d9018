# Ver todos los pendientes

SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha;

# Arranco seleccionando idevento

SELECT idevento, eventos.nombre, fecha, organizaciones.nombre FROM `eventos` LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion WHERE (eventos.idpais = 7 OR organizaciones.idpais = 7 OR eventos.idorganizacion IN (151)) AND estado_pago = 'pendiente' ORDER BY fecha LIMIT 6;

# Controlo que se deban en SaaS

SELECT * FROM ventas WHERE idrelacion IN (2393, 2394, 2448, 2251, 2456, 2391)

# Poner como pagado en Crono y avisar

UPDATE eventos SET estado_pago = 'aprobado' WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)

# Generar la consulta para aprobar y cargar pagos

# Por ahora cargué demás, solo hay que restar en la caja

# SELECT CONCAT("https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=", idevento) FROM eventos WHERE idevento IN (2393, 2394, 2448, 2251, 2456, 2391)

('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2251'),
('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2391'),
('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2393'),
('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2394'),
('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2448'),
('https://scripts.saasargentina.com/?script=1&a=nuevo_pago&forma=paypal&idservicio=2456');