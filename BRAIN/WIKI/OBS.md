# OBS STUDIO

## VERSION

En Debian el repo te instala la versión 29, pero instalé desde la web oficial https://obsproject.com/es la versión 30 en Flatpak desde Flathub https://flathub.org/apps/com.obsproject.Studio

## PARA GRABAR EN LLEGADAS

**Para un sólo celular**

- Instalar en el driver en OBS y la App en Android desde https://droidcam.app/obs/
- Agregar la cámara como DroidCAM y grabar

Luego se puede ver este archivo en VLC. Para verlo mientras sigue grabando, conviene copiar y pegar el archivo y ver la copia.

**Para grabar con más cámaras**

Se tiene que instalar otro plugin que se llama source-record pero no funciona bien por el momento en Linux: https://obsproject.com/forum/resources/source-record.1285


## GRABAR CON WIFI

- Hay otra opción más rápida para grabar con el celular que puede ser por fuera de OBS, y es utilizando vdo.ninja. Entrando al link se genera un link para subir tipo `https://vdo.ninja/?push=AuC3c7k` que se puede consumir desde el link `https://vdo.ninja/?view=AuC3c7k`


## CONFIGURACIÓN DE OBS

- Primero configurar en *Video* la resolución de la pantalla en la pestaña de video (Lienzo), resolución de salida y FPS, teniendo en cuenta la resolución de la cámara
- Luego en *Salida* seleccionar Avanzado para ver más opciones y ahí:
  - La ruta de Salida
  - El formato como MKS o MP4 Fragmentado (es importante para poder verlo mientras se graba)
  - Activar el *Dividir archivo automáticamente* para que se divida en archivos de 30 minutos
  - Subir la *Tasa de bits* a 30k bps para que se vea mejor
- Definir el tamaño de la pantalla
- Agregar Widget de Meta si se puede y logos
- Probar grabación
