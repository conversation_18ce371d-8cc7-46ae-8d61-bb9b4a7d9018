# HACER BACKUP MANUAL DE LA BASE COMPLETA

SET SQL_MODE="NO_AUTO_VALUE_ON_ZERO";

# LAS CAJAS HAY QUE ACOMODARLAS A MANO, DEJANDO UNA ABIERTA POR CADA CATEGORÍA DE CAJA
UPDATE cajas SET fechaapertura = NOW(), fechacierre = '0000-00-00 00:00:00', saldocierre = 0;

# CUANDO HAY QUE RESETEARLA
TRUNCATE cajas;
INSERT INTO `cajas` (`idcaja`, `idtipocaja`, `fechaapertura`) VALUES
(1, 1, NOW()),
(2, 2, NOW()),
(3, 3, NOW()),
(4, 4, NOW());

TRUNCATE categorias_cajas;
INSERT INTO `categorias_cajas` (`idtipocaja`, `nombre`, `compartida`, `estado`, `idcaja`, `tipo`) VALUES
(1, 'Caja principal', 1, 1, 1, 'efectivo'),
(2, 'Banco', 1, 1, 2, 'banco'),
(3, 'Cartera de cheques', 1, 1, 3, 'cheque'),
(4, 'Retenciones', 1, 1, 4, 'retencion');


TRUNCATE movimientosxcajas;
TRUNCATE cheques;
TRUNCATE retenciones;

# REVISAR LOS ARCHIVOS QUE TAMBIÉN HAY QUE HACERLO A MANO
# TRUNCATE archivos;

# VACIAR TABLAS VARIAS
TRUNCATE bienes;
TRUNCATE bienesxservicios;
TRUNCATE servicios;
# TRUNCATE conocimientos;

# VACIAR COMPRAS
TRUNCATE compras;
TRUNCATE ivasxcompras;
TRUNCATE tributosxcompras;
TRUNCATE compraspagos;
TRUNCATE comprasxcompras;
TRUNCATE comprasxproveedores;
TRUNCATE productosxcompras;

# VACIAR VENTAS
TRUNCATE ventas;
TRUNCATE ivasxventas;
TRUNCATE tributosxventas;
TRUNCATE ventaspagos;
TRUNCATE ventasxventas;
TRUNCATE ventasxclientes;
TRUNCATE productosxventas;

# PONER STOCK EN CERO
UPDATE stock SET stockactual='0';

# VACIAR EL RESTO
# TRUNCATE productos;
# TRUNCATE precios;
# TRUNCATE stock;
# TRUNCATE clientes;
# INSERT INTO `clientes` (`idcliente`, `idtipocliente`, `estado`, `nombre`, `contacto`, `telefonos`, `domicilio`, `idlocalidad`, `mail`, `idtipoiva`, `razonsocial`, `cuit`, `pass`, `observacion`, `obsinterna`, `obsrecordatorio`) VALUES (1, 0, 1, 'Consumidor final', '', '', '', 0, '', 0, '', 0, '', '', '', '');
# TRUNCATE proveedores;
# INSERT INTO `proveedores` (`idproveedor`, `nombre`, `contacto`, `telefonos`, `domicilio`, `idlocalidad`, `mail`, `idtipoiva`, `razonsocial`, `cuit`, `obsinterna`) VALUES (0, 'Sin especificar', '', '', '', 0, '', 0, '', 0, ''), (1, 'Proveedor sin registrar', '', '', '', 0, '', 0, '', 0, '');

# VACIAR FULLSEARCH Y EJECUTARLO A MANO!!!!
TRUNCATE fullsearch;
TRUNCATE saldos;
