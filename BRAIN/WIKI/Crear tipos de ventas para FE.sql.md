#El script es para el punto de venta 2, cambiar de ser necesario

INSERT INTO `categorias_ventas` (`idtipoventa`, `idcomportamiento`, `estado`, `nombre`, `letra`, `puntodeventa`, `ultimonumero`, `discrimina`, `mue<PERSON><PERSON>`, `mue<PERSON><PERSON>`, `operacioninversa`, `tipofacturacion`, `tipoimpresion`, `estilo_venta`, `observacion`, `obsinterna`) VALUES
(NULL, '1', '1', 'Factura Electrónica A', 'A', '2', '0', 'A', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '2', '1', 'Nota de Débito Electrónica A', 'A', '2', '0', 'A', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '3', '1', 'Nota de Crédito Electrónica A', 'A', '2', '0', 'A', '0', '1', '1', 'electronico', 'predeterminado', '', '', ''),
(NULL, '6', '1', 'Factura Electrónica B', 'B', '2', '0', 'B', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '7', '1', 'Nota de Débito Electrónica B', 'B', '2', '0', 'B', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '8', '1', 'Nota de Crédito Electrónica B', 'B', '2', '0', 'B', '0', '1', '1', 'electronico', 'predeterminado', '', '', '');

#El script es para el punto de venta 3, cambiar de ser necesario

INSERT INTO `categorias_ventas` (`idtipoventa`, `idcomportamiento`, `estado`, `nombre`, `letra`, `puntodeventa`, `ultimonumero`, `discrimina`, `muevestock`, `muevesaldo`, `operacioninversa`, `tipofacturacion`, `tipoimpresion`, `estilo_venta`, `observacion`, `obsinterna`) VALUES
(NULL, '1', '1', 'Factura Electrónica A', 'A', '3', '0', 'A', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '2', '1', 'Nota de Débito Electrónica A', 'A', '3', '0', 'A', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '3', '1', 'Nota de Crédito Electrónica A', 'A', '3', '0', 'A', '0', '1', '1', 'electronico', 'predeterminado', '', '', ''),
(NULL, '6', '1', 'Factura Electrónica B', 'B', '3', '0', 'B', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '7', '1', 'Nota de Débito Electrónica B', 'B', '3', '0', 'B', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '8', '1', 'Nota de Crédito Electrónica B', 'B', '3', '0', 'B', '0', '1', '1', 'electronico', 'predeterminado', '', '', '');

#El script es para el punto de venta 2, cambiar de ser necesario

INSERT INTO `categorias_ventas` (`idtipoventa`, `idcomportamiento`, `estado`, `nombre`, `letra`, `puntodeventa`, `ultimonumero`, `discrimina`, `muevestock`, `muevesaldo`, `operacioninversa`, `tipofacturacion`, `tipoimpresion`, `estilo_venta`, `observacion`, `obsinterna`) VALUES
(NULL, '11', '1', 'Factura Electrónica C', 'C', '2', '0', 'C', '1', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '12', '1', 'Nota de Débito Electrónica C', 'C', '2', '0', 'C', '0', '1', '0', 'electronico', 'predeterminado', '', '', ''),
(NULL, '13', '1', 'Nota de Crédito Electrónica C', 'C', '2', '0', 'C', '1', '1', '1', 'electronico', 'predeterminado', '', '', '');