Mover saldos de clientes desde 5548 a 12447

1. Correr el script de saldos

```bash
./command.php prod script saldos.php idempresas 5548 clientes
```

2. Duplicar los clientes comparando por ids

3. Generar un tipo de venta Saldo positivo (el idtipoventa lo pongo a mano)

```mysql
INSERT INTO categorias_ventas (idtipoventa, idcomportamiento, nombre, letra, puntodeventa, discrimina, muevesaldo, operacioninversa) VALUES
    (21, 115, 'Saldo Anterior', 'C', 1, 'C', 1, 0),
    (22, 116, 'Saldo Anterior a favor', 'C', 1, 'C', 1, 1);
```
4. Generar un archivo de saldos positivos y otro de saldos negativos

```mysql
SET @correlativo = 12;
SELECT @correlativo := @correlativo + 1 AS  idventa, idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion;
```
5. Generar las queries para agregar las ventas copiando y pegando las values en cada insert into

```mysql
SET @numero = 0;
SET @correlativo = 12;
SET @idtipoventa = 21;

SELECT CONCAT("(",
    @correlativo := @correlativo + 1, ", ",
    @idtipoventa, ", ",
    @numero := @numero + 1, ", ",
    "NOW()", ", ",
    idrelacion, ", ",
    saldo, ", ", saldo, ", ", saldo, ", ",
    "'cerrado', 0, 99, 1, 0, 'Generado automáticamente para migrar saldo anterior'),") AS insert_value
FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;

INSERT INTO ventas (idventa, idtipoventa, numero, fecha, idcliente, subtotal, nogravado, total, estado, idtipoiva, tipodoc, muevesaldo, operacioninversa, observacion) VALUES
```

```mysql
SET @correlativo = 12;

SELECT CONCAT("(",
    @correlativo := @correlativo + 1, ", ",
    saldo, ", ", saldo, ", ",
    "1, 'Saldo anterior'),") AS insert_value
FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;

INSERT INTO productosxventas (idventa, precio, preciofinal, cantidad, nombre) VALUES
```

```mysql
SET @numero = 0;
SET @correlativo = 12;
SET @idtipoventa = 21;

SELECT CONCAT("(",
    @idtipoventa, ", ",
    idrelacion, ", ",
    @correlativo := @correlativo + 1, ", ",
    "NOW()", ", ",
    saldo, ", ",
    "'R00001-0000", @numero := @numero + 1, "'),") AS insert_value
FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 ORDER BY idrelacion LIMIT 500;

INSERT INTO ventasxclientes (idtipoventa, idcliente, id, fecha, total, numero) VALUES
```

6. Hacer lo mismo para los clientes con saldo negativo

7. Correr los saldos en la instancia destino

```bash
./command.php prod script saldos.php idempresas 12447 clientes
```
8. Actualizar los últimos números en categorias_ventas y deshabilito

```mysql
UPDATE categorias_ventas SET estado = 0, ultimonumero = 313 WHERE idtipoventa = 21;
UPDATE categorias_ventas SET estado = 0, ultimonumero = 511 WHERE idtipoventa = 22;
```