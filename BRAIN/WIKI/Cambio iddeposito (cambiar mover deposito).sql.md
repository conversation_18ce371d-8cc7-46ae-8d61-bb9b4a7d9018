UPDATE depositos SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE depositos SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE depositos SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE categorias_proveedores SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE categorias_proveedores SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE categorias_proveedores SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE categorias_ventas SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE categorias_ventas SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE categorias_ventas SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE compras SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE compras SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE compras SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE ventas SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE ventas SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE ventas SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE stock SET iddeposito = 8 WHERE iddeposito = 1;
UPDATE stock SET iddeposito = 1 WHERE iddeposito = 5;
UPDATE stock SET iddeposito = 5 WHERE iddeposito = 8;
UPDATE traslados SET iddepositoinicio = 8 WHERE iddepositoinicio = 1;
UPDATE traslados SET iddepositoinicio = 1 WHERE iddepositoinicio = 5;
UPDATE traslados SET iddepositoinicio = 5 WHERE iddepositoinicio = 8;
UPDATE traslados SET iddepositofin = 8 WHERE iddepositofin = 1;
UPDATE traslados SET iddepositofin = 1 WHERE iddepositofin = 5;
UPDATE traslados SET iddepositofin = 5 WHERE iddepositofin = 8;
