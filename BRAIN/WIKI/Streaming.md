El sistema cuenta con un widgets que generan información para utilizar en softwares de streaming para TV y redes sociales (OBS Studio, vMix, Wirecast, Streamlabs OSB, etc.). Les cuento y paso los enlaces a cada uno de dichos widgets. Todos tienen un chroma key #00FF00

Generalmente mostramos un reloj con el tiempo del que está en carrera y además el podio al terminar cada categoría. Con respecto al reloj, tenemos distintas opciones que pueden agregar:
- Pre-largada: si van a tener cámara en la largada y tiempo para filmar antes de que salgan. Lo usamos en los DH Urbanos de RedBull donde se sigue al corredor en todo el recorrido y no hay nunca 2 ciclistas en pista
- Pre-llegada: este es útil para poner un control unos 200m o 300m antes de la llegada y así asegurar que el que aparece en pantalla es realmente el que viene y no un resagado
- Parcial: este lo usamos para algún punto intermedio y detiene el reloj unos 30 segundos en la mitad de la pista.
También recomiendo si no van a tener pre-llegada, que haya alguien con handy para anunciar quien viene. Esto es porque el momento de tomar el tiempo en la llegada, si el operador comete un error (este domingo hubo un caso), sale mal la información en pantalla. Si bien los tiempos se corrigen y no hay problemas en la clasificación, queda feo en pantalla eso

Tengo entendido que vamos sólo el reloj con tiempo en carrera y tiempo de llegada (les comento las opciones para que sepan que se puede hacer) y también el widget del Podio.

*RELOJ*

Entonces ese widget va a estar en el siguiente enlace:

https://admin.cronometrajeinstantaneo.com/vivos/red-bull-carros-locos/reloj

*PODIO*

Además de eso, cuando termina cada categoría, está bueno mostrar como quedó y mejor si la muestra en pantalla de esta ventana está coordinada con el locutor. Para eso, tenemos el siguiente widget:

https://admin.cronometrajeinstantaneo.com/vivos/red-bull-carros-locos/podio

---

Además les paso esta información para que tengan en cuenta.

*ADMINISTRACIÓN*

Para tener en cuenta hay 2 cosas. Una es que se puede cambiar las categorías que se muestran en pantallas, en el sistema hay que entrar a *Configuración > Streaming > Opciones*

La otra es que el reloj muestra el tiempo en carrera del que tendría que llegar, pero hay casos de sobrepasos o caídas y ahí llega otro participante. Para sacar a ese participante del reloj, se lo puede pasar a DNF en el Panel de Control.

*SINCRONIZACIÓN*

En el reloj, se utiliza la hora de la PC que está transmitiendo, para calcular el tiempo en carrera y por eso es MUY IMPORTANTE que esté sincronziada con el reloj de los celulares que toman largada y llegada. No afecta el cronometraje pero si se ve mal la información en pantalla queda muy feo.

*ERRORES DE CRONOMETRAJE*

Estamos acostumbrados a que podemos corregir cualquier error en el cronometraje en la app y es muy fácil. Pero cuando estamos haciendo streaming, cada error que cometemos se ve en pantalla y queda feo. Es muy importante que los operadores de las fotocélulas NO SE EQUIVOQUEN, especialmente en la llegada. Lo ideal es poner alguien a 200m que informe por handy/radio que número de bici es el que realmente está llegando.

*DISEÑO DE PANTALLAS*

El sistema tiene para que podamos hacer cualquier diseño con CSS, pero la verdad es que lo mejor es poner una imagen de fondo y acomodar el tiempo por sobre esa imagen. Les voy a pasar un par de ejemplos para que puedan basarse y generar el diseño.