UPDATE servicios SET idcliente = 1 WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0);
UPDATE bienes SET idcliente = 1 WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0);

DELETE FROM productosxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0));
DELETE FROM ventasxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0));
DELETE FROM ventasxventas WHERE idrelacion IN (SELECT idventa FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0));
DELETE FROM ivasxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0));
DELETE FROM tributosxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0));

DELETE FROM ventas WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0);
DELETE FROM ventaspagos WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0);
DELETE FROM ventasxclientes WHERE idcliente IN (SELECT idcliente FROM clientes WHERE idtipocliente = 0);

DELETE FROM clientes WHERE idtipocliente = 0;
# EJECUTAR EL FULLSEARCH Y SALDOS
