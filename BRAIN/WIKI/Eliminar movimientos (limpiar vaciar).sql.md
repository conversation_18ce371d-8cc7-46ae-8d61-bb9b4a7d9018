#*******************************************************************************
# SI ES PARA BORRAR TODOS LOS MOVIMIENTOS
#*******************************************************************************
# REVISAR LOS ARCHIVOS
UPDATE categorias_ventas SET ultimonumero = 0;

TRUNCATE compras;
TRUNCATE productosxcompras;
TRUNCATE comprasxcompras;
TRUNCATE compraspagos;
TRUNCATE comprasxproveedores;
TRUNCATE ivasxcompras;
TRUNCATE tributosxcompras;

TRUNCATE ventas;
TRUNCATE productosxventas;
TRUNCATE ventasxventas;
TRUNCATE ventaspagos;
TRUNCATE ventasxclientes;
TRUNCATE ivasxventas;
TRUNCATE tributosxventas;

TRUNCATE movimientosxcajas;
TRUNCATE cheques;
# LAS CAJAS HAY QUE ACOMODARLAS A MANO, DEJANDO UNA ABIERTA POR CADA CATEGORÍA DE CAJA
UPDATE cajas SET fechaapertura = NOW(), fechacierre = '0000-00-00', saldoapertura = 0, saldocierre = 0, observacion = '';

TRUNCATE servicios;
TRUNCATE bienes;
TRUNCATE bienesxservicios;
TRUNCATE categorias_tributos;

DELETE FROM categorias_bienes WHERE idtipobien > 1;
ALTER TABLE categorias_bienes AUTO_INCREMENT =1;

DELETE FROM categorias_conocimientos WHERE idtipoconocimiento > 0;
ALTER TABLE categorias_conocimientos AUTO_INCREMENT = 1;

DELETE FROM categorias_localidades WHERE idlocalidad > 0;
ALTER TABLE categorias_localidades AUTO_INCREMENT = 1;

DELETE FROM categorias_proveedores WHERE idtipoproveedor > 0;
ALTER TABLE categorias_proveedores AUTO_INCREMENT = 1;

DELETE FROM categorias_rubros WHERE idrubro > 0;
ALTER TABLE categorias_rubros AUTO_INCREMENT = 1;

DELETE FROM categorias_servicios WHERE idtiposervicio > 0;
ALTER TABLE categorias_servicios AUTO_INCREMENT = 1;



DELETE FROM clientes WHERE idcliente > 1;
ALTER TABLE `clientes` AUTO_INCREMENT =1;
DELETE FROM proveedores WHERE idproveedor > 1;
ALTER TABLE `proveedores` AUTO_INCREMENT =1;

TRUNCATE productos;
TRUNCATE productosxcombos;
TRUNCATE stock;
TRUNCATE precios;

TRUNCATE datosxextras;

TRUNCATE mensajes;
TRUNCATE tareas;

TRUNCATE fullsearch;
TRUNCATE saldos;
# RENOVAR FULLSEARCH
# RENOVAR SALDOS


#*******************************************************************************
# ELIMINAR COMPRAS, VENTAS Y MOVIMIENTOS DE CAJAS HASTA UNA FECHA
#*******************************************************************************
# VACIAR COMPRAS
DELETE FROM productosxcompras WHERE idcompra IN (SELECT idcompra FROM compras WHERE fecha < '2020-01-01');
DELETE FROM comprasxcompras WHERE idcompra IN (SELECT idcompra FROM compras WHERE fecha < '2020-01-01');
DELETE FROM comprasxcompras WHERE idrelacion IN (SELECT idcompra FROM compras WHERE fecha < '2020-01-01');
DELETE FROM ivasxcompras WHERE idcompra IN (SELECT idcompra FROM compras WHERE fecha < '2020-01-01');
DELETE FROM tributosxcompras WHERE idcompra IN (SELECT idcompra FROM compras WHERE fecha < '2020-01-01');

DELETE FROM compras WHERE fecha < '2020-01-01';
DELETE FROM compraspagos WHERE fecha < '2020-01-01';
DELETE FROM comprasxproveedores WHERE fecha < '2020-01-01';

UPDATE compraspagos SET idcompra = '0' WHERE NOT EXISTS (SELECT compras.idcompra FROM compras WHERE compras.idcompra = compraspagos.idcompra);

# VACIAR VENTAS
DELETE FROM productosxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE fecha < '2020-01-01');
DELETE FROM ventasxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE fecha < '2020-01-01');
DELETE FROM ventasxventas WHERE idrelacion IN (SELECT idventa FROM ventas WHERE fecha < '2020-01-01');
DELETE FROM ivasxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE fecha < '2020-01-01');
DELETE FROM tributosxventas WHERE idventa IN (SELECT idventa FROM ventas WHERE fecha < '2020-01-01');

DELETE FROM ventas WHERE fecha < '2020-01-01';
DELETE FROM ventaspagos WHERE fecha < '2020-01-01';
DELETE FROM ventasxclientes WHERE fecha < '2020-01-01';

UPDATE ventaspagos SET idventa = '0' WHERE NOT EXISTS (SELECT ventas.idventa FROM ventas WHERE ventas.idventa = ventaspagos.idventa);

# LAS CAJAS HAY QUE ACOMODARLAS A MANO, DEJANDO UNA ABIERTA POR CADA CATEGORÍA DE CAJA
DELETE FROM movimientosxcajas WHERE fecha < '2020-01-01';

# SERVICIOS
DELETE FROM servicios WHERE fechasolicitado < '2020-01-01';



#*******************************************************************************
# ELIMINAR PRODUCTOS
#*******************************************************************************

DELETE FROM historial WHERE idproducto IN ();
DELETE FROM stock WHERE idproducto IN ();
DELETE FROM precios WHERE idproducto IN ();
DELETE FROM productosxcombos WHERE idproducto IN ();
DELETE FROM productosxcombos WHERE idcombo IN ();
UPDATE productosxventas SET idproducto = 0 WHERE idproducto IN ();
UPDATE productosxcompras SET idproducto = 0 WHERE idproducto IN ();
UPDATE productosxtraslados SET idproducto = 0 WHERE idproducto IN ();
DELETE FROM productos WHERE idproducto IN ();
DELETE FROM datosxextras WHERE idextraxmodulo IN (SELECT idextraxmodulo FROM extrasxmodulos WHERE modulo = 'productos') AND idrelacion IN ();
SELECT * FROM archivos WHERE modulo = 'productos' AND id IN ();

DELETE FROM historial WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
DELETE FROM stock WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
DELETE FROM precios WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
DELETE FROM productosxcombos WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
DELETE FROM productosxcombos WHERE idcombo IN ( SELECT idproducto FROM productos WHERE codigo IN () );
UPDATE productosxventas SET idproducto = 0 WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
UPDATE productosxcompras SET idproducto = 0 WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
UPDATE productosxtraslados SET idproducto = 0 WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
DELETE FROM productos WHERE idproducto IN ( SELECT idproducto FROM productos WHERE codigo IN () );
SELECT * FROM archivos WHERE modulo = 'productos' AND id IN ( SELECT idproducto FROM productos WHERE codigo IN () );