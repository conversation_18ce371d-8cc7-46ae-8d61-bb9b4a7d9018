**Spotify sites:**

[Obscurify](https://obscurifymusic.com/#!/): Tells you how unique you music taste is in compare to other Obscurify users. Also shows some recommendations. Mobile friendly.

[Skiley](https://www.skiley.net/): Web app to better manage your playlists and discover new music. This has so many functions and really the only thing I miss is search field for when you are managing playlists. You can take any playlist you "own" and order it by many different rules (track name, album name, artist name, BPM, etc.), or just randomly shuffle it (say bye to bad Spotify shuffle). You can also normalize it. For the other functions you don't even need the rights to edit the playlist. Those consists of splitting playlist, filtering out song by genre or year to new playlist, creating similar playlists or exporting it to CFG, CSV, JSON, TXT or XML.

You can also use it to discover music based on your taste and it has a stats section - data different from [Last.fm](http://Last.fm).

Also, dark mode and mobile friendly.

[Sort your music](http://sortyourmusic.playlistmachinery.com/index.html): Lets you sort your playlist by all kinds of different parameters such as BPM, artist, length and more. Similar to <PERSON><PERSON>, but it works as an interactive table with songs from selected playlist.

[Run BPM](https://runbpm.app/connect): Filters playlists based on parameters like BPM, Energy, etc. Great visualized with colorful sliders. Only downside - shows not even half of my playlists. Mobile friendly.

[Fylter.in](https://www.fylter.in/): Sort playlist by BMP, loudness, length, etc and export to Spotify

[Spotify Charts](https://spotifycharts.com/): Daily worldwide charts from Spotify. Mobile friendly

[Kaleidosync](https://kaleidosync-beta.herokuapp.com/): Spotify visualizer. I would personally add epilepsy warning.

[Duet](http://duetwith.me/): Darthmouth College project. Let's you compare your streaming data to other people. Only downside is, those people need to be using the site too, so you have to get your friends to log in. Mobile friendly.

[Discover Quickly](https://discoverquickly.com/): Select any playlist and you will be welcomed with all the songs in a gridview. Hover over song to hear the best part. Click on song to dig deeper or save the song.

[Dubolt](https://dubolt.com/): Helps you discover new music. Select an artist/song to view similar ones. Adjust result by using filters such as tempo, popularity, energy and others.

[Stats for Spotify](https://www.statsforspotify.com/): Shows you Top tracks and Top artists, lets you compare them to last visit. Data different from [Last.fm](http://Last.fm). Mobile friendly

[Record Player](https://record-player.glitch.me/): This site is crazy. It's a [Rube Goldberg Machine](https://en.wikipedia.org/wiki/Rube_Goldberg_machine). You take a picture (any picture) Google Cloud Vision API will guess what it is. The site than takes Google's guess and use it to search Spotify giving you the first result to play. Mobile friendly.

_Author of this site has to pay for the Google Cloud if the site gets more than 1000 requests a month! I assume this post is gonna blow up and the limit will be easily reached. Author suggests to remix the app and set it up with your own Google Cloud to avoid this. If your are able to do so, do it please. Or reach out to the author on Twitter and donate a little if you can._

[Spotify Playlist Randomizer](https://stevenaleong.com/tools/spotifyplaylistrandomizer): Site to randomize order of the songs in playlist. There are 3 shuffling methods you can choose from. Mobile friendly.

[Replayify](https://replayify.com/): Another site showing you your Spotify data. Also lets you create a playlist based on preset rules that cannot be changed (Top 5 songs by Top 20 artists from selected time period/Top 50 songs from selected time period). UI is nice and clean. Mobile friendly, data different from [Last.fm](http://Last.fm).

[Visualify](https://visualify.io/): SImpler replayify without the option to create playlists. Your result can be shared with others. Mobile friendly, data different from [Last.fm](http://Last.fm).

[Playedmost](https://playedmost.com/): Site showing your Spotify data in nice grid view. Contains Top Artists, New Artists, Top Tracks and New Tracks. Data different from [Last.fm](http://Last.fm), mobile friendly.

[musictaste.space](https://musictaste.space/): Shows you some stats about your music habits and let's you compare them to others. You can also create Covid-19 playlist :)

[Playlist Manager](http://playlist-manager.com/): Select two (or more) playlists to see in a table view which songs are shared between them and which are only in one of them. You can add songs to playlists too.

[Boil the Frog](http://boilthefrog.playlistmachinery.com/): Choose two artists and this site will create playlists that slowly transitions between one artist's style to the other.

[SpotifyTV](https://immannino.github.io/SpotifyTelevision/login): Great tool for searching up music videos of songs in your library and playlists.

[Spotify Dedup](https://jmperezperez.com/spotify-dedup/) and [Spotify Organizer](https://hyldmo.github.io/spotify-organizer/): Both do the same - remove duplicates. Spotify Dedup is mobile friendly.

[Smarter Playlists](http://smarterplaylists.playlistmachinery.com/index.html): It lets you build a complex program by assembling components to create new playlists. This seems like a very complex and powerful tool.

[JBQX](https://www.jqbx.fm/): Do you remember [plug.dj](http://plug.dj)? Well this is same thing, only using Spotify instead of YouTube as a source for music. You can join room and listen to music with other people, you all decide what will be playing, everyone can add a song to queue.

[Spotify Buddy](https://spotifybuddy.com/home): Let's you listen together with other people. All can control what's playing, all can listen on their own devices or only one device can be playing. You don't need to have Spotify to control the queue! In my opinion it's great for parties as a wireless aux cord. Mobile friendly.

[Whisperify](https://whisperify.net/): Spotify game! Music quiz based on what you are listening to. Do you know your music? Mobile friendly.

[Popularity Contest](https://playpopcon.com/): Another game. Two artists, which one is more popular according to Spotify data? Mobile friendly, doesn't require Spotify login.

**Spotify Apps:**

[uTrack](https://play.google.com/store/apps/details?id=com.gmail.jbillstromdevelopment.utrack): Android app which generates playlist from your top tracks. Also shows top artists, tracks and genres - data different from [Last.fm](http://Last.fm).

[Statistics for Spotify](https://apps.apple.com/at/app/statistics-for-spotify/id1507964052?l=en): uTrack for iOS. I don't own iOS device so I couldn't test it. iOS users, share your opinions in comments please :).

**Spotify Programs:**

[Spicetify](https://github.com/khanhas/Spicetify): Spicetify used to be a skin for [Rainmeter](https://www.rainmeter.net/). You can still use it as such, but the development is discontinued. You will need to have Rainmeter installed if you want to try. These days it works as a series of PowerShell commands. [New and updated version here](https://github.com/khanhas/spicetify-cli). Spicetify lets you redesign Spotify desktop client and add new functions to it like Trash Bin, Shuffle+, Christian Mode etc. It doesn't work with MS Store app, .exe Spotify client is required.

[Library Bridger](https://github.com/Iztral/Library-Bridger-2.0): The main purpose of this program is to create Spotify playlists from your locally saved songs. But it has some extra functions, check the link.