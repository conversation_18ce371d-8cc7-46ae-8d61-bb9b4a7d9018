# LINUX COMMANDS

## COMPOSER

composer install --prefer-dist --optimize-autoloader --no-dev

--prefer-dist: preferir descargar paquetes como archivos comprimidos (dist) en lugar de clonar el repositorio desde su sistema de control de versiones (source)
--optimize-autoloader: optimiza el autoloader durante la instalación
--no-dev: no instale las dependencias que de "require-dev"

## VARIOS SUELTOS

- Folder disk space: du -hs /home/
- sudo du -h / | grep '[0-9\.]\+G' > espacio

- ifconfig is now: ip a (Try: ip -s -c -h a)
- traceroute cronometrajeinstantaneo.com
- curl ifconfig.me

- find * -type f | fzf > selected
- sed -i 's/cadena_original/cadena_nueva/g' archivo.txt

- GREP AND: grep -E 'pattern1.*pattern2' filename
- GREP OR: grep -E 'pattern1|pattern2' filename
- Wordcount with grep: grep -w foo README.txt
- preg_match('/beta/', $_SESSION['servidor_url'])
- Contar cantidad de líneas: wc -l archivo.txt

- git remote add prod ssh://<EMAIL>:/home/<USER>/gits/andresmaiden.git
- git remote set-url beta ec2-user@*************:/home/<USER>/gits/api-beta
- git remote set-url alfa ec2-user@*************:/home/<USER>/gits/api-alfa
- git remote set-url prod ec2-user@*************:/home/<USER>/gits/api
- git fetch --all

- ctrl+U: borrar todo
- ctrl+ k/w: borra desde el cursor todo hasta el principio (con w) o hasta el fin (con k)
- ctrl+ r: search history (ctrl+r next & ctrl+s back)
- !!: last command (ej. sudo !!)
- !: last specific command (ej. !cat)
- history | grep subl
- !linea (ej !455)

- alias desk=cd\ /home/<USER>/Escritorio
- alias files=find\ .\ -type\ f\ \|\ wc\ -l
- alias network=sudo\ service\ network-manager\ restart
- alias conectados='find /saas/customer/services/acc/empresas/logs/ -maxdepth 1 -mmin -60 -type f -exec ls -la {} \;'
- alias pesados='find /saas/customer/services -type f -size +10M -exec ls -la {} \;'
- function mkcd
- alias modificados=find . -type f -mmin -1 -exec ls -lt --time-style=+"%Y-%m-%d %H:%M:%S" {} +

- sudo timedatectl set-time '2023-04-10 15:30:00'
- setsid gedit

- youtube-dl link
- youtube-dl --extract-audio --audio-format mp3 <video URL>
- ffmpeg -i link.mp4 -f mp3 music.mp3
- ffmpeg -i video.mp4 -vn -ab 128k -ar 44100 -y video.mp3
- ffmpeg -i video.mp4 -i video.wav -c copy -map 0:v:0 -map 1:a:0 video.mkv
- Reducir resolución vídeo a 720p: ffmpeg -i input.mp4 -vf "scale=-1:720" -c:a copy output.mp4

- convert input.jpg output.png
- convert input.png -transparent white output.png

- alias f="fabric"
- alias copy='xsel --clipboard --input'
- alias paste='xsel --clipboard --output'
