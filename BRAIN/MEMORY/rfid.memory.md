# RFID

- Baja Frecuencia (LF 30-300KHz): Operan normalmente en 125KHz. Su velocidad de comunicación es baja, su rango máximo de lectura es de aproximadamente 50cm. La utilización más frecuente es tarjetas de acceso.
- Alta Frecuencia (HF 3-30MHz): Operan normalmente en 13.56MHz. Su velocidad de comunicación es aceptable para sistemas estáticos o de baja velocidad, rango máximo de lectura de 1 metro. La utilización más frecuente es en Librerías, hospitales.
- Ultra Alta Frecuencia (UHF 300MHz-3GHz): Operan entre 868 y 928 MHz. Su velocidad de comunicación es 800 tags por segundo, su rango máximo de lectura es de hasta 9 metros. El principal inconveniente es la interferencia provocada por metales y líquidos. Utilizado en la actualidad en cadena de suministro, identificación cajas y pallets.
- EPC Global Inc. nace a partir de la fusión entre GS1 (antiguamente EAN Internacional) y GS1 US (antiguamente Uniform Code Council, la cual administra el código de barra UPC) Organización independiente, sin fines de lucro y con estándares globales encomendados por la industria para el manejo de la adopción e implementación de la Red EPC Global y la tecnología EPC.
- EPC corresponde a las siglas en inglés de Código Electrónico de Producto y se refiere a una clave de identificación unívoca vinculada a un ítem, caja o pallet que permite detallar información sobre el mismo en cualquier lugar de la cadena de abastecimiento. Su principal objetivo no radica en reemplazar el código de barras, sino en crear un camino para la que las empresas puedan migrar del código de barra hacia la tecnología RFID.
- La norma de los EPCs es la ISO18000-6B y la ISO18000-6C. La norma ISO18000-6B puede leer hasta 10 etiquetas a la vez, el área de datos del usuario es grande, la velocidad de transmisión de datos es de aproximadamente 40 Kbps. Las etiquetas ISO18000-6B se usan generalmente en áreas de circuito cerrado, como la gestión de activos. EPC C1G2 es ISO18000-6C es el que se usa para cronometraje, ya que puede leer cientos de etiquetas al mismo tiempo, el área de datos del usuario es pequeña, y la velocidad de transmisión de datos es más rápida (de 40 Kbps a 640 Kbps). Las etiquetas ISO18000-6B pueden grabar más información interna, pero las ISO18000-6C tiene la ventaja de que pueden tener contraseña.
- En Realtime el reader lee una sola antena a la vez, entrega todo lo leído al software y recién ahí pasa a la próxima antena. Esta opción es recomendable cuando los participantes pasan más lentos y/o hay mucha cantidad de participantes pasando por el mismo puesto de lectura.
- En Fast Switch el reader lee una antena y pasa a la siguiente, mientras un segundo CPU dentro del reader va enviando todo lo leído sin parar. Esta opción es recomendable cuando son pocos participantes y/o pasan muy rápido por el puesto de lectura.
- PC: Protocol-control word (also called StoredPC and PacketPC)
