# MEJORES QUERYS

- En mysql usar UNION en lugar de OR
- En mysql se usa sólo un index por tabla en los where
- En mysql con index compuesto, el orden es importante que el primero sea el que más reduce resultados
- The HAVING clause should be only used with the GROUP BY clause, other than that we should always use the WHERE clause for filtering, as it uses Indexing. WHERE is always executed before GROUP BY, and HAVING is executed afterwards
- Se puede enviar directamente a un archivo por ej "SELECT idrelacion, saldo FROM saldos WHERE tiporelacion = 'clientes' AND saldo > 0 INTO OUTFILE '/tmp/saldos_positivos_5548.csv' FIELDS TERMINATED BY ',' ENCLOSED BY '"' LINES TERMINATED BY '\n';"
- Se puede generar un campo con numeración por ej "SELECT *, 1000 + ROW_NUMBER() OVER (ORDER BY alguna_columna) AS correlativo"
- Para actualizar un AUTO_INCREMENT `ALTER TABLE saas_8093.proveedores AUTO_INCREMENT = 5;`
- Al crear usuario en Mysql 8 se hace primero creando y después asignando privilegios: `CREATE USER 'saas_10798'@'%' IDENTIFIED BY 'e05b09d632c62a9f1cc8bce1dd35a752';` y `GRANT ALL PRIVILEGES ON saas_10798.* TO 'saas_10798'@'%';`, sin olvidar `FLUSH PRIVILEGES;`
- Para sumar minutos `UPDATE lecturas SET tiempo = DATE_ADD(tiempo, INTERVAL 5 MINUTE) WHERE idcontrol = 20500;`
- Para restar segundos `UPDATE lecturas SET tiempo = DATE_SUB(tiempo, INTERVAL 29 SECOND) WHERE idcontrol = 20500;`
- Para duplicar la tabla productos `INSERT INTO saas_12905.productos SELECT * FROM saas_874.productos;`
