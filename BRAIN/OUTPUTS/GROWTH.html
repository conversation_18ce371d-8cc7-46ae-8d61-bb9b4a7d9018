<!DOCTYPE html>
<html>
<head>
<title>GROWTH.md</title>
<meta http-equiv="Content-type" content="text/html;charset=UTF-8">

<style>
/* https://github.com/microsoft/vscode/blob/master/extensions/markdown-language-features/media/markdown.css */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

body {
	font-family: var(--vscode-markdown-font-family, -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif);
	font-size: var(--vscode-markdown-font-size, 14px);
	padding: 0 26px;
	line-height: var(--vscode-markdown-line-height, 22px);
	word-wrap: break-word;
}

#code-csp-warning {
	position: fixed;
	top: 0;
	right: 0;
	color: white;
	margin: 16px;
	text-align: center;
	font-size: 12px;
	font-family: sans-serif;
	background-color:#444444;
	cursor: pointer;
	padding: 6px;
	box-shadow: 1px 1px 1px rgba(0,0,0,.25);
}

#code-csp-warning:hover {
	text-decoration: none;
	background-color:#007acc;
	box-shadow: 2px 2px 2px rgba(0,0,0,.25);
}

body.scrollBeyondLastLine {
	margin-bottom: calc(100vh - 22px);
}

body.showEditorSelection .code-line {
	position: relative;
}

body.showEditorSelection .code-active-line:before,
body.showEditorSelection .code-line:hover:before {
	content: "";
	display: block;
	position: absolute;
	top: 0;
	left: -12px;
	height: 100%;
}

body.showEditorSelection li.code-active-line:before,
body.showEditorSelection li.code-line:hover:before {
	left: -30px;
}

.vscode-light.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(0, 0, 0, 0.15);
}

.vscode-light.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(0, 0, 0, 0.40);
}

.vscode-light.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-dark.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 255, 255, 0.4);
}

.vscode-dark.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 255, 255, 0.60);
}

.vscode-dark.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

.vscode-high-contrast.showEditorSelection .code-active-line:before {
	border-left: 3px solid rgba(255, 160, 0, 0.7);
}

.vscode-high-contrast.showEditorSelection .code-line:hover:before {
	border-left: 3px solid rgba(255, 160, 0, 1);
}

.vscode-high-contrast.showEditorSelection .code-line .code-line:hover:before {
	border-left: none;
}

img {
	max-width: 100%;
	max-height: 100%;
}

a {
	text-decoration: none;
}

a:hover {
	text-decoration: underline;
}

a:focus,
input:focus,
select:focus,
textarea:focus {
	outline: 1px solid -webkit-focus-ring-color;
	outline-offset: -1px;
}

hr {
	border: 0;
	height: 2px;
	border-bottom: 2px solid;
}

h1 {
	padding-bottom: 0.3em;
	line-height: 1.2;
	border-bottom-width: 1px;
	border-bottom-style: solid;
}

h1, h2, h3 {
	font-weight: normal;
}

table {
	border-collapse: collapse;
}

table > thead > tr > th {
	text-align: left;
	border-bottom: 1px solid;
}

table > thead > tr > th,
table > thead > tr > td,
table > tbody > tr > th,
table > tbody > tr > td {
	padding: 5px 10px;
}

table > tbody > tr + tr > td {
	border-top: 1px solid;
}

blockquote {
	margin: 0 7px 0 5px;
	padding: 0 16px 0 10px;
	border-left-width: 5px;
	border-left-style: solid;
}

code {
	font-family: Menlo, Monaco, Consolas, "Droid Sans Mono", "Courier New", monospace, "Droid Sans Fallback";
	font-size: 1em;
	line-height: 1.357em;
}

body.wordWrap pre {
	white-space: pre-wrap;
}

pre:not(.hljs),
pre.hljs code > div {
	padding: 16px;
	border-radius: 3px;
	overflow: auto;
}

pre code {
	color: var(--vscode-editor-foreground);
	tab-size: 4;
}

/** Theming */

.vscode-light pre {
	background-color: rgba(220, 220, 220, 0.4);
}

.vscode-dark pre {
	background-color: rgba(10, 10, 10, 0.4);
}

.vscode-high-contrast pre {
	background-color: rgb(0, 0, 0);
}

.vscode-high-contrast h1 {
	border-color: rgb(0, 0, 0);
}

.vscode-light table > thead > tr > th {
	border-color: rgba(0, 0, 0, 0.69);
}

.vscode-dark table > thead > tr > th {
	border-color: rgba(255, 255, 255, 0.69);
}

.vscode-light h1,
.vscode-light hr,
.vscode-light table > tbody > tr + tr > td {
	border-color: rgba(0, 0, 0, 0.18);
}

.vscode-dark h1,
.vscode-dark hr,
.vscode-dark table > tbody > tr + tr > td {
	border-color: rgba(255, 255, 255, 0.18);
}

</style>

<style>
/* Tomorrow Theme */
/* http://jmblog.github.com/color-themes-for-google-code-highlightjs */
/* Original theme - https://github.com/chriskempson/tomorrow-theme */

/* Tomorrow Comment */
.hljs-comment,
.hljs-quote {
	color: #8e908c;
}

/* Tomorrow Red */
.hljs-variable,
.hljs-template-variable,
.hljs-tag,
.hljs-name,
.hljs-selector-id,
.hljs-selector-class,
.hljs-regexp,
.hljs-deletion {
	color: #c82829;
}

/* Tomorrow Orange */
.hljs-number,
.hljs-built_in,
.hljs-builtin-name,
.hljs-literal,
.hljs-type,
.hljs-params,
.hljs-meta,
.hljs-link {
	color: #f5871f;
}

/* Tomorrow Yellow */
.hljs-attribute {
	color: #eab700;
}

/* Tomorrow Green */
.hljs-string,
.hljs-symbol,
.hljs-bullet,
.hljs-addition {
	color: #718c00;
}

/* Tomorrow Blue */
.hljs-title,
.hljs-section {
	color: #4271ae;
}

/* Tomorrow Purple */
.hljs-keyword,
.hljs-selector-tag {
	color: #8959a8;
}

.hljs {
	display: block;
	overflow-x: auto;
	color: #4d4d4c;
	padding: 0.5em;
}

.hljs-emphasis {
	font-style: italic;
}

.hljs-strong {
	font-weight: bold;
}
</style>

<style>
/*
 * Markdown PDF CSS
 */

 body {
	font-family: -apple-system, BlinkMacSystemFont, "Segoe WPC", "Segoe UI", "Ubuntu", "Droid Sans", sans-serif, "Meiryo";
	padding: 0 12px;
}

pre {
	background-color: #f8f8f8;
	border: 1px solid #cccccc;
	border-radius: 3px;
	overflow-x: auto;
	white-space: pre-wrap;
	overflow-wrap: break-word;
}

pre:not(.hljs) {
	padding: 23px;
	line-height: 19px;
}

blockquote {
	background: rgba(127, 127, 127, 0.1);
	border-color: rgba(0, 122, 204, 0.5);
}

.emoji {
	height: 1.4em;
}

code {
	font-size: 14px;
	line-height: 19px;
}

/* for inline code */
:not(pre):not(.hljs) > code {
	color: #C9AE75; /* Change the old color so it seems less like an error */
	font-size: inherit;
}

/* Page Break : use <div class="page"/> to insert page break
-------------------------------------------------------- */
.page {
	page-break-after: always;
}

</style>

<script src="https://unpkg.com/mermaid/dist/mermaid.min.js"></script>
</head>
<body>
  <script>
    mermaid.initialize({
      startOnLoad: true,
      theme: document.body.classList.contains('vscode-dark') || document.body.classList.contains('vscode-high-contrast')
          ? 'dark'
          : 'default'
    });
  </script>
<h1 id="%F0%9F%93%A6-roads--saas--growth">📦 ROADS &gt; SAAS &gt; GROWTH</h1>
<h2 id="introducci%C3%B3n">INTRODUCCIÓN</h2>
<p>Considerando que llevamos bastante tiempo sin crecimiento de usuarios y que tenemos todo para lograrlo, en este año me gustaría desarrollar algunas estrategias de growth marketing.</p>
<p>Principalmente se me ocurren las siguiente:</p>
<ul>
<li><em>Plan de integraciones</em>, apalancándonos con usuarios de otros sistemas</li>
<li><em>Plan SEO para AI desde FAQs</em>: mejorando nuestro posicionamiento orgánico orientado a AI</li>
<li><em>Plan de micro nicho</em>: generando landings y quizás opciones del sistema específicas para algunos rubros</li>
<li><em>Plan de expansión offline</em>: probar primero en Neuquén con marketing old-school (folletos, radios, carteles, etc.)</li>
</ul>
<p>Para estos planes van un posible escribir un resúmen de objetivos, métricas y tareas.futuro. Para tener una comparativa, también hay que plasmar algunas estadísticas de hoy. Sería ideal preparar un informe automático con esta información.</p>
<h2 id="plan-seo-para-ai">PLAN SEO PARA AI</h2>
<p>La búsqueda está cambiando de sólo Google a otras plataformas que tienen AI: Bing, ChatGPT, Perplexity, etc. Según estuve investigando, todas usan un sistema parecido al de Google para indexar la información de sitios web durante el entrenamiento del LLM, pero hay un interés particular en lo que es preguntas y respuestas. Es por esto que se le llama <em>Optimización de Motores de Respuestas</em>, o AEO (Answer Engine Optimization). Nosotros tenemos un listado de FAQs que además de para poder mejorar la ayuda, nos puede servir para este propósito.</p>
<p>Además si tenemos la información ordenada de esta forma, es mucho más fácil para entrenar un agente que haga de soporte con un RAG.</p>
<p><em>Puntualmente los objetivos son:</em></p>
<ul>
<li>Tener una herramienta completa y cómoda para poder gestionar esta información dentro del sistema sin programar.</li>
<li>Poder generar una web con esa información para que los usuarios puedan ver esa ayuda de forma ordenada.</li>
<li>Que Gilda pueda compartir enlaces por whatsapp a una pregunta específica de esta ayuda.</li>
<li>Poder mostrar esa información en ventanas flotantes de nuestro sistema.</li>
<li>Poder utilizar toda esa información para posicionamiento orgánico en SEO/AEO.</li>
<li>Poder utilizar esa información para entrenar un Agente de AI RAG para atención a usuarios.</li>
</ul>
<p><em>Tareas a realizar:</em></p>
<ul>
<li>Agregar campos a nuestra tabla de preguntas frecuentes en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/2103">FAQS Agregar campos</a></li>
<li>Agregar mejoras en la ayuda dentro del sistema en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/1162">AYUDA Ideas para actualizarla</a></li>
<li>Agregar un sector en la web con el listado de ayuda ordenado <a href="https://gitlab.com/saasargentina/app/-/issues/2104">AYUDA Web pública</a></li>
<li>Es importante que el sitemap se actualice automáticamente cada vez que se agregue o modifique contenido en la página web.</li>
<li>Lograr que el usuario final chico pueda ver distintas funcionalidades que tiene el software y que beneficios le puede traer</li>
<li>Hacer un checkeo de que tengamos bien configurado todo lo de SEO: https://neilpatel.com/blog/seo-checklist/</li>
<li>En la ayuda agregar un botón de &quot;Simplifica tu gestión con SaaS&quot; llevando a una landing. La idea es que si entran desde algún enlace de AI de ver más información y se registren, podemos medirlo.</li>
</ul>
<p><em>Métricas:</em></p>
<ul>
<li>La principal métrica va a ser que a Gilda le ayude con el soporte, lo que ella sienta.</li>
<li>Al usar una landing podemos medir quienes se registraron desde ver una ayuda.</li>
<li>Luego hay que encontrar una forma de medir nuestro posicionamiento antes y después del cambio.</li>
</ul>
<h2 id="plan-de-micro-nicho">PLAN DE MICRO NICHO</h2>
<p>La idea es poder apuntar en campañas de redes sociales a micro nichos. Vamos a probar contratando a la empresa Beside, pero más allá de eso, podemos hacer un replanteamiento de nuestra propuesta.</p>
<p><em>Tareas a realizar:</em></p>
<p>Antes de comenzar, vamos a analizar los siguientes datos:</p>
<ul>
<li>Cuántos hay en FE y cuánto emiten</li>
<li>Cuántos FE pasaron a Full y viceversa</li>
<li>Estadísticas de logs completos</li>
</ul>
<p>Luego la idea es generar distintas versiones del producto, detallado en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/2105">VERSIONES Generar primeras</a></p>
<ul>
<li>Versión reducida para emprendedores que están comenzando (en realidad es para los ratas o los que les está yendo mal)</li>
<li>Versión de comercio online: ventajas de integraciones varias, todo online, 24hs, etc.</li>
<li>Versión para informática</li>
<li>Versión para talleres y autopartes</li>
<li>Versión para ferreterías</li>
</ul>
<p>Hay que hacer páginas para cada uno de los casos de uso primero como landings y luego como subproductos en un menú y con su propia página (Por ej https://gestioo.com/producto/software-para-talleres ). Detallo este trabajo en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/2106">VERSIONES Landings</a></p>
<p>Si empieza a funcionar, podemos filtrar entre nuestros clientes y pasarlos. También podemos enviar mails específicos.</p>
<p><em>Métricas:</em></p>
<ul>
<li>Hay que hacer un informe o board para poder ver fácilmente cuales de las versiones están funcionando mejor.</li>
</ul>
<h2 id="plan-de-integraciones">PLAN DE INTEGRACIONES</h2>
<p>Las integraciones nos ayudan porque son clientes que vienen recomendados de otra plataforma, sin que hagamos inversión en publicidad y con una alta probabilidad de que sigan como clientes. Si bien tenemos una fuerte inversión en desarrollo para que esto suceda, creo que vale la pena.</p>
<p><em>Tareas a realizar:</em></p>
<ul>
<li>Tener reuniones con cada una de las integraciones</li>
<li>Establecer un acuerdo para que entre ambas empresas podamos recomendar a la otra</li>
<li>Actualizar algunos cambios en nuestro sistema de landings y probarlo bien. Lo detalle en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/1965">LANDINGS Actualizar</a></li>
<li>Generar landings para cada integración. La de VentasxMayor hay que revisarla. La de Más Pedidos hay que hacerla en el issue <a href="https://gitlab.com/saasargentina/app/-/issues/2107">LANDINGS Más Pedidos</a></li>
<li>Hacer en nuestra página una sección de integraciones explicando bien que son, para que sirven y que nos sirva de posicionamiento.</li>
<li>Evaluar agregar en el sistema una configuración de Tienda con las opciones, para que lo usuarios sepan que existen todas esas opciones.</li>
<li>Enviar notificación y/o mail avisando de todas las nuevas integraciones.</li>
<li>Buscar alguna integración con algún sistema de logística de entregas o envíos.</li>
</ul>
<p><em>Métricas:</em></p>
<ul>
<li>Tenemos que hacer una métrica sobre todas las landings en general, que nos va a servir para esta y todas las estrategias.</li>
</ul>
<h2 id="plan-de-expansi%C3%B3n-offline">PLAN DE EXPANSIÓN OFFLINE</h2>
<p>La idea es probar con publicidad en medios tradicionales de Neuquén. Además podemos Visitar Centro Pyme, Repartir folletos, Averiguar por radios y cartelería. Podemos empezar por averiguar en https://primamultimedios.com que es uno de los medios más grandes de la zona. Si funciona lo seguimos en otras ciudades.</p>

</body>
</html>
