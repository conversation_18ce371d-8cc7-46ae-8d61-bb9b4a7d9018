# Botones de MercadoPago

Estamos implementando un nuevo módulo de precios y pagos en el sistema. El módulo convive con el anterior, que sería especificar los enlaces a los botones en los textos de pantalla y de mail. Si quieren probar este método, por el momento las configuraciones las hacemos nosotros a mano, por lo tanto necesitamos que nos pasen toda la información con tiempo.

## Configuración de medios de pago

Primero se puede configurar distintos medios de pago juntos, estos son por ej: una cuenta de banco para hacer transferencia, distintos locales o ubicaciones para recibir efectivo, o cuentas en pasarelas de pago como MercadoPago o Paypal.

De cada medio de pago necesito la siguiente información:

- Tipo de pago: efectivo, transferencia, paypal, payu, mercadopago
- Texto describiendo los pasos, datos para transferencia, dirección de cobro en efectivo, etc. (esto es opcional, por ejemplo MercadoPago no necesita esta información, pero un pago en efectivo sí)
- Si es MercadoPago seguir el proceso para generar los permisos de acceso a la API (más info abajo)
- Países donde aceptar el medio de pago. Por ejemplo, si es una cuenta de banco (esto incluye a MercadoPago), se puede aceptar en un solo país, pero si es Paypal, se puede aceptar en varios países.

## Configuración de precios

Se pueden generar distintos precios, cada uno con sus rangos de fechas, cantidades de cupos y carreras a las que corresponde. De esta forma una carrera puede tener distintos precios según la fecha en que se inscriba el corredor. Con respecto al cupo sirve para cuando se quiere hacer un precio especial para los primeros inscriptos a una carrera.

De cada precio necesito la siguiente información:

- Medio de pago (de los informados en la tabla anterior)
- Precio
- Carreras en las que se aplica
- Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones)
- Fecha y hora desde que se habilita este precio
- Fecha y hora hasta que se deshabilita este precio

## Configuración de MercadoPago

Para poder aceptar pagos con MercadoPago, hay 3 formas de hacerlo.

1) Generar botones de pago en la web de MercadoPago y configurarlos en el sistema de Crono. Esta es la versión más simple de configurar, pero requiere la aprobación manual de cada pago que llega por mail.

2) Generar una App dentro de MercadoPago. Esta opción requiere una configuración un poco más compleja dentro de MercadoPago, pero permite que los pagos se acrediten automáticamente en el sistema. Esta función todavía está en modo Beta, es decir que puede tener errores.

3) Conectar la API de MercadoPago a través de OAuth. Esta opción va a ser más fácil de configurar y va a permitir que los pagos se acrediten automáticamente en el sistema. Esta opción todavía no está implementada en el sistema de Crono, pero lo vamos a hacer en una de las próximas versiones.

## Enlace a opciones de pago

En el sistema en *Configuraciones > Inscripciones > Textos* se pueden escribir textos para que lean los participantes que se están inscribiendo. En el texto del mail pre-inscripto hay que agregar un enlace para que desde el mail se pueda acceder a ver las opciones de pago con la cadena de texto "{{preinscripto}}" que se va a reemplazar por el enlace específico de esa inscripción.

## Generar una App dentro de MercadoPago

Para generar una App dentro de MercadoPago, hay que seguir los siguientes pasos:

1. Estando ya logueado en MercadoPago, ir a [https://www.mercadopago.com.ar/developers/panel/app](https://www.mercadopago.com.ar/developers/panel/app)
2. Hacer click en "Crear aplicación". En este momento va a pedir una validación de la cuenta de MercadoPago. Hay que seguir los pasos que indica la página hasta que se pueda crear la aplicación.
3. Una vez que se validó, completar lo siguiente:
   - `Nombre de la aplicación` con "Cronometraje Instantáneo"
   - `¿Qué tipo de solución de pago vas a integrar?` con "Pagos online"
   - `¿Estás usando una plataforma de e-commerce?` con "No"
   - `¿Qué producto estás integrando?` con "CheckoutPro"
   - `Modelo de integración` dejar en blanco
   - Marcar en `Autorizo el uso de mis datos personales ...`
   - Hacer un clic en "Crear aplicación"
4. Cuando nos de la notificación de "Aplicación creada", esperar a que nos redireccione o hacer click en "Tus integraciones" y seleccionar la aplicación que acabamos de crear.
5. Ir a "Credenciales de Producción" y:
   - Seleccionar la `Industria` "Otros" o la que corresponda
   - Marcar en `Autorizo el uso de mis datos personales ...`
   - Hacer click en "Activar credenciales de producción"
6. Copiar el `Public key`, el `Access Token`, `Client ID` y el `Client Secret` y enviarlos a Crono. O también puede hacer un clic en "Compartir credenciales y enviarlo a la cuenta "<EMAIL>".
