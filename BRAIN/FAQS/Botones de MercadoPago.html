<!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>Botones de MercadoPago</title>
            <style>
/* From extension vscode.github */
/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

.vscode-dark img[src$=\#gh-light-mode-only],
.vscode-light img[src$=\#gh-dark-mode-only],
.vscode-high-contrast:not(.vscode-high-contrast-light) img[src$=\#gh-light-mode-only],
.vscode-high-contrast-light img[src$=\#gh-dark-mode-only] {
	display: none;
}

</style>
            
        <link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/markdown.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/gh/Microsoft/vscode/extensions/markdown-language-features/media/highlight.css">
<style>
            body {
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe WPC', 'Segoe UI', system-ui, 'Ubuntu', 'Droid Sans', sans-serif;
                font-size: 14px;
                line-height: 1.6;
            }
        </style>
        <style>
.task-list-item {
    list-style-type: none;
}

.task-list-item-checkbox {
    margin-left: -20px;
    vertical-align: middle;
    pointer-events: none;
}
</style>
<style>
:root {
  --color-note: #0969da;
  --color-tip: #1a7f37;
  --color-warning: #9a6700;
  --color-severe: #bc4c00;
  --color-caution: #d1242f;
  --color-important: #8250df;
}

</style>
<style>
@media (prefers-color-scheme: dark) {
  :root {
    --color-note: #2f81f7;
    --color-tip: #3fb950;
    --color-warning: #d29922;
    --color-severe: #db6d28;
    --color-caution: #f85149;
    --color-important: #a371f7;
  }
}

</style>
<style>
.markdown-alert {
  padding: 0.5rem 1rem;
  margin-bottom: 16px;
  color: inherit;
  border-left: .25em solid #888;
}

.markdown-alert>:first-child {
  margin-top: 0
}

.markdown-alert>:last-child {
  margin-bottom: 0
}

.markdown-alert .markdown-alert-title {
  display: flex;
  font-weight: 500;
  align-items: center;
  line-height: 1
}

.markdown-alert .markdown-alert-title .octicon {
  margin-right: 0.5rem;
  display: inline-block;
  overflow: visible !important;
  vertical-align: text-bottom;
  fill: currentColor;
}

.markdown-alert.markdown-alert-note {
  border-left-color: var(--color-note);
}

.markdown-alert.markdown-alert-note .markdown-alert-title {
  color: var(--color-note);
}

.markdown-alert.markdown-alert-important {
  border-left-color: var(--color-important);
}

.markdown-alert.markdown-alert-important .markdown-alert-title {
  color: var(--color-important);
}

.markdown-alert.markdown-alert-warning {
  border-left-color: var(--color-warning);
}

.markdown-alert.markdown-alert-warning .markdown-alert-title {
  color: var(--color-warning);
}

.markdown-alert.markdown-alert-tip {
  border-left-color: var(--color-tip);
}

.markdown-alert.markdown-alert-tip .markdown-alert-title {
  color: var(--color-tip);
}

.markdown-alert.markdown-alert-caution {
  border-left-color: var(--color-caution);
}

.markdown-alert.markdown-alert-caution .markdown-alert-title {
  color: var(--color-caution);
}

</style>
        
        </head>
        <body class="vscode-body vscode-light">
            <h1 id="botones-de-mercadopago">Botones de MercadoPago</h1>
<p>Estamos implementando un nuevo módulo de precios y pagos en el sistema. El módulo convive con el anterior, que sería especificar los enlaces a los botones en los textos de pantalla y de mail. Si quieren probar este método, por el momento las configuraciones las hacemos nosotros a mano, por lo tanto necesitamos que nos pasen toda la información con tiempo.</p>
<h2 id="configuración-de-medios-de-pago">Configuración de medios de pago</h2>
<p>Primero se puede configurar distintos medios de pago juntos, estos son por ej: una cuenta de banco para hacer transferencia, distintos locales o ubicaciones para recibir efectivo, o cuentas en pasarelas de pago como MercadoPago o Paypal.</p>
<p>De cada medio de pago necesito la siguiente información:</p>
<ul>
<li>Tipo de pago: efectivo, transferencia, paypal, payu, mercadopago</li>
<li>Texto describiendo los pasos, datos para transferencia, dirección de cobro en efectivo, etc. (esto es opcional, por ejemplo MercadoPago no necesita esta información, pero un pago en efectivo sí)</li>
<li>Si es MercadoPago seguir el proceso para generar los permisos de acceso a la API (más info abajo)</li>
<li>Países donde aceptar el medio de pago. Por ejemplo, si es una cuenta de banco (esto incluye a MercadoPago), se puede aceptar en un solo país, pero si es Paypal, se puede aceptar en varios países.</li>
</ul>
<h2 id="configuración-de-precios">Configuración de precios</h2>
<p>Se pueden generar distintos precios, cada uno con sus rangos de fechas, cantidades de cupos y carreras a las que corresponde. De esta forma una carrera puede tener distintos precios según la fecha en que se inscriba el corredor. Con respecto al cupo sirve para cuando se quiere hacer un precio especial para los primeros inscriptos a una carrera.</p>
<p>De cada precio necesito la siguiente información:</p>
<ul>
<li>Medio de pago (de los informados en la tabla anterior)</li>
<li>Precio</li>
<li>Carreras en las que se aplica</li>
<li>Cantidad de inscripciones disponibles para este precio (lo más común es no limitar la cantidad de inscripciones)</li>
<li>Fecha y hora desde que se habilita este precio</li>
<li>Fecha y hora hasta que se deshabilita este precio</li>
</ul>
<h2 id="configuración-de-mercadopago">Configuración de MercadoPago</h2>
<p>Para poder aceptar pagos con MercadoPago, hay 3 formas de hacerlo.</p>
<ol>
<li>
<p>Generar botones de pago en la web de MercadoPago y configurarlos en el sistema de Crono. Esta es la versión más simple de configurar, pero requiere la aprobación manual de cada pago que llega por mail.</p>
</li>
<li>
<p>Generar una App dentro de MercadoPago. Esta opción requiere una configuración un poco más compleja dentro de MercadoPago, pero permite que los pagos se acrediten automáticamente en el sistema. Esta función todavía está en modo Beta, es decir que puede tener errores.</p>
</li>
<li>
<p>Conectar la API de MercadoPago a través de OAuth. Esta opción va a ser más fácil de configurar y va a permitir que los pagos se acrediten automáticamente en el sistema. Esta opción todavía no está implementada en el sistema de Crono, pero lo vamos a hacer en una de las próximas versiones.</p>
</li>
</ol>
<h2 id="enlace-a-opciones-de-pago">Enlace a opciones de pago</h2>
<p>En el sistema en <em>Configuraciones &gt; Inscripciones &gt; Textos</em> se pueden escribir textos para que lean los participantes que se están inscribiendo. En el texto del mail pre-inscripto hay que agregar un enlace para que desde el mail se pueda acceder a ver las opciones de pago con la cadena de texto &quot;{{preinscripto}}&quot; que se va a reemplazar por el enlace específico de esa inscripción.</p>
<h2 id="generar-una-app-dentro-de-mercadopago">Generar una App dentro de MercadoPago</h2>
<p>Para generar una App dentro de MercadoPago, hay que seguir los siguientes pasos:</p>
<ol>
<li>Estando ya logueado en MercadoPago, ir a <a href="https://www.mercadopago.com.ar/developers/panel/app">https://www.mercadopago.com.ar/developers/panel/app</a></li>
<li>Hacer click en &quot;Crear aplicación&quot;. En este momento va a pedir una validación de la cuenta de MercadoPago. Hay que seguir los pasos que indica la página hasta que se pueda crear la aplicación.</li>
<li>Una vez que se validó, completar lo siguiente:
<ul>
<li><code>Nombre de la aplicación</code> con &quot;Cronometraje Instantáneo&quot;</li>
<li><code>¿Qué tipo de solución de pago vas a integrar?</code> con &quot;Pagos online&quot;</li>
<li><code>¿Estás usando una plataforma de e-commerce?</code> con &quot;No&quot;</li>
<li><code>¿Qué producto estás integrando?</code> con &quot;CheckoutPro&quot;</li>
<li><code>Modelo de integración</code> dejar en blanco</li>
<li>Marcar en <code>Autorizo el uso de mis datos personales ...</code></li>
<li>Hacer un clic en &quot;Crear aplicación&quot;</li>
</ul>
</li>
<li>Cuando nos de la notificación de &quot;Aplicación creada&quot;, esperar a que nos redireccione o hacer click en &quot;Tus integraciones&quot; y seleccionar la aplicación que acabamos de crear.</li>
<li>Ir a &quot;Credenciales de Producción&quot; y:
<ul>
<li>Seleccionar la <code>Industria</code> &quot;Otros&quot; o la que corresponda</li>
<li>Marcar en <code>Autorizo el uso de mis datos personales ...</code></li>
<li>Hacer click en &quot;Activar credenciales de producción&quot;</li>
</ul>
</li>
<li>Copiar el <code>Public key</code>, el <code>Access Token</code>, <code>Client ID</code> y el <code>Client Secret</code> y enviarlos a Crono. O también puede hacer un clic en &quot;Compartir credenciales y enviarlo a la cuenta &quot;<a href="mailto:<EMAIL>"><EMAIL></a>&quot;.</li>
</ol>

            
            
        </body>
        </html>