<?php

exit('No está terminado, usar sync_idcontrol');

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$idevento = filter_input(INPUT_GET, 'idevento', FILTER_SANITIZE_SPECIAL_CHARS);
$sync_key = filter_input(INPUT_GET, 'sync_key', FILTER_SANITIZE_SPECIAL_CHARS);

/*if (!NUBE_HIBRIDA){
	header("HTTP/1.1 400 Bad Request");
    exit('ERROR no es nube híbrida');
}*/

if (!$idevento) {
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR en idvento');
}
if (!$sync_key || $sync_key != SYNC_KEY) {
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR en token');
}

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);

//Ver esto:
//Bajo la data de la nube híbrida
if (campo_sql(consulta_sql("SELECT sync_enable FROM eventos WHERE idevento = '".$idevento."'"), 0, 'sync_enable')){
	header('Content-type: application/json');
	exit('El evento ya fue sincronizado');
}

/*$resultado_sql = consulta_sql(
    "SELECT eventos.*, datosxeventos.*, carreras.*, categorias.*, controles.*, penas.*, datosxparticipantes.*, etapas.*, lecturas.*, participantes.*
    FROM eventos
    LEFT JOIN participantes ON eventos.idevento = participantes.idevento
	LEFT JOIN datosxeventos ON eventos.idevento = datosxeventos.idevento
	LEFT JOIN datos ON datosxeventos.iddato = datosxeventos.iddato
	LEFT JOIN carreras ON eventos.idevento = carreras.idevento
	LEFT JOIN categorias ON eventos.idevento = categorias.idevento
	LEFT JOIN controles ON eventos.idevento = controles.idevento
	LEFT JOIN penas ON eventos.idevento = penas.idevento
	LEFT JOIN datosxparticipantes ON eventos.idevento = datosxparticipantes.idevento
	LEFT JOIN etapas ON eventos.idevento = etapas.idevento
	LEFT JOIN lecturas ON eventos.idevento = lecturas.idevento
	WHERE eventos.idevento = '".$idevento."'
    ");*/

//no tengo cómo delimitar para definirlos, hay que hacer una query x c/u

$resultado = array();
$resultado_eventos = consulta_sql("SELECT * FROM eventos WHERE idevento = '".$idevento."'");
if (!contar_sql($resultado_eventos)){
	header('Content-type: application/json');
	exit('No se ha encontrado evento');
}

$resultado['evento'] = array_sql($resultado_eventos);

$resultado['datosxeventos'] = array_all_sql(consulta_sql(
	"SELECT datosxeventos.*
	FROM datosxeventos
	LEFT JOIN datos ON datosxeventos.iddato = datosxeventos.iddato
	WHERE datosxeventos.idevento = '".$idevento."'
	"));

$resultado['participantes'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM participantes
	WHERE idevento = '".$idevento."'
	"));

$resultado['carreras'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM carreras
	WHERE idevento = '".$idevento."'
	"));

$resultado['categorias'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM categorias
	LEFT JOIN carreras ON carreras.idcarrera = categorias.idcarrera
	WHERE categorias.idevento = '".$idevento."'
	"));

$resultado['controles'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM controles
	LEFT JOIN etapas ON etapas.idetapa = controles.idetapa
	WHERE controles.idevento = '".$idevento."'
	"));

$resultado['penas'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM penas
	WHERE idevento = '".$idevento."'
	"));

$resultado['datosxparticipantes'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM datosxparticipantes
	WHERE idevento = '".$idevento."'
	"));

$resultado['etapas'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM etapas
	WHERE idevento = '".$idevento."'
	"));

$resultado['lecturas'] = array_all_sql(consulta_sql(
	"SELECT *
	FROM lecturas
	WHERE idevento = '".$idevento."'
	"));


$resultado['organizacion'] = array_sql(consulta_sql(
	"SELECT *
	FROM organizaciones
	LEFT JOIN eventos ON organizaciones.idorganizacion = eventos.idorganizacion
	WHERE idevento = '".$idevento."'"));

$resultado['datos'] = array_sql(consulta_sql("SELECT * FROM datos")); //Where??

//consulta_sql("UPDATE eventos SET sync_enable = 1 WHERE idevento = '".$idevento."'");

header('Content-type: application/json');



//log_api('Sync down; OK');
echo json_encode($resultado);

/* Código para sincronizar */


?>
