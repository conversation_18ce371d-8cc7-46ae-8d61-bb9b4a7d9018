<?php
/* Usar como
* * * * * wget --delete-after "http://cronometrajeinstantaneo.lan/api/sync_idcontrol.php?idevento=1206&idcontrol_up=12955"
* * * * * wget --delete-after "http://cronometrajeinstantaneo.lan/api/sync_idcontrol.php?idevento=1206&idcontrol_down=12979"
*/

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$idevento = filter_input(INPUT_GET, 'idevento', FILTER_SANITIZE_SPECIAL_CHARS);
$idcontrol_down = filter_input(INPUT_GET, 'idcontrol_down', FILTER_SANITIZE_SPECIAL_CHARS);
$idcontrol_up = filter_input(INPUT_GET, 'idcontrol_up', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);

// SYNC_DOWN
if (!$idcontrol_down || !contar_sql(consulta_sql(
    "SELECT idcontrol FROM controles
        WHERE idcontrol = '$idcontrol_down'
            AND idetapa IN (SELECT idetapa FROM etapas WHERE idcarrera IN
                (SELECT idcarrera FROM carreras WHERE idevento = '$idevento'))"))) {
    echo('El idcontrol_down: '.$idcontrol_down.' no pertenece al idevento: '.$idevento.'<br>');

} else {

    $options = array(
        CURLOPT_URL => 'https://admin.cronometrajeinstantaneo.com/api/lecturas_down?idcontrol='.$idcontrol_down,
        CURLOPT_CUSTOMREQUEST => 'GET',
        CURLOPT_RETURNTRANSFER => TRUE,
        CURLOPT_COOKIEFILE => "/tmp/cookieFileName",
        CURLOPT_HTTPHEADER => array('Content-Type:application/json'),
    );

    $ch = curl_init();
    curl_setopt_array($ch, $options);
    $result = curl_exec($ch);

    $lecturas = json_decode($result, true);

    foreach ($lecturas as $lectura) {

        $updated = false;
        if ($lectura['uuid']
            && contar_sql(consulta_sql("SELECT uuid FROM lecturas WHERE uuid = '{$lectura['uuid']}' LIMIT 1"))) {

            consulta_sql("
                UPDATE lecturas SET
                    estado = '{$lectura['estado']}',
                    tiempo = '{$lectura['tiempo']}',
                    idparticipante = '{$lectura['idparticipante']}'
                WHERE uuid = '{$lectura['uuid']}'");
            if (afectado_sql())
                $updated = true;

        } else {

            consulta_sql("
                INSERT INTO lecturas SET
                    uuid = '{$lectura['uuid']}',
                    idevento = '{$lectura['idevento']}',
                    idcontrol = '{$lectura['idcontrol']}',
                    estado = '{$lectura['estado']}',
                    tiempo = '{$lectura['tiempo']}',
                    idparticipante = '{$lectura['idparticipante']}',
                    tipo = '{$lectura['tipo']}'");
            if (afectado_sql())
                $updated = true;

        }

        if ($updated) {
            $idlectura = campo_sql(consulta_sql("
            SELECT idlectura FROM lecturas WHERE uuid = '{$lectura['uuid']}'"), 0, 'idlectura');

            $url = URL_API.'lecturas';
            $ch = curl_init( $url );
            # Setup request to send json via POST.
            $payload = json_encode([
                'idlectura' => $idlectura,
            ]);
            curl_setopt( $ch, CURLOPT_POSTFIELDS, $payload );
            curl_setopt( $ch, CURLOPT_HTTPHEADER, array('Content-Type:application/json'));
            # Return response instead of printing.
            curl_setopt( $ch, CURLOPT_RETURNTRANSFER, true );
            # Send request.
            $result = curl_exec($ch);
            curl_close($ch);
            echo $result;
        }
    }
}

// SYNC_UP
if (!$idcontrol_up || !contar_sql(consulta_sql(
    "SELECT idcontrol FROM controles
        WHERE idcontrol = '$idcontrol_up'
            AND idetapa IN (SELECT idetapa FROM etapas WHERE idcarrera IN
                (SELECT idcarrera FROM carreras WHERE idevento = '$idevento'))"))) {
    echo('El idcontrol_up: '.$idcontrol_up.' no pertenece al idevento: '.$idevento.'<br>');

} else {

    $lecturas = [];
    $lecturas_sql = consulta_sql("SELECT uuid, idevento, idcontrol, estado, tiempo, idparticipante, tipo FROM lecturas WHERE idcontrol = '$idcontrol_up'");
    while ($lectura = mysqli_fetch_assoc($lecturas_sql))
        $lecturas[] = $lectura;

    echo json_encode(['lecturas' => $lecturas]);
    $options = array(
        CURLOPT_URL => 'https://admin.cronometrajeinstantaneo.com/api/lecturas_up?idcontrol='.$idcontrol_up,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode(['lecturas' => $lecturas]),
        CURLOPT_COOKIEFILE => "/tmp/cookieFileName",
        CURLOPT_HTTPHEADER => array('Content-Type:application/json'),
    );

    $ch = curl_init();
    curl_setopt_array($ch, $options);
    $result = curl_exec($ch);

}
