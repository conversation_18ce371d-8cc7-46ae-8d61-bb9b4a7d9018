<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';

$codigo = filter_input(INPUT_POST, 'codigo', FILTER_SANITIZE_SPECIAL_CHARS);
$idparticipante = intval(filter_input(INPUT_POST, 'idparticipante', FILTER_SANITIZE_SPECIAL_CHARS));
$tagID = filter_input(INPUT_POST, 'tagID', FILTER_SANITIZE_SPECIAL_CHARS);
$a = filter_input(INPUT_POST, 'a', FILTER_SANITIZE_SPECIAL_CHARS);
$tag_prefijo = filter_input(INPUT_POST, 'tag_prefijo', FILTER_SANITIZE_SPECIAL_CHARS);

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);
$mobile = session_mobile($codigo);

if ($codigo == 'DEMO') {
    header("HTTP/1.1 400 Bad Request");
    exit(json_encode([
        'error'     => true,
        'resultado' => "No se puede grabar ni asignar chips en el DEMO"])
    );
}

if (!in_array($a, ['tag', 'tag_codigo'])) {
    header("HTTP/1.1 400 Bad Request");
    exit(json_encode([
        'error'     => true,
        'resultado' => "Error en el tipo de acción"])
    );
}

if (!$idparticipante || !$tagID) {
    header("HTTP/1.1 400 Bad Request");
    exit(json_encode([
        'error'     => true,
        'resultado' => "No se puede grabar el tag a este número"])
    );
}


// GRABAR TAG CON CODIGO
if ($a == 'tag_codigo') {

    $tag_codigo = str_replace(['1', '2', '3', '4', '5', '6', '7', '8', '9', '0'], '0', strtoupper($tag_prefijo));
    $tag_codigo = substr($tag_codigo, 0, -strlen($idparticipante)).$idparticipante;

    $tag = array_sql(consulta_sql(
        "SELECT *
        FROM tags
        WHERE (tagID = '$tagID' OR codigo = '$tag_codigo')
            AND idorganizacion = {$mobile['idcronometrador']}"));

    if (!$mobile['idcronometrador']) {
        $resultado = [
            'error'     => true,
            'resultado' => 'El evento no tiene un cronometrador asignado',
        ];

    } else if ($tag && $tag['codigo'] == $tag_codigo && $tag['tagID'] == $tagID) {
        $resultado = [
            'error'     => true,
            'resultado' => 'Este chip ya existe correctamente con EPC ',
        ];

    } else if ($tag && $tag['codigo'] == $tag_codigo) {
        $resultado = [
            'error'     => true,
            'resultado' => 'Ya existe un chip con código '.$tag_codigo.' y no es el EPC ',
        ];

    } else if ($tag) {
        $resultado = [
            'error'     => true,
            'resultado' => 'Ya existe el chip '.$tag['codigo'].' para el EPC ',
        ];

    } else {
        consulta_sql(
            "INSERT INTO tags SET
                tagID = '$tagID',
                codigo = '$tag_codigo',
                estado = 1,
                idevento = $mobile[idevento],
                idorganizacion = {$mobile['idcronometrador']}");

        if (afectado_sql()) {
            $resultado = [
                'error'     => false,
                'resultado' => 'Chip agregado correctamente con EPC ',
            ];
        } else {
            $resultado = [
                'error'     => true,
                'resultado' => 'No se ha podido agregar el chip con EPC ',
            ];
        }
    }

    $resultado['idparticipante'] = $idparticipante;
    $resultado['nombre'] = $tag_codigo;

    exit(json_encode($resultado));
}


// ASIGNAR TAG A PARTICIPANTE
$participante = array_sql(consulta_sql("SELECT idinscripcion, nombre
    FROM participantes
    WHERE idevento = {$mobile['idevento']}
        AND estado != 'eliminado'
        AND idparticipante = $idparticipante
    LIMIT 1"));

if (!$participante) {
    header("HTTP/1.1 400 Bad Request");
    exit(json_encode([
        'error'     => true,
        'resultado' => "No existe el N° $idparticipante para asignar chip "])
    );
}

$resultado = [
    'idparticipante' => $idparticipante,
    'nombre' => $participante['nombre']
];


$tag = array_sql(consulta_sql(
    "SELECT *
    FROM tags
    WHERE tagID = '$tagID'
        AND idorganizacion = {$mobile['idcronometrador']}"));

if ($tag['idinscripcion'] == $participante['idinscripcion']) {
    $tag_codigo = $tag['codigo'];
    $resultado['resultado'] = "Chip $tag_codigo ya se encuentra asignado a N° $idparticipante";

} else if ($tag) {

    consulta_sql(
        "UPDATE tags
        SET idinscripcion = {$participante['idinscripcion']},
            idevento = {$mobile['idevento']}
        WHERE tagID = '$tagID'
            AND idorganizacion = {$mobile['idcronometrador']}");
    if (afectado_sql()) {
        $tag_codigo = campo_sql(consulta_sql("SELECT codigo FROM tags WHERE tagID = '$tagID'"), 0, 'codigo');
        $resultado['resultado'] = "Chip $tag_codigo asignado a N° $idparticipante";

    } else {
        header("HTTP/1.1 400 Bad Request");
        exit(json_encode([
            'error'     => true,
            'resultado' => "No se pudo asignar al N° $idparticipante el chip "])
        );
    }

} else {
    consulta_sql("INSERT INTO tags SET
        tagID = '$tagID',
        codigo = 'CHIP',
        estado = 1,
        idevento = '{$mobile['idevento']}',
        idorganizacion = '{$mobile['idcronometrador']}',
        idinscripcion = {$participante['idinscripcion']}
    ");
    $tag_codigo = id_sql();
    consulta_sql("UPDATE tags SET codigo = idtag WHERE idtag = ".$tag_codigo);
    $resultado['resultado'] = "Chip $tag_codigo agregado y asignado a N° $idparticipante";
}

echo json_encode($resultado);
