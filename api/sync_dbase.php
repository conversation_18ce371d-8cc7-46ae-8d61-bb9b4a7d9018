<?php
require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

$bd_link = conectar_db(BD_BD, BD_HOST, BD_USER, BD_PASS);


// CONFIG //
$idevento = 825; // Parciales Río Pinto 2022
$idcontrol_largada = 6347; // RIO0
$idcontrol_llegada = 6346; // RIO3
$file_dbf = '/home/<USER>/Escritorio/rio/rppaso.dbf';
$file_log = '/home/<USER>/Escritorio/rio/rppaso.log';
$columnas = [
    'idparticipante' => 0,
    'largada' => 1,
    'llegada' => 2,
    'tiempo' => 6,
    'deleted' => 'deleted',
];


// FUNCIONES //
function log_dbase($log = '')
{
    global $file_log;
    file_put_contents($file_log, date("d-m-Y H:i:s").";".$log."\r\n", FILE_APPEND);
}

function sync_up($idcontrol, $lecturas)
{
    $options = array(
        CURLOPT_URL => 'https://admin.cronometrajeinstantaneo.com/api/lecturas_up?idcontrol='.$idcontrol,
        CURLOPT_CUSTOMREQUEST => 'POST',
        CURLOPT_POSTFIELDS => json_encode(['lecturas' => $lecturas]),
        CURLOPT_COOKIEFILE => "/tmp/cookieFileName",
        CURLOPT_HTTPHEADER => array('Content-Type:application/json'),
    );

    $ch = curl_init();
    curl_setopt_array($ch, $options);
    $result = curl_exec($ch);
    curl_close($ch);
    return $result;
}

function convertir_hora_dbase($hora)
{
    $split_1 = explode('.', $hora);
    $hora_split = explode(':', trim($split_1[0]));

    $hora = (strlen($hora_split[0]) < 2 ? '0' : '').$hora_split[0];
    $minuto = (strlen($hora_split[1]) < 2 ? '0' : '').$hora_split[1];
    $segundo = (strlen($hora_split[2]) < 2 ? '0' : '').$hora_split[2];

    return date("Y-m-d")." ".$hora.":".$minuto.":".$segundo.'.'.$split_1[1].'0';
}

function generar_uuid($idcontrol, $idparticipante)
{
    return 'uuid_rio_'.$idcontrol.'_'.$idparticipante;
}


// ACÁ VAMOS //
$db = dbase_open($file_dbf, 0);
if (!$db)
    log_dbase('Error abriendo archivo de base de datos');

$largadas = [];
$llegadas = [];
$sync_largadas = [];
$sync_llegadas = [];

// Cargo largadas en local
$lecturas_sql = consulta_sql("SELECT uuid, idevento, idcontrol, estado, tiempo, idparticipante FROM lecturas WHERE idcontrol = '$idcontrol_largada'");
while ($lectura = mysqli_fetch_assoc($lecturas_sql))
    $largadas[$lectura['idparticipante']] = $lectura;

// Cargo llegadas en local
$lecturas_sql = consulta_sql("SELECT uuid, idevento, idcontrol, estado, tiempo, idparticipante FROM lecturas WHERE idcontrol = '$idcontrol_llegada'");
while ($lectura = mysqli_fetch_assoc($lecturas_sql))
    $llegadas[$lectura['idparticipante']] = $lectura;

// Recorro la dbase
$número_registros = dbase_numrecords($db);
for ($i = 1; $i <= $número_registros; $i++) {
// for ($i = 1; $i <= 5; $i++) {

    $fila = dbase_get_record($db, $i);
    $idparticipante = intval($fila[$columnas['idparticipante']]);
    $estado = ($fila[$columnas['deleted']] ? 'eliminado' : 'ok');

    // Si es fila vacía continuo
    if ($idparticipante <= 0) {
        log_dbase("Fila $i tiene idparticipante $idparticipante incorrecto;".json_encode($fila));
        continue;
    }

    // Si no existe largada la agrego
    if (!isset($largadas[$fila[$columnas['idparticipante']]])) {

        $lectura = [
            'uuid' => generar_uuid($idcontrol_largada, $idparticipante),
            'idevento' => $idevento,
            'idcontrol' => $idcontrol_largada,
            'idparticipante' => $idparticipante,
            'estado' => $estado,
            'tiempo' => convertir_hora_dbase($fila[$columnas['largada']]),
        ];
        $sync_largadas[] = $lectura;

        log_dbase("Agregar largada $idparticipante;".json_encode($fila));
    }

    // Si largada es diferente actualizo
    else if ($largadas[$idparticipante]['tiempo'] != convertir_hora_dbase($fila[$columnas['largada']])
        || $largadas[$idparticipante]['estado'] != $estado) {

        $largadas[$idparticipante]['tiempo'] = convertir_hora_dbase($fila[$columnas['largada']]);
        $largadas[$idparticipante]['estado'] = $estado;
        $sync_largadas[] = $largadas[$idparticipante];

        log_dbase("Actualizar largada $idparticipante por diferencia entre ".convertir_hora_dbase($fila[$columnas['largada']])." y {$largadas[$idparticipante]['tiempo']};".json_encode($fila));

    }


    // Si no existe llegada la agrego
    if (!isset($llegadas[$fila[$columnas['idparticipante']]])) {

        $lectura = [
            'uuid' => generar_uuid($idcontrol_llegada, $idparticipante),
            'idevento' => $idevento,
            'idcontrol' => $idcontrol_llegada,
            'idparticipante' => $idparticipante,
            'estado' => $estado,
            'tiempo' => convertir_hora_dbase($fila[$columnas['llegada']]),
        ];
        $sync_llegadas[] = $lectura;

        log_dbase("Agregar llegada $idparticipante;".json_encode($fila));
    }

    // Si llegada es diferente actualizo
    else if ($llegadas[$idparticipante]['tiempo'] != convertir_hora_dbase($fila[$columnas['llegada']])
        || $llegadas[$idparticipante]['estado'] != $estado) {

        $llegadas[$idparticipante]['tiempo'] = convertir_hora_dbase($fila[$columnas['llegada']]);
        $llegadas[$idparticipante]['estado'] = $estado;
        $sync_llegadas[] = $llegadas[$idparticipante];

        log_dbase("Actualizar llegada $idparticipante;".json_encode($fila));

    }

    echo '<br>Fila '.$i.': '.json_encode($fila);

}

if (count($sync_largadas)) {
    echo json_encode($sync_largadas);
    $result = sync_up($idcontrol_largada, $sync_largadas);
    log_dbase("Largadas sincronizadas ".count($sync_largadas).";".json_encode($result));
}

if (count($sync_llegadas)) {
    echo json_encode($sync_llegadas);
    $result = sync_up($idcontrol_llegada, $sync_llegadas);
    log_dbase("Llegadas sincronizadas ".count($sync_llegadas).";".json_encode($result));
}
