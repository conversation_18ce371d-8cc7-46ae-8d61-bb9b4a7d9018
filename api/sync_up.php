<?php

exit('No está terminado, usar sync_idcontrol');

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';

if ($_SERVER['REQUEST_METHOD'] != 'POST'){
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR solamente método por POST');
}

$idevento = filter_input(INPUT_POST, 'idevento', FILTER_SANITIZE_SPECIAL_CHARS);

$sync_key = SYNC_KEY; //env

if (NUBE_HIBRIDA){
	header("HTTP/1.1 400 Bad Request");
    exit('ERROR es nube híbrida');
}

if (!$idevento) {
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR en idvento');
}
if (!$sync_key) {
    header("HTTP/1.1 400 Bad Request");
    exit('ERROR en token');
}

$curl = curl_init();
curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
curl_setopt($curl, CURLOPT_HTTPHEADER, array('Accept: application/json', 'Content-Type:application/json'));
curl_setopt($curl, CURLOPT_URL, 'http://localhost/crono-process/public/api/sync_down.php?idevento='.$idevento.'&sync_key='.$sync_key);
$resultado = json_decode(curl_exec($curl));
curl_close($curl);


// Código para sincronizar  //
foreach ($resultado as $tabla => $valor) {
	$query = "";
	$temp_query = "";

	foreach ($valor as $campo => $valor) {
		if (!is_numeric($campo)){
			if (!$temp_query){
				$temp_query .= " $campo = '$valor'";
			} else {
				$temp_query .= ", $campo = '$valor'";
			}
		} elseif (gettype($valor) == 'object') {
			$query_dato = "";
			$temp_query_dato = "";
			foreach ($valor as $dato => $valor_dato) {
				if (!is_numeric($dato)){
					if (!$temp_query_dato){
						$temp_query_dato .= " $dato = '$valor_dato'";
					} else {
						$temp_query_dato .= ", $dato = '$valor_dato'";
					}
				}
			}
			if ($temp_query_dato){
				$query_dato = "INSERT INTO ".$tabla." SET ";
				$query_dato .= $temp_query_dato . " ON DUPLICATE KEY UPDATE " . $temp_query_dato;
				echo $query_dato;
				//consulta_sql($query_dato);
			}
		}
	}
	if ($temp_query){
		$query = "INSERT INTO ".$tabla." SET ";
		$query .= $temp_query . " ON DUPLICATE KEY UPDATE " . $temp_query;
	}
	echo $query;
	//consulta_sql($query);
}

?>