@extends('layout')

@section('content')

<div class="form">
    <section class="datos">
        <div class="mensaje-exito" style="text-align: center; padding: 30px; background: #d4edda; border: 1px solid #c3e6cb; border-radius: 5px; margin: 20px 0;">
            <h2 style="color: #155724; margin: 0 0 15px 0;">✅ {{ $mensaje }}</h2>

            @if(isset($estados))
                <div style="margin-top: 20px; padding: 15px; background: #fff; border-radius: 5px;">
                    <h3 style="margin: 0 0 15px 0; color: #333;">Estado actual de la inscripción:</h3>
                    <ul style="list-style: none; padding: 0; margin: 0;">
                        <li style="margin: 5px 0;"><strong>Estado general:</strong>
                            @if($estados['estado'] == 'inscripto')
                                <span style="color: #28a745;">✅ Inscripto</span>
                            @else
                                <span style="color: #ffc107;">⏳ Pre-inscripto</span>
                            @endif
                        </li>
                        <li style="margin: 5px 0;"><strong>Estado de ficha:</strong>
                            @if($estados['estado_ficha'] == 'aprobado')
                                <span style="color: #28a745;">✅ Aprobado</span>
                            @else
                                <span style="color: #dc3545;">❌ Pendiente</span>
                            @endif
                        </li>
                        <li style="margin: 5px 0;"><strong>Estado de pago:</strong>
                            @if($estados['estado_pago'] == 'aprobado')
                                <span style="color: #28a745;">✅ Aprobado</span>
                            @else
                                <span style="color: #dc3545;">❌ Pendiente</span>
                            @endif
                        </li>
                    </ul>
                </div>
            @endif

            @if(isset($inscripcion))
                <div style="margin-top: 20px;">
                    <a href="{{ URL_ADMIN }}{{ $inscripcion->getRoute()->participantesxequipo() == 1 ? 'participante' : 'equipo' }}?id={{ $inscripcion->idinscripcion() }}"
                       style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;"
                       target="_blank">
                        Ver inscripción en el panel
                    </a>
                </div>
            @endif
        </div>
    </section>
</div>

@endsection