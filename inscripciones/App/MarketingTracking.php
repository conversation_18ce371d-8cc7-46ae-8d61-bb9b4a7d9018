<?php

namespace Inscripciones;

use PDO;

class MarketingTracking
{
    private $db;
    private $session_id;
    private $tracking_data;
    private $is_enabled = false;

    public function __construct($idevento = null)
    {
        // Solo inicializar si el marketing está habilitado
        if ($idevento) {
            $this->db = Db::db();
            $stmt = $this->db->prepare("SELECT marketing FROM eventos WHERE idevento = :idevento");
            $stmt->execute(['idevento' => $idevento]);
            $result = $stmt->fetch(PDO::FETCH_ASSOC);
            $this->is_enabled = $result && $result['marketing'];
        }
    }

    public function isEnabled()
    {
        return $this->is_enabled;
    }

    public function trackVisit($idevento, $conversion_step = 'event_visit', $idinscripcion = null)
    {
        if (!$this->is_enabled) return true;

        // Solo guardar si hay UTM o es un paso de conversión importante
        $utm_params = $this->extractUtmParams();
        $has_utm = !empty($utm_params['utm_source']) || !empty($utm_params['utm_medium']) || !empty($utm_params['utm_campaign']);
        $is_conversion_step = in_array($conversion_step, ['form_complete', 'payment_complete']);

        if (!$has_utm && !$is_conversion_step) {
            return true; // No guardar visitas simples sin UTM
        }

        $data = [
            'idevento' => $idevento,
            'idinscripcion' => $idinscripcion,
            'utm_source' => $utm_params['utm_source'] ?? null,
            'utm_medium' => $utm_params['utm_medium'] ?? null,
            'utm_campaign' => $utm_params['utm_campaign'] ?? null,
            'conversion_step' => $conversion_step,
            'conversion_value' => $this->getConversionValue($idinscripcion)
        ];

        return $this->createTracking($data);
    }

    private function extractUtmParams()
    {
        $params = [];
        $utm_keys = ['utm_source', 'utm_medium', 'utm_campaign'];

        // Buscar en GET y POST
        $sources = [$_GET, $_POST];

        foreach ($sources as $source) {
            foreach ($utm_keys as $key) {
                if (isset($source[$key]) && !isset($params[$key])) {
                    $params[$key] = filter_var($source[$key], FILTER_SANITIZE_STRING);
                }
            }
        }

        return $params;
    }



    private function createTracking($data)
    {
        $stmt = $this->db->prepare(
            "INSERT INTO marketing_tracking
             (idevento, idinscripcion, utm_source, utm_medium, utm_campaign,
              conversion_step, conversion_value)
             VALUES
             (:idevento, :idinscripcion, :utm_source, :utm_medium, :utm_campaign,
              :conversion_step, :conversion_value)"
        );

        return $stmt->execute($data);
    }

    private function getConversionValue($idinscripcion)
    {
        if (!$idinscripcion) return null;

        $stmt = $this->db->prepare(
            "SELECT p.precio
             FROM pagos p
             JOIN precios pr ON p.idprecio = pr.idprecio
             WHERE p.idinscripcion = :idinscripcion
             AND p.estado = 'aprobado'
             ORDER BY p.fecha DESC LIMIT 1"
        );
        $stmt->execute(['idinscripcion' => $idinscripcion]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['precio'] : null;
    }


}