<?php

namespace Inscripciones;

class Logger
{
    /**
     * Método genérico para loguear cualquier acción
     *
     * @param string $tipo Tipo de acción (ej: 'APROBAR_FICHA', 'ACCESO_ENLACE', 'WEBHOOK_PAGO')
     * @param array $datos Datos a loguear
     * @param string $archivo Nombre del archivo de log (sin fecha)
     */
    public function log($tipo, $datos = [], $archivo = 'general')
    {
        $timestamp = date('Y-m-d H:i:s');
        $ip = $_SERVER['REMOTE_ADDR'] ?? 'N/A';
        $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? 'N/A';

        // Construir línea de log
        $log_line = $timestamp . ';' . $tipo;

        // Agregar datos adicionales
        foreach ($datos as $valor) {
            $log_line .= ';' . (is_array($valor) ? json_encode($valor) : $valor);
        }

        // Agregar IP y User-Agent
        $log_line .= ';' . $ip . ';' . $user_agent . "\n";

        // Guardar en archivo
        $archivo_completo = PATH_LOGS . '/' . date("Y-m-d") . '_' . $archivo . '.log';
        file_put_contents($archivo_completo, $log_line, FILE_APPEND);
    }
}