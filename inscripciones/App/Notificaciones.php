<?php

namespace Inscripciones;

class Notificaciones
{
    public function notificar($tipo, Inscripcion $inscripcion, $opciones = [])
    {
        // Determinar asunto y contenido según el tipo de notificación
        $asunto = $this->asuntoPorTipo($tipo, $inscripcion);
        $contenido = $this->contenidoPorTipo($tipo, $inscripcion, $opciones);
        $remitente = [$inscripcion->getEvento()->mail => $inscripcion->getEvento()->nombre_organizacion];

        // Renderizar el contenido usando Blade
        $blade = new \eftec\bladeone\BladeOne(
            __DIR__ . '/../Views',
            __DIR__ . '/../cache',
            defined('ESTADO') && ESTADO == 'desarrollo' ? \eftec\bladeone\BladeOne::MODE_DEBUG : \eftec\bladeone\BladeOne::MODE_AUTO
        );
        $html = $blade->run('mail', ['contenido' => $contenido]);

        // Manejar destinatarios según el tipo
        $mail_organizador = $inscripcion->getEvento()->mail;
        $nombre_organizador = $inscripcion->getEvento()->nombre_organizacion;
        $participantes = [];

        if ($tipo == 'comprobante_pago') {
            // Solo organizador
            $participantes = [];
        } else {
            // Participantes y organizador
            $participantes = $inscripcion->destinatariosParticipantes();
        }

        // Enviar mail a participantes (si hay)
        $resultado_envio_participantes = null;
        if (!empty($participantes)) {
            $resultado_envio_participantes = enviar_mail(
                $participantes,
                $remitente,
                $asunto,
                $html
            );
            // Log de notificaciones participantes
            $log_line = date('Y-m-d H:i:s').';'
                . $tipo . ';'
                . str_replace(["\r","\n",";"], ' ', $asunto) . ';'
                . implode(',', array_keys($participantes)) . ';'
                . ($resultado_envio_participantes ? 'true' : 'false') . "\n";
            file_put_contents(PATH_LOGS.'/'.date("Y-m-d").'_notificaciones.log', $log_line, FILE_APPEND);
        }

        // Enviar mail al organizador (si corresponde)
        if ($mail_organizador) {
            $contenido_organizador = $this->contenidoOrganizadorPorTipo($tipo, $inscripcion, $opciones);
            $html_organizador = $blade->run('mail_organizador', ['contenido' => $contenido_organizador]);
            $resultado_envio_organizador = enviar_mail(
                [$mail_organizador => $nombre_organizador],
                $remitente,
                $asunto,
                $html_organizador
            );
            // Log de notificaciones organizador
            $log_line = date('Y-m-d H:i:s').';'
                . $tipo . ';'
                . str_replace(["\r","\n",";"], ' ', $asunto) . ';'
                . $mail_organizador . ';'
                . ($resultado_envio_organizador ? 'true' : 'false') . "\n";
            file_put_contents(PATH_LOGS.'/'.date("Y-m-d").'_notificaciones.log', $log_line, FILE_APPEND);
        }

        // Si corresponde, enviar WhatsApp (futuro)
        if ($this->debeEnviarWhatsapp($tipo, $inscripcion)) {
            // TODO: Integrar envío de WhatsApp aquí
            // $this->enviarWhatsapp($inscripcion->telefono, $mensajeWhatsapp);
        }
    }

    private function asuntoPorTipo($tipo, Inscripcion $inscripcion)
    {
        switch ($tipo) {
            case 'inscripto':
                return 'Inscripción Confirmada en ' . $inscripcion->getEvento()->nombre;
            case 'preinscripto':
                return 'Pre-inscripción en ' . $inscripcion->getEvento()->nombre;
            case 'comprobante_pago':
                return 'Nuevo comprobante de pago recibido';
            default:
                return 'Notificación de ' . $inscripcion->getEvento()->nombre;
        }
    }

    private function contenidoPorTipo($tipo, Inscripcion $inscripcion, $opciones = [])
    {
        switch ($tipo) {
            case 'inscripto':
                return $inscripcion->prepararTextos($inscripcion->getEvento()->mail_inscripto);
            case 'preinscripto':
                return $inscripcion->prepararTextos($inscripcion->getEvento()->mail_preinscripto);
            case 'comprobante_pago':
                // Solo mensaje, link a inscripción y botones de comprobante
                $equipo = $inscripcion->getRoute()->participantesxequipo() == 1 ? 'participante' : 'equipo';
                $link_inscripcion = '<a href="'.URL_ADMIN.$equipo.'?id='.$inscripcion->idinscripcion().'" style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;margin:5px 0 18px 0;" target="_blank">Ver inscripción en el panel</a>';
                $mail = '<p><b>' . ($opciones['mensaje'] ?? 'Se recibió un nuevo comprobante de pago.') . '</b></p>';
                $mail .= $link_inscripcion;
                if (!empty($opciones['links'])) {
                    $mail .= '<br><b>Comprobantes de pago enviados:</b><br>' . implode('<br>', $opciones['links']);
                }
                return $mail;
            default:
                return '';
        }
    }

    private function contenidoOrganizadorPorTipo($tipo, Inscripcion $inscripcion, $opciones = [])
    {
        switch ($tipo) {
            case 'inscripto':
                $contenido = $inscripcion->mailOrganizador('inscripto');
                $contenido .= $this->generarBotonVerInscripcion($inscripcion);
                return $contenido;
            case 'preinscripto':
                $contenido = $inscripcion->mailOrganizador('preinscripto');
                $contenido .= $this->generarBotonesAprobacion($inscripcion);
                return $contenido;
            case 'comprobante_pago':
                // Solo mensaje, link a inscripción y botones de comprobante
                $equipo = $inscripcion->getRoute()->participantesxequipo() == 1 ? 'participante' : 'equipo';
                $link_inscripcion = '<a href="'.URL_ADMIN.$equipo.'?id='.$inscripcion->idinscripcion().'" style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;margin:5px 0 18px 0;" target="_blank">Ver inscripción en el panel</a>';
                $mail = '<p><b>' . ($opciones['mensaje'] ?? 'Se recibió un nuevo comprobante de pago.') . '</b></p>';
                $mail .= $link_inscripcion;
                if (!empty($opciones['links'])) {
                    $mail .= '<br><b>Comprobantes de pago enviados:</b><br>' . implode('<br>', $opciones['links']);
                }
                $mail .= $this->generarBotonesAprobacion($inscripcion, 'pago');
                return $mail;
            default:
                return '';
        }
    }

    private function generarBotonVerInscripcion(Inscripcion $inscripcion)
    {
        $equipo = $inscripcion->getRoute()->participantesxequipo() == 1 ? 'participante' : 'equipo';
        $botones = '<br><br><div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">';
        $botones .= '<h3 style="margin: 0 0 15px 0; color: #333;">Acciones rápidas:</h3>';
        $botones .= '<a href="'.URL_ADMIN.$equipo.'?id='.$inscripcion->idinscripcion().'" style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;margin:5px 10px 5px 0;" target="_blank">👁️ Ver inscripción en el panel</a>';
        $botones .= '</div>';

        return $botones;
    }

    private function generarBotonesAprobacion(Inscripcion $inscripcion, $tipo = 'ambos')
    {
        $equipo = $inscripcion->getRoute()->participantesxequipo() == 1 ? 'participante' : 'equipo';
        $botones = '<br><br><div style="margin-top: 20px; padding: 15px; background: #f8f9fa; border-radius: 5px;">';
        $botones .= '<h3 style="margin: 0 0 15px 0; color: #333;">Acciones rápidas:</h3>';

        // Botón "Ver inscripción" siempre presente
        $botones .= '<a href="'.URL_ADMIN.$equipo.'?id='.$inscripcion->idinscripcion().'" style="display:inline-block;padding:10px 18px;background:#ff770d;color:#fff;text-decoration:none;border-radius:5px;margin:5px 10px 5px 0;" target="_blank">👁️ Ver inscripción en el panel</a>';

        if ($tipo == 'ambos' || $tipo == 'ficha') {
            $url_aprobar_ficha = $inscripcion->getRoute()->generateUri($inscripcion->idinscripcion(), 'aprobar_ficha');
            $botones .= '<a href="'.$url_aprobar_ficha.'" style="display:inline-block;padding:10px 18px;background:#28a745;color:#fff;text-decoration:none;border-radius:5px;margin:5px 10px 5px 0;" target="_blank">✅ Aprobar Ficha</a>';
        }

        if ($tipo == 'ambos' || $tipo == 'pago') {
            $url_aprobar_pago = $inscripcion->getRoute()->generateUri($inscripcion->idinscripcion(), 'aprobar_pago');
            $botones .= '<a href="'.$url_aprobar_pago.'" style="display:inline-block;padding:10px 18px;background:#007bff;color:#fff;text-decoration:none;border-radius:5px;margin:5px 10px 5px 0;" target="_blank">💰 Aprobar Pago</a>';
        }

        $botones .= '</div>';

        return $botones;
    }

    private function debeEnviarWhatsapp($tipo, Inscripcion $inscripcion)
    {
        // Por el momento no se envía WhatsApp
        return false;

        // Solo si el participante tiene teléfono válido
        if (isset($inscripcion->telefono) && preg_match('/^[0-9]{10,15}$/', preg_replace('/\D/', '', $inscripcion->telefono))) {
            return true;
        }
        return false;
    }

    // private function enviarWhatsapp($telefono, $mensaje) { ... } // Futuro
}