<?php

namespace Inscripciones;

use PDO;

class MarketingPixels
{
    private $db;
    private $idevento;
    private $pixels;
    private $is_enabled = false;

    public function __construct($idevento)
    {
        $this->db = Db::db();
        $this->idevento = $idevento;

        // Verificar si el marketing está habilitado
        $stmt = $this->db->prepare("SELECT marketing FROM eventos WHERE idevento = :idevento");
        $stmt->execute(['idevento' => $idevento]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        $this->is_enabled = $result && $result['marketing'];

        if ($this->is_enabled) {
            $this->loadPixels();
        }
    }

    public function isEnabled()
    {
        return $this->is_enabled;
    }

    private function loadPixels()
    {
        $stmt = $this->db->prepare(
            "SELECT * FROM marketing_pixels
             WHERE idevento = :idevento AND is_active = 1
             ORDER BY pixel_type, pixel_name"
        );
        $stmt->execute(['idevento' => $this->idevento]);
        $this->pixels = $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    public function getPixelsForStep($step)
    {
        if (!$this->is_enabled) return [];

        $step_pixels = [];
        $track_field = 'track_' . $step;

        foreach ($this->pixels as $pixel) {
            if (isset($pixel[$track_field]) && $pixel[$track_field]) {
                $step_pixels[] = $pixel;
            }
        }

        return $step_pixels;
    }

    public function generatePixelCode($pixel, $step, $conversion_data = [])
    {
        if (!$this->is_enabled) return '';

        // Si tiene código personalizado, usarlo
        if (!empty($pixel['pixel_code'])) {
            return $this->generateCustomPixelCode($pixel, $step, $conversion_data);
        }

        switch ($pixel['pixel_type']) {
            case 'google_analytics':
                return $this->generateGoogleAnalyticsCode($pixel, $step, $conversion_data);
            case 'google_tag_manager':
                return $this->generateGoogleTagManagerCode($pixel, $step, $conversion_data);
            case 'facebook_pixel':
            case 'meta_pixel':
                return $this->generateMetaPixelCode($pixel, $step, $conversion_data);
            case 'custom':
                return $this->generateCustomPixelCode($pixel, $step, $conversion_data);
            default:
                return '';
        }
    }

    private function generateMetaPixelCode($pixel, $step, $conversion_data)
    {
        $event_name = $this->getMetaEventName($step);
        $value = $conversion_data['value'] ?? null;

        // Usar el archivo JavaScript externo y solo inicializar con el ID
        $code = "<script>
        // Inicializar Meta Pixel con ID específico
        initMetaPixel('{$pixel['pixel_id']}');

        // Disparar evento específico
        trackMetaPixelEvent('{$event_name}', {
            'value': " . ($value ?: 'null') . ",
            'currency': 'ARS',
            'content_name': '{$pixel['pixel_name']}'
        });
        </script>";

        return $code;
    }

    private function generateGoogleAnalyticsCode($pixel, $step, $conversion_data)
    {
        $event_name = $this->getEventName($step);
        $event_value = $conversion_data['value'] ?? null;

        $code = "<script>
        gtag('event', '{$event_name}', {
            'event_category': 'inscripciones',
            'event_label': '{$pixel['pixel_name']}',
            'value': " . ($event_value ?: 'null') . "
        });
        </script>";

        return $code;
    }

    private function generateGoogleTagManagerCode($pixel, $step, $conversion_data)
    {
        $event_name = $this->getEventName($step);

        $code = "<script>
        dataLayer.push({
            'event': '{$event_name}',
            'event_category': 'inscripciones',
            'event_label': '{$pixel['pixel_name']}',
            'conversion_value': " . ($conversion_data['value'] ?? 'null') . ",
            'currency': 'ARS'
        });
        </script>";

        return $code;
    }

    private function generateCustomPixelCode($pixel, $step, $conversion_data)
    {
        // Si tiene código personalizado, usarlo directamente
        if (!empty($pixel['pixel_code'])) {
            return $pixel['pixel_code'];
        }

        // Si no, usar custom_events
        $custom_events = json_decode($pixel['custom_events'], true) ?? [];
        $event_config = $custom_events[$step] ?? null;

        if (!$event_config) return '';

        $code = "<script>
        {$event_config['code']}
        </script>";

        return $code;
    }

    private function getEventName($step)
    {
        $events = [
            'site_visit' => 'site_view',
            'event_visit' => 'form_view',
            'form_complete' => 'form_submit',
            'payment_complete' => 'purchase'
        ];

        return $events[$step] ?? 'custom_event';
    }

    private function getMetaEventName($step)
    {
        $events = [
            'site_visit' => 'ViewContent',
            'event_visit' => 'ViewContent',
            'form_complete' => 'Lead',
            'payment_complete' => 'Purchase'
        ];

        return $events[$step] ?? 'CustomEvent';
    }
}