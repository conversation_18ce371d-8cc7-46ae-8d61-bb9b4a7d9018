<?php

require __DIR__.'/../../cronometrajeinstantaneo.env';
require __DIR__.'/../../funciones.php';
require __DIR__.'/../../vendor/autoload.php';

use Inscripciones\Logger;

// Función para mostrar estadísticas de un archivo de log
function mostrarEstadisticas($archivo, $fecha = null) {
    $fecha = $fecha ?: date('Y-m-d');
    $archivo_completo = PATH_LOGS.'/'.$fecha.'_'.$archivo.'.log';
    
    if (!file_exists($archivo_completo)) {
        echo "No hay logs para $archivo en la fecha: $fecha\n";
        return;
    }
    
    $lineas = file($archivo_completo, FILE_IGNORE_NEW_LINES);
    $stats = [];
    
    foreach ($lineas as $linea) {
        $campos = explode(';', $linea);
        if (count($campos) >= 2) {
            $tipo = $campos[1];
            $stats[$tipo] = ($stats[$tipo] ?? 0) + 1;
        }
    }
    
    echo "=== ESTADÍSTICAS DE $archivo - $fecha ===\n";
    foreach ($stats as $tipo => $cantidad) {
        echo "$tipo: $cantidad\n";
    }
    echo "Total: " . array_sum($stats) . "\n";
    echo "==========================================\n\n";
}

// Función para mostrar logs detallados
function mostrarLogsDetallados($archivo, $fecha = null, $tipo_filtro = null) {
    $fecha = $fecha ?: date('Y-m-d');
    $archivo_completo = PATH_LOGS.'/'.$fecha.'_'.$archivo.'.log';
    
    if (!file_exists($archivo_completo)) {
        echo "No hay logs para $archivo en la fecha: $fecha\n";
        return;
    }
    
    echo "=== LOGS DETALLADOS DE $archivo - $fecha ===\n";
    $lineas = file($archivo_completo, FILE_IGNORE_NEW_LINES);
    
    foreach ($lineas as $linea) {
        $campos = explode(';', $linea);
        if (count($campos) >= 2) {
            $tipo = $campos[1];
            
            // Filtrar por tipo si se especifica
            if ($tipo_filtro && $tipo !== $tipo_filtro) {
                continue;
            }
            
            $timestamp = $campos[0];
            echo "[$timestamp] $tipo";
            
            // Mostrar datos relevantes según el tipo
            if ($archivo == 'aprobaciones' && count($campos) >= 6) {
                $idevento = $campos[2];
                $idinscripcion = $campos[3];
                $codigo_evento = $campos[4];
                $nombre = $campos[5];
                $resultado = isset($campos[6]) ? $campos[6] : 'N/A';
                echo " - Evento: $codigo_evento, Inscripción: $idinscripcion, Participante: $nombre, Resultado: $resultado";
            } elseif ($archivo == 'visitas' && count($campos) >= 8) {
                $codigo = $campos[2];
                $equipo = $campos[3];
                $idinscripcion = $campos[4];
                $accion = $campos[5];
                $method = $campos[6];
                echo " - Código: $codigo, Equipo: $equipo, Inscripción: $idinscripcion, Acción: $accion, Método: $method";
            } elseif ($archivo == 'accesos' && count($campos) >= 8) {
                $idevento = $campos[2];
                $idinscripcion = $campos[3];
                $codigo_evento = $campos[4];
                $nombre = $campos[5];
                $accion = $campos[6];
                $valido = $campos[7];
                echo " - Evento: $codigo_evento, Inscripción: $idinscripcion, Acción: $accion, Válido: $valido";
            }
            
            echo "\n";
        }
    }
    echo "==========================================\n\n";
}

// Procesar argumentos de línea de comandos
$fecha = null;
$archivo = 'aprobaciones';
$mostrar_detallado = false;
$tipo_filtro = null;

foreach ($argv as $arg) {
    if (preg_match('/^--fecha=(.+)$/', $arg, $matches)) {
        $fecha = $matches[1];
    } elseif (preg_match('/^--archivo=(.+)$/', $arg, $matches)) {
        $archivo = $matches[1];
    } elseif ($arg == '--detallado') {
        $mostrar_detallado = true;
    } elseif (preg_match('/^--tipo=(.+)$/', $arg, $matches)) {
        $tipo_filtro = $matches[1];
    }
}

// Mostrar estadísticas
mostrarEstadisticas($archivo, $fecha);

// Mostrar logs detallados si se solicita
if ($mostrar_detallado) {
    mostrarLogsDetallados($archivo, $fecha, $tipo_filtro);
}

echo "Uso:\n";
echo "php estadisticas.php [--fecha=YYYY-MM-DD] [--archivo=aprobaciones|visitas|accesos] [--detallado] [--tipo=TIPO]\n";
echo "Ejemplos:\n";
echo "php estadisticas.php\n";
echo "php estadisticas.php --fecha=2024-01-15\n";
echo "php estadisticas.php --archivo=visitas --detallado\n";
echo "php estadisticas.php --archivo=aprobaciones --tipo=APROBAR_FICHA --detallado\n"; 