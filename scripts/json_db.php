<?php
// Cargar el archivo JSON
$filePath = 'IC62-2025-02-23.json';
$jsonData = file_get_contents($filePath);
$data = json_decode($jsonData, true);

// Verificar si el JSON se cargó correctamente
if (!$data || !isset($data['rows'])) {
    die("Error al cargar o decodificar el JSON.");
}

// Construir la consulta INSERT
$values = [];

foreach ($data['rows'] as $row) {
    $idlectura = isset($row['idlectura']) ? $row['idlectura'] : 'NULL';
    $uuid = isset($row['uuid']) ? $row['uuid'] : '';
    $idevento = isset($row['codigo']) ? $row['codigo'] : '';
    $idcontrol = isset($row['idcrono']) ? $row['idcrono'] : 'NULL';
    $estado = isset($row['state']) ? $row['state'] : '';
    $tiempo = isset($row['tiempo']) ? $row['tiempo'] : '';
    $idparticipante = isset($row['idparticipante']) ? $row['idparticipante'] : 'NULL';

    $values[] = sprintf(
        "(%d, '%s', '%s', %d, '%s', '%s', %d)",
        $idlectura,
        addslashes($uuid),
        addslashes($idevento),
        $idcontrol,
        addslashes($estado),
        addslashes($tiempo),
        $idparticipante
    );
}

$insertQuery = "INSERT INTO lecturas (idlectura, uuid, idevento, idcontrol, estado, tiempo, idparticipante) VALUES \n";
$insertQuery .= implode(",\n", $values) . ";";

// Guardar la consulta en un archivo
$outputFile = 'insert_lecturas.sql';
file_put_contents($outputFile, $insertQuery);

echo "Consulta INSERT generada y guardada en '$outputFile'.";
?>
