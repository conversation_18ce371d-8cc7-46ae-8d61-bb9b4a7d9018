<?php

require __DIR__.'/../cronometrajeinstantaneo.env';
require __DIR__.'/../funciones.php';
require __DIR__.'/../vendor/autoload.php';


$check = filter_input(INPUT_GET, 'check', FILTER_SANITIZE_SPECIAL_CHARS);
$idevento = filter_input(INPUT_GET, 'idevento', FILTER_VALIDATE_INT);

if ($check != 'jsWWQwymkSOuiHPVzbrbwVpNw0kA4Taw' || !$idevento) {
    header("HTTP/1.1 400 Bad Request");
    enviar_mail(MAIL_SOPORTE, MAIL_SISTEMA, 'Intento de activar vivo', 'En https://cronometrajeinstantaneo.com/scripts/activar_vivo.php han intentado acceder con el check '.$check.' y el idevento '.$idevento);
    exit('El url ingresado es incorrecto');
}

try {
    $bd = new PDO('mysql:host='.BD_HOST.';dbname='.BD_BD, BD_USER, BD_PASS, array(PDO::ATTR_EMULATE_PREPARES => false, PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION, PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"));

    $resultado_sql = $bd->query(
        "UPDATE eventos SET vivo = '1'
        WHERE idevento = $idevento");

    $evento = $bd->query(
        "SELECT eventos.idorganizacion, eventos.nombre, fecha,
            organizaciones.nombre AS nombre_organizacion, organizaciones.mail AS mail_organizacion
        FROM eventos
            LEFT JOIN organizaciones ON eventos.idorganizacion = organizaciones.idorganizacion
        WHERE idevento = $idevento")->fetch(PDO::FETCH_ASSOC);

    $body = 'Vivo activado<br><br>'
        .'Organización: '.$evento['nombre_organizacion'].' ('.$evento['mail_organizacion'].' | ID '.$evento['idorganizacion'].')<br>'
        .'Evento: '.$evento['nombre'].' (ID '.$idevento.')<br>'
        .'Fecha: '.$evento['fecha'].'<br>';
    enviar_mail(MAIL_SOPORTE, MAIL_SISTEMA, 'Nuevo vivo activado', $body);

    echo 'Vivo activado';

} catch(PDOException $ex) {
    if (ESTADO == 'desarrollo') {
        echo $ex -> getMessage();
    } else {
        echo "Ha ocurrido un error en nuestra base de datos y nuestro equipo de desarrollo se encuentra trabajando en el problema";
        enviar_mail(MAIL_SOPORTE, MAIL_SISTEMA, 'Error consultando la base de datos', $ex -> getMessage());
    }
}
