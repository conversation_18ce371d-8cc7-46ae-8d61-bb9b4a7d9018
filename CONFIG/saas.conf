####### SAAS #######
<VirtualHost *:80>
    ServerName saasargentina.des
    ServerAlias www.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/www/public
    <Directory "/home/<USER>/www/saasargentina/services/www/public/">
        Options FollowSymLinks Indexes
        AllowOverride None
        RewriteEngine On

        RewriteRule google28a12d46db9cf0d6.html google28a12d46db9cf0d6.html [L]
        RewriteRule BingSiteAuth.xml BingSiteAuth.xml [L]
        RewriteRule sitemap.xml sitemap.xml [L]
        RewriteRule favicon.ico favicon.ico [L]
        RewriteRule robots.txt robots.txt [L]
        RewriteRule ^css/.*$ - [L]
        RewriteRule ^img/.*$ - [L]
        RewriteRule ^js/.*$ - [L]

        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
        Require all granted
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName app.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/app/public
</VirtualHost>

<VirtualHost *:80>
    ServerName scripts.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public
</VirtualHost>

<VirtualHost *:80>
    ServerName api.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/api/public
    <Directory "/home/<USER>/www/saasargentina/services/api/public/">
        Options FollowSymLinks Indexes
        AllowOverride None
        RewriteEngine On
        RewriteRule ^v0.1/(.*)$ v0.1/index.php?url=$1 [QSA,L]
        RewriteRule ^v0.2/(.*)$ v0.2/index.php?url=$1 [QSA,L]
        RewriteRule ^ index.php [L]

        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName login.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/login/public
    <Directory "/home/<USER>/www/saasargentina/services/login/public/">
        RewriteEngine On
        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName informes.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/informes/public
    <Directory "/home/<USER>/www/saasargentina/services/informes/public/">
        RewriteEngine On
        # Redirect Trailing Slashes If Not A Folder...
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)/$ /$1 [L,R=301]

        # Handle Front Controller...
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^ index.php [L]

        # Handle Authorization Header
        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    </Directory>
</VirtualHost>
