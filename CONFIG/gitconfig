[user]
name = <PERSON><PERSON>
email = and<PERSON><PERSON><EMAIL>

[merge]
    tool = meld
[mergetool "meld"]
    cmd = meld "$LOCAL" "$BASE" "$REMOTE" --output "$MERGED"
[color]
	ui = true
[color "diff-highlight"]
	oldNormal = red bold
	oldHighlight = red bold 52
	newNormal = green bold
	newHighlight = green bold 22
[color "diff"]
	meta = yellow
	frag = magenta bold
	commit = yellow bold
	old = red bold
	new = green bold
	whitespace = red reverse
[core]
	pager = diff-so-fancy | less --tabs=4 -RFX
[diff-so-fancy]
	first-run = false
