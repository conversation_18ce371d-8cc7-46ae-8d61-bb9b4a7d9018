####### SAAS #######
<VirtualHost *:80>
    ServerName saasargentina.des
    ServerAlias www.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/www/public
    <Directory "/home/<USER>/www/saasargentina/services/www/public/">
        Options FollowSymLinks Indexes
        AllowOverride None
        RewriteEngine On

        RewriteRule google28a12d46db9cf0d6.html google28a12d46db9cf0d6.html [L]
        RewriteRule BingSiteAuth.xml BingSiteAuth.xml [L]
        RewriteRule sitemap.xml sitemap.xml [L]
        RewriteRule favicon.ico favicon.ico [L]
        RewriteRule robots.txt robots.txt [L]
        RewriteRule ^css/.*$ - [L]
        RewriteRule ^img/.*$ - [L]
        RewriteRule ^js/.*$ - [L]

        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
        Require all granted
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName app.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/app/public
</VirtualHost>

<VirtualHost *:80>
    ServerName scripts.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/scripts/public
</VirtualHost>

<VirtualHost *:80>
    ServerName api-dev.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/lambda/api/public
</VirtualHost>

<VirtualHost *:80>
    ServerName api.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/api/public
    <Directory "/home/<USER>/www/saasargentina/services/api/public/">
        Options FollowSymLinks Indexes
        AllowOverride None
        RewriteEngine On
        RewriteRule ^v0.1/(.*)$ v0.1/index.php?url=$1 [QSA,L]
        RewriteRule ^v0.2/(.*)$ v0.2/index.php?url=$1 [QSA,L]
        RewriteRule ^v1/(.*)$ v1/index.php?url=$1 [QSA,L]
        RewriteRule ^ml/(.*)$ ml/index.php?url=$1 [QSA,L]
        RewriteRule ^ index.php [L]

        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName login.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/login/public
    <Directory "/home/<USER>/www/saasargentina/services/login/public/">
        RewriteEngine On
        RewriteRule ^(.*)$ index.php?url=$1 [QSA,L]
    </Directory>
</VirtualHost>

<VirtualHost *:80>
    ServerName informes.saasargentina.des
    DocumentRoot /home/<USER>/www/saasargentina/services/informes/public
    <Directory "/home/<USER>/www/saasargentina/services/informes/public/">
        RewriteEngine On
        # Redirect Trailing Slashes If Not A Folder...
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)/$ /$1 [L,R=301]

        # Handle Front Controller...
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteRule ^ index.php [L]

        # Handle Authorization Header
        RewriteCond %{HTTP:Authorization} .
        RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
    </Directory>
</VirtualHost>


# ####### CRONO #######
<VirtualHost *:80>
    ServerName cronometrajeinstantaneo.lan
    ServerAlias cronometrajeinstantaneo.des, crono.des
    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/www
</VirtualHost>

<VirtualHost *:80>
    ServerName admin.cronometrajeinstantaneo.lan
    ServerAlias admin.cronometrajeinstantaneo.des, admin.crono.des
    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/admin/public
</VirtualHost>

<VirtualHost *:80>
    ServerName beta.cronometrajeinstantaneo.lan
    ServerAlias beta.cronometrajeinstantaneo.des, beta.crono.des
    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/filament/public
</VirtualHost>

<VirtualHost *:80>
    ServerName app.cronometrajeinstantaneo.lan
    ServerAlias app.cronometrajeinstantaneo.des, app.crono.des
    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/app/www
</VirtualHost>

<VirtualHost *:80>
    ServerName vivo.cronometrajeinstantaneo.lan
    ServerAlias vivo.cronometrajeinstantaneo.des, vivo.crono.des
    DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/vivo/src
</VirtualHost>

# <VirtualHost *:80>
#     ServerName server.cronometrajeinstantaneo.des
#     DocumentRoot /home/<USER>/www/cronometrajeinstantaneo/server-local-vivo
# </VirtualHost>

# ####### ANDRESMAIDEN #######

<VirtualHost *:80>
    ServerName andresmisiak.des
    DocumentRoot /home/<USER>/www/andresmisiak/public
</VirtualHost>

<VirtualHost *:80>
    ServerName tools.andresmisiak.des
    DocumentRoot /home/<USER>/www/andresmisiak/tools
</VirtualHost>

<VirtualHost *:80>
    ServerName brain.andresmisiak.des
    DocumentRoot /home/<USER>/www/andresmisiak/brain
</VirtualHost>

<VirtualHost *:80>
    ServerName ai.andresmisiak.des
    DocumentRoot /home/<USER>/www/andresmisiak/ai
</VirtualHost>

# ####### OTROS #######

<VirtualHost *:80>
   ServerName laravel.des
   DocumentRoot /home/<USER>/www/laravel/public
</VirtualHost>

<VirtualHost *:80>
   ServerName woocommerce.des
   DocumentRoot /home/<USER>/www/woocommerce
</VirtualHost>

<VirtualHost *:80>
   ServerName visiteangostura.des
   DocumentRoot /home/<USER>/www/visiteangostura/public_html
</VirtualHost>

<VirtualHost *:80>
    ServerName simplementeviviendo.des
    DocumentRoot /home/<USER>/www/simplementeviviendo/public
</VirtualHost>

<VirtualHost *:80>
   ServerName audiologiccorp.des
   DocumentRoot /home/<USER>/www/audiologiccorp/public
</VirtualHost>

# <VirtualHost *:80>
#    ServerName todoangostura.des
#    DocumentRoot /home/<USER>/www/todoangostura
# </VirtualHost>
