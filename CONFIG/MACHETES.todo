
grep 91279849 /saas/customer/services/acc/empresas/logs/api-v0.1/tokens.log
grep 4355483748 /saas/customer/services/acc/empresas/logs/api-v0.1/*
grep 4282731837 /saas/customer/services/acc/empresas/logs/notificaciones/2021-01-05*

scp -i ~/.ssh/desarrollo.pem ec2-user@**************:/saas/customer/services/acc/empresas/logs/api-v0.1/tokens.log .
scp -i ~/.ssh/desarrollo.pem ec2-user@**************:/saas/customer/services/acc/empresas/logs/api-v0.1/2021-06-15.log api-v0_1_2021-06-15.log
scp -i ~/.ssh/desarrollo.pem ec2-user@**************:/saas/customer/services/acc/empresas/logs/api-v0.2/2021-06-08.log api-v0_2_2021-06-08.log

scp -i ~/.ssh/andresmaiden2 <EMAIL>:/home/<USER>/backups/mysql_2021-08-08.sql.gz .

sudo cp /saas/customer/services/acc/empresas/logs/api-v0.1/2021-03-01.log /saas/customer/services/scripts/public/
sudo chown apache:apache /saas/customer/services/scripts/public/2021-03-01.log
scripts.saasargentina.com/2021-03-01.log

python /saas/customer/services/acc/tools/pyafipws/ws_sr_padron.py /saas/customer/services/acc/empresas/wsfe/30715446967.ini 20187874603 --constancia

ini_set('memory_limit', '-1');
ini_set('max_execution_time', 0);


*******************************************************************************
FILEZILLA DOWNLOAD FROM BACKUPS
j soporte
rm saas_6149.sql
scp -i ~/.ssh/desarrollo.pem ec2-user@**************:/saas/customer/services/acc/backups/saas_6149_2022- + tab
gzip -d saas_6149_2022 + tab
mv saas_6149{*,}.sql
sudo mysql -uroot -p

DROP DATABASE saas_6149;
CREATE DATABASE saas_6149;
USE saas_6149;
SOURCE saas_6149.sql;

DROP DATABASE cronometrajeinstantaneo;
CREATE DATABASE cronometrajeinstantaneo;
USE cronometrajeinstantaneo;
SOURCE cronometrajeinstantaneo.sql;


Cambio de versión PHP en apache:
sudo a2dismod php8.1
sudo a2enmod php7.4
sudo service apache2 restart

---
sudo a2dismod php7.4
sudo a2enmod php8.1
sudo service apache2 restart

---
sudo a2dismod php8.1-fpm
sudo a2disconf php8.1-fpm
sudo a2enmod php7.4-fpm
sudo a2enconf php7.4-fpm
sudo service apache2 restart

update-alternatives --list php
sudo update-alternatives --set php /usr/bin/php7.4
sudo update-alternatives --set phar /usr/bin/phar7.4
sudo update-alternatives --set phar.phar /usr/bin/phar.phar7.4
sudo update-alternatives --set phpize /usr/bin/phpize7.4
sudo update-alternatives --set php-config /usr/bin/php-config7.4

sudo php-fpm7.4 -t
sudo service php8.1-fpm restart

Arreglar composer:
composer clearcache
composer selfupdate

Instalar Laravel:
composer create-project laravel/laravel laravel


*******************************************************************************

In Laravel you can add error_reporting(0) or whatever you want into \app\Providers\AppServiceProvider.php boot() method

cordova create hello com.example.hello HelloWorld
cordova platform update android@latest

## Para Laravel
sudo chown -R andresmaiden:www-data storage
sudo chown -R andresmaiden:www-data bootstrap/cache
chmod -R 775 storage
chmod -R 775 bootstrap/cache

## Ver para otros proyectos
Permisos para /www cuando el www-data pertenece al grupo
chmod 750 $(find . -type d)
chmod 640 $(find . -type f)
umask 0002 -R *
Permisos para /www cuando el www-data NO pertenece al grupo
chmod 775 $(find . -type d)
chmod 664 $(find . -type f)
umask 0002 -R *

sudo find . -type d -exec chmod 775 {} \;
sudo find . -type f -exec chmod 664 {} \;
sudo find . -type f -exec chown apache:apache {} \;


sudo mysqldumpslow -a -s t -t 5 /var/log/mysql/mysql-slow.log
mysql> use saas_1991
mysql> source saas_1991.sql;

Comprimir: tar -zcvf tar-archive-name.tar.gz source-folder-name
Descomprimir: tar -zxvf tar-archive-name.tar.gz

sudo rfkill unblock bluetooth

git submodule foreach --recursive git checkout -b working-demo_deadline

scp -P 3223 <EMAIL>:/saas/customer/services/empresas/logs/productos_2030.csv .

scp -i ~/.ssh/andresmaiden andresmaiden@23.251.144.81:/home/<USER>/backups/mysql_2018-08-17.sql.gz .

0 6 * * * /home/<USER>/backup_collector.sh
0 0 * * * for i in `find /home/<USER>/mysql_dumps -type f -mtime +60`; do rm -f "$i"; done



Bluetooth en Debian:
firmware: failed to load intel/ibt-12-16.sfi
sudo wget https://git.kernel.org/pub/scm/linux/kernel/git/firmware/linux-firmware.git/plain/intel/ibt-12-16.sfi
sudo -H pactl load-module module-bluetooth-discover


Video para whatsapp: MP4 Quicktime  / 640x352 / H.264 / 24 fps / bitrate 796Kbps

equipo_bd / conexion_sql / conexion / bd_equipo
equipos_str / consulta_sql / consulta_equipos / str_equipos
equipos_sql / resultado_sql / resultado_equipos / sql_equipos
equipos_res / datos_sql / datos_equipos / res_equipos

ini_set('display_errors', 1);
ini_set('display_startup_errors', 1);
error_reporting(E_ALL);

Update npm to last packages. Looks like npm-check-updates is the only way to make this happen now.
npm i -g npm-check-updates
ncu -u
npm install
