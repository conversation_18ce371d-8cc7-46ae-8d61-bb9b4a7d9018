#!/bin/bash
date=`date +%Y%m%d`
#user=`whoami`
#dir=/home/"$user"/mysql_dumps
dir=/home/<USER>/MANTENIMIENTO/BACKUP/mysqldumps

#ls $dir
#if [ ! -z $? ]
#then
#  mkdir $dir
#fi

cd $dir
touch remote_content.tmp
touch local_content.tmp
touch backup_content.tmp

ssh -p 3223 <EMAIL> ls /home/<USER>/backups > remote_content.tmp
#ls . | grep *.gz > local_content.tmp
ls *.gz > local_content.tmp
diff remote_content.tmp local_content.tmp | grep '^<' | awk '{print $2}' > backup_content.tmp

awk '{print "scp -P 3223 -q <EMAIL>:/home/<USER>/backups/"$0" ./"}' backup_content.tmp | sh

rm -f remote_content.tmp local_content.tmp backup_content.tmp

exit 0
